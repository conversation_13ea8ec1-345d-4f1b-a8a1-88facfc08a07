# Fans United Automation Project

## Getting Started

## Run test on local environment

1. Start all APIs in dockerized environment (local-env)
2. Set the following environment variables
    - FIREBASE_AUTH_EMULATOR_HOST=localhost:19199
    - FIRESTORE_EMULATOR_HOST=localhost:18084
    - GCLOUD_PROJECT=automationtesting-project
3. Set java parameter ```-Denv=local```
4. Run the test

## Run test on cloud vm instance
1. Start cloud VM instance
2. Start all APIs in dockerized environment (local-env)
```shell script
gcloud compute ssh --zone "europe-west1-b" "local-env-instance-1" --project "fans-united-dev"
```
or for vm2
```shell script
gcloud compute ssh --zone "europe-west1-b" "local-env-instance-2" --project "fans-united-dev"
```

```
tmux
sudo su
cd /opt/local-env
./start.sh
```
3. Right click on the test
4. Click on Modify Run Configuration
5. Set the following environment variables
    - FIREBASE_AUTH_EMULATOR_HOST=*************:19199
    - FIRESTORE_EMULATOR_HOST=*************:18084
    - GCLOUD_PROJECT=automationtesting-project
      ```FIREBASE_AUTH_EMULATOR_HOST=*************:19199;FIRESTORE_EMULATOR_HOST=*************:18084;GCLOUD_PROJECT=automationtesting-project```
    - For VM2:
   - FIREBASE_AUTH_EMULATOR_HOST=*************:19199
   - FIRESTORE_EMULATOR_HOST=*************:18084
   - GCLOUD_PROJECT=automationtesting-project
     ```FIREBASE_AUTH_EMULATOR_HOST=*************:19199;FIRESTORE_EMULATOR_HOST=*************:18084;GCLOUD_PROJECT=automationtesting-project```
7. Set java parameter ```-Denv=vm2```
7. Run the test

## Install Google cloud SQL proxy
1. Go to [GIT repo](https://github.com/GoogleCloudPlatform/cloud-sql-proxy#installation:~:text=configuring%20Public%20IP.-,Installation,-Check%20for%20the)
and follow the instructions.
2. You will need to be authorized, open terminal and run ```gcloud auth application-default login```
3. To run it use ```sh proxystage.sh```

Note: you can use two environments now vm1 and vm2

