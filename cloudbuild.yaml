###########################################################
# Google Cloud Build script for automation testing
#
# This build script is used to run automation tests on Google Cloud Build
#
# Manual Execution:
#
# gcloud builds submit --config=cloudbuild.yaml .
###########################################################
steps:
  ###########################################################
  # Step 1: Download cached maven dependencies
  ###########################################################
  - id: 'download-cached-maven-dependencies'
    name: gcr.io/cloud-builders/gsutil
    entrypoint: bash
    volumes:
      - name: 'maven-repository'
        path: '/root/.m2'
    args:
      - '-c'
      - |
        gsutil -m cp gs://${PROJECT_ID}_cloudbuild/maven/maven-${_IMAGE_NAME}-dependencies.tgz maven-dependencies.tgz || exit 0
        tar -zxvf maven-dependencies.tgz --directory / || exit 0
#  ###########################################################
#  # Step 2: Run tests
#  ###########################################################
  - id: 'test-project'
    name: 'eu.gcr.io/${PROJECT_ID}/quarkus-maven-builder:17'
    env:
      - 'ENV_PROFILE=$_ENV_PROFILE'
      - 'TEST_SUITE=$_TEST_SUITE'
    entrypoint: './cicd/runtestsuite.sh'
    volumes:
      - name: 'maven-repository'
        path: '/root/.m2'
  ###########################################################
  # Step 3: Copy report to Google Cloud Storage
  ###########################################################
  - id: 'copy-report'
    name: gcr.io/cloud-builders/gsutil
    entrypoint: bash
    args:
      - '-c'
      - |
        ls -l
        echo "status:"
        cat status.txt
        dir=report-$(date +%F_%H:%M:%S)-$BUILD_ID
        gsutil -m cp -r ./target/report/ gs://${PROJECT_ID}_automation_reports/$dir
        url=https://storage.cloud.google.com/fans-united-stage_automation_reports/$dir/SparkReport.html?authuser=1
        echo "{\"text\":\"Automation report generated and published on $url\"}" > msg.txt
        cat msg.txt
        curl -X POST -H 'Content-type: application/json' --data "@msg.txt" *********************************************************************************
        grep 0 status.txt
        status=$?
        exit $status
  ###########################################################
  # Step 4: Cache maven dependencies
  ###########################################################
  - id: 'upload-cached-maven-dependencies'
    waitFor: [ 'test-project' ]
    name: gcr.io/cloud-builders/gsutil
    entrypoint: bash
    volumes:
      - name: 'maven-repository'
        path: '/root/.m2'
    args:
      - '-c'
      - |
        tar -zcvf maven-dependencies.tgz /root/.m2 || exit 0
        gsutil -m cp maven-dependencies.tgz gs://${PROJECT_ID}_cloudbuild/maven/maven-${_IMAGE_NAME}-dependencies.tgz || exit 0


timeout: '1200s'
substitutions:
  _IMAGE_NAME: 'fansunited-automation'
options:
  machineType: 'N1_HIGHCPU_8'