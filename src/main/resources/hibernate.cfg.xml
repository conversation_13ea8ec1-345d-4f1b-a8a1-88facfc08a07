<?xml version = "1.0" encoding = "utf-8"?>
<!DOCTYPE hibernate-configuration SYSTEM
    "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
  <session-factory>
    <property name="hibernate.dialect">
      org.hibernate.dialect.MySQLDialect
    </property>
    <property name="hibernate.connection.driver_class">
      com.mysql.cj.jdbc.Driver
    </property>
    <property name="hibernate.current_session_context_class">thread</property>
    <!--    <property name="show_sql">-->
    <!--      true-->
    <!--    </property>-->
<!--    <property name="connection.pool_size">200</property>-->
  </session-factory>
</hibernate-configuration>