votingApiBaseUrl=https://voting.fansunitedstagingapi.com
votingApiPort=443
miniGamesApiBaseUrl=https://mini-games.fansunitedstagingapi.com
miniGamesApiPort=443
discussionApiBaseUrl=https://discussions.fansunitedstagingapi.com
discussionApiPort=443
leaguesApiBaseUrl=https://private-leagues.fansunitedstagingapi.com
leaguesApiPort=443
profileApiBaseUrl=https://profile.fansunitedstagingapi.com
profileApiPort=443
footballApiBaseUrl=https://football.fansunitedstagingapi.com
footballApiPort=443
predictionApiBaseUrl=https://prediction.fansunitedstagingapi.com
predictionApiPort=443
resolverApiBaseUrl=https://prediction-resolver-6nkiyrdjfq-ew.a.run.app
resolverApiPort=443
clientApiBaseUrl=https://client.fansunitedstagingapi.com
clientApiPort=443
reportingApiBaseUrl=https://reporting.fansunitedstagingapi.com
reportingApiPort=443
loyaltyApiBaseUrl=https://loyalty.fansunitedstagingapi.com
loyaltyApiPort=443
mockApiBaseUrl=https://mock-6nkiyrdjfq-ew.a.run.app
mockApiPort=443
firebaseWebApiKeyProfileProject=AIzaSyBKhAYwkvkmjm16FxCMvCUQjSRpKC0DbM8
firebaseWebApiKeyClientsProject=AIzaSyBKhAYwkvkmjm16FxCMvCUQjSRpKC0DbM8
firebasePathToServiceAccountProfileProject=/src/main/resources/FansUnitedStageProjectServiceAccountKey.json
googleEndpointsApiKey=AIzaSyBu4pRQ48MoMmq4CfsnhR4aapn1PrNcwXs
mysqlConnectionUrl=******************************************************
mysqlUser=football-api
mysqlPass=w2fbb1g0xk4sesm77xbthb2w85
identityToolkitProfileUrl=https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword
identityToolkitClientUrl=https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword
useFirebaseEmulator=false
authUserPlatformOperator=<EMAIL>
authUserClientAdmin=<EMAIL>
authUserBillingManager=<EMAIL>
baseAuthUser=ringierwebhookuser
baseAuthPass=ringierPass