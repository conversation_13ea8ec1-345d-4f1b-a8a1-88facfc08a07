package com.fansunited.automation.constants;

public class RegexConstants {
  public static final String NOT_CONTAINS_INVALID_OR_SINGLE = "^((?!INVALID|SINGLE).)*$";
  public static final String NOT_CONTAINS_WORD_INVALID = "^((?!INVALID).)*$";
  public static final String LEAGUE_INVITATION_CODE_REGEX = "[A-Za-z0-9]{10}";
  public static final String CONTAINS_SPECIAL_CHARACTERS = ".*[^a-zA-Z0-9 ].*";
  public static final String SPECIAL_CHARACTERS_TO_BE_REPLACED = "[^a-zA-Z0-9]";
  public static final String CONTAINS_WORD_INVALID = "INVALID.*$";
  public static final String CONTAINS_ABBREVIATION_MQ = "MQ.*$";
  public static final String CONTAINS_ABBREVIATION_TX = "MQTX.*$|TX.*$";
  public static final String GAME_STATUSES_NOT_OPEN_FOR_PREDICTIONS =
      "^((?!INVALID|INVALID_EMPTY|OPEN).)*$";
  public static final String GAME_STATUSES_NOT_AVAILABLE_FOR_UPDATE_IF_GAME_IS_OPENED =
      "^((?!INVALID|INVALID_EMPTY|PENDING|CANCELED|OPEN).)*$";
}
