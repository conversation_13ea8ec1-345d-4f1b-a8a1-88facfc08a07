package com.fansunited.automation.constants;

public class Endpoints {

  public static final class ProfileApi {
    public static final String COUNTRIES = "/v1/countries";
    public static final String PROFILE = "/v1/profile";
    public static final String PROFILE_BADGES = PROFILE + "/badges";
    public static final String PROFILES = "/v1/profiles";
    public static final String PROFILE_BY_ID = PROFILES + "/{userId}";
    public static final String PROFILE_BY_ID_BADGES = PROFILE + "/{userId}/badges";
    public static final String FOLLOW_PROFILES = PROFILE + "/follow";
    public static final String PROFILE_FOLLOWERS = PROFILE + "/followers";
    public static final String FOLLOWERS_BY_PROFILE_ID = PROFILE + "/{userId}" + "/followers";
    public static final String PROFILE_FOLLOWINGS = PROFILE + "/following";
    public static final String FOLLOWINGS_BY_PROFILE_ID = PROFILE + "/{userId}" + "/following";
    public static final String RINGER_PROFILE = "/v1/webhooks/ringier";
    public static final String VERIFIED_USER = PROFILES + "/{userId}/verify";
    public static final String LEADS = "/v1/leads";
    public static final String LEADS_BY_ID = LEADS + "/{lead_id}";
    public static final String LEADS_STATISTICS = LEADS + "/statistics";
    public static final String EXPORT_LEADS = LEADS + "/export";
    public static final String UPDATE_PROFILE_AS_ADMIN = PROFILE_BY_ID + "/verify";
    public static final String DELETE_OWN_PROFILE = "/v1/user-data";
  }

  public static final class PredictionApi {
    public static final String GAMES = "/v1/games";
    public static final String GAME_BY_ID = GAMES + "/{gameId}";
    public static final String GAME_RESULTS = GAME_BY_ID + "/results";
    public static final String PREDICTIONS_FOR_GAME = GAME_BY_ID + "/predictions";
    public static final String PREDICTIONS = "/v1/predictions";
    public static final String PREDICTION_BY_ID = PREDICTIONS + "/{predictionId}";
    public static final String PREDICTIONS_SUMMARY = PREDICTIONS + "/summary/{matchId}";
    public static final String PREDICTIONS_BY_USER_ID = PREDICTIONS + "/users/{userId}";
    public static final String REDIS = "/v1/redis";
    public static final String REDIS_GAMES = REDIS + "/games";
    public static final String REDIS_GAME_BY_ID = REDIS_GAMES + "/{gameId}";
    public static final String REDIS_ACTIVE_GAMES = REDIS_GAMES + "/active";
    public static final String REDIS_MATCHES = REDIS + "/matches";
    public static final String REDIS_PREDICTIONS = REDIS + "/predictions";
    public static final String REDIS_PREDICTIONS_BY_MATCH_ID = REDIS_PREDICTIONS + "/{matchId}";
    public static final String REDIS_PROCESSING_PREDICTIONS = REDIS_PREDICTIONS + "/processing";
    public static final String GAME_CORRECT_RESULTS = GAME_BY_ID + "/correct-results";
    public static final String WINNERS = "/v1/winners";
    public static final String ANNOUNCE = WINNERS + "/announce";
    public static final String REDIS_CLEAR_ALL = "/v1/tools/clear-all";
    public static final String GET_USER_DATA = PREDICTIONS + "/userdata";
    public static final String BRACKET_GAME = "/v1/custom/bracket";
    public static final String GET_BRACKET_GAME = BRACKET_GAME + "/{bracket_id}";
    public static final String PARTICIPATE_IN_BRACKET_GAME = GET_BRACKET_GAME + "/predictions";
    public static final String STANDING_GAME = "/v1/custom/standing";
    public static final String STANDING_GAME_BY_ID = STANDING_GAME + "/{game_id}";
    public static final String PARTICIPATE_IN_STANDING_GAME =
        STANDING_GAME + "/{game_id}/predictions";
    public static final String CREATE_CUSTOM_LEADERBOARD = "/v1/custom/leaderboards";
    public static final String GET_CUSTOM_LEADERBOARD = CREATE_CUSTOM_LEADERBOARD + "/{leaderboard_id}";
    public static final String GET_CUSTOM_LEADERBOARD_RANKINGS ="/v1/custom/leaderboards/{custom_leaderboard_id}/ranking";

    public static final String DELETE_USER_DATA = "/v1/user-data";
    public static final String CUSTOM_EVENT_GAME = "/v1/custom/event";
    public static final String CUSTOM_EVENT_GAME_BY_ID = CUSTOM_EVENT_GAME +"/{event_id}";

  }

  public static final class FootballApi {
    public static final String PLAYERS = "/v1/players";
    public static final String PLAYER_BY_ID = PLAYERS + "/{playerId}";
    public static final String PLAYER_NEXT_MATCH = PLAYER_BY_ID + "/next-match";
    public static final String PLAYER_PREVIOUS_MATCH = PLAYER_BY_ID + "/previous-match";
    public static final String TOP_PLAYERS = PLAYERS + "/top";
    public static final String TEAMS = "/v1/teams";
    public static final String TEAM_BY_ID = TEAMS + "/{teamId}";
    public static final String TEAM_PREVIOUS_MATCH = TEAM_BY_ID + "/previous-match";
    public static final String TEAM_NEXT_MATCH = TEAM_BY_ID + "/next-match";
    public static final String TOP_TEAMS = TEAMS + "/top";
    public static final String COMPETITIONS = "/v1/competitions";
    public static final String COMPETITION_BY_ID = COMPETITIONS + "/{id}";
    public static final String TOP_COMPETITIONS = COMPETITIONS + "/top";
    public static final String MATCHES = "/v1/matches";
    public static final String MATCH_BY_ID = MATCHES + "/{matchId}";
    public static final String COUNTRIES = "/v1/countries";
    public static final String SEARCH = "/v1/search";
    public static final String PLAYER_STATS = "/v1/matches/player-stats";
  }

  public static final class ReportingApi {
    public static final String USERS = "/v1/users";
    public static final String USERS_COUNTRIES = USERS + "/countries";
    public static final String USERS_MONTHLY_ACTIVE = USERS + "/mau";
    public static final String USERS_MONTHLY_ACTIVE_VS_REGISTRATION = USERS + "/mauvsreg";
    public static final String USERS_TOTAL = USERS + "/total";
    public static final String FOLLOWS = "/v1/follows";
    public static final String FOLLOWS_AVERAGES = FOLLOWS + "/averages";
    public static final String FOLLOWS_FOOTBALL = FOLLOWS + "/football";
    public static final String FOLLOWS_FOOTBALL_COMPS = FOLLOWS_FOOTBALL + "/competitions";
    public static final String FOLLOWS_BY_COMP_ID_FOOTBALL = FOLLOWS_FOOTBALL_COMPS + "/{compId}";
    public static final String SUCCESS_RATE_FOR_CLIENT = "/v1/success-rates/football/markets";
    public static final String SUCCESS_RATE_SPORTS_ENTITIES = "/v1/success-rates/football/entities";
    public static final String CLIENT_PREDICTIONS = "/v1/predictions";
    public static final String CLIENT_PREDICTIONS_FOR_PERIOD = CLIENT_PREDICTIONS + "/football";
    public static final String CLIENT_TOTAL_PREDICTIONS = CLIENT_PREDICTIONS_FOR_PERIOD + "/total";
    public static final String EITHER_OR_PARTICIPATIONS = "/v1/either-or/participations";
  }

  public static final class ClientApi {

    // roles
    public static final String ROLES = "/v1/roles";

    // clients
    public static final String CLIENTS = "/v1/clients";
    public static final String CLIENT_BY_ID = CLIENTS + "/{client_id}";

    // features
    public static final String FEATURES = "/v1/clients/{client_id}/features";
    public static final String FEATURES_TOP_X = FEATURES + "/top_x";
    public static final String FEATURES_MATCH_QUIZ = FEATURES + "/match_quiz";
    public static final String FEATURES_PREDICTOR = FEATURES + "/predictor";
    public static final String FEATURES_LOYALTY = FEATURES + "/loyalty";
    public static final String FEATURES_CLASSIC_QUIZ = FEATURES + "/classic_quiz";
    public static final String FEATURES_FANTASY_GAME = FEATURES + "/football_fantasy";
    public static final String FEATURES_EITHER_OR = FEATURES + "/either_or";
    public static final String FEATURES_REWARDS = FEATURES + "/external_points";
    public static final String FEATURES_BADGES = FEATURES_LOYALTY + "/badges";
    public static final String FEATURES_FOOTBALL_FANTASY = FEATURES + "/football_fantasy";
    public static final String FEATURES_POTM = FEATURES + "/potm";
    public static final String FEATURES_PROFILE_PREFERENCES = FEATURES + "/profile_preferences";

    // staff
    public static final String POST_STAFF_MEMBER = CLIENT_BY_ID + "/staff";
    public static final String GET_STAFF_MEMBER = CLIENT_BY_ID + "/staff";
    public static final String GET_STAFF_MEMBER_USER_ID = "/v1/staff/{user_id}/clients";
    public static final String GET_STAFF_MEMBERS_BY_USER_AND_CLIENT_ID =
        CLIENT_BY_ID + "/staff/{user_id}";
    public static final String SET_GLOBAL_ROLES = "/v1/staff/{user_id}";
    public static final String DELETE_STAFF_MEMBER_USER_ID = CLIENT_BY_ID + "/staff/{user_id}";
    public static final String UPDATE_STAFF_MEMBER_USER_ID = DELETE_STAFF_MEMBER_USER_ID;

    // profile
    public static final String GET_PROFILE_FOR_USER = "/v1/profile";
    // cache ttl
    public static final String CACHE_TTL_CONFIG = "/v1/client/config/cache_ttl/{client_id}";

    // lists
    public static final String GET_LISTS = CLIENTS + "/{client_id}/lists";
    public static final String GET_LIST_BY_ID = GET_LISTS + "/{list_id}";
    public static final String DELETE_LIST_BY_ID = GET_LIST_BY_ID;
    public static final String UPDATE_LIST_BY_ID = GET_LIST_BY_ID;
    public static final String CREATE_LIST = GET_LISTS;
  }

  public static final class ResolverApi {
    public static final String RESOLVER = "/v1/resolver";
    public static final String EXECUTE = RESOLVER + "/execute";
  }

  public static final class CustomResolverApi {
    public static final String CUSTOM_RESOLVER = "/v1/custom";
    public static final String STANDING_RESOLVER = CUSTOM_RESOLVER + "/standing/resolve";
    public static final String RESOLVER_BRACKET = CUSTOM_RESOLVER + "/bracket/resolve";
    public static final String LEADERBOARD_RESOLVER = CUSTOM_RESOLVER + "/leaderboard/resolve";
  }

  public static final class LoyaltyApi {
    public static final String LEADERBOARD = "/v1/leaderboard";
    public static final String LEADERBOARD_BY_ID = LEADERBOARD + "/{templateId}";
    public static final String LEADERBOARD_RANKING = LEADERBOARD + "/users/{userId}/rankings";
    public static final String LEADERBOARD_BY_GAME_ID = LEADERBOARD + "/games/{gameId}";
    public static final String TEMPLATES = LEADERBOARD + "/templates";
    public static final String TEMPLATE_BY_ID = TEMPLATES + "/{templateId}";
    public static final String ACTIVITIES = "/v1/activities";
    public static final String ACTIVITY_BY_ID = ACTIVITIES + "/{id}";
    public static final String ACTIVITIES_BY_USER_ID = "/v1/users/{userId}/activities";
    public static final String STATISTICS = "/v1/statistics";
    public static final String STATISTICS_BY_USER_ID = "/v1/users/{userId}/statistics";
    public static final String TOP_RATES_BY_USER_ID = "/v1/statistics/top";
    public static final String BADGES_BY_USER_ID = "/v1/badges/{userId}";
    public static final String CONTEST = "/v1/contest/{contest_id}/validate/profiles";
    public static final String REWARDS = "/v1/points/reward";
    public static final String DETAILED_LEADERBOARD_INFO = "/v1/leaderboard/{template_id}/detailed";
    public static final String DELETE_USER_DATA = "/v1/user-data";
  }

  public static final class MockApi {
    public static final String ACTIVITY = "/v1/activity";
    public static final String REGISTRATION = ACTIVITY + "/registration";
    public static final String PROFILE = ACTIVITY + "/profile";
    public static final String COUNTRY = ACTIVITY + "/country";
    public static final String GENDER = ACTIVITY + "/gender";
    public static final String INTEREST = ACTIVITY + "/interest";
    public static final String INTEREST_FOLLOW = INTEREST + "/follow";
    public static final String RANKINGS = ACTIVITY + "/rankings";
    public static final String PREDICTION_MADE = ACTIVITY + "/prediction/made";
    public static final String CORRECT_PREDICTION = ACTIVITY + "/prediction/correct";
    public static final String REPORTING = "/v1/reporting";
    public static final String TRUNCATE = REPORTING + "/truncate";
  }

  public static final class LeaguesApi {
    public static final String CREATE_LEAGUE = "/v1/leagues";
    public static final String GET_LEAGUE_BY_ID = CREATE_LEAGUE + "/{league_id}";
    public static final String UPDATE_LEAGUE_BY_ID = GET_LEAGUE_BY_ID;
    public static final String UPDATE_LEAGUE_TEMPLATE_BY_ID = GET_LEAGUE_BY_ID + "/template";
    public static final String DELETE_LEAGUE_BY_ID = GET_LEAGUE_BY_ID;
    public static final String GET_PREDICTIONS_FOR_LEAGUE = GET_LEAGUE_BY_ID + "/predictions";
  }

  public static final class MembershipApi {
    public static final String INVITE_MEMBERS = "/v1/membership/invite/{league_id}";
    public static final String JOIN_MEMBER = "/v1/membership/join";
    public static final String BAN_MEMBERS = "/v1/membership/ban/{league_id}";
    public static final String UNBAN_MEMBERS = "/v1/membership/unban/{league_id}";
    public static final String ACCEPT_MEMBER = "/v1/membership/accept/{league_id}";
    public static final String LEAVE_MEMBER = "/v1/membership/leave/{league_id}";
    public static final String GET_INVITATION = "/v1/membership/invitations";
    public static final String DELETE_INVITATION = INVITE_MEMBERS;
  }

  public static final class DiscussionApi {
    public static final String CREATE_DISCUSSION = "/v1/discussions";
    public static final String CREATE_POST = "/v1/discussions/{discussionId}/posts";
    public static final String GET_DISCUSSION_BY_ID = "/v1/discussions/{discussionId}";
    public static final String GET_POST_BY_POST_ID = "/v1/posts/{postId}";
    public static final String GET_REPLIES_POSTS = GET_POST_BY_POST_ID + "/replies";
    public static final String UPDATE_USERS_POSTS = GET_POST_BY_POST_ID;
    public static final String REACT_ON_USERS_POST = GET_POST_BY_POST_ID + "/reaction";
    public static final String MODERATE_POST_BY_STAFF = GET_POST_BY_POST_ID + "/moderate";
    public static final String DELETE_USERS_POSTS = GET_POST_BY_POST_ID;
    public static final String GET_POST_BY_USER_ID = "/v1/posts/users/{userId}";
    public static final String GET_REPORTED_POSTS_BY_LEAGUE_ID =
        CREATE_DISCUSSION + "/{league_id}/posts/reported";
    public static final String REPORT_USER_POST = GET_POST_BY_POST_ID + "/report";
    public static final String GET_FILTERED_POSTS = "/v1/posts/staff";
    public static final String GET_REPORTED_POSTS = GET_FILTERED_POSTS + "/reported";
    public static final String BAN_USER = CREATE_DISCUSSION + "/users/{user_id}/ban";
    public static final String PINNED_POSTS_DISCUSSION = GET_DISCUSSION_BY_ID + "/pins";
    public static final String GET_DISCUSSION_POSTS = CREATE_DISCUSSION + "/{discussionId}/posts";
    public static final String GET_PUBLIC_DISCUSSION = CREATE_DISCUSSION;
    public static final String GET_USERS_OWN_POSTS = "/v1/posts";
    public static final String MODERATE_POST_BY_LEAGUE_ADMIN =
        GET_POST_BY_POST_ID + "/admin/moderate";
    public static final String GET_BANNED_USER_HISTORY = BAN_USER;
    public static final String GET_DISCUSSIONS_POSTS_COUNT = CREATE_DISCUSSION + "/posts/count";
  }

  public static final class MiniGamesApi {
    public static final String CREATE_QUIZ = "/v1/classic-quizzes";
    public static final String DELETE_QUIZ = CREATE_QUIZ + "/{classic_quiz_id}";
    public static final String QUIZ_PARTICIPATION = DELETE_QUIZ + "/participate";
    public static final String GET_QUIZ_BY_STAFF = DELETE_QUIZ + "/staff";
    public static final String USERS_PARTICIPATION = "/v1/classic-quizzes/participations";
    public static final String QUIZ_RANK = CREATE_QUIZ + "/rank";
    public static final String USER_QUIZ_RANK = CREATE_QUIZ + "/users/{user_id}/rank";
    public static final String EITHER_OR = "/v1/either-or";
    public static final String EITHER_OR_BY_ID = EITHER_OR + "/{either_or_id}";
    public static final String PLAY_IN_EITHER_OR = EITHER_OR_BY_ID + "/participate";
    public static final String EITHER_OR_OWN_STATS = EITHER_OR_BY_ID + "/own-stats";
    public static final String EITHER_OR_RESULTS = EITHER_OR_BY_ID + "/results";
    public static final String EITHER_OR_OWN_STAFF = EITHER_OR_BY_ID + "/staff";
  }

  public static final class VotingApi {
    public static final String CREATE_POLL = "/v1/polls";
    public static final String GET_POLL_BY_ID = CREATE_POLL + "/{poll_id}";
    public static final String GET_POLLS = CREATE_POLL;
    public static final String GET_OWN_VOTES = CREATE_POLL + "/votes";
    public static final String GET_OWN_VOTES_FOR_A_POLL = GET_POLL_BY_ID + "/votes";
    public static final String DELETE_POLLS = GET_POLL_BY_ID;
    public static final String DELETE_POLLS_VOTES = GET_OWN_VOTES_FOR_A_POLL;
    public static final String PUT_VOTE_FOR_A_POLL = GET_OWN_VOTES_FOR_A_POLL;
    public static final String GET_OWN_POTM_VOTES = "/v1/football/potm";
    public static final String GET_POTM_RESULTS = GET_OWN_POTM_VOTES + "/{match_id}";
    public static final String PUT_VOTE_FOR_POTM = GET_POTM_RESULTS;
    public static final String DELETE_VOTE_FOR_POTM = GET_POTM_RESULTS;
  }
}
