package com.fansunited.automation.constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class ApiConstants {

  // Common constants for all APIs
  @Getter
  @AllArgsConstructor
  public enum HttpMethods {
    POST("POST"),
    GET("GET"),
    PATCH("PATCH"),
    DELETE("DELETE"),
    PUT("PUT"),
    HEAD("HEAD"),
    OPTIONS("OPTIONS");

    private final String value;

    public static List<String> getFilteredValuesAsList(HttpMethods skipMethod) {
      return Arrays.stream(HttpMethods.values()).filter(method-> !skipMethod.equals(method))
              .map(HttpMethods::getValue)
              .collect(Collectors.toList());
    }
    public static List<String> getValuesAsList() {
      return Arrays.stream(HttpMethods.values())
              .map(HttpMethods::getValue)
              .collect(Collectors.toList());
    }
  }

  // Number of allowed IDs(comp, team, match, player etc...) that can be attached as query param in order to avoid URL too long exception
  public static final int LIMIT_QUERY_IDS = 199;

  public static final int LIMIT_PARAM_DEFAULT_VALUE = 20;
  public static final int DEFAULT_PAGE_LIMIT = 10;

  @Getter
  @AllArgsConstructor
  public enum SortOrder {
    ASC("asc"),
    DESC("desc");

    private final String value;
  }

  public static final class ProfileApi {

    public static final String BIRTHDATE_PROP = "birth_date";
    public static final String INTERESTS_PROP = "interests";
    public static final String NAME_PROP = "name";
    public static final String GENDER_PROP = "gender";
    public static final String COUNTRY_PROP_REQUEST = "country_id";
    public static final String COUNTRY_PROP_RESPONSE = "country";
    public static final String PHONE_COUNTRY_CODE = "phone_country_code";
    public static final String PHONE_NUMBER = "phone_number";
    public static final String AVATAR_PROP = "avatar";
    public static final String EMAIL_PROP = "email";
    public static final String PROFILE_IDS_PROP = "profile_ids";
    public static final String FOLLOWING_ID_PROP = "following_id";
    public static final String FOLLOWER_ID_PROP = "follower_id";
    public static final String FOLLOWING_COUNT = "following_count";
    public static final String FOLLOWERS_COUNT = "followers_count";
    public static final String PROFILE_ID_PROP = "profile_id";
    public static final String ANONYMOUS = "anonymous";

    public static final String COUNTRY_ID_BG = "profile:cnt:23";
    public static final String NICKNAME = "nickname";
    public static final String SUB = "sub";
    public static final String DELETED_USER_NAME = "Deleted user";
    public static final String CREATE_RINGER_PROFILE_TYPE = "user_signed_agreement";
    public static final String DELETE_RINGER_PROFILE_TYPE = "user_deleted";
    public static final String UPDATE_RINGER_PROFILE_TYPE = "user_fields_change";
    public static final String CHANGE_RINGER_PROFILE_EMAIL_TYPE = "email_changed";
    public static final String FIELDS = "fields";
    public static final String OLD_EMAIL = "old_email";
    public static final String NEW_EMAIL = "new_email";
    public static final String CREATED_AT = "created_at";

    public static final String STAFF_MEMBER="staff_member";
    public static final String VERIFIED="verified";
    public static final String CAMPAIGN_ID_PROP  = "campaign_id";
    public static final String CONTENT_TYPE_PROP  = "content_type";
    public static final String CONTENT_ID_PROP  = "content_id";
    public static final String LEAD_ID_PATH  = "lead_id";
    public static final String FROM_DATE_PROP = "from_date";
    public static final String TO_DATE_PROP = "to_date";



    @AllArgsConstructor
    public enum Interest {
      FOOTBALL("football"),
      TENNIS("tennis"),
      SPORT("sport"),
      CUSTOM("custom");

      @Getter
      private final String source;

      @AllArgsConstructor
      public enum Football {
        TEAM("team"),
        PLAYER("player"),
        COMPETITION("competition");

        @Getter
        private final String type;
      }

      @AllArgsConstructor
      public enum Tennis {
        PLAYER("player"),
        COMPETITION("competition");

        @Getter
        private final String tennisIntType;
      }

      @AllArgsConstructor
      public enum Sport {
        FOOTBALL("football"),
        CRICKET("cricket"),
        FIELD_HOCKEY("field-hockey"),
        TENNIS("tennis"),
        VOLLEYBALL("volleyball"),
        TABLE_TENNIS("table-tennis"),
        BASKETBALL("basketball"),
        BASEBALL("baseball"),
        RUGBY("rugby"),
        GOLF("golf"),
        ATHLETICS("athletics"),
        FORMULA_1("formula-1"),
        BOXING("boxing"),
        ICE_HOCKEY("ice-hockey"),
        AMERICAN_FOOTBALL("american-football"),
        MMA("mma"),
        MOTO_GP("motogp"),
        BADMINTON("badminton"),
        CYCLING("cycling"),
        SNOOKER("snooker"),
        GYMNASTICS("gymnastics"),
        HANDBALL("handball"),
        WRESTLING("wrestling"),
        HORSE_RACING("horse-racing"),
        WEIGHT_LIFTING("weight-lifting"),
        CHESS("chess"),
        SQUASH("squash"),
        BIATHLON("biathlon"),
        WINTER_SPORTS("winter-sports"),
        MARTIAL_ARTS("martial-arts"),
        WATER_SPORTS("water-sports"),
        SHOOTING("shooting"),
        RALLY("rally"),
        RHYTHMIC_GYMNASTICS("rhythmic-gymnastics");

        @Getter
        private final String sportIntType;
      }
    }
  }

  public static final class PredictionApi {

    // Used by the API, If predictions cutoff date is NOT provided when a game is created, API subtracts 15 minutes from the earliest match kickoff date time and sets it as game prediction cutoff
    public static final int PREDICTIONS_CUTOFF_SUBTRACTION_MINUTES = 15;
    public static final int LIMIT_PARAM_MAX_VALUE_GAMES = 50;
    public static final int DEFAULT_LIMIT_PARAM_GAME_RESULTS = 10;

    public static final String ID_PROP = "id";
    public static final String TITLE_PROP = "title";
    public static final String DESC_PROP = "description";
    public static final String PREDICTIONS_CUTOFF_PROP = "predictions_cutoff";
    public static final String FIXTURES_PROP = "fixtures";
    public static final String STATUS_PROP = "status";
    public static final String TYPE_PROP = "type";
    public static final String CREATED_AT_PROP = "created_at";
    public static final String UPDATED_AT_PROP = "updated_at";
    public static final String GAME_INSTANCE_ID_PROP = "game_instance_id";
    public static final String SCHEDULE_OPEN_AT = "schedule_open_at";
    public static final String WAGER_PROP = "wager";
    public static final String TOTAL_FIXTURES_PROP = "total_fixtures";
    public static final String SETTLED_FIXTURES_PROP = "settled_fixtures";
    public static final String USER_ID_PROP = "user_id";
    public static final String TIE_BREAKERS = "tie_breakers";
    public static final String TIEBREAKER_PROP = "tiebreaker";
    public static final String GOLDEN_GOALS_PROP = "golden_goals";
    public static final String MINUTE_PROP = "minute";
    public static final String PLAYER_ID_PROP = "player_id";
    public static final String POINTS_PROP = "points";
    public static final String MATCH_ID_PROP = "match_id";
    public static final String MATCH_TYPE_PROP = "match_type";
    public static final String GOAL_TYPE_PROP = "goal_type";
    public static final String RESULT_PROP = "result";
    public static final String RESULT_SETTLED_AT_PROP = "settled_at";
    public static final String RESULT_RESETTLED_AT_PROP = "resettled_at";
    public static final String RESULT_STATUS = "status";
    public static final String RESULT_OUTCOME = "outcome";
    public static final String PREDICTIONS_PROP = "predictions";
    public static final String RESULTS_PROP = "results";
    public static final String IMAGES_PROP = "images";
    public static final String IMAGES_MAIN = "main";
    public static final String IMAGES_COVER = "cover";
    public static final String IMAGES_MOBILE = "mobile";
    public static final String SCHEDULER = "schedule_open_at";
    public static final String QUERY_ERROR_MESSAGE = "Query params 'game_types', 'match_ids', 'game_ids' and 'status' cannot be used together.";
    public static final String CURRENT_PROFILE_ID_PROP = "current_profile_id";
    public static final String NEW_PROFILE_ID_PROP = "new_profile_id";
  }

  public static final class ClientApi {
    public static final String DEFAULT_BADGES_VALUES_JSON = "default-badges-values.json";
    public static final String MAX_FIXTURES_PROP = "max_fixtures";
    public static final String COMPETITIONS_WHITELIST_PROP = "competitions_whitelist";
    public static final String PREDICTOR_PROP = "predictor";
    public static final String FULL_COVERAGE_COMPETITIONS_WHITELIST_PROP =
        "full_coverage_competitions";
    public static final String DEFAULT_ROLES_ID = "default_roles";
    public static final String PERMISSION_CLIENTS_VIEW_ALL = "view_all_clients";
    public static final String PERMISSION_CLIENTS_CREATE = "create_client";
    public static final String PERMISSION_CLIENTS_UPDATE = "update_client";
    public static final String PERMISSION_MANAGE_STAFF = "manage_staff";
    public static final String PERMISSION_MANAGE_ID_MAPPINGS = "manage_id_mappings";
    public static final String PERMISSION_VIEW_PLATFORM_REPORTS = "view_platform_reports";
    public static final String PERMISSION_VIEW_REPORTS = "view_reports";
    public static final String PERMISSION_MANAGE_BILLING_DETAILS = "manage_billing_details";
    public static final String PERMISSION_MANAGE_INVOICES = "manage_invoices";
    public static final String PERMISSION_MANAGE_USERS = "manage_users";
    public static final String PERMISSION_DELETE_USER = "delete_user";
    public static final String PERMISSION_MANAGE_TOP_X = "manage_top_x";
    public static final String PERMISSION_MANAGE_FEATURE_TOP_X = "manage_feature_top_x";
    public static final String PERMISSION_MANAGE_MATCH_QUIZZES = "manage_match_quizzes";
    public static final String PERMISSION_MANAGE_FEATURE_MATCH_QUIZZES =
        "manage_feature_match_quizzes";
    public static final String PERMISSION_MANAGE_FEATURE_PREDICTOR = "manage_feature_predictor";
    public static final String PERMISSION_MANAGE_FEATURE_LOYALTY = "manage_feature_loyalty";
    public static final String PERMISSION_MANAGE_LEADERBOARD_TEMPLATES =
        "manage_leaderboard_templates";
    public static final String ROLE_PLATFORM_OPERATOR = "platform_operator";
    public static final String ROLE_CLIENT_BILLING_MANAGER = "client_billing_manager";
    public static final String ROLE_CLIENT_EDITOR = "client_editor";
    public static final String ROLE_CLIENT_PRODUCT_MANAGER = "client_product_manager";
    public static final String ROLE_CLIENT_ADMIN = "client_admin";
    public static final String ROLE_USER = "user";
    public static final String ROLE_SERVICE_USER = "service_user";

    public static final String CLIENT_ID = "staff_test";
  }

  public static final class FootballApi {
    public static final int LIMIT_PARAM_MAX_VALUE_MATCHES = 500;
    public static final int LIMIT_PARAM_MAX_VALUE_TEAMS = 200;
    public static final int LIMIT_PARAM_MAX_VALUE_PLAYERS = 200;

    public static final String FU_PLAYER_ID_PREFIX = "fb:p:";
    public static final String FU_TEAM_ID_PREFIX = "fb:t:";
    public static final String FU_MATCH_ID_PREFIX = "fb:m:";

    public static final String ID_PROP = "id";
    public static final String KICK_OFF_AT_PROP = "kickoff_at";
    public static final String HOME_TEAM_PROP = "home_team";
    public static final String AWAY_TEAM_PROP = "away_team";
    public static final String CONTEXT_PROP = "context";
    public static final String SQUAD_PROP = "squad";
    public static final String COUNTRY_PROP = "country";
    public static final String TEAMS_PROP = "teams";
    public static final String NAME_PROP = "name";
    public static final String GENDER_PROP = "gender";
    public static final String TYPE_PROP = "type";
    public static final String SUB_TYPE_PROP = "sub_type";
    public static final String PARTICIPANTS_PROP = "participants";
    public static final String NATIONAL_PROP = "national";
    public static final String COMPETITIONS_PROP = "competitions";
    public static final String PLAYERS_PROP = "players";
    public static final String STATUS_PROP = "status";
    public static final String PLAYER_STATS= "player_stats";

    public static final String INVALID_MATCH_ID = "fb:m:185128553124125";
    public static final String INVALID_PLAYER_ID = "fb:p:1284581825125";
    public static final String INVALID_TEAM_ID = "fb:t:159185624626813651";

    public static final String TEAM_ID_LIVERPOOL = "fb:t:892";
    public static final String TEAM_ID_MAN_UTD = "fb:t:8102";
    public static final String TEAM_ID_CHELSEA = "fb:t:882";
    public static final String VALID_PLAYER_ID = "fb:p:42430";
    public static final String OTHER_VALID_PLAYER_ID = "fb:p:10150";
    public static final String PLAYER_ID_FOR_LOST_PREDICTIONS = "fb:p:45137";
    public static final String PREMIER_LEAGUE_COMP_ID = "fb:c:3";
    public static final String FIRST_PROFESSIONAL_LEAGUE = "fb:c:1";
    public static final String LA_LEAGUE = "fb:c:5";
    public static final String BUNDESLIGA_COMP_ID = "fb:c:6";
    public static final String COUNTRY_ENGLAND_ID = "fb:cnt:1522";
    public static final String COUNTRY_GERMANY_ID = "fb:cnt:924";

    @AllArgsConstructor
    public enum CompetitionType {
      LEAGUE("league"),
      CUP("cup"),
      PLAY_OFF("playoff");

      @Getter
      private final String value;

      public static CompetitionType getRandomCompetitionType() {
        var competitionTypeList = Arrays.stream(values()).toList();
        var random = new Random();
        return competitionTypeList.get(random.nextInt(competitionTypeList.size()));
      }
    }

    @AllArgsConstructor
    public enum SortField {
      ID("id"),
      COUNTRY("country"),
      NAME("name"),
      DATE("date"); // /v1/matches endpoint only

      @Getter
      private final String value;
    }

    @AllArgsConstructor
    public enum MatchStatus {
      LIVE("live"),
      UPCOMING("upcoming"),
      FINISHED("finished");

      @Getter
      private final String value;

      private static final String[] UPCOMING_SUB_TYPES =
          new String[] {"postponed", "not_started", "kick_off_delayed", "to_finish", "notstarted",
              "Not started", "not_started", "Postponed", "postponed", "Kick Off Delayed", "kick_off_delayed"};

      private static final String[] LIVE_SUB_TYPES =
          new String[] {"inprogress", "1st half", "1st_half", "2nd half", "2nd_half", "Halftime", "halftime",
              "Waiting for Penalty", "waiting_for_penalty", "waiting_for_extra_time", "Penalty", "penalty",
              "Pause", "pause", "Extra Time - 1st half", "extra_time_1st_half", "Extra Time - 2nd half",
              "extra_time_2nd_half", "Extra Time - End of 1st half", "extra_time_end_of_1st_half",
              "Awaiting info", "awaiting_info"};

      private static final String[] FINISHED_SUB_TYPES =
          new String[] {"finished", "Finished", "finished", "cancelled", "Cancelled", "cancelled",
                  "interrupted", "Interrupted", "interrupted", "interrupted", "Abandoned", "abandoned",
                  "Finished after awarded win", "finished_after_awarded_win", "finished_aet", "finished_ap",
                  "Finished AET", "finished_aet", "Finished AP", "finished_ap",
                  "Finished ASG", "finished_asg", "To Finish", "to_finish"};

      public static String[] getStatusType(MatchStatus matchStatus) {
        switch (matchStatus) {
          case LIVE -> {
            return LIVE_SUB_TYPES;
          }
          case FINISHED -> {
            return FINISHED_SUB_TYPES;
          }
          case UPCOMING -> {
            return UPCOMING_SUB_TYPES;
          }
        }
        throw new IllegalArgumentException(matchStatus + " has no type");
      }

      public static String[] getStatusSubTypes(MatchStatus matchStatus) {
        switch (matchStatus) {
          case LIVE -> {
            return LIVE_SUB_TYPES;
          }
          case FINISHED -> {
            return FINISHED_SUB_TYPES;
          }
          case UPCOMING -> {
            return UPCOMING_SUB_TYPES;
          }
        }
        throw new IllegalArgumentException(matchStatus + " has no sub types");
      }
    }

    @AllArgsConstructor
    public enum SearchEntity {
      TEAMS("teams"),
      PLAYERS("players"),
      COMPETITIONS("competitions");

      @Getter
      private final String value;

      public static String commaSeparatedSearchEntities() {
        var searchEntityList = new ArrayList<String>();

        for (var searchEntity : SearchEntity.values()) {
          searchEntityList.add(searchEntity.getValue());
        }
        return String.join(",", searchEntityList);
      }
    }
  }

  @AllArgsConstructor
  public enum AuthRequirement {
    FREE,
    LEAD,
    REGISTERED;

    public static AuthRequirement getRandomAuthRequirementValue() {
      AuthRequirement[] values = values();
      return values[ThreadLocalRandom.current().nextInt(values.length)];
    }
  }

  public static final class ReportingApi {
    public static final String BREAKDOWN_PROP = "breakdown";
    public static final String FOLLOWS_PROP = "follows";
    public static final String ALL_PROP = "all";
    public static final String ID_PROP = "id";
    public static final String NAME_PROP = "name";
    public static final String USERS_PROP = "users";
    public static final String REGISTRATIONS = "registrations";
  }

  public static final class LoyaltyApi {
    public static final int DEFAULT_PAGE_LIMIT = 10;
    public static final String DATA = "data";

    public static final String ID_PROP = "id";
    public static final String NAME_PROP = "name";
    public static final String TYPE_PROP = "type";
    public static final String FROM_DATE_PROP = "from_date";
    public static final String TO_DATE_PROP = "to_date";
    public static final String MARKETS_PROP = "markets";
    public static final String TEAM_IDS_PROP = "team_ids";
    public static final String MATCH_IDS_PROP = "match_ids";
    public static final String GAME_IDS_PROP = "game_ids";
    public static final String GAME_TYPES_PROP = "game_types";
    public static final String GAME_TYPE_PROP = "game_type";
    public static final String COMPETITION_IDS_PROP = "competition_ids";
    private static final String EXCLUDED_IDS="excluded_profile_ids";

    public static final String RULES = "rules";
    public static final String FLAGS = "flags";

    public static final String POSITION_PROP = "position";
    public static final String PROFILE_ID_PROP = "profile_id";
    public static final String PREDICTIONS_MADE_PROP = "predictions_made";
    public static final String TIER_PROP = "tier";
    public static final String POINTS_PROP = "points";
    public static final String SUCCESS_RATES = "success_rate";

    public static final String OVERALL_PERCENT = "overall_percent";
    public static final String ACTION_PROP = "action";
    public static final String CONTEXT_PROP = "context";
    public static final String CONTENT_PROP = "content";
    public static final String CONTENT_ID_PROP = "id";
    public static final String CONTENT_TYPE_PROP = "type";
    public static final String CONTENT_LABEL = "label";
    public static final String TAGS_PROP = "tags";
    public static final String TAGS_ID_PROP = "id";
    public static final String TAGS_TYPE_PROP = "type";
    public static final String TAGS_SOURCE_PROP = "source";
    public static final String CAMPAIGN_PROP = "campaign";
    public static final String CAMPAIGN_ID_PROP = "id";
    public static final String CAMPAIGN_LABEL_PROP = "label";
    public static final String TOTAL_ITEMS_PROP = "total_items";

    public static final String RANK_TYPE = "rank_type";
    public static final String DESCRIPTION = "description";
    public static final String PAGINATION = "pagination";
    public static final String MESSAGE_PROP = "message";
    public static final String CURRENT_PROFILE_ID = "current_profile_id";
    public static final String NEW_PROFILE_ID = "new_profile_id";
  }

  //Leagues API
  public static final class LeaguesApi {
    public static final String LEAGUE_NAME = "name";
    public static final String LEAGUE_ID= "id";
    public static final String LEAGUE_ID_PROP= "league_id";
    public static final String LEAGUE_TYPE = "type";
    public static final String LEAGUE_DESCRIPTION = "description";
    public static final String LEAGUE_TEMPLATE_ID = "template_id";
    public static final String LEAGUE_OLD_TEMPLATES = "old_templates";
    public static final String LEAGUE_INVITATION_CODE = "invitation_code";
    public static final String LEAGUE_PINNED_POSTS = "pinned_posts";
    public static final String LEAGUE_BANNED = "banned";
    public static final String LEAGUE_INVITES = "invites";
    public static final String LEAGUE_ADMINS = "administrators";
    public static final String LEAGUE_MEMBERS = "members";
    public static final String LEAGUE_USERS_CAN_INVITE = "users_can_invite_users";
    public static final String LEAGUE_SCORING_START_AT = "scoring_starts_at";
  }
  //Discussions API
  public static final class DiscussionsApi{
    public static final String DISCUSSION_TYPE_PRIVATE = "PRIVATE";
    public static final String DISCUSSION_TYPE_PUBLIC = "PUBLIC";
    public static final String ID_PROP = "id";
    public static final String LABEL_PROP = "label";
    public static final String DISCUSSION_TYPE_PROP = "discussion_type";
    public static final String MODERATION_TYPE_PROP = "moderation_type";
    public static final String POSTS_COUNT_PROP = "posts_count";
    public static final String LAST_POST_ID_PROP = "last_post_id";
    public static final String CONTEXT_PROP = "context";
    public static final String CREATED_AT_PROP = "created_at";
    public static final String UPDATED_AT_PROP = "updated_at";
    public static final String PINNED_POSTS_PROP = "pinned_posts";
    public static final String DISCUSSION_IDS_PROP = "discussion_ids";
    public static final String STATUS_PROP = "status";
    public static final String SORT_PROP = "sort";
    public static final String FROM_COUNT_PROP = "from_count";
    public static final String TO_COUNT_PROP = "to_count";
    public static final String DISCUSSION_URL_PROP = "discussion_url";
    public static final String FROM_DATE_PROP = "from_date";
    public static final String TO_DATE_PROP = "to_date";
    public static final String LIMIT_PROP = "limit";
    public static final String START_AFTER_PROP = "start_after";
    public static final String SKIP_DELETED_PROP = "skip_deleted";
    public static final String SKIP_MODERATED_PROP = "skip_moderated";
  }

  // Voting API
  public static final class VotingApi {
    public static final String INVALID_POLL_ID = "invalidPollId123";
    public static final String POLL_TITLE_FIELD = "title";
    public static final String POLL_STATUS_FIELD = "status";
    public static final String POLL_OPTION_FIELD = "option";
    public static final String POLL_OPTION_TITLE_FIELD = POLL_OPTION_FIELD + "_" + POLL_TITLE_FIELD;
  }

  public static final class ApiIdConstants {

    public static final String PROFILE_API_ID = "com.fansunited.profile";

    public static final String PREDICTION_API_ID = "com.fansunited.prediction";

    public static final String LOYALTY_API_ID = "com.fansunited.loyalty";

    public static final String CLIENT_API_ID = "com.fansunited.client";

    public static final String FOOTBALL_API_ID = "com.fansunited.football";

    public static final String PREDICTION_GAMES_API_ID = "com.fansunited.prediction";

    public static final String PREDICTION_RESOLVER_API_ID = "com.fansunited.prediction.engine";

    public static final String REPORTING_API_ID = "com.fansunited.reporting";

    public static final String INVALID_API_ID = "invalid";
  }

  public static final class EndpointConstants {

    //Profile API

    public static final String ENDPOINT_COUNTRY = "/countries";

    public static final String ENDPOINT_PROFILES = "/profile/{id}";
    public static final String ENDPOINT_BADGES = "/profile/{profile_id}/badges";
    public static final String INVALID_ENDPOINT_PATH = "/invalid";

    public static final String PROFILE = "/profile/{id}";

    public static final String PROFILE_BY_ID = "/profile/{profile_id}/badges";

    public static final String PROFILE_BY_USER_ID = "/profile/{userId}/followers";

    public static final String FOLLOWING = "/profile/{userId}/following";

    //    Prediction API

    public static final String GAMES = "/games";

    public static final String GAME_BY_GAME_ID = "/games/{game_id}";

    public static final String CORRECT_RESULT = "/games/{game_id}/correct-results";

    public static final String PREDICTIONS = "games/{game_id}/predictions";

    public static final String RESULTS = "games/{game_id}/results";

    public static final String PREDICTIONS_BY_MATCH_ID = "predictions/summary/{match_id}";

    public static final String USER_BY_USER_ID = "predictions/users/{user_id}";

    //Loyalty API

    public static final String ACTIVITIES = "/users/{profile_id}/activities";

    public static final String LEADERBOARD_BY_GAME_ID = "/leaderboard/games/{game_id}";

    public static final String RANKINGS = "/leaderboard/users/{user_id}/rankings";

    public static final String LEADERBOARD_BY_TEMPLATE_ID = "/leaderboard/{template_id}";

    public static final String LEADERBOARD_TEMPLATES = "/leaderboard/templates";

    public static final String ENGAGEMENT = "profile/{user_id}/engagement";

    public static final String STATISTICS = "users/{profile_id}/statistics";

    public static final String STATISTICS_TOP = "statistics/top";

    public static final String FOOTBALL_FANTASY= "data.football_fantasy";
  }
}

