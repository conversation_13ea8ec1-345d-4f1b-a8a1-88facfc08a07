package com.fansunited.automation.constants;

public class ApiErrorCodes {

  // Common error codes
  public static final String STATUS_VALIDATION_ERROR = "validation_error";
  public static final String INVALID_IMAGE_EXTENSION = "main: Invalid url. A correct URL could have the form https://imagestorage.com/mainimage.jpg. Supported image extensions are jpg|gif|png.";
  public static final String INVALID_IMAGE_URL_FOR_STAGE = "main: Invalid url";
  public static final String INVALID_CONTENT_TYPE = "invalid_content_type";

  public static class ProfileErrorCodes {
    public static final String STATUS_INVALID_GENDER = "invalid_gender";
    public static final String MESSAGE_INVALID_GENDER =
        "Invalid gender, available types are male, female and unspecified.";
    public static final String CODE_INVALID_ID = "invalid_ids";
    public static final String MESSAGE_INVALID_ID = "Invalid profile IDs are provided.";
    public static final String CODE_NO_USER = "user_not_exists";
    public static final String MESSAGE_NO_USER = "No user record found for the provided user ID";
    public static final String CODE_REQUIRED_CLIENT_ID = "client_id_required";
    public static final String MESSAGE_REQUIRED_CLIENT_ID = "The client_id param is required.";
    public static final String CODE_INVALID_CLIENT = "invalid_client_id";
    public static final String CODE_INVALID_LANGUAGE = "invalid_language";
    public static final String CODE_INVALID_JSON = "invalid_json";
    public static final String STATUS_INVALID_AVATAR = "invalid_avatar";
    public static final String STATUS_INVALID_NAME = "invalid_name";
    public static final String CODE_INVALID_NAME="code_invalid_name";
    public static final String CODE_STATUS_NOT_AVAILABLE = "N/A";
    public static final String MESSAGE_INVALID_CLIENT_ID = "Invalid client ID is provided.";
    public static final String MESSAGE_NO_PROFILE_IDS =
        "Invalid or empty profile_ids array. Please send one or more profile IDs.";
    public static final String CODE_NO_PROFILE_IDS = "empty_profile_ids";
    public static final String CODE_INTERNAL_SERVER_ERROR = "internal_server_error";
    public static final String CODE_UNAUTHORIZED_ERROR = "unauthorized";
    public static final String CODE_FORBIDDEN = "forbidden";

    private ProfileErrorCodes() {
    }

    public static final String CODE_INVALID_ID_PROVIDER = "invalid_id_provider";
    public static final String MESSAGE_INVALID_ID_PROVIDER = "Invalid ID provider.";
    public static final String STATUS_INVALID_INTEREST_DATA = "invalid_interests";

    public static final String MESSAGE_INVALID_INTEREST_DATA =
        "One or more of provided interests are invalid.";
    public static final String CODE_COUNTRY_NOT_EXISTS = "country_not_exists";
    public static final String MESSAGE_COUNTRY_NOT_EXISTS = "Country doesn't exist";
    public static final String CODE_INVALID_CONTENT_TYPE = INVALID_CONTENT_TYPE;
    public static final String MESSAGE_EXISTING_NICKNAME = "This nickname has already been taken.";
  }

  public static class PredictionErrorCodes {
    public static final String STATUS_INVALID_PREDICTION_DATA = "invalid_prediction_data";
    public static final String STATUS_INVALID_GAME_DATA = "invalid_game_data";
    public static final String MESSAGE_STATUS_NOT_VALID = "Status not valid for new game";
    public static final String CODE_INVALID_CLIENT_ID = "invalid_client_id";
    public static final String MESSAGE_INVALID_CLIENT_ID = "Invalid client ID is provided.";
    public static final String MESSAGE_GAME_NOT_FOUND = "Game with this ID cannot be found.";
    public static final String CODE_GAME_NOT_FOUND = "game_not_found";
    public static final String MESSAGE_UPDATE_STATUS_VOID =
        "When updating status it can be only changed to VOID.";
    public static final String MESSAGE_PREDICTION_NOT_FOUND =
        "Prediction with this ID cannot be found.";
    public static final String CODE_PREDICTION_NOT_FOUND = "predicton_not_found";
    public static final String MESSAGE_FOUND_DUPLICATED_MATCHES =
        "Found duplicate matches in gam fixtures.";
    public static final String MESSAGE_ONLY_ONE_MATCH_POSSIBLE =
        "Only 1 match should be available with different markets.";
    public static final String MESSAGE_INVALID_MATCH_IDS = "Invalid Match IDs in game fixtures.";
    public static final String MESSAGE_MACTHES_STARTS_AFTER_CUTOFF =
        "There are matches that starts after cutoff time.";
    public static final String MESSAGE_MACTHES_NOT_UPCOMMING =
        "There are matches that are finished or live.";
    public static final String MESSAGE_MATCH_NOT_FROM_COMP_WHITELIST =
        "Provided match/es are not from competitions whitelist.";
    public static final String MESSAGE_SECONDARY_MARKET_NOT_FULL_COVERAGE_COMPETITION =
        "If the market is not in main markets, it can be used only on matches that are played in full_coverage_competitions.";
    public static final String MESSAGE_MARKET_NOT_FULL_COVERAGE_COMPETITION =
        "For Match Quiz games, only matches in full_coverage_competitions can be used.";
    public static final String MESSAGE_INVALID_PREDICTION_FIXTURE = "Invalid prediction fixture.";
    public static final String MESSAGE_INVALID_GAME_ID = "Invalid game ID is provided.";
    public static final String STATUS_INVALID_GAME_ID = "id_doesnt_exists";
    public static final String MESSAGE_INVALID_MATCH_ID = "Invalid match ID is provided.";
    public static final String STATUS_INVALID_MATCH_ID = "invalid_match_id";
    public static final String STATUS_INVALID_ARGUMENT = "invalid_argument";
    public static final String STATUS_INVALID_DATE = "invalid_date";
    public static final String MESSAGE_CANNOT_UPDATE_OPEN_GAME =
        "Cannot update open game properties.";
    public static final String STATUS_CANNOT_UPDATE_OPEN_GAME = "cannot_update_open_game";

    public static final String MESSAGE_CANNOT_UPDATE_GAME_PROPERTIES =
        "Cannot update game properties. Game status is not PENDING.";

    private PredictionErrorCodes() {

    }

    public static final String CODE_INVALID_GAME_CUTOFF = "predictions_cutoff";
    public static final String MESSAGE_INVALID_GAME_CUTOFF =
        "Predictions cutoff should be in future.";

    public static final String MESSAGE_INVALID_GAME_STATUS =
        "This status cannot be assigned to current game.";

    public static final String MESSAGE_INVALID_GAME_FIXTURES =
        "Some fixtures cannot be part of this game.";

    public static final String MESSAGE_INVALID_GAME_FIXTURES_COUNT =
        "Prediction must contain exact number of fixtures as game or just one if not part of game.";
    public static final String MESSAGE_INVALID_GAME_FIXTURES_MARKET =
        "Prediction must contain valid market.";
    public static final String MESSAGE_INVALID_GAME_FIXTURES_MATCH =
        "Prediction must contain valid match.";

    public static final String STATUS_INVALID_PREDICTION_GAME_INSTANCE = "invalid_game_instance_id";
    public static final String MESSAGE_INVALID_PREDICTION_GAME_INSTANCE =
        "Trying to enroll in non-existent or already settled game.";
  }

  public static class FootballErrorCodes {
    public static final String CODE_INVALID_ID_PROVIDER = "invalid_id_provider";
    public static final String MESSAGE_INVALID_ID_PROVIDER = "Invalid ID provider.";
    public static final String CODE_INVALID_LANG = "invalid_language";
    public static final String MESSAGE_INVALID_LANG = "Invalid language.";
    public static final String CODE_PARAM_MANDATORY = "missing_mandatory_param";
    public static final String MESSAGE_PARAM_MANDATORY = "Mandatory param/s is/are missing.";
    public static final String CODE_BAD_DATE_FORMAT = "bad_date_format";
    public static final String MESSAGE_BAD_DATE_FORMAT = "Invalid date format provided.";
    public static final String CODE_NO_NEXT_MATCH = "next_match_not_found";
    public static final String MESSAGE_NO_NEXT_MATCH = "Next match not found.";
    public static final String CODE_NO_PREV_MATCH = "previous_match_not_found";
    public static final String MESSAGE_NO_PREV_MATCH = "Previous match not found.";
    public static final String CODE_ID_DOESNT_EXISTS = "id_doesnt_exists";
    public static final String MESSAGE_ID_DOESNT_EXISTS = "Selected ID doesn't exists.";
    public static final String CODE_UNKNOWN_ERROR = "unknown_error";
    public static final String MESSAGE_UNKNOWN_ERROR = "Unknown error logged. Please try again later.";
    public static final String CODE_INVALID_STATUS = "invalid_status";
    public static final String MESSAGE_INVALID_STATUS = "Invalid match status. Valid statuses are upcoming, live, finished.";
    public static final String CODE_FORBIDDEN = "forbidden";
    public static final String CODE_INTERNAL_SERVER_ERROR = "internal_server_error";
    public static final String MESSAGE_INTERNAL_SERVER_ERROR = "Internal server error logged. Please try again later.";
    public static final String CODE_INVALID_PAGE = "invalid_page";
    public static final String MESSAGE_INVALID_PAGE = "Invalid page number provided. Use number between 1 and max number of pages.";
    public static final String CODE_INVALID_JSON = "invalid_json";
    public static final String CODE_INVALID_ARGUMENT = "invalid_argument";
    public static final String MESSAGE_INVALID_GENDER =
        "Invalid gender, available types are male and female.";

    private FootballErrorCodes() {
    }

    public static class LoyaltyErrorCode {
      public static final String INVALID_LIMIT_ERROR_MESSAGE = "limit: Limit param must be in between 1 and 100";
    }
  }

  public static class ClientErrorCodes {

    public static final String INVALID_CLIENT_ID_STATUS= "invalid_client_id";
    public static final String PARAMETER_MISSING = "Parameter should not be null or empty.";
    public static final String CLIENT_NOT_FOUND = "no_client_found";
    public static final String EMAIL_NOT_BE_NULL = "invalid_argument";
    public static final String CAN_NOT_GET_STAFF_INFO = "can_not_get_staff_information";
    public static final String ROLE_DOES_NOT_EXIST="role_does_not_exist";
    public static final String BLANK_LIST = "<list element>[0]: Elements should not be blank";
    public static final String FORBIDDEN = "HTTP 403 Forbidden";
    public static final String FORBIDDEN_STATUS = "forbidden";
    public static final String INVALID_ACTION = "invalid_actions";

    private ClientErrorCodes() {
    }
  }

  public static class LoyaltyErrorCodes {
    public static final String CODE_INVALID_ACTION = "invalid_action";

    private LoyaltyErrorCodes() {
    }
  }
  public static class LeaguesErrorCodes{
    public static final String INVALID_DESCRIPTION_LENGTH_ERROR_MSG = "description: Length must be between 1 and 50 000 (inclusive) characters.";
    public static final String INVALID_NAME_LENGTH_ERROR_MSG = "name: Length must be between 1 and 100 (inclusive) characters.";
    public static final String BLANK_AND_LENGTH_NAME_ERROR_MSG = "name: Field should not be blank, name: Length must be between 1 and 100 (inclusive) characters.";
    public static final String SPECIAL_CHARACTERS_NAME_ERROR_MSG = "name: Field cannot contain special characters.";
    public static final String BLANK_NAME_ERROR_MSG = "name: Field should not be blank";
    public static final String CODE_OLD_TEMPLATE_NAME = "old_template_error";
    public static final String INVALID_API_KEY_ERROR_MSG = "API key not valid. Please pass a valid API key.";
    public static final String MISSING_OR_EMPTY_API_KEY_ERROR_MSG = "Method doesn't allow unregistered callers (callers without" +
            " established identity). Please use API Key or other form of API consumer identity to call this API.";
    public static final String CODE_PROFILE_FETCH_FAILURE = "profile_fetch_failure";
  }

  public static class DiscussionErrorCodes{
  public static final String INVALID_CLIENT_ID_STATUS= "invalid_client_id";
  public static final String INVALID_CONTENT_TYPE_STATUS = INVALID_CONTENT_TYPE;
  public static final String FORBIDDEN_STATUS = "forbidden";
  public static final String POST_DUPLICATION = "post_duplication";
  }

  public static class VotingErrorCodes{
    public static final String INVALID_CONTENT_TYPE_STATUS =INVALID_CONTENT_TYPE;
    public static final String POLL_NOT_FOUND ="poll_not_found";
    public static final String POLL_VOTE_NOT_FOUND ="poll_vote_not_found";
    public static final String PLAYER_ID_NOT_PARTICIPATED ="player_id_not_participated";
    public static final String MATCH_NOT_FOUND ="match_not_found";
    public static final String MATCH_NOT_FINISHED ="match_not_finished";
    public static final String MATCH_NOT_STARTED   ="match_not_started";
    public static final String MATCH_IS_NOT_FULL_COVERAGE ="match_id_not_full_coverage";
    public static final String PREFERENCES_NOT_ENABLED_IN_REQUEST ="profile_preferences_not_enabled_in_request";
  }
}
