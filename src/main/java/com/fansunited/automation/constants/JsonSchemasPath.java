package com.fansunited.automation.constants;

public class JsonSchemasPath {

  public static final class ProfileApi {
    public static final class Endpoints {
      public static final class Countries {
        public static final String GET_COUNTRIES_SCHEMA =
            "schemas/profileapi/countries/get_countries_schema.json";
      }

      public static final class Profile {
        public static final String GET_PROFILE_SCHEMA =
            "schemas/profileapi/profiles/get_profile_schema.json";
        public static final String GET_PROFILES_SCHEMA =
            "schemas/profileapi/profiles/get_profiles_schema.json";
        public static final String PROFILE_FOLLOWINGS_SCHEMA =
            "schemas/profileapi/profiles/profile_followings_schema.json";
        public static final String PROFILE_FOLLOWERS_SCHEMA =
            "schemas/profileapi/profiles/profile_followers_schema.json";
        public static final String PROFILE_DELETED_SCHEMA =
            "schemas/profileapi/profiles/profile_deleted_schema.json";
        public static final String PROFILES_DELETED_SCHEMA =
            "schemas/profileapi/profiles/profiles_delete_schema.json";
        public static final String GET_PROFILES_SCHEMA_WITH_DELETED_USERS =
            "schemas/profileapi/profiles/profiles_delete_schema_with_deleted_users.json";
        public static final String GET_RINGER_PROFILES_SCHEMA =
            "schemas/profileapi/profiles/get_ringer_profile_schema.json";
      }
    }
  }

  public static final class PredictionApi {
    public static final class Endpoints {
      public static final class Games {
        public static final String GET_GAME_LIST_MATCH_QUIZ_SCHEMA =
            "schemas/predictionapi/games/get/get_game_list_match_quiz.json";
        public static final String GET_GAME_LIST_TOP_X_SCHEMA =
            "schemas/predictionapi/games/get/get_game_list_top_x.json";
        public static final String GET_SPECIFIC_GAME_MATCH_QUIZ_SCHEMA =
            "schemas/predictionapi/games/get/get_specific_game_match_quiz.json";
        public static final String GET_SPECIFIC_GAME_TOP_X_SCHEMA =
            "schemas/predictionapi/games/get/get_specific_game_top_x.json";
        public static final String GET_GAME_RESULTS_SCHEMA =
            "schemas/predictionapi/games/get/get_game_results.json";
        public static final String CREATE_GAME_MATCH_QUIZ_SCHEMA =
            "schemas/predictionapi/games/post/create_game_match_quiz.json";
        public static final String CREATE_GAME_TOP_X_SCHEMA =
            "schemas/predictionapi/games/post/create_game_top_x.json";
        public static final String UPDATE_GAME_MATCH_QUIZ_SCHEMA =
            "schemas/predictionapi/games/post/update_game_match_quiz.json";
        public static final String UPDATE_GAME_FIELDS_MATCH_QUIZ_SCHEMA =
            "schemas/predictionapi/games/post/update_game_fields_for_all_statuses_match_quiz.json";
        public static final String UPDATE_GAME_TOP_X_SCHEMA =
            "schemas/predictionapi/games/post/update_game_top_x.json";
        public static final String UPDATE_GAME_FIELDS_TOP_X_SCHEMA =
            "schemas/predictionapi/games/post/update_game_fields_for_all_statuses_top_x.json";
        public static final String GET_GAME_CORRECT_RESULT_SCHEMA =
            "schemas/predictionapi/games/get/get_game_correct_result.json";
      }

      public static final class Predictions {
        public static final String CREATE_GAME_PREDICTION_SCHEMA =
            "schemas/predictionapi/predictions/post/create_game_prediction.json";
        public static final String CREATE_SINGLE_PREDICTION_SCHEMA =
            "schemas/predictionapi/predictions/post/create_single_prediction.json";
        public static final String GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA =
            "schemas/predictionapi/predictions/get/get_own_or_specific_user_predictions.json";
        public static final String GET_AGGREGATED_PREDICTIONS_SUMMARY_RESULTS_SCHEMA =
            "schemas/predictionapi/predictions/get/get_aggregated_predictions_summary_results.json";
        public static final String GET_PREDICTIONS_FOR_GAME_SCHEMA =
            "schemas/predictionapi/predictions/get/get_predictions_for_game.json";
      }
    }
  }

  public static final class FootballApi {
    public static final class Endpoints {
      public static final class Countries {
        public static final String GET_COUNTRIES_SCHEMA =
            "schemas/footballapi/countries/get_countries_schema.json";
      }

      public static final class Competitions {
        public static final String GET_COMPETITIONS_SCHEMA =
            "schemas/footballapi/competitions/get_competitions_schema.json";
        public static final String GET_COMPETITION_BY_ID_SCHEMA =
            "schemas/footballapi/competitions/get_competition_by_id_schema.json";
      }

      public static final class Teams {
        public static final String GET_TEAMS_SCHEMA =
            "schemas/footballapi/teams/get_teams_schema.json";
        public static final String GET_TEAM_BY_ID_SCHEMA =
            "schemas/footballapi/teams/get_team_by_id_schema.json";
      }

      public static final class Players {
        public static final String GET_PLAYERS_SCHEMA =
            "schemas/footballapi/players/get_players_schema.json";
        public static final String GET_PLAYER_BY_ID_SCHEMA =
            "schemas/footballapi/players/get_player_by_id_schema.json";
        public static final String GET_TOP_PLAYERS_SCHEMA =
            "schemas/footballapi/players/get_top_players_schema.json";
      }

      public static final class Matches {
        public static final String GET_MATCHES_SCHEMA =
            "schemas/footballapi/matches/get_matches_schema.json";
        public static final String GET_MATCH_BY_ID_SCHEMA =
            "schemas/footballapi/matches/get_match_by_id.json";
        public static final String GET_PLAYER_STATS_FOR_MATCH_BY_ID_SCHEMA =
            "schemas/footballapi/matches/get_player_stats_for_match_schema.json";
      }

      public static final class Search {
        public static final String SEARCH_SCHEMA = "schemas/footballapi/search/search_schema.json";
      }
    }
  }

  public static final class ReportingApi {
    public static final class Endpoints {
      public static final class Users {
        public static final String GET_ACQUIRED_USERS_SCHEMA =
            "schemas/reportingapi/users/get_acquired_users_schema.json";
        public static final String GET_USERS_PER_COUNTRY_SCHEMA =
            "schemas/reportingapi/users/get_users_per_country_schema.json";
        public static final String GET_TOTAL_USERS_PER_PERIOD_SCHEMA =
            "schemas/reportingapi/users/get_total_users_per_period_schema.json";
        public static final String GET_MANTLY_ACTIVE_USERS_SCHEMA =
            "schemas/reportingapi/users/get_monthly_active_users_schema.json";

        public static final String GET_MONTHLY_ACTIVE_VS_USERS_SCHEMA =
            "schemas/reportingapi/users/get_mau_vs_reg_schema.json";
        public static final String GET_CLIENT_PREDICTIONS_SCHEMA =
            "schemas/reportingapi/predictions/get_client_predictions_schema.json";
        public static final String GET_CLIENT_TOTAL_PREDICTIONS_SCHEMA =
            "schemas/reportingapi/predictions/get_client_total_predictions_schema.json";
      }

      public static final class Follows {
        public static final String GET_AVERAGE_FOLLOWS_PER_INTEREST_SCHEMA =
            "schemas/reportingapi/follows/get_average_follows_per_interest_schema.json";
        public static final String GET_TOP_FOLLOWED_COMPETITIONS_SCHEMA =
            "schemas/reportingapi/follows/get_top_followed_competitions_schema.json";
        public static final String GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA =
            "schemas/reportingapi/follows/get_follows_by_comp_id_football.json";
      }

      public static final class Markets {
        public static final String GET_SUCCESS_RATE_PER_CLIENT_SCHEMA =
            "schemas/reportingapi/markets/get_success_rate_per_client_schema.json";
      }
    }
  }

  public static final class ClientsApi {
    public static final class Endpoints {
      public static final class Roles {
        public static final String GET_ROLES_SCHEMA =
            "schemas/clientsapi/acl/get_roles_schema.json";
      }

      public static final class Clients {
        public static final String GET_CLIENTS_BY_CLIENTS_ID_SCHEMA =
            "schemas/clientsapi/clients/get_clients_by_clients_id_schema.json";
        public static final String GET_CLIENTS_BY_ENDPOINTS_KEY_SCHEMA =
            "schemas/clientsapi/clients/get_clients_by_endpoints_key_schema.json";
        public static final String GET_CLIENTS_BY_ID_PREDICTOR_FEATURES_BASIC_SCHEMA =
            "schemas/clientsapi/clients/get_clients_by_id_predictor_features_basic_schema.json";
        public static final String GET_CLIENTS_BY_ID_FEATURES_SCHEMA =
            "schemas/clientsapi/clients/get_clients_by_id_features.json";
        public static final String GET_CLIENTS_BY_ID_TOP_X_FEATURES_SCHEMA =
            "schemas/clientsapi/clients/get_clients_by_id_topX_features.json";
        public static final String GET_CLIENT_BY_ID_MATCH_QUIZ_FEATURES_SCHEMA =
            "schemas/clientsapi/clients/get_clients_by_id_match_quiz_features.json";
        public static final String POST_CREATE_MANAGE_LISTS_SCHEMA =
            "schemas/clientsapi/managelists/post_create_manage_lists_schema.json";
      }

      public static final class Staff {
        public static final String GET_STAFF_BY_CLIENTS_ID_SCHEMA =
            "schemas/clientsapi/staff/get_staff_by_cliend_id_schema.json";
      }

      public static final class Profile {
        public static final String GET_PROFILE_FOR_USER_SCHEMA =
            "schemas/clientsapi/profile/get_profile_for_user_schema.json";
      }
    }
  }

  public static final class LoyaltyApi {
    public static final class Endpoints {
      public static final class Templates {
        public static final String CREATE_UPDATE_TEMPLATE_SCHEMA =
            "schemas/loyaltyapi/templates/post/create_update_template_schema.json";
        public static final String GET_TEMPLATES_SCHEMA =
            "schemas/loyaltyapi/templates/get/get_templates_schema.json";
        public static final String GET_TEMPLATES_SCHEMA_BY_ID =
            "schemas/loyaltyapi/templates/get/get_templates_by_Id_schema.json";
      }

      public static final class Ranks {
        public static final String GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA =
            "schemas/loyaltyapi/ranks/get_leaderboard_for_template_schema.json";
      }

      public static final class Activity {
        public static final String CREATE_ACTIVITY_SCHEMA =
            "schemas/loyaltyapi/activity/post/create_activity_schema.json";
        public static final String GET_OWN_ACTIVITIES_SCHEMA =
            "schemas/loyaltyapi/activity/get/get_own_activities_schema.json";
      }

      public static final class Statistics {
        public static final String GET_STATISTICS_SCHEMA =
            "schemas/loyaltyapi/statistics/get_statistics_schema.json";
      }
    }
  }

  public static final class LeaguesApi {
    public static final String CREATE_LEAGUE_SCHEMA = "schemas/leagueapi/post/create_league.json";
    public static final String GET_LEAGUE_SCHEMA = "schemas/leagueapi/get/get_league.json";
  }

  public static final class DiscussionsApi {
   // public static final String CREATE_LEAGUE_SCHEMA = "schemas/leagueapi/post/create_league.json";
    public static final String GET_DISCUSSION_SCHEMA = "schemas/discussionapi/discussions/get/get_discussion.json";
  }
}
