package com.fansunited.automation.arguments.profileapi;

import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.constants.AuthConstants;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidJwtTokenArgumentsProvider implements ArgumentsProvider {

  @Override
  public Stream provideArguments(ExtensionContext extensionContext) {
    return Stream.of(
            new InvalidJwtTokenArgumentsHolder("", HttpStatus.SC_UNAUTHORIZED),
            new InvalidJwtTokenArgumentsHolder(
                AuthConstants.FansUnitedProfileProject.INVALID_JWT_TOKEN, HttpStatus.SC_FORBIDDEN),
            new InvalidJwtTokenArgumentsHolder(
                AuthConstants.FansUnitedProfileProject.EXPIRED_JWT_TOKEN, HttpStatus.SC_FORBIDDEN))
        .map(Arguments::of);
  }
}
