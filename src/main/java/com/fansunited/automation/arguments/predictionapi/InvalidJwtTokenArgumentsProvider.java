package com.fansunited.automation.arguments.predictionapi;

import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.constants.AuthConstants;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidJwtTokenArgumentsProvider implements ArgumentsProvider {
  @Override public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) {
    return Stream.of(
        new InvalidJwtTokenArgumentsHolder("", HttpStatus.SC_UNAUTHORIZED),
        new InvalidJwtTokenArgumentsHolder(AuthConstants.FansUnitedClientsProject.INVALID_JWT_TOKEN,
            HttpStatus.SC_UNAUTHORIZED),
        new InvalidJwtTokenArgumentsHolder(AuthConstants.FansUnitedClientsProject.EXPIRED_JWT_TOKEN,
            HttpStatus.SC_UNAUTHORIZED)
    ).map(Arguments::of);
  }
}
