package com.fansunited.automation.arguments.discussionapi;

import static com.fansunited.automation.constants.ApiErrorCodes.DiscussionErrorCodes.FORBIDDEN_STATUS;
import static com.fansunited.automation.constants.ApiErrorCodes.DiscussionErrorCodes.INVALID_CLIENT_ID_STATUS;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidDiscussionClientIdArguments implements ArgumentsProvider {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext context) throws Exception {
    return Stream.of(
            new InvalidClientIdHolder(INVALID_CLIENT_ID, INVALID_CLIENT_ID_STATUS),
            new InvalidClientIdHolder(null, FORBIDDEN_STATUS),
            new InvalidClientIdHolder("", INVALID_CLIENT_ID_STATUS))
        .map(Arguments::of);
  }
}
