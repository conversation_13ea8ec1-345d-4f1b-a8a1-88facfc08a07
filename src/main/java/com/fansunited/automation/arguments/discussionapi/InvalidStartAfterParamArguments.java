package com.fansunited.automation.arguments.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.INVALID_POST_ID;

import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidStartAfterParamArguments implements ArgumentsProvider {

  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) {
    return Stream.of(
            new InvalidStartAfterParamHolder("", HttpStatus.SC_BAD_REQUEST),
            new InvalidStartAfterParamHolder(INVALID_POST_ID, HttpStatus.SC_NOT_FOUND))
        .map(Arguments::of);
  }
}
