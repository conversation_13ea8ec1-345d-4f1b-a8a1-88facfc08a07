package com.fansunited.automation.arguments.leagueapi;

import com.fansunited.automation.constants.AuthConstants;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidApiKeyArguments implements ArgumentsProvider {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) {
    return Stream.of(
            new InvalidApiKeyArgumentsHolder("", HttpStatus.SC_UNAUTHORIZED),
            new InvalidApiKeyArgumentsHolder(null, HttpStatus.SC_UNAUTHORIZED),
            new InvalidApiKeyArgumentsHolder(
                AuthConstants.INVALID_ENDPOINTS_API_KEY, HttpStatus.SC_BAD_REQUEST))
        .map(Arguments::of);
  }
}
