package com.fansunited.automation.arguments.commonarguments;

public class InvalidArgumentsHolder {
  private final String arguments;
  private final int statusCode;

  public InvalidArgumentsHolder(String arguments, int statusCode) {
    this.arguments = arguments;
    this.statusCode = statusCode;
  }

  public String getArguments() {
    return arguments;
  }

  public int getStatusCode() {
    return statusCode;
  }

  @Override public String toString() {
    return arguments;
  }
}
