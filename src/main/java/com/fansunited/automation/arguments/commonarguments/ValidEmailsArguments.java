package com.fansunited.automation.arguments.commonarguments;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;

import com.fansunited.automation.core.base.AuthBase;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class ValidEmailsArguments extends AuthBase implements ArgumentsProvider {
    @Override
    public Stream<? extends Arguments> provideArguments(ExtensionContext context) throws Exception {
        return Stream.of(
             new   ValidEmailsArgumentsHolder(getCurrentTestUser().getEmail(), HttpStatus.SC_FORBIDDEN),
             new   ValidEmailsArgumentsHolder(BILLING_MANAGER_EMAIL, HttpStatus.SC_FORBIDDEN)).map(Arguments::of);

    }
}
