package com.fansunited.automation.arguments.commonarguments;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;

import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidClientIdArguments implements ArgumentsProvider {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext context) {
    return Stream.of(
            new InvalidClientIdHolder(INVALID_CLIENT_ID, CODE_INVALID_CLIENT),
            new InvalidClientIdHolder(null, STATUS_VALIDATION_ERROR),
            new InvalidClientIdHolder("", STATUS_VALIDATION_ERROR))
        .map(Arguments::of);
  }
}
