package com.fansunited.automation.arguments.commonarguments;

import io.restassured.http.ContentType;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidContentTypeArguments implements ArgumentsProvider {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) {
    return Stream.of(
            new InvalidContentTypesHolder(null, HttpStatus.SC_BAD_REQUEST),
            new InvalidContentTypesHolder(ContentType.TEXT, HttpStatus.SC_BAD_REQUEST),
            new InvalidContentTypesHolder(ContentType.HTML, HttpStatus.SC_BAD_REQUEST))
        .map(Arguments::of);
  }
}
 