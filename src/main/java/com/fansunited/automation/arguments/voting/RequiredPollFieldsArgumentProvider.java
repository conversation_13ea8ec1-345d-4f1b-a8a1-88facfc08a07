package com.fansunited.automation.arguments.voting;

import static com.fansunited.automation.constants.ApiConstants.VotingApi.POLL_OPTION_FIELD;
import static com.fansunited.automation.constants.ApiConstants.VotingApi.POLL_OPTION_TITLE_FIELD;
import static com.fansunited.automation.constants.ApiConstants.VotingApi.POLL_STATUS_FIELD;
import static com.fansunited.automation.constants.ApiConstants.VotingApi.POLL_TITLE_FIELD;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.SIX_WHITE_SPACES;

import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class RequiredPollFieldsArgumentProvider implements ArgumentsProvider {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext context) throws Exception {
    return Stream.of(
            new RequiredPollFieldsArgumentsHolder(POLL_TITLE_FIELD, null),
            new RequiredPollFieldsArgumentsHolder(POLL_TITLE_FIELD, ""),
            new RequiredPollFieldsArgumentsHolder(POLL_TITLE_FIELD, SIX_WHITE_SPACES),
            new RequiredPollFieldsArgumentsHolder(POLL_OPTION_FIELD,null),
            new RequiredPollFieldsArgumentsHolder(POLL_STATUS_FIELD,null),
            new RequiredPollFieldsArgumentsHolder(POLL_OPTION_TITLE_FIELD, ""),
            new RequiredPollFieldsArgumentsHolder(POLL_OPTION_TITLE_FIELD, SIX_WHITE_SPACES),
            new RequiredPollFieldsArgumentsHolder(POLL_OPTION_TITLE_FIELD, null))
        .map(Arguments::of);
  }
}
