package com.fansunited.automation.arguments.voting;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.CommonStatus;
import java.util.List;

public record FilterPollsArgumentsHolder(
    int limit,
    int minVotes,
    String startAfterId,
    List<String> entityIds,
    List<String> flags,
    ApiConstants.SortOrder sortOrder,
    CommonStatus status) {}
