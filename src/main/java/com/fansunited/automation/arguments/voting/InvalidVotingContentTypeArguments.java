package com.fansunited.automation.arguments.voting;

import com.fansunited.automation.arguments.commonarguments.InvalidContentTypeArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import io.restassured.http.ContentType;
import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;

public class InvalidVotingContentTypeArguments extends InvalidContentTypeArguments {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext extensionContext) {
    return Stream.of(
            new InvalidContentTypesHolder(ContentType.TEXT, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE),
            new InvalidContentTypesHolder(ContentType.XML, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE),
            new InvalidContentTypesHolder(ContentType.HTML, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE))
        .map(Arguments::of);
  }
}
