package com.fansunited.automation.arguments.voting;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.CommonStatus;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class FilterPollsParametersProvider implements ArgumentsProvider {
    private static final int ZERO_MIN_VOTES = 0;
    private static final String NULL_START_AFTER_ID = null;
    @Override
    public Stream<? extends Arguments> provideArguments(ExtensionContext context) throws Exception {
        return Stream.of(
                // Flags and entity_ids cannot be combined
                new FilterPollsArgumentsHolder(1, ZERO_MIN_VOTES, NULL_START_AFTER_ID, null, null, ApiConstants.SortOrder.ASC, CommonStatus.ACTIVE),
                new FilterPollsArgumentsHolder(2, ZERO_MIN_VOTES, NULL_START_AFTER_ID, null, List.of("Premier League", "Top scorers"), null, CommonStatus.ACTIVE),
                new FilterPollsArgumentsHolder(3, ZERO_MIN_VOTES, NULL_START_AFTER_ID, null, List.of("Premier League", "Top scorers"), ApiConstants.SortOrder.DESC, CommonStatus.ACTIVE),
                new FilterPollsArgumentsHolder(4, ZERO_MIN_VOTES, NULL_START_AFTER_ID, List.of("tag:id:1", "tag:id:2"), null, ApiConstants.SortOrder.ASC, CommonStatus.INACTIVE),
                new FilterPollsArgumentsHolder(5, ZERO_MIN_VOTES, NULL_START_AFTER_ID, List.of(PREMIER_LEAGUE_COMP_ID, BUNDESLIGA_COMP_ID), null, ApiConstants.SortOrder.DESC, CommonStatus.ACTIVE),
                new FilterPollsArgumentsHolder(6, ZERO_MIN_VOTES, NULL_START_AFTER_ID, List.of("tag:id:1", "tag:id:2"), null, ApiConstants.SortOrder.DESC, CommonStatus.INACTIVE),
                new FilterPollsArgumentsHolder(7, ZERO_MIN_VOTES, NULL_START_AFTER_ID, null, List.of("flag1", "flag2"), ApiConstants.SortOrder.ASC, CommonStatus.INACTIVE),
                new FilterPollsArgumentsHolder(8, ZERO_MIN_VOTES, NULL_START_AFTER_ID, List.of(PREMIER_LEAGUE_COMP_ID, BUNDESLIGA_COMP_ID), null, ApiConstants.SortOrder.ASC, null),
                new FilterPollsArgumentsHolder(9, ZERO_MIN_VOTES, NULL_START_AFTER_ID, List.of("tag:id:1"), null, ApiConstants.SortOrder.ASC, CommonStatus.INACTIVE),
                new FilterPollsArgumentsHolder(11, ZERO_MIN_VOTES, NULL_START_AFTER_ID, null, null, ApiConstants.SortOrder.ASC, null),
                new FilterPollsArgumentsHolder(12, ZERO_MIN_VOTES, NULL_START_AFTER_ID,null, List.of("BG League"), ApiConstants.SortOrder.DESC, CommonStatus.ACTIVE),
                new FilterPollsArgumentsHolder(13, ZERO_MIN_VOTES, NULL_START_AFTER_ID, null, null, ApiConstants.SortOrder.DESC, CommonStatus.INACTIVE))
                .map(Arguments::of);
    }
}
