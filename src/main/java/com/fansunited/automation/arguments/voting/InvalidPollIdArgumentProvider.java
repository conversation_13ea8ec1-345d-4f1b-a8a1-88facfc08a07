package com.fansunited.automation.arguments.voting;

import static com.fansunited.automation.constants.ApiConstants.VotingApi.INVALID_POLL_ID;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.SIX_WHITE_SPACES;

import java.util.stream.Stream;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidPollIdArgumentProvider implements ArgumentsProvider {
  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext context) throws Exception {
    return Stream.of(
            new InvalidPollIdArgumentsHolder(SIX_WHITE_SPACES, HttpStatus.SC_BAD_REQUEST),
            new InvalidPollIdArgumentsHolder(INVALID_POLL_ID, HttpStatus.SC_NOT_FOUND))
        .map(Arguments::of);
  }
}
