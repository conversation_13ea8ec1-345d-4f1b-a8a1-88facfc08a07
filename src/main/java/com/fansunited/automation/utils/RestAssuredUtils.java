package com.fansunited.automation.utils;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import io.restassured.RestAssured;
import io.restassured.config.ObjectMapperConfig;
import io.restassured.config.RestAssuredConfig;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.specification.RequestSpecification;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.HttpException;
import org.apache.http.util.TextUtils;

public class RestAssuredUtils {

  private static final int HTTPS_PORT = 443;

  private RestAssuredUtils() {
  }

  public static RequestSpecification requestWithAuth(String authToken, String baseUri,
      String port) {

    return requestWithoutAuth(baseUri, port)
        .auth()
        .oauth2(authToken);
  }

  public static RequestSpecification requestWithAuth(FirebaseHelper.FansUnitedProject project,
      String email,
      String password, String baseUri, String port)
      throws HttpException {

    final String authToken = FirebaseHelper.generateAuthToken(project, email, password);

    return requestWithoutAuth(baseUri, port)
        .auth()
        .oauth2(authToken);
  }

  /**
   * The method prepares the Request Specification that is used for Profile Ringer Endpoint.
   * The method creates the spec with or without Basic Auth, depending on the target of test.
   * If with Basic Auth, there are two options:
   * <li> - Username and password provided in the Test</li>
   * <li> - Default username and password are used</li>
   * @param specUsedFor for what the Request Specification will be set up - RINGIER or REDIS...
   * @param baseAuth Map of username and password
   * @param withBasicAuth defines if the spec will set basic auth or not
   * @param baseUri the base URI
   * @param port the port
   * @return Request Specification for Ringer Profile Endpoint
   */
  public static RequestSpecification baseAuthRequest(String specUsedFor, Map<String, String> baseAuth, boolean withBasicAuth,
      String baseUri, String port) {

    Map.Entry<String,String> entry;
    RequestSpecification basicRequestSpecification = null;

    if(baseAuth == null){
      baseAuth = new HashMap<>();
    }

    if(withBasicAuth) {
      if (!baseAuth.isEmpty()) {
        entry = baseAuth.entrySet().iterator().next();
        basicRequestSpecification = requestWithoutAuth(baseUri, port)
            .auth()
            .basic(entry.getKey(), entry.getValue());
      } else {
        String user = null, pass = null;

        switch (specUsedFor){
          case "ringier":
            user = ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.RINGIER_BASE_AUTH_USER);
            pass = ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.RINGIER_BASE_AUTH_PASS);
            break;
          case "redis":
            user = AuthConstants.DEFAULT_USER;
            pass = AuthConstants.DEFAULT_USER_PASS;
            break;
          default: throw new IllegalArgumentException(String.format("The provided 'specUsedFor'=%s value is invalid", specUsedFor));
        }
        basicRequestSpecification = requestWithoutAuth(baseUri, port)
            .auth()
            .basic(user, pass);
        String basicAuthValue = Base64.getEncoder().encodeToString((user + ":" + pass).getBytes(
            StandardCharsets.UTF_8));
        basicRequestSpecification.header("Authorization", "Basic " + basicAuthValue);
      }
    } else {
      basicRequestSpecification = requestWithoutAuth(baseUri, port);
    }

    return basicRequestSpecification;
  }

  public static RequestSpecification requestWithoutAuth(String baseUri, String port) {
    if (TextUtils.isEmpty(port)) {
      throw new IllegalArgumentException("Port cannot be null or empty.");
    }
    if (Integer.parseInt(port) == HTTPS_PORT) {
      return RestAssured.given().baseUri(baseUri);
    }
    return RestAssured.given().baseUri(baseUri).port(Integer.parseInt(port));
  }

  public static void setPropertyNamingStrategy(PropertyNamingStrategy propertyNamingStrategy) {
    RestAssured.config = RestAssuredConfig.config()
        .objectMapperConfig(new ObjectMapperConfig().jackson2ObjectMapperFactory(
            (cls, charset) -> {
              ObjectMapper om = new ObjectMapper().findAndRegisterModules();
              om.setPropertyNamingStrategy(propertyNamingStrategy);
              return om;
            }
        ));
  }

  public static void setLoggingEnabled(boolean enabled) {
    if (enabled) {
      RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
    }
  }

  public static void enableLoggingOnTestFailure() {
    RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
  }
}
