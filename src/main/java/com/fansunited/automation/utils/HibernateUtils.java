package com.fansunited.automation.utils;

import com.fansunited.automation.helpers.ConfigReader;
import java.io.File;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.SessionFactory;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.cfg.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HibernateUtils {

  private static SessionFactory sessionFactory;
  private static final String PACKAGE_ENTITIES = "com.fansunited.automation.core.resolver.hibernate";
  private static final String HIBERNATE_CONN_URL_PROP = "hibernate.connection.url";
  private static final String HIBERNATE_MYSQL_USER_PROP = "hibernate.connection.username";
  private static final String HIBERNATE_MYSQL_USER_PASS = "hibernate.connection.password";
  private static final Logger LOG = LoggerFactory.getLogger(HibernateUtils.class);

  public static synchronized SessionFactory getSessionFactory() {
    if (sessionFactory == null) {
      sessionFactory = buildSessionFactory();
    }
    return sessionFactory;
  }

  private static SessionFactory buildSessionFactory() {
    try {
      var cfg = new Configuration().configure();
      for (var cls : getEntityClassesFromPackage(PACKAGE_ENTITIES)) {
        cfg.addAnnotatedClass(cls);
      }
      loadSqlCredentials(cfg);
      var serviceRegistry = new StandardServiceRegistryBuilder()
          .applySettings(cfg.getProperties())
          .build();
      return cfg.buildSessionFactory(serviceRegistry);
    } catch (Exception e) {
      throw new RuntimeException("Error initializing Hibernate SessionFactory", e);
    }
  }

  private static List<Class<?>> getEntityClassesFromPackage(String packageName)
      throws ClassNotFoundException, URISyntaxException {
    LOG.debug("Loading entity classes from package: {}", packageName);

    var classNames = getClassNamesFromPackage(packageName);
    var classes = new ArrayList<Class<?>>();
    for (String className : classNames) {
      var cls = Class.forName(packageName + "." + className);
      var annotations = cls.getAnnotations();

      for (var annotation : annotations) {
        if (annotation instanceof jakarta.persistence.Entity) {
          classes.add(cls);
        }
      }
    }

    return classes;
  }

  private static ArrayList<String> getClassNamesFromPackage(String packageName) throws
      URISyntaxException {
    if (packageName == null) {
      throw new RuntimeException("Package name cannot not be null!");
    }
    var classLoader = Thread.currentThread().getContextClassLoader();
    var names = new ArrayList<String>();

    packageName = packageName.replace(".", "/");
    var packageURL = classLoader.getResource(packageName);

    if (packageURL == null) {
      LOG.warn("No entity classes found in package: {}", packageName);
    }

    var uri = new URI(packageURL.toString());
    var folder = new File(uri.getPath());
    var files = folder.listFiles();
    if (files == null || files.length == 0) {
      throw new RuntimeException("Package name: " + packageName + " is empty!");
    }

    for (var file : files) {
      String name = file.getName();
      name = name.substring(0, name.lastIndexOf('.'));  // remove ".class"
      names.add(name);
    }
    return names;
  }

  private static void loadSqlCredentials(Configuration configuration) {
    var configReader = ConfigReader.getInstance();
    var hibernateProps = configuration.getProperties();
    hibernateProps.put(HIBERNATE_CONN_URL_PROP, configReader.getProperty(
        ConfigReader.PropertyKey.MYSQL_CONNECTION_URL));
    hibernateProps.put(HIBERNATE_MYSQL_USER_PROP, configReader.getProperty(
        ConfigReader.PropertyKey.MYSQL_USER));
    hibernateProps.put(HIBERNATE_MYSQL_USER_PASS,
        configReader.getProperty(ConfigReader.PropertyKey.MYSQL_PASS));
  }
}
