package com.fansunited.automation.utils;

import static com.fansunited.automation.constants.ApiConstants.ClientApi.DEFAULT_BADGES_VALUES_JSON;

import com.fansunited.automation.model.clientapi.features.response.BadgeRewards;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;

public class JsonReader {

  private JsonReader() {}

  public static BadgeRewards getDefaultBadgesValuesFromJson() {
    final String badgesPath = "src/main/resources/" + DEFAULT_BADGES_VALUES_JSON;
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      return objectMapper.readValue(new File(badgesPath), BadgeRewards.class);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
}
