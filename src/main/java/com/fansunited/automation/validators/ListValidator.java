package com.fansunited.automation.validators;

import static com.fansunited.automation.constants.JsonSchemasPath.ClientsApi.Endpoints.Clients.POST_CREATE_MANAGE_LISTS_SCHEMA;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;

import com.fansunited.automation.model.clientapi.features.response.managelists.CreateListRequest;
import com.fansunited.automation.model.clientapi.features.response.managelists.CreateListResponse;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import org.junit.jupiter.api.Assertions;

public class ListValidator {
  private ListValidator() {
    throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
  }

  public static void validateCreateListResponse(CreateListRequest request, Response response) {
    response
        .then()
        .assertThat()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(POST_CREATE_MANAGE_LISTS_SCHEMA));

    var createListResponse = response.as(CreateListResponse.class).getData();
    Assertions.assertNotNull(createListResponse.getId());
    Assertions.assertEquals(CLIENT_AUTOMATION_ID, createListResponse.getClientId());
    Assertions.assertEquals(request.getName(), createListResponse.getName());
    Assertions.assertEquals(request.getType(), createListResponse.getType());
    assertThat(
        createListResponse.getEntities().getList(),
        containsInAnyOrder(request.getEntities().getList().toArray()));
    Assertions.assertNotNull(createListResponse.getCreatedAt());
    Assertions.assertNotNull(createListResponse.getUpdatedAt());
    Assertions.assertNotNull(createListResponse.getCreatedBy());
  }
}
