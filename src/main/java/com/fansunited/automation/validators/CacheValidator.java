package com.fansunited.automation.validators;

import static com.fansunited.automation.constants.HttpValues.CacheStatus.CACHE_MISS;
import static com.fansunited.automation.constants.HttpValues.HeaderKey.CACHE_CONTROL;
import static com.fansunited.automation.constants.HttpValues.HeaderKey.CACHE_STATUS;
import static com.fansunited.automation.constants.HttpValues.HeaderKey.EXPIRES;
import static com.fansunited.automation.constants.HttpValues.HeaderKey.FU_CACHE_STATUS;
import static com.fansunited.automation.constants.HttpValues.HeaderKey.LAST_MODIFIED;
import static com.fansunited.automation.helpers.DateFormatter.getTime;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.helpers.DateFormatter;
import io.restassured.response.Response;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.hamcrest.MatcherAssert;

public class CacheValidator {

  private static final int CACHE_OFFSET_SECONDS = 30;
  public static int validateResponseIsNotCached;

  @AllArgsConstructor
  public enum CachePeriod {
    ONE_MINUTE(60),
    TWO_MINUTES(120),
    TEN_MINUTES(600),
    THIRTY_MINUTES(1800),
    ONE_HOUR(3600),
    TWO_HOURS(7200),
    THREE_HOURS(10800),
    FOUR_HOURS(14400),
    EIGHT_HOURS(28800),
    HALF_DAY(43200),
    ONE_DAY(86400),
    TWO_DAYS(172800),
    THREE_DAYS(259200),
    FIVE_DAYS(432000),
    SEVEN_DAYS(604800),
    ONE_MONTH(2629800);

    @Getter
    private final int value;
  }

  public static void validateCacheExpirationDate(Response response, CachePeriod cachePeriod) {

    var lastModifiedHeaderValue = response.then().extract().header(LAST_MODIFIED);
    var expiresHeaderValue = response.then().extract().header(EXPIRES);

    MatcherAssert.assertThat("Expires header is null", expiresHeaderValue, notNullValue());

    var cacheExpireDatetime =
        LocalDateTime.parse(
                expiresHeaderValue,
                DateTimeFormatter.ofPattern(DateFormatter.CLOUDFLARE_DATE_TIME_FORMAT))
            .atZone(ZoneId.of("GMT"))
            .withZoneSameInstant(ZoneId.of(ZoneId.systemDefault().getId()))
            .toLocalDateTime();

    var cacheLastModifiedDatetime =
        LocalDateTime.parse(
                lastModifiedHeaderValue,
                DateTimeFormatter.ofPattern(DateFormatter.CLOUDFLARE_DATE_TIME_FORMAT))
            .atZone(ZoneId.of("GMT"))
            .withZoneSameInstant(ZoneId.of(ZoneId.systemDefault().getId()))
            .toLocalDateTime();

    int timeInSeconds = getTime(cacheLastModifiedDatetime, cacheExpireDatetime);

    MatcherAssert.assertThat(String.format("""
                    Cache expiration date is incorrect
                    Expected: %s min
                    Actual: %s min
                    """, cachePeriod.getValue() / 60, timeInSeconds / 60),
        isCacheExpirationValid(cacheExpireDatetime, cachePeriod),
        equalTo(true));
  }

  public static void validateResponseIsNotCached(Response response) {
    validateResponseIsNotCachedIfStaffMember(response);
  }

  public static void validateCacheStatus(Response response){

    String expiresHeaderValue = response.then().extract().header(EXPIRES);
    String cacheControlValue = response.then().extract().header(CACHE_CONTROL);
    String cacheStatusHeaderValue = response.then().extract().header(CACHE_STATUS);

    MatcherAssert.assertThat("Header key is cached!", expiresHeaderValue, nullValue());
    MatcherAssert.assertThat("Header key is cached!", cacheControlValue,nullValue() );
    MatcherAssert.assertThat("CF-Cache-Status is not BYPASS", cacheStatusHeaderValue,
              equalTo(CACHE_MISS.getValue()));
  }

  private static boolean isCacheExpirationValid(LocalDateTime cacheExpirationDate,
      CachePeriod cacheExpire) {
    return cacheExpirationDate.isAfter(
        LocalDateTime.now().plusSeconds(cacheExpire.value - CACHE_OFFSET_SECONDS))
        && cacheExpirationDate.isBefore(
        LocalDateTime.now().plusSeconds(cacheExpire.value + CACHE_OFFSET_SECONDS));
  }

  public static void validateResponseIsNotCachedIfStaffMember(Response response) {
    response
        .then()
        .assertThat()
        .headers(EXPIRES, nullValue())
        .headers(CACHE_CONTROL, nullValue())
        .headers(CACHE_STATUS, CACHE_MISS.getValue())
        .headers(FU_CACHE_STATUS, CACHE_MISS.getValue());
  }

  public static void validateResponseIsNotCachedProfileApi(Response response) {
    response
        .then()
        .assertThat()
        .headers(EXPIRES, nullValue())
        .headers(CACHE_CONTROL, "no-cache")
        .headers(CACHE_STATUS, CACHE_MISS.getValue())
        .headers(FU_CACHE_STATUS, CACHE_MISS.getValue());
  }

  public static void validateResponseIsCached(Response response) {
    response
        .then()
        .assertThat()
        .headers(CACHE_STATUS, CACHE_MISS.getValue())
        .headers(FU_CACHE_STATUS, CACHE_MISS.getValue());

    String expiresHeader = response.getHeader(EXPIRES);

    if (expiresHeader != null) {
      // Check that the cache miss status is consistent even when Expires is set
      assertCacheHeadersWhenExpiresPresent(response);
    } else {
      validateResponseIsNotCachedIfStaffMember(response);
    }
  }

  private static void assertCacheHeadersWhenExpiresPresent(Response response) {
    // Validate Expires header does not conflict with cache miss status
    response
        .then()
        .assertThat()
        .headers(CACHE_STATUS, CACHE_MISS.getValue())
        .headers(FU_CACHE_STATUS, CACHE_MISS.getValue());
  }
}
