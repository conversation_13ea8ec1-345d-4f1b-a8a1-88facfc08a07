package com.fansunited.automation.validators;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.helpers.CustomHamcrestMatchers;
import io.restassured.response.Response;
import java.util.List;
import org.hamcrest.Matcher;

public class ErrorValidator {

  public static void validateErrorResponse(Response response, int expectedStatusCode) {
    response.then().assertThat().statusCode(expectedStatusCode);
  }

  public static void validateErrorResponse(Response response, List<Integer> errorCodeList) {
    response
        .then()
        .assertThat()
        .statusCode(in(errorCodeList))
        .body("error.status", CustomHamcrestMatchers.stringNotContainsIntervals());
  }

  public static void validateErrorResponse(Response response, List<Integer> errorCodeList, String status) {
    response
            .then()
            .assertThat()
            .statusCode(in(errorCodeList))
            .body("error.status", equalTo(status));
  }

  public static void validateErrorResponseEmptyBody(
      Response response, List<Integer> errorCodeList) {
    response.then().assertThat().statusCode(in(errorCodeList));
  }

  public static void validateErrorResponse(
      Response response, int expectedStatusCode, String errorMessage) {
    response
        .then()
        .assertThat()
        .statusCode(expectedStatusCode)
        .body("message", equalTo(errorMessage));
  }


  public static void validateErrorResponse(Response response, Matcher<Integer> statusCode) {
    response.then().statusCode(statusCode).body("error.status", notNullValue());
  }
}
