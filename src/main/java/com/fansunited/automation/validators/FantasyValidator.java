package com.fansunited.automation.validators;

import static com.fansunited.automation.constants.ApiConstants.EndpointConstants.FOOTBALL_FANTASY;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.Matchers.hasItems;

import com.fansunited.automation.model.clientapi.features.response.AssistsExtended;
import com.fansunited.automation.model.clientapi.features.response.Coefficients;
import com.fansunited.automation.model.clientapi.features.response.ConcededGoalsExtended;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.Multipliers;
import com.fansunited.automation.model.clientapi.features.response.FootballFantasyFeature;
import com.fansunited.automation.model.clientapi.features.response.FoulsCommittedExtended;
import com.fansunited.automation.model.clientapi.features.response.Players;
import com.fansunited.automation.model.clientapi.features.response.SavesExtended;
import com.fansunited.automation.model.clientapi.features.response.ShotsOnExtended;
import com.fansunited.automation.model.clientapi.features.response.TacklesExtended;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;

public class FantasyValidator {
  public static void validateFantasyFeaturesResponse(
      Response response, FootballFantasyFeature fantasyFeature) {
    Coefficients coefficients = fantasyFeature.getCoefficients();
    Players player = fantasyFeature.getPlayers();
    Multipliers multipliers = fantasyFeature.getMultipliers();

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(FOOTBALL_FANTASY + ".enabled", equalTo(true))
        .body(FOOTBALL_FANTASY + ".coefficients.assists", equalTo(coefficients.getAssists()))
        .body(FOOTBALL_FANTASY + ".coefficients.shots", equalTo(coefficients.getShots()))
        .body(FOOTBALL_FANTASY + ".coefficients.offsides", equalTo(coefficients.getOffsides()))
        .body(FOOTBALL_FANTASY + ".coefficients.tackles", equalTo(coefficients.getTackles()))
        .body(FOOTBALL_FANTASY + ".coefficients.saves", equalTo(coefficients.getSaves()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.minutes_equal_or_over_60",
            equalTo(coefficients.getMinutesEqualOrOver60()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.minutes_under_60",
            equalTo(coefficients.getMinutesUnder60()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.yellow_cards", equalTo(coefficients.getYellowCards()))
        .body(FOOTBALL_FANTASY + ".coefficients.red_cards", equalTo(coefficients.getRedCards()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.goals_goalkeeper",
            equalTo(coefficients.getGoalsGoalkeeper()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.goals_defender",
            equalTo(coefficients.getGoalsDefender()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.goals_midfielder",
            equalTo(coefficients.getGoalsMidfielder()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.goals_forwards",
            equalTo(coefficients.getGoalsForwards()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.penalty_goals",
            equalTo(coefficients.getPenaltyGoals()))
        .body(FOOTBALL_FANTASY + ".coefficients.own_goals", equalTo(coefficients.getOwnGoals()))
        .body(FOOTBALL_FANTASY + ".coefficients.shots_on", equalTo(coefficients.getShotsOn()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.clean_sheets_goalkeeper",
            equalTo(coefficients.getCleanSheetsGoalkeeper()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.clean_sheets_defender",
            equalTo(coefficients.getCleanSheetsDefender()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.clean_sheets_midfielder",
            equalTo(coefficients.getCleanSheetsMidfielder()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.clean_sheets_forwards",
            equalTo(coefficients.getCleanSheetsForwards()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.fouls_committed",
            equalTo(coefficients.getFoulsCommitted()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.penalty_committed",
            equalTo(coefficients.getPenaltyCommitted()))
        .body(FOOTBALL_FANTASY + ".coefficients.penalty_won", equalTo(coefficients.getPenaltyWon()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.penalty_missed",
            equalTo(coefficients.getPenaltyMissed()))
        .body(
            FOOTBALL_FANTASY + ".coefficients.conceded_goals",
            equalTo(coefficients.getConcededGoals()))
        .body(FOOTBALL_FANTASY + ".coefficients.caught_ball", equalTo(coefficients.getCaughtBall()))
        .body(
            FOOTBALL_FANTASY + ".competitions_whitelist",
            hasItems(PREMIER_LEAGUE_COMP_ID, BUNDESLIGA_COMP_ID))
        .body(FOOTBALL_FANTASY + ".add_points_to_profile_total", equalTo(true))
        .body(FOOTBALL_FANTASY + ".players.total", equalTo(player.getTotal()))
        .body(FOOTBALL_FANTASY + ".players.min_goalkeepers", equalTo(player.getMinGoalkeepers()))
        .body(FOOTBALL_FANTASY + ".players.max_goalkeepers", equalTo(player.getMaxGoalkeepers()))
        .body(FOOTBALL_FANTASY + ".players.min_defenders", equalTo(player.getMinDefenders()))
        .body(FOOTBALL_FANTASY + ".players.max_defenders", equalTo(player.getMaxDefenders()))
        .body(FOOTBALL_FANTASY + ".players.min_midfielders", equalTo(player.getMinMidfielders()))
        .body(FOOTBALL_FANTASY + ".players.max_midfielders", equalTo(player.getMaxMidfielders()))
        .body(FOOTBALL_FANTASY + ".players.min_strikers", equalTo(player.getMinStrikers()))
        .body(FOOTBALL_FANTASY + ".players.max_strikers", equalTo(player.getMaxStrikers()))
        .body(FOOTBALL_FANTASY + ".multipliers.captain", equalTo(multipliers.getCaptain()))
        .body(FOOTBALL_FANTASY + ".multipliers.vice_captain", equalTo(multipliers.getViceCaptain()));

    verifyAssistsExtended(response, coefficients.getAssistsExtended());
    verifySavesExtended(response, coefficients.getSavesExtended());
    verifyTacklesExtended(response, coefficients.getTacklesExtended());
    verifyShotsOnExtended(response, coefficients.getShotsOnExtended());
    verifyConcededGoalsExtended(response, coefficients.getConcededGoalsExtended());
    verifyFoulsCommittedExtended(response, coefficients.getFoulsCommittedExtended());
  }

  private static void verifyAssistsExtended(Response response, AssistsExtended assistsExtended) {
    var actualAssistsExd =
        response
            .then()
            .extract()
            .body()
            .jsonPath()
            .getObject(FOOTBALL_FANTASY + ".coefficients.assists_extended", AssistsExtended.class);
    Assertions.assertEquals(assistsExtended, actualAssistsExd);
  }

  private static void verifySavesExtended(Response response, SavesExtended savesExtended) {
    var actualSavesExd =
        response
            .then()
            .extract()
            .body()
            .jsonPath()
            .getObject(FOOTBALL_FANTASY + ".coefficients.saves_extended", SavesExtended.class);
    Assertions.assertEquals(savesExtended, actualSavesExd);
  }

  private static void verifyTacklesExtended(Response response, TacklesExtended tacklesExtended) {
    var actualTacklesExd =
        response
            .then()
            .extract()
            .body()
            .jsonPath()
            .getObject(FOOTBALL_FANTASY + ".coefficients.tackles_extended", TacklesExtended.class);
    Assertions.assertEquals(tacklesExtended, actualTacklesExd);
  }

  private static void verifyShotsOnExtended(Response response, ShotsOnExtended shotsOnExtended) {
    var actualShotsOnExd =
        response
            .then()
            .extract()
            .body()
            .jsonPath()
            .getObject(FOOTBALL_FANTASY + ".coefficients.shots_on_extended", ShotsOnExtended.class);
    Assertions.assertEquals(shotsOnExtended, actualShotsOnExd);
  }

  private static void verifyConcededGoalsExtended(
      Response response, ConcededGoalsExtended concededGoalsExtended) {
    var actualConcededGoalsExd =
        response
            .then()
            .extract()
            .body()
            .jsonPath()
            .getObject(
                FOOTBALL_FANTASY + ".coefficients.conceded_goals_extended",
                ConcededGoalsExtended.class);
    Assertions.assertEquals(concededGoalsExtended, actualConcededGoalsExd);
  }

  private static void verifyFoulsCommittedExtended(
      Response response, FoulsCommittedExtended concededGoalsExtended) {
    var actualFoulsCommitedExd =
        response
            .then()
            .extract()
            .body()
            .jsonPath()
            .getObject(
                FOOTBALL_FANTASY + ".coefficients.fouls_committed_extended",
                FoulsCommittedExtended.class);
    Assertions.assertEquals(concededGoalsExtended, actualFoulsCommitedExd);
  }
}
