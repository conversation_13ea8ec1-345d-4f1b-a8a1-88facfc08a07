package com.fansunited.automation.validators;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_CLIENT_PREDICTIONS_SCHEMA;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_CLIENT_TOTAL_PREDICTIONS_SCHEMA;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.helpers.PeriodGenerator;
import com.fansunited.automation.model.reportingapi.predictions.allpredictions.PredictionsByMarket;
import com.fansunited.automation.model.reportingapi.predictions.clientpredictions.PredictionsBreakdownDto;
import com.fansunited.automation.model.reportingapi.predictions.common.ClientPredictionsDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;

public class ReportingValidator {

  public static void validateClientPredictionsGroupedBy(Response response, GroupBy groupBy,
      int predictionsCount, String dateFrom, String dateTo) {

    LocalDate fromDate = LocalDate.parse(dateFrom, DateTimeFormatter.ISO_LOCAL_DATE);
    LocalDate toDate = LocalDate.parse(dateTo, DateTimeFormatter.ISO_LOCAL_DATE);

    if (groupBy == null) {
      groupBy = GroupBy.DAY;
    }

    switch (groupBy) {
      case DAY -> response.then().assertThat()
          .body("data.breakdown.size()",
              equalTo(PeriodGenerator.countDaysInPeriod(fromDate, toDate)));
      case WEEK -> {
        response.then().assertThat()
            .body("data.breakdown.size()",
                equalTo(PeriodGenerator.countWeeksInPeriod(fromDate, toDate)));
        fromDate = PeriodGenerator.getFirstDateOfWeek(fromDate);
        toDate = PeriodGenerator.getLastDateOfWeek(toDate);
      }
      case MONTH -> {
        response.then().assertThat()
            .body("data.breakdown.size()",
                equalTo(PeriodGenerator.countMonthsInPeriod(fromDate, toDate)));
        fromDate = PeriodGenerator.getFirstDateOfMonth(fromDate);
        toDate = PeriodGenerator.getLastDateOfMonth(toDate);
      }
      case YEAR -> {
        fromDate = PeriodGenerator.getFirstDateOfYear(fromDate);
        toDate = PeriodGenerator.getLastDateOfYear(toDate);

        response.then().assertThat()
            .body("data.breakdown.size()",
                equalTo(PeriodGenerator.countYearsInPeriod(fromDate, toDate)));
      }
    }

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_CLIENT_PREDICTIONS_SCHEMA))
        .body("meta.markets", equalTo("FT_1X2,HT_1X2,BOTH_TEAMS_SCORE,OVER_GOALS_0_5,OVER_GOALS_1_5,OVER_GOALS_2_5,OVER_GOALS_3_5,OVER_GOALS_4_5,OVER_GOALS_5_5,OVER_GOALS_6_5,OVER_CORNERS_6_5,OVER_CORNERS_7_5,OVER_CORNERS_8_5,OVER_CORNERS_9_5,OVER_CORNERS_10_5,OVER_CORNERS_11_5,OVER_CORNERS_12_5,OVER_CORNERS_13_5,DOUBLE_CHANCE,HT_FT,PLAYER_SCORE,PLAYER_YELLOW_CARD,PLAYER_RED_CARD,RED_CARD_MATCH,PENALTY_MATCH,PLAYER_SCORE_FIRST_GOAL,CORNERS_MATCH,CORRECT_SCORE,CORRECT_SCORE_HT,CORRECT_SCORE_ADVANCED,PLAYER_SCORE_HATTRICK,PLAYER_SCORE_TWICE"))
        .body("meta.from_date", equalTo(fromDate.toString()))
        .body("meta.to_date", equalTo(toDate.toString()))
        .body("meta.grouped_by", equalTo(groupBy.getValue()));

    List<Object> list = response.then().extract().body().jsonPath()
        .getList("data.breakdown.predictions");

    ObjectMapper objectMapper = new ObjectMapper();
    List<Map<String, Integer>> mapList = new ArrayList<>();

    for (int i = 0; i < list.size(); i++) {
      mapList.add(objectMapper.convertValue(list.get(i), Map.class));
    }

    for (int i = 0; i < mapList.size(); i++) {
      if (mapList.get(i) == null) {
        mapList.remove(i);
        i--;
      }
    }

    List<Integer> totalPredictionCount = new ArrayList<>();
    List<Integer> allPredictions = new ArrayList<>();
    mapList.stream()
        .forEach(
            m -> m.entrySet()
                .stream()
                .filter(map -> !map.getKey().equals("all"))
                .forEach(map -> totalPredictionCount.add(map.getValue()))
        );

    mapList.stream()
        .forEach(
            m -> m.entrySet()
                .stream()
                .filter(map -> map.getKey().equals("all"))
                .forEach(map -> allPredictions.add(map.getValue()))
        );

    int all = allPredictions.stream().reduce(0, (a, b) -> a + b);
    int sumOfPredictions = totalPredictionCount.stream().reduce(0, (a, b) -> a + b);

    Assertions.assertEquals(predictionsCount, all,
        String.format(
            "The sum of all prediction doesn't match the sum of the mocked number of predictions\n"
                + "Expected = %s\n"
                + "Actual   = %s", predictionsCount, sumOfPredictions));

    Assertions.assertEquals(predictionsCount, sumOfPredictions,
        String.format(
            "The sum of the prediction by market doesn't match the sum of the mocked number of predictions"
                + "\n"
                + "Expected = %s"
                + "Actual   = %s", predictionsCount, sumOfPredictions));

    assertTotalPredictionsObject(response, predictionsCount);
  }

  private static void assertTotalPredictionsObject(Response response, int predictionsCount) {
    Object total = response
        .then()
        .extract()
        .body()
        .jsonPath()
        .get("data.total");

    ObjectMapper objectMapper = new ObjectMapper();

    Map<String, Integer> totalMap = objectMapper.convertValue(total, Map.class);
    List<Integer> totalPredictionCount = new ArrayList<>();
    List<Integer> allPredictions = new ArrayList<>();

    totalMap.entrySet()
        .stream()
        .filter(map -> map.getKey().equals("all"))
        .forEach(map -> allPredictions.add(map.getValue()));

    totalMap.entrySet()
        .stream()
        .filter(map -> !map.getKey().equals("all"))
        .forEach(map -> totalPredictionCount.add(map.getValue()));

    int all = allPredictions.stream().reduce(0, (a, b) -> a + b);
    int sumOfPredictions = totalPredictionCount.stream().reduce(0, (a, b) -> a + b);

    Assertions.assertEquals(predictionsCount, all,
        String.format(
            "The sum of all prediction doesn't match the sum of the mocked number of predictions\n"
                + "Expected = %s\n"
                + "Actual   = %s", predictionsCount, sumOfPredictions));

    Assertions.assertEquals(predictionsCount, sumOfPredictions,
        String.format(
            "The sum of the prediction by market doesn't match the sum of the mocked number of predictions"
                + "\n"
                + "Expected = %s"
                + "Actual   = %s", predictionsCount, sumOfPredictions));
  }

  public static void validateClientPredictionsWithoutParamsReturnsThirtyDays(Response response,
      int predictionsCount) {

    String fromDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE);
    String toDate = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.from_date", equalTo(fromDate))
        .body("meta.to_date", equalTo(toDate));

    assertTotalPredictionsObject(response, predictionsCount);
  }

  public static void validateClientPredictionsWithoutParamsReturnsThirtyDays(Response response) {

    String fromDate = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE);
    String toDate = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.from_date", equalTo(fromDate))
        .body("meta.to_date", equalTo(toDate));

  }

  public static void validateClientPredictionsFilteredByMarket(Response response, String market,
      int predictionsMadeWithMarket) {

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_CLIENT_PREDICTIONS_SCHEMA))
        .body("data.total.all", equalTo(predictionsMadeWithMarket))
        .body(String.format("data.total.%s", market), equalTo(predictionsMadeWithMarket));

    Object total = response
        .then()
        .extract()
        .body()
        .jsonPath()
        .get("data.total");

    ObjectMapper objectMapper = new ObjectMapper();
    Map<String, Integer> totalMap = objectMapper.convertValue(total, Map.class);

    List<String> allMarkets = new ArrayList<>();

    totalMap.entrySet()
        .stream()
        .filter(map -> !map.getKey().equals("all"))
        .forEach(map -> allMarkets.add(map.getKey()));

    Assertions.assertTrue(allMarkets.get(0).contains(market),
        "The list of total markets does NOT contain market=" + market);
    Assertions.assertEquals(allMarkets.size(), 1);
  }

  public static void validateClientTotalPredictions(Response response, int predictionsCount) {

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_CLIENT_TOTAL_PREDICTIONS_SCHEMA))
        .body("meta.from_date", is(nullValue()))
        .body("meta.to_date", is(nullValue()))
        .body("meta.grouped_by", equalTo("markets"));

    Map<Object, Object> marketTotals = response.then().extract().body().jsonPath()
        .getMap("data.breakdown");

    List<Integer> totalPredictionCount = new ArrayList<>();

    marketTotals.entrySet()
        .forEach(entry -> totalPredictionCount.add((Integer) entry.getValue()));

    int sumOfPredictions = totalPredictionCount.stream().reduce(0, (a, b) -> a + b);

    Assertions.assertEquals(predictionsCount, sumOfPredictions,
        String.format(
            "The sum of the prediction by market doesn't match the sum of the mocked number of predictions"
                + "\n"
                + "Expected = %s"
                + "Actual   = %s", predictionsCount, sumOfPredictions));

    Object total = response
        .then()
        .extract()
        .body()
        .jsonPath()
        .get("data.total.total");

    Assertions.assertEquals(predictionsCount, total,
        "The total of all prediction doesn't match the sum of the mocked number of predictions\nExpected = "
            + predictionsCount + "\nActual   = " + sumOfPredictions);

    Assertions.assertEquals(sumOfPredictions, total,
        String.format(
            "The sum of the prediction by market doesn't match the total predictions"
                + "\n"
                + "Expected = %s"
                + "Actual   = %s", predictionsCount, sumOfPredictions));
  }

  public static void validateClientPredictionsForPeriod(Response response, String dateFrom,
      String dateTo, List<LocalDateTime> timestampList) {

    LocalDate fromDate = LocalDate.parse(dateFrom, DateTimeFormatter.ISO_LOCAL_DATE);
    LocalDate toDate = LocalDate.parse(dateTo, DateTimeFormatter.ISO_LOCAL_DATE);

    List<LocalDateTime> afterFromDatePredictionsList = timestampList.stream()
        .filter(t -> t.toLocalDate().isAfter(fromDate.minusDays(1))).toList();

    List<LocalDateTime> predictionsWithinTimeFrameList = afterFromDatePredictionsList.stream()
        .filter(t -> t.toLocalDate().isBefore(toDate.plusDays(1)))
        .collect(Collectors.toList());

    List<PredictionsBreakdownDto> breakdown =
        response.as(ClientPredictionsDto.class).getData().getBreakdown();

    List<Long> numberOfActualPredictionsWithinPeriod = breakdown.stream()
        .map(e -> e.getPredictions())
        .toList()
        .stream()
        .filter(Objects::nonNull)
        .map(
            PredictionsByMarket::getAll)
        .toList();

    int all = Integer.parseInt(
        numberOfActualPredictionsWithinPeriod.stream().reduce(0L, (a, b) -> a + b).toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_CLIENT_PREDICTIONS_SCHEMA))
        .body("meta.markets", equalTo("all"))
        .body("meta.from_date", equalTo(fromDate.toString()))
        .body("meta.to_date", equalTo(toDate.toString()))
        .body("meta.grouped_by", equalTo(GroupBy.DAY.getValue()))
        .body("data.total.all", equalTo(predictionsWithinTimeFrameList.size()));

    Assertions.assertEquals(predictionsWithinTimeFrameList.size(), all);
  }

  public static void verifyEitherOrParticipationsResponse(
      Response response,
      int expectedParticipationsCount,
      String fromDate,
      String toDate,
      GroupBy groupBy,
      List<String> eitherOrIds) {

    if (eitherOrIds != null && !eitherOrIds.isEmpty()) {
      String eitherOrIdsValue = response.path("meta.either_or_ids");

      for (String expectedId : eitherOrIds) {
        Assertions.assertTrue(eitherOrIdsValue.contains(expectedId),
            "Response should contain the expected either_or_id: " + expectedId);
      }
    }

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .rootPath("meta")
        .body("from_date", equalTo(fromDate))
        .body("to_date", equalTo(toDate))
        .body("grouped_by", equalTo(groupBy.getValue()))
        .rootPath("data.total")
        .body("all", equalTo(expectedParticipationsCount))
        .rootPath("data.breakdown[0]")
        .body("participations.all", equalTo(expectedParticipationsCount));
  }
}
