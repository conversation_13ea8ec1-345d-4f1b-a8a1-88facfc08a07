package com.fansunited.automation.helpers;

import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.getOrderGamesWithIds;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.ListOfGamesData;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;

public class AssertionHelper {

  /**
   * The method asserts if the sort order of a game is in ascending or descending order based on game title
   * @param gameInstanceMap this is a list of all games of type MATCH_QUIZ that the assertion will be against
   * @param sortOrder the type of sort order. Values are: "desc", "asc"
   * @throws HttpException exception
   */
  public static void assertSortOrderBasedOnMatchKickoffAt(GameType gameType,
      Map<LocalDateTime, GameInstance> gameInstanceMap, ApiConstants.SortOrder sortOrder)
      throws HttpException {

      var gameIdsCommaSeparated = String.join(",", gameInstanceMap.values().stream().map(GameInstance::getId).map(String::valueOf).toList());

      var listOfGamesResponse =
              getOrderGamesWithIds(gameIdsCommaSeparated, gameType.getValue(), sortOrder != null ? sortOrder.getValue() : null);

    listOfGamesResponse.then().statusCode(HttpStatus.SC_OK).body("data", hasSize(greaterThan(0)));

    var gameInstanceList = listOfGamesResponse.as(ListOfGamesData.class).getData();

    List<GameInstance> gamesRelatedToTheCurrentTest = new ArrayList<>();
    var gameTitleList = gameInstanceMap.values().stream().map(GameInstance::getTitle).toList();

      for (GameInstance instance : gameInstanceList) {
          String title = instance.getTitle();

          for (String s : gameTitleList) {
              if (title.equals(s)) {
                  gamesRelatedToTheCurrentTest.add(instance);
              }
          }
      }


    Map<LocalDateTime, GameInstance> sortedMap = createSortedMap(sortOrder, gameInstanceMap);
    var gamesList = new ArrayList<>(sortedMap.values());

    List<String> expectedMatchIds =
        gamesList.stream()
            .map(gameInstance -> gameInstance.getFixtures().get(0).getMatchId())
            .toList();

    List<String> actualMatchIds =
            gamesRelatedToTheCurrentTest.stream()
            .map(gameInstance -> gameInstance.getFixtures().get(0).getMatchId())
            .toList();

    boolean isOrdered = expectedMatchIds.equals(actualMatchIds);

    Assertions.assertTrue(
        isOrdered,
        "Sort order is incorrect!\n"
            + "Expected: "
            + expectedMatchIds
            + "\n"
            + "Actual: "
            + actualMatchIds);
  }

  private static Map<LocalDateTime, GameInstance> createSortedMap(
      ApiConstants.SortOrder sortOrder, Map<LocalDateTime, GameInstance> gameInstanceMap) {

    if (sortOrder != null) {
      TreeMap<LocalDateTime, GameInstance> sortedMap =
          switch (sortOrder) {
            case DESC -> new TreeMap<>(Comparator.reverseOrder());
            case ASC -> new TreeMap<>(Comparator.naturalOrder());
          };
      sortedMap.putAll(gameInstanceMap);
      return sortedMap;
    }
    TreeMap<LocalDateTime, GameInstance> sortedMap = new TreeMap<>(Comparator.reverseOrder());
    sortedMap.putAll(gameInstanceMap);
    return sortedMap;
  }
}
