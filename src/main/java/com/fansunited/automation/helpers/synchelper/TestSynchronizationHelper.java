package com.fansunited.automation.helpers.synchelper;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static java.lang.Thread.sleep;
import static org.awaitility.Awaitility.await;

import com.fansunited.automation.core.apis.loyaltyapi.GameLeaderboardsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint;
import com.fansunited.automation.core.apis.reportingapi.ActiveMonthlyUsersEndpoint;
import com.fansunited.automation.helpers.BigQueryHelper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.apache.arrow.util.VisibleForTesting;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TestSynchronizationHelper {

  private static int MAX_THREADS = 16;
  private static int CHUNK_TIMEOUT_SECONDS = 30;
  private static int MID_CONDITION_TIMEOUT_SECONDS = 300;

  public static final String DEFAULT_MIDCONDITION = "default";
  public static final String USER_RANKING_GAME_MV = "user_ranking_game_mv";
  public static final String USER_RANKING_TEMPLATE_MV = "user_ranking_template_mv";
  public static final String MONTHLY_ACTIVE_USERS_MV = "monthly_active_users_mv";
  public static final String USER_RANKING_TEMPLATE_MV_NO_WAIT = "user_ranking_template_mv_no_wait";
  public static final String USER_RANKING_GAME_MV_NO_WAIT = "user_ranking_game_mv_no_wait";

  private final List<TestTask> testQueue = Collections.synchronizedList(new ArrayList<>());
  private final Object lock = new Object();
  private static final Logger LOG = LoggerFactory.getLogger(TestSynchronizationHelper.class);
  private static volatile TestSynchronizationHelper instance;

  private TestSynchronizationHelper() {
    new Thread(this::chunkDispatcher, "ChunkDispatcher").start();
  }

  public static TestSynchronizationHelper getInstance() {
    if (instance == null) {
      synchronized (TestSynchronizationHelper.class) {
        if (instance == null) {
          instance = new TestSynchronizationHelper();
        }
      }
    }
    return instance;
  }

  public void completePreconditionAndAddMidCondition(String name, String id) {
    completePreconditionAndAddMidCondition(name, id, 0);
  }

  //todo: think of better way than overloading
  public void completePreconditionAndAddMidCondition(String name, String id, int size) {
    LOG.info("Start: completePreconditionAndAddMidCondition for name={} id={} size={}", name, id, size);

    TestTask task = createAndAddTaskToQueue(name, id, size);
    waitForTaskToComplete(task);

    LOG.info("End: completePreconditionAndAddMidCondition for name={} id={} size={}", name, id, size);
  }

  private TestTask createAndAddTaskToQueue(String name, String id, int size) {
    validateTaskName(name);

    TestTask task = new TestTask(name, id, size);
    synchronized (lock) {
      testQueue.add(task);
      LOG.info("Test added to queue: {}, queue size: {}", task, testQueue.size());
      lock.notifyAll(); // Notify dispatcher in case it’s waiting
    }
    return task;
  }

  private void validateTaskName(String name) {
    if (!isValidTaskName(name)) {
      throw new IllegalArgumentException("Invalid task name: " + name + ". Must be one of: " +
          String.join(", ", DEFAULT_MIDCONDITION, USER_RANKING_GAME_MV, USER_RANKING_TEMPLATE_MV,
              USER_RANKING_TEMPLATE_MV_NO_WAIT, USER_RANKING_GAME_MV_NO_WAIT, MONTHLY_ACTIVE_USERS_MV));
    }
  }

  private boolean isValidTaskName(String name) {
    return DEFAULT_MIDCONDITION.equals(name) ||
        USER_RANKING_GAME_MV.equals(name) ||
        USER_RANKING_TEMPLATE_MV.equals(name) ||
        USER_RANKING_TEMPLATE_MV_NO_WAIT.equals(name) ||
        USER_RANKING_GAME_MV_NO_WAIT.equals(name) ||
        MONTHLY_ACTIVE_USERS_MV.equals(name);
  }

  private void waitForTaskToComplete(TestTask task) {
    synchronized (lock) {
      while (!task.isSignaled()) {
        try {
          lock.wait(); // Block until this thread is signaled
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
          LOG.error("Thread interrupted while waiting: {}", e.getMessage());
          return;
        }
      }
    }
  }

  private void chunkDispatcher() {
    while (true) {
      List<TestTask> chunk = collectTasksForChunk();
      if (!chunk.isEmpty()) {
        processChunk(chunk);
      }
    }
  }

  private List<TestTask> collectTasksForChunk() {
    List<TestTask> chunk = new ArrayList<>();
    long chunkStartTime = System.currentTimeMillis();

    synchronized (lock) {
      while (chunk.size() < MAX_THREADS
          && System.currentTimeMillis() - chunkStartTime < CHUNK_TIMEOUT_SECONDS * 1000) {
        if (!testQueue.isEmpty()) {
          chunk.add(testQueue.remove(0));
        } else {
          try {
            lock.wait(100); // Wait for new tasks or timeout
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOG.error("Dispatcher interrupted: {}", e.getMessage());
            break;
          }
        }
      }

      // Check if timeout occurred
      if (chunk.size() > 0
          && chunk.size() < MAX_THREADS
          && System.currentTimeMillis() - chunkStartTime >= CHUNK_TIMEOUT_SECONDS * 1000) {
        LOG.warn(
            "Timeout occurred while collecting tasks for chunk. No tasks added within {} seconds.",
            CHUNK_TIMEOUT_SECONDS);
      } else if (!chunk.isEmpty()) {
        LOG.info("Dispatcher collected chunk with {} tests, {}", chunk.size(),
            getCurrentTimestamp());
      }
    }
    return chunk;
  }

  private void processChunk(List<TestTask> chunk) {
    LOG.info("Processing chunk with {} tests", chunk.size());

    simulateChunkProcessing(chunk);

    LOG.info("Chunk processing completed.");
    signalTasksInChunk(chunk);
  }

  private void simulateChunkProcessing(List<TestTask> chunk) {
    LOG.info("Simulating chunk processing with Awaitility.");

    // Refresh materialized views for distinct names in the chunk
    var names = chunk.stream().map(TestTask::name).distinct().toList();
    names.forEach(n -> {
      try {
        // refresh only items that are actually a materialized view
        var mv = mapTaskMv(n);
        if (null != mv) {
          LOG.info("Refreshing mv {} {}", mv, getCurrentTimestamp());
          BigQueryHelper.refreshMaterializedView(mv);
        }
      } catch (Exception e) {
        LOG.error("Error refreshing materialized view for {}: {}", n, e.getMessage(), e);
      }
    });

    // Await readiness for each task in the chunk
    for (TestTask task : chunk) {
      try {
        LOG.info("Awaiting readiness for task: {}", task);

        await()
            .atMost(MID_CONDITION_TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .pollInterval(1, TimeUnit.SECONDS)
            .until(() -> mapTaskToReadinessCheck(task).call());

        LOG.info("Task {} is ready.", task.id());
      } catch (Exception e) {
        LOG.error("Error during task readiness check for {}: {}", task.id(), e.getMessage(),
            e);
        return;
      }
    }

    LOG.info("All tasks in the chunk processed.");
  }

  private Callable<Boolean> mapTaskToReadinessCheck(TestTask task) {
    switch (task.name()) {
      case USER_RANKING_GAME_MV:
        return () -> isReadyUserRankingGameMv(task.id(), task.size());
      case USER_RANKING_TEMPLATE_MV:
        return () -> isReadyUserRankingTemplateMv(task.id(), task.size());
      case MONTHLY_ACTIVE_USERS_MV:
        return () -> isReadyMonthlyActiveUsersMv(task.id(), task.size());
      case DEFAULT_MIDCONDITION:
      case USER_RANKING_TEMPLATE_MV_NO_WAIT:
      case USER_RANKING_GAME_MV_NO_WAIT:
        return () -> isReadyDefault();
      default:
        throw new IllegalArgumentException("Unknown midcondition name: " + task.name());
    }
  }

  private String mapTaskMv(String taskName) {
    return switch (taskName) {
      case USER_RANKING_GAME_MV, USER_RANKING_GAME_MV_NO_WAIT -> USER_RANKING_GAME_MV;
      case USER_RANKING_TEMPLATE_MV, USER_RANKING_TEMPLATE_MV_NO_WAIT -> USER_RANKING_TEMPLATE_MV;
      case MONTHLY_ACTIVE_USERS_MV -> MONTHLY_ACTIVE_USERS_MV;
      case DEFAULT_MIDCONDITION -> null;
      default -> throw new IllegalArgumentException("Unknown midcondition name: " + taskName);
    };
  }

  private void signalTasksInChunk(List<TestTask> chunk) {
    synchronized (lock) {
      for (TestTask task : chunk) {
        task.signal(); // Signal threads for these tests
      }
      lock.notifyAll(); // Allow new threads to queue
    }
  }

  @SneakyThrows
  private boolean isReadyUserRankingGameMv(String id, int size) {
    LOG.info("isReadyUserRankingGameMv {}, {}", id, getCurrentTimestamp());
    return isDataAvailable(
        GameLeaderboardsEndpoint.builder()
            .gameId(id)
            .build()
            .getLeaderboardForGameId()
            .then()
            .assertThat()
            .log().ifValidationFails()
            .extract()
            .response()
            .jsonPath()
            .getList("data"),
        size
    );
  }

  @SneakyThrows
  private boolean isReadyUserRankingTemplateMv(String id, int size) {
    LOG.info("isReadyUserRankingTemplateMv {}", id);
    return isDataAvailable(
        LeaderboardByIdEndpoint.getLeaderboardForTemplateWithIdNoCache(
                id, CLIENT_AUTOMATION_ID, null, null)
            .then()
            .assertThat()
            .log().ifValidationFails()
            .extract()
            .response()
            .jsonPath()
            .getList("data"),
        size
    );
  }

  @SneakyThrows
  private boolean isReadyMonthlyActiveUsersMv(String id, int size) {
    LOG.info("isReadyMonthlyActiveUsersMv {}", id);
    var responseSize = ActiveMonthlyUsersEndpoint.builder().build().getMonthlyActiveUsers()
            .then()
            .assertThat()
            .log().ifValidationFails()
            .extract()
            .response()
            .jsonPath()
            .getList("data.breakdown").size();
    return responseSize > size;
  }

  @SneakyThrows
  private boolean isReadyDefault() {
    LOG.info("isReadyDefault");
    sleep(100);
    return true;
  }

  private boolean isDataAvailable(List<?> data, int size) {
    if (size == 0) {
      return data != null && !data.isEmpty();
    }
    return data != null && !data.isEmpty() && size == data.size();
  }

  private String getCurrentTimestamp() {
    return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
  }

  @VisibleForTesting
  public void setMaxThreads(int threads) {
    MAX_THREADS = threads;
  }

  @VisibleForTesting
  public void setMidConditionTimeoutSeconds(int seconds) {
    MID_CONDITION_TIMEOUT_SECONDS = seconds;
  }

  @VisibleForTesting
  public void setChunkTimeoutSeconds(int seconds) {
    CHUNK_TIMEOUT_SECONDS = seconds;
  }
}
