package com.fansunited.automation.helpers.synchelper;

public class TestTask {
  private final String name;
  private final String id;
  private final int size;
  private boolean signaled = false;

  public TestTask(String name, String id, int size) {
    this.name = name;
    this.id = id;
    this.size = size;
  }

  public synchronized boolean isSignaled() {
    return signaled;
  }

  public synchronized void signal() {
    this.signaled = true;
  }

  public String name() {
    return name;
  }

  public String id() {
    return id;
  }

  public int size() { return size; }

  @Override
  public String toString() {
    return "TestTask{name='" + name + "', id='" + id + "'}";
  }
}