package com.fansunited.automation.helpers;

import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getMatchById;
import static com.fansunited.automation.helpers.BigQueryHelper.isProfileIdExistingInTable;
import static com.fansunited.automation.helpers.ConfigReader.getCurrentTestEnvironment;

import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import io.restassured.response.Response;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WaitHelper {
  private static final Logger LOG = LoggerFactory.getLogger(WaitHelper.class);

  public static void waitGameStatusToBeUpdatedToLive() throws InterruptedException {
    String env = getCurrentTestEnvironment();

    switch (env) {
      case "local", "vm1", "vm2":
        TimeUnit.SECONDS.sleep(180);
        break;
      case "stage":
        TimeUnit.SECONDS.sleep(120);
        break;
      default:
        throw new RuntimeException("Could not get correctly Environment=" + env);
    }
  }

  public static void waitGameStatusToBeUpdatedFromLiveToClosed() throws InterruptedException {
    String env = getCurrentTestEnvironment();

    switch (env) {
      case "local", "vm1", "vm2":
      case "stage":
        TimeUnit.SECONDS.sleep(360);
        break;
      default:
        throw new RuntimeException("Could not get correctly Environment=" + env);
    }
  }

  /**
   * This method periodically checks if the game status has changed from "Open" to something else
   * within the specified time frame.
   *
   * @param gameInstance The game instance that will be monitored.
   * @param expectedStatus The name of the status that is expected to be set for the game instance.
   * @param checkEverySeconds The interval (in seconds) at which the status will be checked.
   * @param waitUntil The maximum duration (in seconds) to wait for the status to change.
   * @throws InterruptedException If the thread is interrupted while waiting.
   * @throws HttpException If there is an HTTP-related error during the check.
   */
  public static void waitGameStatusToBeUpdated(
      GameInstance gameInstance, GameStatus expectedStatus, int checkEverySeconds, int waitUntil)
      throws InterruptedException, HttpException {

    Response response = GameEndpoint.getGameById(gameInstance.getId());

    int waitedForSeconds = 0;

    while (!response.jsonPath().get("status").equals(expectedStatus.getValue())) {
      TimeUnit.SECONDS.sleep(checkEverySeconds);
      waitedForSeconds += checkEverySeconds;

      if (waitedForSeconds >= waitUntil) {
        LOG.error(
            "Expected status '{}' was not reached within the required period. Waited for {} seconds, id '{}', status '{}'",
            expectedStatus.getValue(),
            waitedForSeconds,
            gameInstance.getId(),
            gameInstance.getStatus());

        throw new RuntimeException(
            String.format(
                "Expected status '%s' was not reached within the required period. Waited for %s seconds, id %s, status %s",
                expectedStatus.getValue(),
                waitedForSeconds,
                gameInstance.getId(),
                gameInstance.getStatus()));
      }

      response = GameEndpoint.getGameById(gameInstance.getId());
    }
  }

  public static void waitGameStatusToBeUpdated(
      String gameId, List<GameStatus> expectedStatusList, int checkEverySeconds, int waitUntil) {

    // Convert the expected status list to their string values for comparison
    var statuses = expectedStatusList.stream().map(GameStatus::getValue).toList();

    int waitedForSeconds = 0;

    while (true) {
      // Fetch the current game status from the endpoint
      Response response = null;
      try {
        response = GameEndpoint.getGameById(gameId);
      } catch (HttpException e) {
        e.printStackTrace();
        return;
      }
      String currentStatus = response.jsonPath().get("status");

      // Check if the current status is in the expected status list
      boolean found = statuses.stream().anyMatch(currentStatus::equals);

      // If the status is found, exit the loop
      if (found) {
        break;
      }

      // Sleep for the defined interval before checking again
      try {
        TimeUnit.SECONDS.sleep(checkEverySeconds);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
      waitedForSeconds += checkEverySeconds;

      // Throw an exception if the maximum wait time is exceeded
      if (waitedForSeconds >= waitUntil) {
        LOG.error(
            "Expected status(es) '{}' were not reached within the required period. Waited for {} seconds. Current status: '{}', id '{}'",
            statuses,
            waitedForSeconds,
            currentStatus,
            gameId);

        throw new RuntimeException(
            String.format(
                "Expected status(es) '%s' were not reached within the required period. Waited for %s seconds. Current status: '%s', id %s",
                statuses, waitedForSeconds, currentStatus, gameId));
      }
    }
  }

  public static void waitMatchStatusToBeUpdatedFromNotStartedToPostponed(
      String matchId, int checkEverySeconds, int waitUntil) throws InterruptedException {
    Response response = getMatchById(matchId);

    int waitedForSeconds = 0;

    while (!response.jsonPath().getString("data.status.sub_type").equals("postponed")) {
      TimeUnit.SECONDS.sleep(checkEverySeconds);
      waitedForSeconds += checkEverySeconds;

      if (waitedForSeconds >= waitUntil) {
        LOG.error(
            "Expected status '%s' was not reached within the required period. Waited for %s seconds.",
            "postponed", waitedForSeconds);
        throw new RuntimeException(
            String.format(
                "Expected status '%s' was not reached within the required period. Waited for %s seconds.",
                "postponed", waitedForSeconds));
      }

      response = getMatchById(matchId);
    }
  }

  public static void waitProfileIDToBeUpdated(
      String profileId, String table, int checkEverySeconds, int waitUntil)
      throws InterruptedException {

    int waitedForSeconds = 0;

    while (!isProfileIdExistingInTable(table, profileId)) {
      TimeUnit.SECONDS.sleep(checkEverySeconds);
      waitedForSeconds += checkEverySeconds;

      if (waitedForSeconds >= waitUntil) {
        String errorMessage =
            String.format(
                "Profile ID '%s' was not created within the expected timeframe. Waited %s seconds.",
                profileId, waitedForSeconds);

        LOG.error(errorMessage);
        throw new RuntimeException(errorMessage);
      }
    }
  }
}
