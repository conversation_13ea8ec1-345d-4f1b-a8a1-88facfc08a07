package com.fansunited.automation.helpers;

import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.request.UpdateCustomEventGameRequest;
import com.github.javafaker.Faker;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

/**
 * Helper class for creating update requests for testing purposes.
 */
public class UpdateRequest {

  private static final Faker faker = new Faker();

  /**
   * Creates an update custom event game request with modified fixtures.
   *
   * @return A CreateCustomEventGameRequest with updated values for testing updates
   */
  public static UpdateCustomEventGameRequest updateCustomEventGameRequest() {
    return UpdateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you today")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title("Updated Test Game")
        .description("Updated test description")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
        .outcome(false)
        .build();
  }

  /**
   * Creates an update custom event game request with custom title and description.
   *
   * @param title The title for the updated game
   * @param description The description for the updated game
   * @return A CreateCustomEventGameRequest with custom title and description
   */
  public static UpdateCustomEventGameRequest updateCustomEventGameRequest(String title, String description) {
    return UpdateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you today")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .outcome("fine")
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title(title)
        .description(description)
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
        .outcome(false)
        .build();
  }

  /**
   * Creates an update custom event game request with custom fixtures.
   *
   * @param fixtures The fixtures for the updated game
   * @return A CreateCustomEventGameRequest with custom fixtures
   */
  public static UpdateCustomEventGameRequest updateCustomEventGameRequestWithFixtures(List<CustomEventFixture> fixtures) {
    return UpdateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(fixtures)
        .title("Updated Test Game")
        .description("Updated test description")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
        .outcome(false)
        .build();
  }

  /**
   * Creates an update custom event game request with custom status.
   *
   *
   * @return A CreateCustomEventGameRequest with custom status
   */
  public static UpdateCustomEventGameRequest updateCustomEventGameRequestWithOutcome(Date cutoffTime) {
    return UpdateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id("1")
                    .question("how are you today")
                    .outcome("fine")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id("2")
                    .outcome("all good")
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title("Updated Test Game")
        .description("Updated test description")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .status(GameStatus.OPEN)
        .customFields(null)
        .predictionsCutoff(cutoffTime)
        .outcome(false)
        .build();
  }

  /**
   * Creates an update custom event game request with custom predictions cutoff.
   *
   * @param cutoff The predictions cutoff date for the updated game
   * @return A CreateCustomEventGameRequest with custom predictions cutoff
   */
  public static UpdateCustomEventGameRequest updateCustomEventGameRequestWithCutoff(Date cutoff) {
    return UpdateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you today")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .outcome("fine")
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title("Updated Test Game")
        .description("Updated test description")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(cutoff)
        .outcome(false)
        .build();
  }

  /**
   * Creates an update custom event game request without a specific field for validation testing.
   *
   * @param fieldToRemove The field to remove ("type", "title", "fixtures", "predictionsCutoff")
   * @return A CreateCustomEventGameRequest with the specified field removed
   */
  public static UpdateCustomEventGameRequest updateCustomEventGameRequestWithoutField(String fieldToRemove) {
    var builder = UpdateCustomEventGameRequest.builder()
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you today")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .outcome("fine")
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .description("Updated test description")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .outcome(false);

    // Add fields conditionally based on what should be removed
    switch (fieldToRemove.toLowerCase()) {
      case "type" -> {
        // Don't set type
        builder.title("Updated Test Game")
               .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()));
      }
      case "title" -> {
        // Don't set title
        builder.type("EVENT")
               .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()));
      }
      case "fixtures" -> {
        // Don't set fixtures
        builder.type("EVENT")
               .title("Updated Test Game")
               .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
               .fixtures(null);
      }
      case "predictionscutoff" -> {
        // Don't set predictions cutoff
        builder.type("EVENT")
               .title("Updated Test Game");
      }
      default -> {
        // Default case - include all fields
        builder.type("EVENT")
               .title("Updated Test Game")
               .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()));
      }
    }

    return builder.build();
  }

  /**
   * Creates an update custom event game request with outcome for resolving.
   *
   * @param predictionsCutoff The predictions cutoff date for the updated game
   * @return A CreateCustomEventGameRequest with outcome set for resolution
   */
  public static CreateCustomEventGameRequest updateCustomEventGameRequestWithOutcome(Date predictionsCutoff) {
    return CreateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you today")
                    .outcome("great") // Set outcome for resolution
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .outcome("fine") // Set outcome for resolution
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title("Updated Test Game with Outcome")
        .description("Updated test description with outcome")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(predictionsCutoff)
        .outcome(true) // Set outcome to true for resolution
        .build();
  }
}
