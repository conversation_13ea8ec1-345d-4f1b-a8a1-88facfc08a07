package com.fansunited.automation.helpers;

import static com.fansunited.automation.helpers.Helper.convertStringDateTimeToZonedTime;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;

public class CustomHamcrestMatchers {

  public static Matcher<? super List<String>> isInAscendingAlphabeticalOrder() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("List was not sorted in ascending alphabetical order!");
      }

      @Override
      protected boolean matchesSafely(List<String> list) {
        var copy = new ArrayList<>(list);
        copy.sort(String::compareToIgnoreCase);
        return list.equals(copy);
      }
    };
  }

  public static Matcher<? super List<String>> isInDescendingAlphabeticalOrder() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("List was not sorted in descending alphabetical order!");
      }

      @Override
      protected boolean matchesSafely(List<String> list) {
        var copy = new ArrayList<>(list);
        copy.sort(String::compareToIgnoreCase);
        Collections.reverse(copy);
        return list.equals(copy);
      }
    };
  }

  public static Matcher<? super List<Integer>> IsInDescendingNumericalOrder() {
    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("Integers were not sorted in descending order");
      }

      @Override
      protected boolean matchesSafely(List<Integer> item) {
        for (int i = 0; i < item.size() - 1; i++) {
          if (item.get(i) < item.get(i + 1)) return false;
        }
        return true;
      }
    };
  }

  public static Matcher<? super List<Integer>> IsInAscendingNumericalOrder() {
    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("Integers were not sorted in ascending order");
      }

      @Override
      protected boolean matchesSafely(List<Integer> item) {
        for (int i = 0; i < item.size() - 1; i++) {
          if (item.get(i) > item.get(i + 1)) return false;
        }
        return true;
      }
    };
  }

  public static Matcher<String> containsCyrillic() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("in cyrillic chars");
      }

      @Override
      protected boolean matchesSafely(String word) {
        return word.chars()
            .mapToObj(Character.UnicodeBlock::of)
            .anyMatch(Character.UnicodeBlock.CYRILLIC::equals);
      }
    };
  }
  public static Matcher<String> containsGreek(){

    return new TypeSafeMatcher<>() {
      @Override
      protected boolean matchesSafely(String word) {
        return word.chars()
                .mapToObj(Character.UnicodeBlock::of)
                .anyMatch(Character.UnicodeBlock.GREEK::equals);
      }

      @Override
      public void describeTo(Description description) {description.appendText("in Greek chars");}
    };
  }

  public static Matcher<? super List<String>> listOfStringsContainsCyrillic() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("list contains cyrillic chars");
      }

      @Override
      protected boolean matchesSafely(List<String> words) {
        return words.stream().anyMatch(word -> word.chars()
            .mapToObj(Character.UnicodeBlock::of)
            .anyMatch(Character.UnicodeBlock.CYRILLIC::equals));
      }
    };
  }

  public static Matcher<? super List<String>> isoDatesAreInAscendingOrder() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("list of dates are sorted in ascending order");
      }

      @Override
      protected boolean matchesSafely(List<String> datesInIsoFormat) {
        var listOfZonedDateTimeObjects = new ArrayList<ZonedDateTime>();
        datesInIsoFormat.stream().filter(Objects::nonNull).forEach(
            date -> listOfZonedDateTimeObjects.add(convertStringDateTimeToZonedTime(date)));

        for (int i = 0; i < listOfZonedDateTimeObjects.size() - 1; i++) {
          if (listOfZonedDateTimeObjects.get(i).isAfter(listOfZonedDateTimeObjects.get(i + 1))) {
            return false;
          }
        }
        return true;
      }
    };
  }

  public static Matcher<? super List<String>> isoDatesAreInDescendingOrder() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("list of dates are sorted in descending order");
      }

      @Override
      protected boolean matchesSafely(List<String> datesInIsoFormat) {
        var listOfZonedDateTimeObjects = new ArrayList<ZonedDateTime>();
        datesInIsoFormat.stream().filter(Objects::nonNull).forEach(
            date -> listOfZonedDateTimeObjects.add(convertStringDateTimeToZonedTime(date)));

        for (int i = 0; i < listOfZonedDateTimeObjects.size() - 1; i++) {
          if (listOfZonedDateTimeObjects.get(i).isBefore(listOfZonedDateTimeObjects.get(i + 1))) {
            return false;
          }
        }
        return true;
      }
    };
  }

  public static Matcher<String> stringNotContainsIntervals() {

    return new TypeSafeMatcher<>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("String does not contain intervals");
      }

      @Override
      protected boolean matchesSafely(String word) {
        return !word.contains(" ");
      }
    };
  }
}

