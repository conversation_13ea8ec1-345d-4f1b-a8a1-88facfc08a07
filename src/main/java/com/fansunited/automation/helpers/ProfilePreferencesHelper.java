package com.fansunited.automation.helpers;

import com.fansunited.automation.model.clientapi.features.response.ProfilePreference;
import com.fansunited.automation.model.clientapi.features.response.ProfilePreferenceCategory;
import com.fansunited.automation.model.clientapi.features.response.ProfilePreferencesFeature;
import com.github.javafaker.Faker;
import java.util.List;

public class ProfilePreferencesHelper {

  private static final Faker faker = new Faker();

  public static final String FOOD_CATEGORY_ID = "food";
  public static final String BRAND_CATEGORY_ID = "brand";
  public static final String PIZZA_PREFERENCE_ID = "pizza";
  public static final String NIKE_PREFERENCE_ID = "nike";

  public static ProfilePreferencesFeature createProfilePreferencesFeature(
      boolean enabled,
      String preferenceId1,
      String preferenceId2,
      String categoryId1,
      String categoryId2) {

    return ProfilePreferencesFeature.builder()
        .enabled(enabled)
        .preferences(
            List.of(
                ProfilePreference.builder()
                    .id(preferenceId1)
                    .displayName(faker.name().name())
                    .categories(List.of(categoryId1, categoryId2))
                    .build(),
                ProfilePreference.builder()
                    .id(preferenceId2)
                    .displayName(faker.name().name())
                    .categories(List.of(categoryId1, categoryId2))
                    .build()))
        .categories(
            List.of(
                ProfilePreferenceCategory.builder()
                    .id(categoryId1)
                    .displayName(faker.funnyName().name())
                    .build(),
                ProfilePreferenceCategory.builder()
                    .id(categoryId2)
                    .displayName(faker.funnyName().name())
                    .build()))
        .build();
  }

  public static ProfilePreferencesFeature createDefaultProfilePreferencesFeature(boolean enabled) {
    return createProfilePreferencesFeature(
        enabled, PIZZA_PREFERENCE_ID, NIKE_PREFERENCE_ID, FOOD_CATEGORY_ID, BRAND_CATEGORY_ID);
  }
}
