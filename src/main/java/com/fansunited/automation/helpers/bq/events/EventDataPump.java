package com.fansunited.automation.helpers.bq.events;

import static com.fansunited.automation.mappers.EventMapper.EVENT_TABLE;

import com.fansunited.automation.helpers.bq.BaseDataPump;
import com.fansunited.automation.helpers.bq.ProfileList;
import com.fansunited.automation.mappers.EventMapper;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.google.cloud.bigquery.BigQuery;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EventDataPump extends BaseDataPump {

  private static final Logger LOG = LoggerFactory.getLogger(EventDataPump.class);
  public static final String PROFILE_ID = "af17e34c-06ee-417c-b18d-25bd8fd71267";
  public static List<String> DUMMY_PROFILES;

  private final BigQuery bigQuery;
  private EventMapper eventMapper = new EventMapper();

  public EventDataPump(BigQuery bigQuery) {
    try {
      DUMMY_PROFILES = ProfileList.getProfiles(bigQuery);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
    this.bigQuery = bigQuery;
  }

  public void addSingleGamePrediction(int iterationCount, String action, Long points,
      String property,
      String category, String source,String profileId) throws Exception {
    final List<SampleEvent> events = new ArrayList<>();
    for (int i = 0; i < iterationCount; i++) {
      events.add(buildSampleEvent(GameType.SINGLE.toString(), profileId, action, points, property,
          category, generatePredictionId(), source, null));
    }
    insertJson(events, bigQuery, EVENT_TABLE);
  }

  public void massInsertCompetitions(int numberOfCompetitions, int numberOfMatches, String gameType,
      String action, Long points, String property, String category, String source)
      throws Exception {
    final List<SampleEvent> events = new ArrayList<>();
    for (int i = 0; i < numberOfCompetitions; i++) {
      events.addAll(
          massInsertGamePredictions(numberOfMatches, action, gameType, points, property, category,
              source));
    }
    insertJson(events, bigQuery, EVENT_TABLE);
  }

  public void massInsertEvents(int number, String activity, String context, String category,
      String action) throws Exception {
    final List<SampleEvent> events = new ArrayList<>();
    for (int i = 0; i < number; i++) {
      if (DUMMY_PROFILES == null || DUMMY_PROFILES.isEmpty()) {
        LOG.error("No DUMMY_PROFILES in bigquery. Using random uuids");
        events.add(buildSampleEvent(
            context, UUID.randomUUID().toString(), action,
            generateRandomInt(), activity,
            category, "fyCnS7rPA5d3iyCnlo86bkKL18f33456", "mini-games-api", createSampleData()
        ));
      } else {
        events.add(buildSampleEvent(
            context, DUMMY_PROFILES.get(
                getRandomNumberUsingNextInt(0, DUMMY_PROFILES.size() > 2 ? DUMMY_PROFILES.size() - 1 : 1)
            ), action,
            generateRandomInt(), activity,
            category, "fyCnS7rPA5d3iyCnlo86bkKL18f33456", "mini-games-api", createSampleData()
        ));
      }
    }
    insertJson(events, bigQuery, EVENT_TABLE);
  }

  public void insertSingleProfile(LocalDateTime eventTimestamp,
      RegistrationProfile profile, int points) throws Exception {
    var event = eventMapper.profileToEvent(eventTimestamp, profile, points);
    insertJson(List.of(event), bigQuery, EVENT_TABLE);
  }

  public void insertProfiles(List<RegistrationProfile> profiles) throws Exception {
    var events = profiles.stream().map(p -> eventMapper.profileToEvent(p, 10)).toList();
    insertJson(events, bigQuery, EVENT_TABLE);
  }

  @SneakyThrows public void insertSingleInterest(LocalDateTime eventTimestamp, String profileId,
      ProfileData.Interest interest, boolean isFollow, int points) {
    var event =
        eventMapper.profileDataInterestToEvent(eventTimestamp, profileId, interest, isFollow,
            points);
    insertJson(List.of(event), bigQuery, EVENT_TABLE);
  }

  @SneakyThrows public void insertCorrectPrediction(LocalDateTime eventTimestamp,
      String profileId, String predictionId, PredictionType gameType,
      PredictionMarket predictionMarket,
      int points) {

    var event = eventMapper.mapCorrectPredictionToEvent(eventTimestamp, profileId, predictionId, gameType,
        predictionMarket, points);
    insertJson(List.of(event), bigQuery, EVENT_TABLE);
  }

  @SneakyThrows public void insertPredictionMade(PredictionInstance predictionInstance) {

    var event = eventMapper.predictionMade(predictionInstance);
    insertJson(List.of(event), bigQuery, EVENT_TABLE);
  }

  @SneakyThrows public void insertPredictionMadeForMarket(PredictionInstance predictionInstance, String market) {

    var event = eventMapper.predictionMadeForMarket(predictionInstance, market);
    insertJson(List.of(event), bigQuery, "event");
  }

  private static long generateRandomInt() {
    return new Random().nextLong(2, 15);
  }

  private List<SampleEvent> massInsertGamePredictions(int iterationCount, String action,
      String gameType, Long points,
      String property, String category, String source) {
    /**
     * We should use the same prediction id for every row the batch
     */
    String predictionIdValue = generatePredictionId();
    final List<SampleEvent> events = new ArrayList<>();
    for (int i = 0; i < iterationCount; i++) {
      events.add(buildSampleEvent(gameType, PROFILE_ID, action, points, property, category,
          predictionIdValue, source, null));
    }
    return events;
  }

  public Data createSampleData() {
    final Data data = new Data();
    int tagType = getRandomNumberUsingNextInt(0, 3);
    if (tagType == 0) {
      data.setTags(buildCompetitionTags());
    } else if (tagType == 1) {
      data.setTags(buildFootballTags());
    } else if (tagType == 2) {
      data.setTags(buildPlayerTags());
    }

    return data;
  }

  //, "fb:t:899", "fb:t:892"
  private List<Tag> buildFootballTags() {
    List<String> teams = Arrays.asList("fb:t:8104", "fb:t:892");
    String source = "football";
    String type = "team";
    Tag tag = new Tag();
    tag.setId(teams.get(getRandomNumberUsingNextInt(0, teams.size())));
    tag.setType(type);
    tag.setSource(source);
    Tag tag2 = new Tag();
    tag2.setId(teams.get(getRandomNumberUsingNextInt(0, teams.size())));
    tag2.setType(type);
    tag2.setSource(source);
    List<Tag> tags = Arrays.asList(tag, tag2);
    return tags;
  }
  //"fb:c:119", "fb:c:538",fb:c:27, fb:c:28, fb:c:553, fb:c:30, fb:c:37, fb:c:3, fb:c:5, fb:c:6, fb:c:7, fb:t:8204, fb:t:8205, fb:t:8102, fb:t:892, fb:t:897, fb:t:882, fb:t:8104, fb:t:8245, fb:t:8255, fb:t:8247, fb:t:8233, fb:t:8295, fb:p:43400, fb:p:43399, fb:p:46150, fb:p:4133190, fb:c:8, fb:p:43404, fb:p:44971

  private List<Tag> buildCompetitionTags() {
    List<String> competitions = Arrays.asList("fb:c:3", "fb:c:5", "fb:c:6", "fb:c:7");
    String source = "football";
    String type = "competition";
    Tag tag = new Tag();
    tag.setId(competitions.get(getRandomNumberUsingNextInt(0, competitions.size())));
    tag.setType(type);
    tag.setSource(source);
    Tag tag2 = new Tag();
    tag2.setId(competitions.get(getRandomNumberUsingNextInt(0, competitions.size())));
    tag2.setType(type);
    tag2.setSource(source);
    List<Tag> tags = Arrays.asList(tag);
    return tags;
  }

  //, "fb:p:899"
  private List<Tag> buildPlayerTags() {
    List<String> players = Arrays.asList("fb:p:899", "fb:p:43399");
    String source = "football";
    String type = "player";
    Tag tag = new Tag();
    tag.setId(players.get(getRandomNumberUsingNextInt(0, players.size())));
    tag.setType(type);
    tag.setSource(source);
    Tag tag2 = new Tag();
    tag2.setId(players.get(getRandomNumberUsingNextInt(0, players.size())));
    tag2.setType(type);
    tag2.setSource(source);
    List<Tag> tags = Arrays.asList(tag);
    return tags;
  }

  private SampleEvent buildSampleEvent(String context, String profileId, String action, Long points,
      String property,
      String category, String value, String source, Data data) {
    final SampleEvent event = new SampleEvent();

    event.withExtra("unspecified")
        .withLabel("Adding content")
        .withContext(context)
        .withAction(action)
        .withProperty(property)
        .withCategory(category)
        .withValue(value)
        .withSource(source)
        .withProfileId(profileId)
        .withTimestamp(generateRandomDate().minusDays(4).toString())
        .withEventId(UUID.randomUUID().toString())
        .withPoints(points)
        .withData(data)
        .withWeight(points)
        .withActivity(action);

    return event;
  }

  private LocalDateTime generateRandomDate() {
    return LocalDateTime.now().minusMonths(1);
  }

  private String generatePredictionId() {
    int length = 25;
    boolean useLetters = true;
    boolean useNumbers = true;
    return RandomStringUtils.random(length, useLetters, useNumbers);
  }
}
