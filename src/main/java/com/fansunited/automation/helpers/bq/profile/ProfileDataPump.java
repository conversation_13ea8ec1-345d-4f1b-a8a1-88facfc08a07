package com.fansunited.automation.helpers.bq.profile;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.PROFILE_AVATAR_URL;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.mappers.EventMapper.PROFILE_TABLE;

import com.fansunited.automation.helpers.bq.BaseDataPump;
import com.fansunited.automation.mappers.EventMapper;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.javafaker.Faker;
import com.google.cloud.bigquery.BigQuery;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.SneakyThrows;

@Getter
public class ProfileDataPump extends BaseDataPump {

  private final List<Country> countries;
  private final List<InterestsContainer> interests;
  private final ObjectMapper objectMapper = new ObjectMapper();

  private final BigQuery bigQuery;
  private EventMapper eventMapper = new EventMapper();

  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
  private final Faker faker = new Faker();

  public ProfileDataPump(BigQuery bigQuery) {
    this.bigQuery = bigQuery;
    try {
      countries =
          (List<Country>) objectMapper.readValue(new File("src/test/resources/countries.json"),
              new TypeReference<List<Country>>() {
              });
      interests = objectMapper.readValue(new File("src/test/resources/countries.json"),
          new TypeReference<List<InterestsContainer>>() {
          });
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public void massInsertProfiles(int number) throws Exception {
    final List<Profile> profiles = new ArrayList<>();
    for (int i = 0; i < number; i++) {
      profiles.add(buildSampleProfile());
    }
    insertJson(profiles, bigQuery, PROFILE_TABLE);
  }

  public void massInsertProfiles(LocalDateTime eventTimestamp,
      List<RegistrationProfile> profiles, LocalDateTime profileUpdatedAt) throws Exception {
    var events = profiles.stream().map(p ->
        eventMapper.profileToProfileEvent(eventTimestamp, p, profileUpdatedAt)
    ).collect(Collectors.toList());
    insertJson(events, bigQuery, PROFILE_TABLE);
  }

  public void massInsertProfiles(List<RegistrationProfile> profiles) throws Exception {
    var events = profiles.stream().map(p ->
        eventMapper.profileToProfileEvent(p)
    ).collect(Collectors.toList());
    insertJson(events, bigQuery, PROFILE_TABLE);
  }


  public void insertSingleProfile(LocalDateTime eventTimestamp,
      RegistrationProfile profile, LocalDateTime profileUpdatedAt) throws Exception {
    var event = eventMapper.profileToProfileEvent(eventTimestamp, profile, profileUpdatedAt);
    insertJson(List.of(event), bigQuery, PROFILE_TABLE);
  }

  @SneakyThrows public void insertSingleInterest(LocalDateTime eventTimestamp, String profileId,
      ProfileData.Interest interest) {
    var event =
        eventMapper.profileDataInterestToProfileEvent(eventTimestamp, profileId, interest);
    insertJson(List.of(event), bigQuery, PROFILE_TABLE);
  }

  private Profile buildSampleProfile() {
    final Profile profile = new Profile();
    profile
        .withName(faker.name().name())
        .withEmail(faker.internet().emailAddress())
        .withBirthDate(generateRandomDate(30 * 365).format(formatter))
        .withUpdatedAt(generateRandomDate(1 * 5).toString())
        .withCountry(generateRandomCountry())
        .withCreatedAt(generateRandomDate(1 * 5).toString())
        .withGender(generateRandomGender())
        .withId(UUID.randomUUID().toString())
        .withInterests(generateRandomInterests().getInterests())
        .withAvatar(PROFILE_AVATAR_URL);
    return profile;
  }

  private Profile buildSampleProfile(String userId, String localDateTime) {
    final Profile profile = new Profile();
    profile
        .withName(faker.name().name())
        .withEmail(faker.internet().emailAddress())
        .withBirthDate(generateRandomDate(30 * 365).format(formatter))
        .withUpdatedAt(localDateTime)
        .withCountry(generateRandomCountry())
        .withCreatedAt(localDateTime)
        .withGender(generateRandomGender())
        .withId(userId)
        .withInterests(generateRandomInterests().getInterests())
        .withAvatar(PROFILE_AVATAR_URL);
    return profile;
  }

  private Country generateRandomCountry() {
    return countries.get(new Random().nextInt(countries.size() - 1));
  }

  private InterestsContainer generateRandomInterests() {
    return interests.get(new Random().nextInt(interests.size() - 1));
  }

  public Profile insertProfile() throws Exception {
    Profile profile = buildSampleProfile();
    insertJson(List.of(profile), bigQuery, PROFILE_TABLE);
    return profile;
  }

  public Profile insertProfile(String userId, String localDateTime) throws Exception {
    Profile profile = buildSampleProfile(userId, localDateTime);
    insertJson(List.of(profile), bigQuery, PROFILE_TABLE);
    return profile;
  }
}
