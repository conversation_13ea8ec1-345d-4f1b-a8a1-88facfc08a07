package com.fansunited.automation.helpers.bq.truncate;

import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.QueryJobConfiguration;
import lombok.SneakyThrows;

/**
 * The type Client repository.
 */
public class BQRepository {
  public static final String TABLE_ID = "tableId";
  public static final String DATASET_NAME = "automationtesting";
  public static final String CLIENT_AUTOMATIONTESTING = "automationtesting";

  public static final String REQUIRED = "Required";
  private static final String PARAM_PROFILE_ID = "profile_id";
  private static final String PARAM_FOLLOW = "follow";
  private static final String PARAM_COUNTRY_ID = "country_id";
  private static final String DATE_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'";
  private static final String UPDATED_AT = "updated_at";
  private static final String PREDICTION_ID = "prediction_id";

  private static final BigQuery bigQuery = BigQueryOptions.getDefaultInstance().getService();

  public static void truncateAll() {
    truncateEvents();
    truncateProfiles();
    truncateRankings();
    truncateTemplate();
    truncateDailyActiveUsers();
  }

  public static void truncateEvents() {
    truncate("event");
  }

  public static void truncateTemplate() {
    truncate("template");
  }

  public static void truncateRankings() {
    truncate("rank");
  }

  public static void truncateProfiles() {
    truncate("profile");
  }

  public static void truncateDailyActiveUsers() {
    truncate("daily_active_users");
  }

  @SneakyThrows private static void truncate(String tableName) {
    // Generate the fully-qualified table ID
    String tableId = genTableId(DATASET_NAME, tableName);

    // Properly format the query with the tableId
    String query = String.format("TRUNCATE TABLE %s", tableId);

    // Create the query configuration
    QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(query).build();

    // Execute the query
    bigQuery.query(queryConfig);
  }

  public static String genTableId(String datasetName, String tableName) {
    return "`" + datasetName + "." + tableName + "`";
  }
}
