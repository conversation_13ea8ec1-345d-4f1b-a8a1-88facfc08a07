package com.fansunited.automation.helpers.bq;

import static com.fansunited.automation.helpers.bq.InsertBigQData.TEST_CLIENT_ID_MASS;

import com.google.cloud.bigquery.BigQuery;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

public class BaseDataPump {


  protected int getRandomNumberUsingNextInt(int min, int max) {
    Random random = new Random();
    return random.nextInt(max - min) + min;
  }

  protected void checkIfProjectIsTestingOrDev(BigQuery bigQuery) throws Exception {
    final String projectId = bigQuery.getOptions().getProjectId();
    System.out.println("YOU ARE USING PROJECT WITH AN ID:  "+projectId);
    if(projectId.equalsIgnoreCase("fans-united-dev") ||
        projectId.equalsIgnoreCase("fans-united-testing") ||
        projectId.equalsIgnoreCase("fans-united-stage")){
      return;
    }
    throw new Exception("This script should be run only on testing or dev!!!");
  }

  protected String generateRandomGender(){
    int gender = new Random().nextInt(0,2);
    return gender == 0 ? "male" : "female";
  }

  protected boolean generateTrueFalse(){
    int value = new Random().nextInt(0,2);
    return value == 0 ? true : false;
  }

  protected LocalDateTime generateRandomDate(int maxDaysFromNow){
    return LocalDateTime.now().minusDays(new Random().nextInt(0, maxDaysFromNow));
  }

  protected void insertJson(List<? extends Object> itemsToInsert, BigQuery bigQuery, String tableName) throws Exception {
    checkIfProjectIsTestingOrDev(bigQuery);
    final WritePendingStreamHelper writeHelper = new WritePendingStreamHelper();
    writeHelper.runWritePendingStream(bigQuery.getOptions().getProjectId(), TEST_CLIENT_ID_MASS, tableName,itemsToInsert);
  }
}
