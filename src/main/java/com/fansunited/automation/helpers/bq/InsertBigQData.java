package com.fansunited.automation.helpers.bq;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_PATTERN;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.profileapi.CountriesEndpoint;
import com.fansunited.automation.helpers.PeriodGenerator;
import com.fansunited.automation.helpers.bq.MAU.MAUDataPump;
import com.fansunited.automation.helpers.bq.events.EventDataPump;
import com.fansunited.automation.helpers.bq.excluded.ExcludedDataPump;
import com.fansunited.automation.helpers.bq.profile.Profile;
import com.fansunited.automation.helpers.bq.profile.ProfileDataPump;
import com.fansunited.automation.helpers.bq.rank.RankDataPump;
import com.fansunited.automation.helpers.bq.template.TemplateDataPump;
import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.github.javafaker.Faker;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.QueryJobConfiguration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;

public class InsertBigQData {
  // eager initialization, but it's a test instance, so it seems ok
  private static final BigQuery bigQuery = BigQueryOptions.getDefaultInstance().getService();
  static final EventDataPump eventDataPump = new EventDataPump(bigQuery);
  static final ProfileDataPump profileDataPump = new ProfileDataPump(bigQuery);
  static final RankDataPump rankDataPump = new RankDataPump(bigQuery);
  static final MAUDataPump mauDataPump = new MAUDataPump(bigQuery);
  static final TemplateDataPump templateDataPump = new TemplateDataPump(bigQuery);
  static final ExcludedDataPump excludedDataPump = new ExcludedDataPump(bigQuery);
  static final CountryProfile country = getCountryProfile();

  public static final String TEST_CLIENT_ID_MASS = CLIENT_AUTOMATION_ID;

  /**
   * In order to use this script you need to export GOOGLE_APPLICATION_CREDENTIALS env variable.
   * The variable should point to the location of a BigQuery config file.
   * In Intellij Idea you should choose: Run->Edit Configurations and fill the field named
   * "Environment variables"
   */

  @SneakyThrows public static void insertExcluded(String excludedProfileId){
    excludedDataPump.insertSingleExcludedItem(excludedProfileId);
  }

  @SneakyThrows public static void insertTemplate(List<String>profileIds){
    templateDataPump.insertSingleTemplate(profileIds);
  }

  @SneakyThrows public static Profile insertProfileRow() {
    return profileDataPump.insertProfile();
  }

  @SneakyThrows public static Profile insertProfileRow(String userId, String localDateTime) {
    return profileDataPump.insertProfile(userId,localDateTime);
  }

  @SneakyThrows public static void insertProfiles(int number) {
    profileDataPump.massInsertProfiles(number);
  }

  @SneakyThrows public static void insertEvents(int number, String activity, String context, String category,
      String action) {
    eventDataPump.massInsertEvents(number, activity, context, category, action);
  }

  @SneakyThrows public static void insertRankEvent(int number, PredictionMarket predictionMarket,
      String profileId, int point, String gameId,
      String gameType, boolean rankType) {
    rankDataPump.massInsertRanks(number, predictionMarket.getValue(), profileId, point, gameId,
        gameType, rankType);
  }

  @SneakyThrows public static void insertSingleRankEvent(LocalDateTime eventTimestamp, String profileId,
      PredictionMarket predictionMarket,
      GameType predictionType, int points, String gameInstanceId,
      Match match, Integer goldenGoalMinute, String firstGoalMinute, String predictionLastUpdate) {
    rankDataPump.insertSingleRank(eventTimestamp, profileId,
        predictionMarket,
        predictionType, points, gameInstanceId,
        match, goldenGoalMinute, firstGoalMinute, predictionLastUpdate);
  }

  @SneakyThrows
  public static void insertSinglePredictionEvent(LocalDateTime eventTimestamp, String profileId,
      PredictionMarket predictionMarket,
      PredictionType predictionType,
      //int points, String gameInstanceId,
      Match match
      //Integer goldenGoalMinute, String firstGoalMinute, String predictionLastUpdate
  ) {

    var predictionInstance = PredictionInstance.builder()
        .id(UUID.randomUUID().toString())
        .userId(profileId)
        .gameType(predictionType)
        .fixtures(
            PredictionsEndpoint.generateValidSinglePredictionFixture(
                predictionMarket,
                match.getId(),
                VALID_PLAYER_ID))
        //2024-10-06 11:06:09 UTC
        .createdAt(eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_PATTERN)))
        .updatedAt(eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_PATTERN)))
        .build();

    eventDataPump.insertPredictionMadeForMarket(predictionInstance, predictionMarket.getValue());
  }

  @SneakyThrows public static void insertSingleRankEventWithFinishDate(LocalDateTime eventTimestamp,
      String profileId,
      PredictionMarket predictionMarket,
      GameType predictionType, int points, String gameInstanceId,
      Match match, Integer goldenGoalMinute, String firstGoalMinute, String predictionLastUpdate,
      String finishDate) {
    rankDataPump.insertSingleRankWithFinishDate(eventTimestamp, profileId,
        predictionMarket,
        predictionType, points, gameInstanceId,
        match, goldenGoalMinute, firstGoalMinute, predictionLastUpdate, finishDate);
  }

  @SneakyThrows public static void insertSingleProfile(LocalDateTime eventTimestamp,
      RegistrationProfile profile, int points, LocalDateTime profileUpdatedAt) {
    eventDataPump.insertSingleProfile(eventTimestamp, profile, points);
    profileDataPump.insertSingleProfile(eventTimestamp, profile, profileUpdatedAt);
  }

  public static Map<LocalDate, Long> generateRegistrationEventsForPeriod(
      long eventsPerDateInPeriod,
      LocalDate fromDate,
      LocalDate toDate,
      Periodicity periodicity) {

    if (eventsPerDateInPeriod <= 0) {
      throw new IllegalArgumentException("Events per date in period must be greater than 0");
    }

    // Generate the period based on the periodicity
    var period = PeriodGenerator.genPeriod(fromDate, toDate, periodicity);
    var periodEventsCountMap = new LinkedHashMap<LocalDate, Long>();
    List<RegistrationProfile> allProfiles = new ArrayList<>();

    switch (periodicity) {
      case DAY -> {
        period.forEach(localDate -> {
          var profiles = getProfilesForDate(eventsPerDateInPeriod);
          profiles.forEach(p -> p.setUpdatedAt(convertLocalDateToDateTime(localDate).toString()));
          periodEventsCountMap.put(localDate, eventsPerDateInPeriod);
          allProfiles.addAll(profiles);
        });

        try {
          profileDataPump.massInsertProfiles(allProfiles);
          eventDataPump.insertProfiles(allProfiles);
        } catch (Exception e) {
          throw new RuntimeException("Failed to insert profiles", e);
        }
      }
      case WEEK -> {
        for (var weekStart : period) {
          var weekPeriod =
              PeriodGenerator.genPeriod(weekStart, weekStart.plusDays(6), Periodicity.DAY);
          weekPeriod.forEach(localDate -> addProfilesForDate(allProfiles, eventsPerDateInPeriod));
          periodEventsCountMap.put(weekStart, 7 * eventsPerDateInPeriod);
        }
      }
      case MONTH -> {
        for (var monthStart : period) {
          var monthPeriod = PeriodGenerator.genPeriod(monthStart, monthStart.plusDays(monthStart.lengthOfMonth() - 1), Periodicity.DAY);
          monthPeriod.forEach(localDate -> addProfilesForDate(allProfiles, eventsPerDateInPeriod));
          periodEventsCountMap.put(monthStart, monthStart.lengthOfMonth() * eventsPerDateInPeriod);
        }
      }
      case YEAR -> {
        for (var yearStart : period) {
          var yearPeriod = PeriodGenerator.genPeriod(yearStart, LocalDate.of(yearStart.getYear(), 12, 31), Periodicity.DAY);
          yearPeriod.forEach(localDate -> addProfilesForDate(allProfiles, eventsPerDateInPeriod));
          periodEventsCountMap.put(yearStart, yearStart.lengthOfYear() * eventsPerDateInPeriod);
        }
      }
      default -> throw new IllegalArgumentException("Unsupported periodicity: " + periodicity);
    }

    return periodEventsCountMap;
  }

  private static void addProfilesForDate(List<RegistrationProfile> allProfiles, long eventsPerDate) {
    for (int i = 0; i < eventsPerDate; i++) {
      RegistrationProfile profile = createRegistrationProfile();
      allProfiles.add(profile);
    }
  }

  private static List<RegistrationProfile> getProfilesForDate(long eventsPerDate) {
    var ret = new ArrayList<RegistrationProfile>();
    for (int i = 0; i < eventsPerDate; i++) {
      RegistrationProfile profile = createRegistrationProfile();
      ret.add(profile);
    }
    return ret;
  }

  private static RegistrationProfile createRegistrationProfile() {
    return RegistrationProfile.builder()
        .id(UUID.randomUUID().toString())
        .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
        .gender(ProfileData.Profile.Gender.getRandomGender().getValue())
        .country(country)
        .followersCount(0)
        .followingCount(0)
        .email(new Faker().internet().emailAddress())
        .avatar("http://noavatar.com")
        .build();
  }

  private static CountryProfile getCountryProfile() {
    try {
      return CountriesEndpoint.getRandomCountryDto();
    } catch (Exception e) {
      return CountryProfile.builder()
          .id(ApiConstants.ProfileApi.COUNTRY_ID_BG)
          .name("Bulgaria")
          .assets(new Country.AssetsFlag(
              "https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
          .build();
    }
  }

  /**
   * Generate registration events for the specified period
   *
   * @param eventsPerDate long
   * @param fromDate      LocalDate
   * @param toDate        LocalDate
   * @return Map<String, Long> Number of events generated for every country in the map
   */

  public static Map<String, Long> generateRegistrationEvents(
      long eventsPerDate, LocalDate fromDate, LocalDate toDate) {
    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var countryIdEventCountMap = new HashMap<String, Long>();

    dates.forEach(localDate -> {
      var countryIdEventsCountPair = generateRegistrationEvents(eventsPerDate,
          convertLocalDateToDateTime(localDate));
      var exists = countryIdEventCountMap.get(countryIdEventsCountPair.getLeft()) != null;
      if (!exists) {
        countryIdEventCountMap.putIfAbsent(countryIdEventsCountPair.getLeft(),
            countryIdEventsCountPair.getRight());
      } else {
        var eventsCount = countryIdEventCountMap.get(countryIdEventsCountPair.getLeft());
        countryIdEventCountMap.replace(countryIdEventsCountPair.getLeft(),
            eventsCount + countryIdEventsCountPair.getRight());
      }
    });

    return countryIdEventCountMap;
  }

  public static void generateProfileEvents(List<RegistrationProfile> profileList,
      LocalDateTime createdAt, LocalDateTime updatedAt) {
    for (var profile : profileList) {
      generateProfileEvent(profile, createdAt, updatedAt);
    }
  }

  @SneakyThrows
  public static void generateProfileEvent(RegistrationProfile profile, LocalDateTime createdAt,
      LocalDateTime updatedAt) {
    profileDataPump.insertSingleProfile(createdAt, profile, updatedAt);
  }

  public static void generateFollowEvents(int count, LocalDateTime eventTimestamp,
      ProfileData.Interest interest, boolean isFollow, int points) {
    if (count <= 0) {
      throw new IllegalArgumentException("Follow events count must be greater than 0");
    }
    for (int i = 0; i < count; i++) {
      generateFollowEvent(eventTimestamp, UUID.randomUUID().toString(), interest,
          isFollow, points);
    }
  }

  public static void generateCorrectPredictionEvent(LocalDateTime eventTimestamp,
      String profileId, String predictionId, PredictionType gameType,
      PredictionMarket predictionMarket,
      int points) {
    eventDataPump.insertCorrectPrediction(eventTimestamp, profileId, predictionId, gameType,
        predictionMarket, points);
  }

  public static void generatePredictionMadeEvent(PredictionInstance predictionInstance) {
    eventDataPump.insertPredictionMade(predictionInstance);
  }

  private static void generateFollowEvent(LocalDateTime eventTimestamp, String profileId,
      ProfileData.Interest interest, boolean isFollow, int points) {
    eventDataPump.insertSingleInterest(eventTimestamp, profileId, interest, isFollow, points);
    profileDataPump.insertSingleInterest(eventTimestamp, profileId, interest);
  }

  @SneakyThrows private static Pair<String, Long> generateRegistrationEvents(long count,
      LocalDateTime eventTimestamp) {
    if (count < 0) {
      throw new IllegalArgumentException("Events count must be greater than 0");
    }

    CountryProfile country;
    try {
      country = CountriesEndpoint.getRandomCountryDto();
    } catch (Exception e) {
      country = CountryProfile.builder()
          .id(ApiConstants.ProfileApi.COUNTRY_ID_BG)
          .name("Bulgaria")
          .assets(new Country.AssetsFlag(
              "https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
          .build();
    }
    ArrayList<RegistrationProfile> profiles = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      var profile = RegistrationProfile.builder()
          .id(UUID.randomUUID().toString())
          .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
          .gender(ProfileData.Profile.Gender.getRandomGender().getValue())
          .country(country)
          .followersCount(0)
          .followingCount(0)
          .email(new Faker().internet().emailAddress())
          .avatar("http://noavatar.com").build();
      profiles.add(profile);
    }
    profileDataPump.massInsertProfiles(eventTimestamp, profiles, null);

    return Pair.of(country.getId(), count);
  }

  public static void insertMauEvent(int number) throws Exception {
    mauDataPump.massInsertMAU(number);
  }

  public static void insertMauEventWithDate(int number) throws Exception {
    mauDataPump.massInsertMAUWithDate(number);
  }

  public static void runSQLScript() {

    String sql =
        """
    CREATE OR REPLACE TABLE `fans-united-stage.automationtesting.user_ranking` AS (
      -- Template-based ranking
      (
        SELECT
          RANK() OVER (PARTITION BY template.id ORDER BY SUM(r.points) DESC) AS position,
          SUM(r.points) AS points,
          r.profile_id,
          template.id AS id,
          "template" AS rank_type,
          MAX(r.date_finished) AS date_finished
        FROM
          `fans-united-stage.automationtesting.rank` AS r
        CROSS JOIN (
          SELECT
            c.competition_ids,
            c.game_ids,
            c.markets,
            c.match_ids,
            c.team_ids,
            c.to_date,
            c.from_date,
            c.game_types,
            c.id,
            c.name
          FROM
            `fans-united-stage.automationtesting.template` AS c
        ) AS template
        WHERE
          DATE(r.date_finished) BETWEEN DATE(template.from_date) AND DATE(template.to_date)
          AND (
            template.markets IS NULL
            OR r.market IN UNNEST(SPLIT(template.markets, ","))
          )
          AND (
            template.match_ids IS NULL
            OR r.match_id IN UNNEST(SPLIT(template.match_ids, ","))
          )
          AND (
            template.team_ids IS NULL
            OR r.home_team_id IN UNNEST(SPLIT(template.team_ids, ","))
            OR r.away_team_id IN UNNEST(SPLIT(template.team_ids, ","))
          )
          AND (
            template.game_ids IS NULL
            OR r.game_id IN UNNEST(SPLIT(template.game_ids, ","))
          )
          AND (
            template.game_types IS NULL
            OR template.game_types = ""
            OR r.game_type IN UNNEST(SPLIT(template.game_types, ","))
          )
          AND (
            template.competition_ids IS NULL
            OR r.competition_id IN UNNEST(SPLIT(template.competition_ids, ","))
          )
        GROUP BY
          template.id,
          r.profile_id
        ORDER BY
          template.id DESC
      )

      UNION ALL

      -- Game-based ranking
      (
        SELECT
          RANK() OVER (PARTITION BY r2.game_id ORDER BY SUM(r2.points) DESC) AS position,
          SUM(r2.points) AS points,
          r2.profile_id,
          r2.game_id AS id,
          "game" AS rank_type,
          MAX(r2.date_finished) AS date_finished
        FROM
          `fans-united-stage.automationtesting.rank` AS r2
        WHERE
          r2.game_id IS NOT NULL
          AND r2.game_id != ""
        GROUP BY
          r2.game_id,
          r2.profile_id
      )

      ORDER BY
        rank_type,
        id
    );
    """;

    QueryJobConfiguration job = QueryJobConfiguration.newBuilder(sql).build();
    try {
      bigQuery.query(job);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
  }
}
