package com.fansunited.automation.helpers.bq.MAU;

import com.fansunited.automation.helpers.bq.BaseDataPump;
import com.fansunited.automation.helpers.bq.ProfileList;
import com.fansunited.automation.helpers.bq.events.EventDataPump;
import com.google.cloud.bigquery.BigQuery;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MAUDataPump extends BaseDataPump {

  private static final Logger LOG = LoggerFactory.getLogger(MAUDataPump.class);
  public static List<String> DUMMY_PROFILES;

  private final BigQuery bigQuery;

  List<String> sources = Arrays.asList("loyalty-api", "prediction-games-api", "profile-api");

  public MAUDataPump(BigQuery bigQuery) {
    try {
      DUMMY_PROFILES = ProfileList.getProfiles(bigQuery);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
    this.bigQuery = bigQuery;
  }

  public void massInsertMAU(int number) throws Exception {
    final List<MAUItem> dailyActiveUsers = new ArrayList<>();
    {

      String source = sources.get(getRandomNumberUsingNextInt(0, sources.size() - 1));

      for (int j = 0; j < number; j++) {
        if (DUMMY_PROFILES == null || DUMMY_PROFILES.isEmpty()) {
          LOG.error("No DUMMY_PROFILES in bigquery. Using random uuids");
          dailyActiveUsers.add(buildSampleMauData(source, UUID.randomUUID().toString()));
        } else {
          dailyActiveUsers.add(buildSampleMauData(source,
              DUMMY_PROFILES.get(getRandomNumberUsingNextInt(0, DUMMY_PROFILES.size() - 1))));
        }
      }
    }
    insertJson(dailyActiveUsers, bigQuery, "daily_active_users");
  }

  public void massInsertMAUWithDate(int number) throws Exception {
    List<MAUItem> dailyActiveUsers = new ArrayList<>();
    LocalDateTime currentDate = LocalDateTime.now();

    String source = sources.get(getRandomNumberUsingNextInt(0, sources.size() - 1));

    for (int j = 0; j < number; j++) {
      LocalDateTime dateForEntry = currentDate.minusMonths(j);

      if (DUMMY_PROFILES == null || DUMMY_PROFILES.isEmpty()) {
        LOG.error("No DUMMY_PROFILES in bigquery. Using random uuids");
        dailyActiveUsers.add(
            buildSampleMauDataWithDate(source, UUID.randomUUID().toString(), dateForEntry));
      } else {
        dailyActiveUsers.add(buildSampleMauData(source,
            DUMMY_PROFILES.get(
                getRandomNumberUsingNextInt(0, DUMMY_PROFILES.size() > 2 ? DUMMY_PROFILES.size() - 1 : 1)
            )));
      }
    }

    insertJson(dailyActiveUsers, bigQuery, "daily_active_users");
  }

  private MAUItem buildSampleMauData(String source,String profileId) {
    MAUItem daily_active_users = new MAUItem();
    daily_active_users
        .withEventId(UUID.randomUUID().toString())
        .withTimestamp(generateRandomDate(360).toString())
        .withProfileId(profileId)
        .withSourse(source)
        .withAction("USER_ACTIVE");

    return daily_active_users;
  }

  private MAUItem buildSampleMauDataWithDate(String source,String profileId, LocalDateTime date) {
    MAUItem daily_active_users = new MAUItem();
    daily_active_users
        .withEventId(UUID.randomUUID().toString())
        .withTimestamp(date.toString())
        .withProfileId(profileId)
        .withSourse(source)
        .withAction("USER_ACTIVE");

    return daily_active_users;
  }
}
