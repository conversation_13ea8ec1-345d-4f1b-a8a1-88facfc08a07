package com.fansunited.automation.helpers.bq.excluded;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonPropertyOrder({
  "game_id",
  "excluded_profile_ids",
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleExcluded {
  @JsonProperty("game_id")
  private String gameId;

  @JsonProperty("excluded_profile_ids")
  private String excludedProfileIds;
}
