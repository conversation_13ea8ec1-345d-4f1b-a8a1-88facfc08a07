package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.PLATFORM_OPERATOR;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedProfileProject.SERVICE_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedProfileProject.TERRAFORM_USER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;

import com.auth0.jwt.JWT;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.profileapi.ProfileByIdEndpoint;
import com.fansunited.automation.model.firebase.FireBaseCredentials;
import com.fansunited.automation.model.firebase.FireBaseTokenInfo;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.google.api.core.ApiFuture;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.firestore.WriteResult;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.ErrorInfo;
import com.google.firebase.auth.ExportedUserRecord;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.cloud.FirestoreClient;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import lombok.Getter;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FirebaseHelper {

  private static final Logger LOG = LoggerFactory.getLogger(FirebaseHelper.class);

  private static final Map<FansUnitedProject, FirebaseApp> firebaseAppMap = new HashMap<>();

  @Getter
  private static final Map<String, String> userTokenMap = new ConcurrentHashMap<>();

  private static final Map<String, Object> locks = new ConcurrentHashMap<>();

  public static final String WINNERS_COLLECTION = "fu_winners";
  public static final String POLLS_COLLECTION = "fu_poll";
  public static final String BAN_COLLECTION = "fu_ban";
  public static final String ENGAGEMENT_STATE_COLLECTION = "fu_engagement_state";
  public static final String POTM_COLLECTION = "fu_potm";
  public static final String MATCH_COLLECTION = "fu_match_vote";
  public static final String POTM_USER_VOTE_COLLECTION = "fu_potm_user_vote";
  public static final String TOP_X_COLLECTION = "top_x";
  public static final String PREDICTOR_COLLECTION = "predictor";
  public static final String LEAGUES_COLLECTION = "fu_league";
  public static final String MEMBERS_COLLECTION = "fu_members";
  public static final String DISCUSSIONS_COLLECTION = "fu_discussion";
  public static final String PROFILE_COLLECTION = "fu_profile";
  public static final String LEAD_COLLECTION = "fu_lead";
  public static final String POLL_VOTE_COLLECTION = "fu_poll_vote";
  public static final String LEAGUE_USER_COLLECTION = "fu_league_user";
  public static final String ARCHIVE_LEADERBOARD_COLLECTION = "fu_archive_leaderboard";
  public static final String TEMPLATE_LEADERBOARD_COLLECTION = "fu_template_leaderboard";
  public static final String CLASSIC_QUIZ_PARTICIPATION_COLLECTION = "fu_classic_quiz_participation";
  public static final String GAME_COLLECTION = "fu_game";
  public static final String PREDICTION_COLLECTION = "fu_prediction";
  public static final String CLIENT_COLLECTION = "fu_client";
  public static final String STAFF_COLLECTION = "fu_staffmember";
  public static final String CLASSIC_QUIZ_COLLECTION = "fu_classic_quiz";
  public static final String EITHER_OR_COLLECTION="fu_either_or";
  public static final String PARTICIPATION_COLLECTION="fu_participation";
  public static final String USERDATA_COLLECTION="fu_userdata";
  public static final String POST_COLLECTION="fu_post";
  public static final String REWARDS = "fu_rewards";
  public static final String CONFIG = "fu_config";
  public static final String CACHE_CONFIG = "cache";
  public static final String FOOTBALL_CONFIG = "footballdata_config";
  public static final String FOLLOWING = "fu_following";
  public static final String FOLLOWERS = "fu_follower";
  public static final String STATS = "fu_profile_stats";
  public static final String LISTS = "fu_content_lists";
  public static final String CUSTOM_BRACKET_GAME = "fu_custom_bracket";
  public static final String CUSTOM_BRACKET_GAME_PREDICTION = "fu_custom_bracket_prediction";
public static final String CUSTOM_STANDING = "fu_custom_standing";
public static final String CUSTOM_STANDING_PREDICTION = "fu_custom_standing_prediction";
  public static final int FIREBASE_DELETE_BATCH_LIMIT = 10000;
  private static final List<String> CLIENTS =
      List.of("stage", "productiontesting1", "productiontesting2",
          "automationtesting", "stage_apifootball", "stage_sportradar");

  public static String generateAuthToken(FansUnitedProject project, String email, String password)
      throws HttpException {
    FireBaseCredentials fireBaseCredentials;
    String fireBaseKey;
    String identityToolkitUrl;
    switch (project) {
      case FANS_UNITED_CLIENTS -> {
        fireBaseCredentials = new FireBaseCredentials(email,
            password, true);
        fireBaseKey = ConfigReader.getInstance().getProperty(
            ConfigReader.PropertyKey.FIREBASE_WEB_API_KEY_CLIENTS_PROJECT);
        identityToolkitUrl = ConfigReader.getInstance()
            .getProperty(ConfigReader.PropertyKey.IDENTITY_TOOLKIT_CLIENT_URL);
      }
      case FANS_UNITED_PROFILE -> {
        fireBaseCredentials = new FireBaseCredentials(email,
            password, true);
        fireBaseKey = ConfigReader.getInstance().getProperty(
            ConfigReader.PropertyKey.FIREBASE_WEB_API_KEY_PROFILE_PROJECT);
        identityToolkitUrl = ConfigReader.getInstance()
            .getProperty(ConfigReader.PropertyKey.IDENTITY_TOOLKIT_PROFILE_URL);
      }
      default -> throw new IllegalArgumentException(
          project + " has no logic implemented for generating token");
    }

    synchronized (locks.computeIfAbsent(email, k -> new Object())) {
      if (shouldGenerateAuthToken(email)) {

        Response response = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam(QUERY_PARAM_KEY, fireBaseKey)
            .body(fireBaseCredentials)
            .urlEncodingEnabled(false)
            .when()
            .post(identityToolkitUrl)
            .then()
            .extract()
            .response();

        if (response.getStatusCode() == HttpStatus.SC_BAD_REQUEST) {
          throw new HttpException(
              "Could NOT generate auth token for user: "
                  + fireBaseCredentials.getEmail()
                  + ", reason: "
                  + response.getBody().jsonPath().getString("error.message"));
        }

        var token = response.getBody().as(FireBaseTokenInfo.class).getIdToken();

        userTokenMap.put(email, token);

        return token;
      }
    }
    return userTokenMap.get(email);
  }

  private static synchronized FirebaseApp initializeFirebaseApp(FansUnitedProject project)
      throws IOException {
    switch (project) {
      case FANS_UNITED_PROFILE -> {
        if (firebaseAppMap.get(project) == null) {
          FirebaseApp app;
          if (ConfigReader.getInstance()
              .getProperty(ConfigReader.PropertyKey.USE_FIREBASE_EMULATOR)
              .equals("true")) {
            app = getEmulatorFirebaseApp();
          } else {
            app = initRealFirebaseApp();
          }
          firebaseAppMap.put(project, app);
        }
      }
      case FANS_UNITED_CLIENTS -> {

        //TODO add when you have serviceAccountKey.json file for client project
      }
      default -> throw new IllegalArgumentException(
          project + " has no logic implemented for initializing firebase");
    }
    return firebaseAppMap.get(project);
  }

  private static FirebaseApp initRealFirebaseApp() throws IOException {
    var serviceAccount =
        new FileInputStream(System.getProperty("user.dir")
            + ConfigReader.getInstance()
            .getProperty(
                ConfigReader.PropertyKey.FIREBASE_PATH_TO_SERVICE_ACCOUNT_PROFILE_PROJECT));

    var options = FirebaseOptions.builder()
        .setCredentials(GoogleCredentials.fromStream(serviceAccount))
        .build();
    return FirebaseApp.initializeApp(options);
  }

  private static FirebaseApp getEmulatorFirebaseApp() {
    var credentials = GoogleCredentials.create(new AccessToken(
        "owner",
        Date.from(LocalDateTime.now().plusYears(1).atZone(ZoneId.systemDefault()).toInstant())));
    var options = FirebaseOptions.builder()
        .setCredentials(credentials)
        .build();
    return FirebaseApp.initializeApp(options);
  }

  public static FirebaseAuth getFireBaseAuthInstance(FansUnitedProject project)
      throws IOException {
    return FirebaseAuth.getInstance(initializeFirebaseApp(project));
  }

  public static void cleanUpFirebase(FansUnitedProject project) {
    try {
      deleteFirebaseCollection(getFirestoreCollection(project, BAN_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, ENGAGEMENT_STATE_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, WINNERS_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, POTM_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, POLLS_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, POLL_VOTE_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, TOP_X_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, PREDICTOR_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, LEAD_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, LEAGUES_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, LEAGUE_USER_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, GAME_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollection(getFirestoreCollection(project, ARCHIVE_LEADERBOARD_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, CLIENT_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, STAFF_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, CONFIG), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, CACHE_CONFIG), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, FOOTBALL_CONFIG), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, REWARDS), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreCollection(project, STATS), FIREBASE_DELETE_BATCH_LIMIT);
      deleteFirebaseCollectionClients(getFirestoreSubCollectionByClientId(CLIENT_AUTOMATION_ID, project, LISTS), FIREBASE_DELETE_BATCH_LIMIT);
     // deleteFirebaseCollection(getFirestoreCollection(project,CUSTOM_BRACKET_GAME ),FIREBASE_DELETE_BATCH_LIMIT);
     // deleteFirebaseCollection(getFirestoreCollection(project,CUSTOM_BRACKET_GAME_PREDICTION ),FIREBASE_DELETE_BATCH_LIMIT);
     // deleteFirebaseCollection(getFirestoreCollection(project,CUSTOM_STANDING ),FIREBASE_DELETE_BATCH_LIMIT);
//deleteFirebaseCollection(getFirestoreCollection(project,CUSTOM_STANDING_PREDICTION ),FIREBASE_DELETE_BATCH_LIMIT);
      deleteCollectionWithSubCollectionData(CLASSIC_QUIZ_COLLECTION, CLASSIC_QUIZ_PARTICIPATION_COLLECTION);
      deleteCollectionWithSubCollectionData(EITHER_OR_COLLECTION, PARTICIPATION_COLLECTION);
      deleteCollectionWithSubCollectionData(LEAGUES_COLLECTION, POST_COLLECTION);
      deleteCollectionWithSubCollectionData(DISCUSSIONS_COLLECTION, POST_COLLECTION);
      deleteCollectionWithSubCollectionData(POTM_USER_VOTE_COLLECTION, MATCH_COLLECTION);
      deleteCollectionWithSubCollectionData(LEAGUES_COLLECTION, MEMBERS_COLLECTION);
      deleteCollectionWithSubCollectionData(PREDICTION_COLLECTION, USERDATA_COLLECTION);
      deleteCollectionWithSubCollectionData(ARCHIVE_LEADERBOARD_COLLECTION, TEMPLATE_LEADERBOARD_COLLECTION);
      //deleteCollectionWithSubCollectionData(PROFILE_COLLECTION, FOLLOWERS);
      //deleteCollectionWithSubCollectionData(PROFILE_COLLECTION, FOLLOWING);
      //deleteFirebaseCollection(getFirestoreCollection(project, PROFILE_COLLECTION), FIREBASE_DELETE_BATCH_LIMIT);
      //deleteAllFirebaseUsers(project);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private static void deleteAllFirebaseUsers(FansUnitedProject project)
      throws FirebaseAuthException, IOException, InterruptedException, ExecutionException {

    var firebaseAuth = getFireBaseAuthInstance(project);
    final var uidList = new ArrayList<String>();
    var page = firebaseAuth.listUsers(null);

    while (page != null && page.getValues().iterator().hasNext()) {
      for (ExportedUserRecord user : page.getValues()) {

        // Skip deleting test or service users
        if ((user.getEmail() != null && user.getEmail().equals(PLATFORM_OPERATOR)) ||
            (user.getEmail() != null && user.getEmail().equals(ADMIN_USER)) ||
            (user.getEmail() != null && user.getEmail().equals(BILLING_USER)) ||
            (user.getEmail() != null && user.getEmail().equals(SERVICE_USER)) ||
            (user.getEmail() != null && user.getEmail().equals(TERRAFORM_USER))) {
          continue;
        }

        uidList.add(user.getUid());
      }

      if (!uidList.isEmpty()) {
        var result = firebaseAuth.deleteUsersAsync(uidList).get();
        LOG.debug("Successfully deleted " + result.getSuccessCount() + " users");

        if (result.getFailureCount() > 0) {
          LOG.error("Failed to delete " + result.getFailureCount() + " users");
          for (ErrorInfo error : result.getErrors()) {
            LOG.error("error #" + error.getIndex() + ", reason: " + error.getReason());
          }
        }
      }
      uidList.clear();
      page = page.getNextPage();
    }
  }

  public static void deleteFirebaseCollectionClients(
      CollectionReference collectionReference,
      int batchSize) {
    try {
      // retrieve a small batch of documents to avoid out-of-memory errors
      ApiFuture<QuerySnapshot> future = collectionReference.limit(batchSize).get();
      int deleted = 0;
      // future.get() blocks on document retrieval
      List<QueryDocumentSnapshot> documents = future.get().getDocuments();
      for (QueryDocumentSnapshot document : documents) {
        if (CLIENTS.contains(document.getData().get("id"))) {
          continue;
        }

        document.getReference().delete();
        ++deleted;
      }
      if (deleted >= batchSize) {
        // retrieve and delete another batch
        deleteFirebaseCollection(collectionReference, batchSize);
      }
    } catch (Exception e) {
      LOG.error("Error deleting collection : " + e.getMessage());
    }
  }

  public static void deleteFirebaseCollection(
      CollectionReference collectionReference,
      int batchSize) {
    try {
      // retrieve a small batch of documents to avoid out-of-memory errors
      ApiFuture<QuerySnapshot> future = collectionReference.limit(batchSize).get();
      int deleted = 0;
      // future.get() blocks on document retrieval
      List<QueryDocumentSnapshot> documents = future.get().getDocuments();
      for (QueryDocumentSnapshot document : documents) {
        document.getReference().delete();
        ++deleted;
      }
      if (deleted >= batchSize) {
        // retrieve and delete another batch
        deleteFirebaseCollection(collectionReference, batchSize);
      }
    } catch (Exception e) {
      LOG.error("Error deleting collection : " + e.getMessage());
    }
  }

  public static CollectionReference getFirestoreCollection(FansUnitedProject project,
      String collectionName) throws IOException {
    if (firebaseAppMap.get(project) == null) {
      initializeFirebaseApp(project);
    }
    return FirestoreClient.getFirestore(firebaseAppMap.get(project)).collection(collectionName);
  }
  public static CollectionReference getFirestoreSubCollectionByClientId(String clientId,
          FansUnitedProject project, String subCollectionName) throws IOException {
    if (firebaseAppMap.get(project) == null) {
      initializeFirebaseApp(project);
    }
    subCollectionName = CLIENT_COLLECTION + "/" + clientId + "/" + subCollectionName;
    return FirestoreClient.getFirestore(firebaseAppMap.get(project)).collection(subCollectionName);
  }

  public static void updateCollectionField(final CollectionReference collectionReference,
      final String document, final String field, final String value)
      throws ExecutionException, InterruptedException {
    DocumentReference docRef = collectionReference.document(document);

    // Verify document existence
    if (!docRef.get().get().exists()) {
      LOG.warn("Document with ID '" + document + "' does not exist.");
      return;
    }

    // Proceed with update
    try {
      ApiFuture<WriteResult> writeResult = docRef.update(field, value);
      WriteResult result = writeResult.get();
      LOG.info("Field '" + field + "' updated successfully at: " + result.getUpdateTime() + ", val: " + value + " document: " + document);
    } catch (Exception e) {
      LOG.error("Failed to update field '" + field + "' on document '" + document + "': " + e.getMessage());
      throw e;
    }
  }

  public static void updateCollectionTimestampField(final CollectionReference collectionReference,
      final String document, final String field, final Timestamp value)
      throws ExecutionException, InterruptedException {
    DocumentReference docRef = collectionReference.document(document);

    // Verify if the document exists
    if (!docRef.get().get().exists()) {
      LOG.warn("Document with ID " + document + " does not exist.");
      return;
    }

    // Proceed with the update
    try {
      ApiFuture<WriteResult> writeResult = docRef.update(field, value);
      WriteResult result = writeResult.get();
      LOG.info("Field '" + field + "' updated successfully at: " + result.getUpdateTime() + ", val: " + value);
    } catch (Exception e) {
      LOG.error("Failed to update document: " + e.getMessage());
      throw e;
    }
  }

  public enum FansUnitedProject {
    FANS_UNITED_PROFILE,
    FANS_UNITED_CLIENT, FANS_UNITED_CLIENTS
  }

  private static boolean shouldGenerateAuthToken(String email) {
    return userTokenMap.get(email) == null || JWT.decode(userTokenMap.get(email))
        .getExpiresAt()
        .before(new Date(System.currentTimeMillis() + 720 * 1000));
  }

  /**
   * This method send GET gameById request in periods and checks if the game status is updated
   * to the required one. If the status has not been updated until the required wait time
   * Runtime exception is thrown
   *
   * @param gameId                 the id of the game which status will be checked
   * @param expectedGameStatus     the status of the game that is expected
   * @param checkEveryMilliseconds the period after which the check will be performed again
   * @param waitUntil              the overall wait time. After this time, the exception will be thrown
   * @throws HttpException, InterruptedException
   */
  public static void waitForGameStatusToUpdate(String gameId, String expectedGameStatus,
      int checkEveryMilliseconds,
      int waitUntil) throws HttpException, InterruptedException {

    String gameStatus;

    boolean isStatusUpdated = false;

    int waitedForSeconds = 0;

    while (!isStatusUpdated) {
      gameStatus = GameEndpoint.getGameById(gameId).as(GameInstance.class).getStatus();

      if (gameStatus.equals(expectedGameStatus)) {
        isStatusUpdated = true;
      } else {
        Thread.sleep(checkEveryMilliseconds);
        waitedForSeconds = waitedForSeconds + (checkEveryMilliseconds / 1000);
      }

      if (!isStatusUpdated) {
        if (waitedForSeconds >= waitUntil) {
          throw new RuntimeException(
              String.format(
                  "Status did not updated within the required period. Waited for %s seconds, gameId: %s",
                  waitedForSeconds, gameId));
        }
      }
    }
  }

  /**
   * This method send GET predictionById request in periods and checks if the prediction's fixture outcome is updated
   * to the required one. If the status has not been updated until the required wait time
   * Runtime exception is thrown
   *
   * @param expectedPredictionFixtureOutcome the status of the game that is expected
   * @param fixtureIndexInGame               the index of the fixture that is being tracked
   * @param checkEveryMilliseconds           the period after which the check will be performed again
   * @param waitUntil                        the overall wait time. After this time, the exception will be thrown
   * @throws HttpException, InterruptedException
   */
  public static void waitForGamePredictionFixtureOutcomeAndStatusToUpdate(String userEmail,
      String expectedPredictionFixtureOutcome, int fixtureIndexInGame,
      int checkEveryMilliseconds, int waitUntil) throws HttpException, InterruptedException {

    String predictionFixtureOutcome;
    String predictionFixtureStatus;

    boolean isOutcomeUpdated = false;

    int waitedForSeconds = 0;

    while (!isOutcomeUpdated) {
      Response userPredictions = PredictionsEndpoint.getOwnPredictionsForUser(userEmail);

      int dataSize = userPredictions.then().extract().body().jsonPath().getList("data").size();

      // Check if the fixture outcome is updated
      for (int i = 0; i < dataSize; i++) {
        predictionFixtureOutcome = userPredictions
            .as(PredictionsData.class)
            .getData().get(i)
            .getFixtures()
            .get(fixtureIndexInGame)
            .getResult()
            .getOutcome()
            .getValue();

        // Check if the fixture status is updated
        predictionFixtureStatus = userPredictions
            .as(PredictionsData.class)
            .getData().get(i)
            .getFixtures()
            .get(fixtureIndexInGame)
            .getResult()
            .getStatus()
            .name();

        if (predictionFixtureOutcome.equals(expectedPredictionFixtureOutcome) ||
            predictionFixtureStatus.equals(expectedPredictionFixtureOutcome)) {
          isOutcomeUpdated = true;
          break;
        } else {
          Thread.sleep(checkEveryMilliseconds);
          waitedForSeconds = waitedForSeconds + (checkEveryMilliseconds / 1000);
        }
        if (waitedForSeconds >= waitUntil) {
          throw new RuntimeException(
              String.format(
                  "Status did not updated within the required period. Waited for %s seconds",
                  waitedForSeconds));
        }
      }
    }
  }

  public static Map<String, Object> getEntityFromFirebaseCollection(FansUnitedProject project, String collectionName, String id,
      String idFieldName)
      throws InterruptedException, ExecutionException, IOException {

    // Initialize Firebase App if not already initialized
    if (firebaseAppMap.get(project) == null) {
      initializeFirebaseApp(project);
    }

    // Get Firestore collection
    var firestoreCollection = FirestoreClient.getFirestore(firebaseAppMap.get(project)).collection(collectionName);

    // Query the collection for the document with the given id
    List<QueryDocumentSnapshot> documents = firestoreCollection
        .whereEqualTo(idFieldName, id)
        .limit(1) // Limit to 1 result since IDs are unique
        .get()
        .get()
        .getDocuments();

    // Handle case where no document is found
    if (documents.isEmpty()) {
      throw new IllegalArgumentException("No document found with id: " + id);
    }

    // Retrieve and return the "id" field from the first (and only) matching document
    return documents.get(0).getData();
  }

  public static boolean isEntityIdFromArchiveCollection(CollectionReference firestoreCollection,
      String id)
      throws InterruptedException, ExecutionException {
    List<QueryDocumentSnapshot> documents =
        firestoreCollection.limit(10000).get().get().getDocuments();
    return documents.stream().anyMatch(d -> d.getData().get("id").toString().equals(id));
  }

  public static List<String> getAllPollsFromFirebase() throws IOException {
    return getAllCollectionDocumentsFromFirebase(POLLS_COLLECTION);
  }

  public static void deleteDocument(FansUnitedProject project, String collectionName, String documentId)
      throws IOException, InterruptedException, ExecutionException {
    // Get the Firestore collection
    CollectionReference collectionReference = getFirestoreCollection(project, collectionName);

    // Get a reference to the specific document
    DocumentReference documentReference = collectionReference.document(documentId);

    // Validate if the document exists
    if (!documentReference.get().get().exists()) {
      LOG.warn("Document with ID " + documentId + " does not exist in collection " + collectionName);
      return;
    }

    // Delete the document
    WriteResult writeResult = documentReference.delete().get();

    // Log success or failure
    if (writeResult != null) {
      LOG.info("Document with ID "
          + documentId
          + " deleted successfully at "
          + writeResult.getUpdateTime());
    } else {
      LOG.error("Failed to delete document with ID " + documentId);
    }
  }

  public static void deleteLoyaltyDocument(FansUnitedProject project)
      throws IOException, InterruptedException, ExecutionException {
    // Define the collection name and document ID
    String collectionName = "fu_client/automationtesting/fu_feature";
    String documentId = "loyalty";

    // Get the Firestore collection
    CollectionReference collectionReference = getFirestoreCollection(project, collectionName);

    // Get a reference to the specific document
    DocumentReference documentReference = collectionReference.document(documentId);

    // Validate if the document exists
    if (!documentReference.get().get().exists()) {
      LOG.warn("Document with ID " + documentId + " does not exist in collection " + collectionName);
      return;
    }

    // Delete the document
    WriteResult writeResult = documentReference.delete().get();

    // Log success or failure
    if (writeResult != null) {
      LOG.info("Document with ID "
          + documentId
          + " deleted successfully at "
          + writeResult.getUpdateTime());
    } else {
      LOG.error("Failed to delete document with ID " + documentId);
    }
  }

  /**
   * Deletes a collection and its nested sub-collections recursively.
   *
   * @param collectionName The name of the parent collection to delete
   * @param subCollectionName The name of the first-level sub-collection
   */
  public static void deleteCollectionWithSubCollectionData(
      String collectionName, String subCollectionName) {
    try {
      var ids = getAllCollectionDocumentsFromFirebase(collectionName);

      ids.parallelStream()
          .forEach(
              id -> {
                String path = String.format("%s/%s/%s", collectionName, id, subCollectionName);
                try {
                  deleteFirebaseCollection(
                      getFirestoreCollection(FansUnitedProject.FANS_UNITED_PROFILE, path),
                      FIREBASE_DELETE_BATCH_LIMIT);

                } catch (IOException e) {
                  throw new RuntimeException(e);
                }
              });

      // If sub-collection exists, parent collection must be deleted separately
      deleteFirebaseCollection(
          getFirestoreCollection(FansUnitedProject.FANS_UNITED_PROFILE, collectionName),
          FIREBASE_DELETE_BATCH_LIMIT);

    } catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }

  /**
   * Retrieves all document IDs from a specified Firestore collection.
   *
   * @param collectionName The name of the collection to retrieve documents from
   * @return List<String> A list containing all document IDs in the collection
   * @throws IOException If there is an error accessing the Firestore collection
   */
  public static List<String> getAllCollectionDocumentsFromFirebase(String collectionName)
      throws IOException {
    CollectionReference collectionReference =
        getFirestoreCollection(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, collectionName);
    List<String> ids = new ArrayList<>();
    collectionReference
        .listDocuments()
        .forEach(documentReference -> ids.add(documentReference.getId()));
    return ids;
  }

/**
 * Waits for a deleted profile ID to appear in the Firestore.
 * Polls the profile endpoint until a profile with the expected ID is found
 * or until the maximum wait time is exceeded.

 * @param expectedUserID The expected user ID (typically "deleted_[hash]")
 * @param checkEveryMilliseconds Polling interval in milliseconds
 * @param waitUntil Maximum wait time in seconds
 * @throws HttpException On HTTP request failure
 * @throws InterruptedException If thread sleep is interrupted
 * @throws RuntimeException If profile ID not found within timeout
 **/
  public static void waitForDeletedProfileIdToBeCreated(
      String expectedUserID, int checkEveryMilliseconds, int waitUntil)
      throws HttpException, InterruptedException {

    String userId;

    boolean isUserDeletedIdCreated = false;

    int waitedForSeconds = 0;

    while (!isUserDeletedIdCreated) {
      userId = ProfileByIdEndpoint.getProfileById(expectedUserID).jsonPath().getString("data.id");

      if (userId!= null && userId.equals(expectedUserID)) {
        isUserDeletedIdCreated = true;
      } else {
        Thread.sleep(checkEveryMilliseconds);
        waitedForSeconds = waitedForSeconds + (checkEveryMilliseconds / 1000);
      }

      if (!isUserDeletedIdCreated && waitedForSeconds >= waitUntil) {
          throw new RuntimeException(
              String.format(
                  "No profile id has been created with ID: %s. Waited for %s seconds",
                  expectedUserID, waitedForSeconds));
        }

    }
  }
}

