package com.fansunited.automation.helpers;

import static com.fansunited.automation.mappers.EventMapper.EVENT_TABLE;
import static com.fansunited.automation.mappers.EventMapper.EXCLUDED_TABLE;
import static com.fansunited.automation.mappers.EventMapper.PROFILE_TABLE;
import static com.fansunited.automation.mappers.EventMapper.PROPERTY_EXCLUDED_PROFILE_IDS;
import static com.fansunited.automation.mappers.EventMapper.PROPERTY_ID;
import static com.fansunited.automation.mappers.EventMapper.PROPERTY_PROFILE_ID;
import static com.fansunited.automation.mappers.EventMapper.RANK_TABLE;
import static com.fansunited.automation.mappers.EventMapper.TEMPLATE_TABLE;

import com.fansunited.automation.core.apis.loyaltyapi.GameLeaderboardsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint;
import com.google.api.gax.rpc.ApiException;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.Job;
import com.google.cloud.bigquery.JobId;
import com.google.cloud.bigquery.JobInfo;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.QueryParameterValue;
import com.google.cloud.bigquery.TableResult;
import com.google.cloud.bigquery.datatransfer.v1.DataTransferServiceClient;
import com.google.cloud.bigquery.datatransfer.v1.ScheduleTransferRunsRequest;
import com.google.cloud.bigquery.datatransfer.v1.ScheduleTransferRunsResponse;
import com.google.protobuf.Timestamp;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.apache.http.HttpException;

public class BigQueryHelper {

  private static final BigQuery bigQuery = BigQueryOptions.getDefaultInstance().getService();

  private BigQueryHelper() {}

  @SneakyThrows
  public static void waitForEventsToBeSaved(int seconds) {
    TimeUnit.SECONDS.sleep(seconds);
  }

  public static void runCreateUserRankingTable() {

    LocalDateTime startOfDay = LocalDateTime.of(LocalDate.now().minusDays(3), LocalTime.MIDNIGHT);
    LocalDateTime endDay = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIDNIGHT);
    Timestamp startTime =
        Timestamp.newBuilder()
            .setSeconds(startOfDay.toEpochSecond(ZoneOffset.UTC))
            .setNanos(0)
            .build();
    Timestamp endTime =
        Timestamp.newBuilder().setSeconds(endDay.toEpochSecond(ZoneOffset.UTC)).setNanos(0).build();

    final String configId =
        "projects/131809830171/locations/europe-west1/transferConfigs/633d0404-0000-2a61-bab5-089e082fc1d4";
    try (DataTransferServiceClient client = DataTransferServiceClient.create()) {
      ScheduleTransferRunsRequest request =
          ScheduleTransferRunsRequest.newBuilder()
              .setParent(configId)
              .setStartTime(startTime)
              .setEndTime(endTime)
              .build();
      ScheduleTransferRunsResponse response = client.scheduleTransferRuns(request);
      System.out.println("Schedule backfill run successfully :" + response.getRunsCount());
    } catch (ApiException | IOException ex) {
      System.out.print("Schedule backfill was not run." + ex);
    }
  }

  public static void runSQLScript() {

    String sql =
        """
             CREATE OR REPLACE TABLE
              `fans-united-stage.automationtesting.user_ranking` AS ( (
                        SELECT
                    RANK() OVER (PARTITION BY template.id ORDER BY SUM(r.points) DESC) AS position,
                    SUM(r.points) AS points,
                    r.profile_id,
                    template.id AS id,
                    "template" AS rank_type,
                    MAX(r.date_finished) AS date_finished
                    FROM
                    `fans-united-stage.automationtesting.rank` AS r
                    CROSS JOIN (
                        SELECT
                        c.competition_ids,
                        c.game_ids,
                        c.markets,
                        c.match_ids,
                        c.team_ids,
                        c.to_date,
                        c.from_date,
                        c.game_types,
                        c.id,
                        c.name
                        FROM
                        `fans-united-stage.automationtesting.template`AS c) AS template
                    WHERE
                    DATE(r.date_finished) BETWEEN DATE(template.from_date)
                    AND DATE(template.to_date)
                    AND (template.markets IS NULL
                        OR r.market IN UNNEST(SPLIT(template.markets, ",")))
                    AND (template.match_ids IS NULL
                        OR r.match_id IN UNNEST(SPLIT(template.match_ids, ",")))
                    AND (template.team_ids IS NULL
                        OR r.home_team_id IN UNNEST(SPLIT(template.team_ids, ","))
                        OR r.away_team_id IN UNNEST(SPLIT(template.team_ids, ",")))
                    AND (template.game_ids IS NULL
                        OR r.game_id IN UNNEST(SPLIT(template.game_ids, ",")))
                    AND (template.game_types IS NULL
                        OR template.game_types = ""
                        OR r.game_type IN UNNEST(SPLIT(template.game_types, ",")))
                    AND (template.competition_ids IS NULL
                        OR r.competition_id IN UNNEST(SPLIT(template.competition_ids, ",")))
                    GROUP BY
                    template.id,
                    r.profile_id
                    ORDER BY
                    template.id DESC)
                UNION ALL (
                    SELECT
                RANK() OVER (PARTITION BY r2.game_id ORDER BY SUM(r2.points) DESC ) AS position,
                SUM(r2.points) AS points,
                r2.profile_id,
                    r2.game_id AS id,
                "game" AS rank_type,
                MAX(r2.date_finished) AS date_finished
                FROM
                  `fans-united-stage.automationtesting.rank` AS r2
                WHERE
                r2.game_id IS NOT NULL
                and r2.game_id != ""
                GROUP BY
                r2.game_id,
                    r2.profile_id )
                ORDER BY
                rank_type,
                    id );\
            """;

    QueryJobConfiguration job = QueryJobConfiguration.newBuilder(sql).build();
    try {
      bigQuery.query(job);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
  }

  /***
   * This method forces a refresh of `user_ranking_game_mv` in order to sync the data from BigQuery to MySQL.
   *
   * @param materializedViewName The name of the materialized view that we want to refresh.
   */
  // TODO: Invoke only through syncHelper
  public static void refreshMaterializedView(String materializedViewName) {

    String sql =
        "CALL BQ.REFRESH_MATERIALIZED_VIEW('fans-united-stage.automationtesting."
            + materializedViewName
            + "');";

    QueryJobConfiguration jobConfig = QueryJobConfiguration.newBuilder(sql).build();

    BigQuery bigQuery = BigQueryOptions.getDefaultInstance().getService();

    JobId jobId =
        JobId.newBuilder()
            .setJob(
                "refresh_user_ranking_game_mv_"
                    + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy_HH_mm_ssSSS")))
            .setLocation("europe-west1")
            .build();

    JobInfo jobInfo = JobInfo.newBuilder(jobConfig).setJobId(jobId).build();

    try {

      Job queryJob = bigQuery.create(jobInfo);
      queryJob = queryJob.waitFor();
      queryJob.getStatus();
      queryJob.getQueryResults().getValues();

      if (queryJob == null) {
        throw new RuntimeException("Job no longer exists.");
      } else if (queryJob.getStatus().getError() != null) {
        throw new RuntimeException(queryJob.getStatus().getError().toString());
      }
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
  }

  /***
   * This method will check at the given interval if the data has been synced with MySQL for the specified time.
   * @param table Postfix of the materialized view in BigQuery
   * @param templateOrGameId The ID of the template or the game instance
   * @param checkEverySeconds The interval, in seconds, at which updates will be checked.
   * @param waitUntil The maximum time until which we will check for updates.
   * @throws InterruptedException
   * @throws HttpException
   */
  public static void waitDataToBeSavedInMySQL(
      String table, String templateOrGameId, int checkEverySeconds, int waitUntil)
      throws InterruptedException, HttpException {

    Response response = fetchData(table, templateOrGameId);

    int waitedForSeconds = 0;

    while (response.jsonPath().getList("data").isEmpty()) {
      TimeUnit.SECONDS.sleep(checkEverySeconds);
      waitedForSeconds += checkEverySeconds;

      if (waitedForSeconds >= waitUntil) {
        throw new RuntimeException(
            String.format("NO data has been sync. Waited for %s seconds", waitedForSeconds));
      }

      response = fetchData(table, templateOrGameId);
    }
  }

  private static Response fetchData(String table, String templateOrGameId) throws HttpException {
    try {
      if ("game".equalsIgnoreCase(table)) {
        GameLeaderboardsEndpoint gameLeaderboardsEndpoint =
            GameLeaderboardsEndpoint.builder().gameId(templateOrGameId).build();
        return gameLeaderboardsEndpoint.getLeaderboardForGameId();
      } else if ("template".equalsIgnoreCase(table)) {
        return LeaderboardByIdEndpoint.getLeaderboardForTemplateWithId(
            templateOrGameId, null, null);
      } else {
        throw new IllegalArgumentException("Invalid table name: " + table);
      }
    } catch (HttpException e) {
      System.err.println(
          "Invalid or non-existing " + table.toUpperCase() + " ID: " + templateOrGameId);
      throw e;
    }
  }

  /**
   * Builds a SQL query for checking if a profile ID exists in a specified table.
   *
   * @param tableName The name of the table to query
   * @return A SQL query string
   */
  private static String buildProfileIdExistenceQuery(String tableName) {
    String idColumn = PROPERTY_PROFILE_ID;
    String secondCriteria = "";

    switch (tableName) {
      case PROFILE_TABLE -> {
        idColumn = PROPERTY_ID;
        secondCriteria = "and updated_at is not NULL";
      }
      case TEMPLATE_TABLE, EXCLUDED_TABLE -> idColumn = PROPERTY_EXCLUDED_PROFILE_IDS;
      case EVENT_TABLE -> secondCriteria = "and weight IS NOT NULL";
    }

    String datasetName = "fans-united-stage.automationtesting";

    String query;
    if (secondCriteria.isEmpty()) {
      query = String.format(
          "SELECT COUNT(*) as count FROM `%s.%s` WHERE %s = @value",
          datasetName, tableName, idColumn);
    } else {
      query = String.format(
          "SELECT COUNT(*) as count FROM `%s.%s` WHERE %s = @value %s",
          datasetName, tableName, idColumn, secondCriteria);
    }

    return query;
  }

  public static boolean isProfileIdExistingInTable(String tableName, String profileId) {
    String query = buildProfileIdExistenceQuery(tableName);

    QueryParameterValue parameterValue = QueryParameterValue.string(profileId);
    QueryJobConfiguration queryConfig =
        QueryJobConfiguration.newBuilder(query).addNamedParameter("value", parameterValue).build();

    try {
      TableResult result = bigQuery.query(queryConfig);

      for (FieldValueList row : result.iterateAll()) {
        long count = row.get("count").getLongValue();
        return count > 0;
      }

      return false;
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException("Query execution interrupted", e);
    }
  }
}
