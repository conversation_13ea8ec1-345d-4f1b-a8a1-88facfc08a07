package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.predictionBracket;
import static java.time.ZoneOffset.UTC;
import static org.apache.http.HttpStatus.SC_OK;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.bracket.Fixture;
import com.fansunited.automation.model.predictionapi.bracket.Meta;
import com.fansunited.automation.model.predictionapi.bracket.Participant;
import com.fansunited.automation.model.predictionapi.bracket.request.CreateBracketGameRequest;
import com.fansunited.automation.model.predictionapi.bracket.request.UpdateBracketRequest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;

public class BracketGameHelper {

  public static CreateBracketGameRequest createBracketGame() throws HttpException {
    return createBracketGameRequest(
        "Original Title", "Original Description", "Original Rules", 10, GameStatus.OPEN);
  }

  public static CreateBracketGameRequest createBracketGameRequest(
      String title, String description, String rules, int points, GameStatus status)
      throws HttpException {

    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var faker = new Faker();
    var images = faker.internet().avatar();

    var createBracketRequest =
        CreateBracketGameRequest.builder()
            .id(faker.internet().uuid())
            .title(title)
            .type(GameType.CustomGameType.BRACKET)
            .fixtures(
                List.of(
                    Fixture.builder()
                        .matchId("1")
                        .start_date(date)
                        .participantOne("A")
                        .participantTwo("B")
                        .home_participant("A")
                        .build(),
                    Fixture.builder()
                        .matchId("2")
                        .start_date(date)
                        .participantOne("C")
                        .participantTwo("D")
                        .home_participant("C")
                        .build()))
            .description(description)
            .meta(
                Meta.builder()
                    .participants(
                        List.of(
                            Participant.builder()
                                .id("B")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("A")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("C")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("D")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build()))
                    .build())
            .rules(rules)
            .images(Images.builder().cover(images).main(images).mobile(images).build())
            .points(points)
            .predictionsCutoff(
                Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(1)))
            .status(status)
            .build();

    var response =
        predictionBracket(
            createBracketRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().statusCode(SC_OK);

    return createBracketRequest;
  }

  public static UpdateBracketRequest updateRequest(
      GameStatus status, int points, String winnerId) {
    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var faker = new Faker();
    var images = faker.internet().avatar();

    var updateBracketRequest =
        UpdateBracketRequest.builder()
            
            .title(faker.internet().domainWord())
            .description(faker.internet().domainWord())
            .rules(faker.internet().domainWord())
            .fixtures(
                List.of(
                    Fixture.builder()
                        .matchId("1")
                        .start_date(date)
                        .participantOne("A")
                        .participantTwo("B")
                        .home_participant("A")
                        .winner(winnerId)
                        .build(),
                    Fixture.builder()
                        .matchId("2")
                        .start_date(date)
                        .participantOne("C")
                        .participantTwo("D")
                        .home_participant("C")
                        .winner(winnerId)
                        .build()))
            .meta(
                Meta.builder()
                    .participants(
                        List.of(
                            Participant.builder()
                                .id("B")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("A")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("C")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("D")
                                .name(faker.name().firstName())
                                .image(images)
                                .undecided(false)
                                .build()))
                    .build())
            .images(Images.builder().cover(images).main(images).mobile(images).build())
            .points(points)
            .predictions_cutoff(
                Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(1)))
            .status(status)
            .build();

    return updateBracketRequest;
  }



}
