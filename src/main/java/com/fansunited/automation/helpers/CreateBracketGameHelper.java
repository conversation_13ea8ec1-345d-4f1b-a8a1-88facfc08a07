package com.fansunited.automation.helpers;

import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.bracket.BracketTieBreaker;
import com.fansunited.automation.model.predictionapi.bracket.Fixture;
import com.fansunited.automation.model.predictionapi.bracket.Meta;
import com.fansunited.automation.model.predictionapi.bracket.Participant;
import com.fansunited.automation.model.predictionapi.bracket.request.CreateBracketGameRequest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.github.javafaker.Faker;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * Helper class for creating bracket game requests for testing purposes.
 */
public class CreateBracketGameHelper {

  private static final Faker faker = new Faker();

  /**
   * Creates a default bracket game request for validation testing.
   *
   * @return A CreateBracketPredictionsRequest object with default values
   */
  public static CreateBracketGameRequest createDefaultBracketGameRequest() {
    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var images = faker.internet().avatar();

    return CreateBracketGameRequest.builder()
        .id(faker.internet().uuid())
        .title("Test Bracket Game " + ZonedDateTime.now())
        .fixtures(
            List.of(
                Fixture.builder()
                    .matchId(faker.internet().uuid())
                    .start_date(date)
                    .participantOne("A")
                    .participantTwo("B")
                    .home_participant("A")
                    .build(),
                Fixture.builder()
                    .matchId(faker.internet().uuid())
                    .start_date(date)
                    .participantOne("C")
                    .participantTwo("D")
                    .home_participant("C")
                    .build()))
        .description(faker.internet().domainWord())
        .meta(
            Meta.builder()
                .participants(
                    List.of(
                        Participant.builder()
                            .id("B")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build(),
                        Participant.builder()
                            .id("A")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build(),
                        Participant.builder()
                            .id("C")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build(),
                        Participant.builder()
                            .id("D")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build()))
                .build())
        .rules(faker.internet().domainWord())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .points(1)
        .predictionsCutoff(
            Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(1)))
        .status(GameStatus.PENDING)
        .build();
  }

  /**
   * Creates a bracket game request with custom values.
   *
   * @param title The title of the bracket game
   * @param description The description of the bracket game
   * @param rules The rules of the bracket game
   * @param points The points for the bracket game
   * @param status The status of the bracket game
   * @return A CreateBracketPredictionsRequest object with custom values
   */
  public static CreateBracketGameRequest createCustomBracketGameRequest(
      String title, String description, String rules, int points, GameStatus status, boolean tiebreaker, int statTotal) {
    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var images = faker.internet().avatar();

    return CreateBracketGameRequest.builder()
        .id(faker.internet().uuid())
        .title(title)
        .fixtures(
            List.of(
                Fixture.builder()
                    .matchId(faker.internet().uuid())
                    .start_date(date)
                    .participantOne("A")
                    .participantTwo("B")
                    .home_participant("A")
                    .build(),
                Fixture.builder()
                    .matchId(faker.internet().uuid())
                    .start_date(date)
                    .participantOne("C")
                    .participantTwo("D")
                    .home_participant("C")
                    .build()))
        .description(description)
        .type(GameType.CustomGameType.BRACKET)
        .meta(
            Meta.builder()
                .participants(
                    List.of(
                        Participant.builder()
                            .id("B")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build(),
                        Participant.builder()
                            .id("A")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build(),
                        Participant.builder()
                            .id("C")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build(),
                        Participant.builder()
                            .id("D")
                            .name(faker.name().firstName())
                            .image(images)
                            .undecided(false)
                            .build()))
                .build())
        .rules(rules)
        .tiebreaker(BracketTieBreaker.builder()
            .statTotal(statTotal)
            .statTiebreakerEnabled(tiebreaker)
            .build())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .points(points)
        .predictionsCutoff(
            Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(1)))
        .status(status)
        .build();
  }

  /**
   * Creates a bracket game request with a specific ID.
   *
   * @param id The ID to use for the bracket game
   * @return A CreateBracketPredictionsRequest object with the specified ID
   */
  public static CreateBracketGameRequest createBracketGameRequestWithId(String id) {
    CreateBracketGameRequest request = createDefaultBracketGameRequest();
    request.setId(id);
    return request;
  }

  /**
   * Creates a bracket game request with a specific title.
   *
   * @param title The title to use for the bracket game
   * @return A CreateBracketPredictionsRequest object with the specified title
   */
  public static CreateBracketGameRequest createBracketGameRequestWithTitle(String title) {
    CreateBracketGameRequest request = createDefaultBracketGameRequest();
    request.setTitle(title);
    return request;
  }

  /**
   * Creates a bracket game request with a specific predictions cutoff.
   *
   * @param cutoff The predictions cutoff to use for the bracket game
   * @return A CreateBracketPredictionsRequest object with the specified predictions cutoff
   */
  public static CreateBracketGameRequest createBracketGameRequestWithCutoff(String cutoff) {
    CreateBracketGameRequest request = createDefaultBracketGameRequest();
    request.setPredictionsCutoff(cutoff);
    return request;
  }

  /**
   * Creates a bracket game request without fixtures.
   *
   * @return A CreateBracketPredictionsRequest object without fixtures
   */
  public static CreateBracketGameRequest createBracketGameRequestWithoutFixtures() {
    CreateBracketGameRequest request = createDefaultBracketGameRequest();
    request.setFixtures(null);
    return request;
  }

  /**
   * Creates a bracket game request without participants.
   *
   * @return A CreateBracketPredictionsRequest object without participants
   */
  public static CreateBracketGameRequest createBracketGameRequestWithoutParticipants() {
    CreateBracketGameRequest request = createDefaultBracketGameRequest();
    request.setMeta(Meta.builder().participants(null).build());
    return request;
  }

}
