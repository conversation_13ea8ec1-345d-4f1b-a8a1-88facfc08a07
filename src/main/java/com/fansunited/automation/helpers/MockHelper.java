package com.fansunited.automation.helpers;

import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class MockHelper {

  /**
   * The method creates a list of timestamps where the most resent is today,
   * and the oldest is same date minus predictionsCount months.
   * So if today is 2022-05-01 and prediction count is = 5,
   * then the first date of the list is 2022-05-01 and the last date is 2022-01-01.
   * The method is useful when one need to mock events with different time span.
   *
   * @param predictionsCount the number of dates in the list that will be generated
   * @return a list with generated timestamps where the period between each is three months
   */
  public static List<LocalDateTime> generateTimeStampsForEvent(int predictionsCount) {

    List<LocalDateTime> timeStamps = new ArrayList<>();

    for (int i = 0; i < predictionsCount; i++) {
      var timeStamp = LocalDateTime.now().minusMonths(i).minusDays(1);
      timeStamps.add(timeStamp);
    }
    return timeStamps;
  }

  /**
   * The method creates a list of timestamps (as Strings) based on a passed List of time stamps
   * The dates are formatted so that can be used by Mock API for setting predictionLastUpdate field
   * in Rank Table
   * The method is useful when one need to mock events with different predictionLastUpdate dates.
   *
   * @param timeStamps list of timestamps, based on which the predictionLastUpdate dates are generated
   * @return a list with generated predictionLastUpdate dates where the period between each is three months
   */
  public static List<String> generatePredictionLastUpdatesForEvent(List<LocalDateTime> timeStamps,
      int predictionsCount) {

    List<String> predictionLastUpdates = new ArrayList<>();

    for (int i = predictionsCount - 1; i >= 0; i--) {
      predictionLastUpdates.add(
          timeStamps.get(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'hh:mm:ss.000000'Z'")));
    }
    return predictionLastUpdates;
  }

  public static void mockEventForEachMarket(List<LocalDateTime> timeStamps,
      List<String> predictionLastUpdates, String userId) {

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(0), userId,
        PredictionMarket.FT_1X2,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(1), userId,
        PredictionMarket.BOTH_TEAMS_SCORE,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(2), userId,
        PredictionMarket.OVER_CORNERS_6_5,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(3), userId,
        PredictionMarket.OVER_CORNERS_12_5,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(4), userId,
        PredictionMarket.PENALTY_MATCH,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(5), userId,
        PredictionMarket.CORRECT_SCORE,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(6), userId,
        PredictionMarket.PLAYER_SCORE_TWICE,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(7), userId,
        PredictionMarket.CORRECT_SCORE,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));

    InsertBigQData.insertSinglePredictionEvent(timeStamps.get(8), userId,
        PredictionMarket.CORRECT_SCORE_ADVANCED,
        PredictionType.SINGLE,
        MatchesEndpoint.getRandomFinishedMatchDto(3));
  }
}
