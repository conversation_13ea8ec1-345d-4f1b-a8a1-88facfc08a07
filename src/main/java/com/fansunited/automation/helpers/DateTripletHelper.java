package com.fansunited.automation.helpers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Singleton helper class for providing non-overlapping date triplets.
 */
public class DateTripletHelper {

  private static final int MAX_DAYS = 365; // 1 year from now
  private final LocalDate startDate; // Starting point (tomorrow)
  private final LocalDate startNegativeDate; // Starting point (yesterday)
  private final AtomicInteger currentDayIndex; // Keeps track of the next triplet
  private final AtomicInteger currentNegativeDayIndex; // Keeps track of the next negative triplet

  // Singleton instance
  private static volatile DateTripletHelper instance;

  private DateTripletHelper() {
    this.startDate = LocalDate.now().plusDays(150); // Start from tomorrow
    this.startNegativeDate = LocalDate.now().minusDays(150); // Start from yesterday
    this.currentDayIndex = new AtomicInteger(0); // Initialize the day index
    this.currentNegativeDayIndex = new AtomicInteger(0); // Initialize the negative day index
  }

  /**
   * Gets the singleton instance of DateTripletHelper.
   *
   * @return the singleton instance
   */
  public static DateTripletHelper getInstance() {
    if (instance == null) {
      synchronized (DateTripletHelper.class) {
        if (instance == null) {
          instance = new DateTripletHelper();
        }
      }
    }
    return instance;
  }

  /**
   * Gets the next triplet of future dates (from, mid, to).
   *
   * @return a DateTriplet object containing the next triplet of dates, or null if the range is exceeded.
   */
  public synchronized DateTriplet getNext() {
    int index = currentDayIndex.getAndAdd(3); // Reserve the next 3 days
    if (index + 3 > MAX_DAYS) {
      return null; // No more triplets available
    }

    LocalDate from = startDate.plusDays(index);
    LocalDate mid = from.plusDays(1);
    LocalDate to = mid.plusDays(1);

    return new DateTriplet(from, mid, to);
  }

  /**
   * Gets the next triplet of past dates (from, mid, to).
   *
   * @return a DateTriplet object containing the next triplet of dates in the past, or null if the range is exceeded.
   */
  public synchronized DateTriplet getNextNegative() {
    int index = currentNegativeDayIndex.getAndAdd(3); // Reserve the next 3 days in the past
    if (index + 3 > MAX_DAYS) {
      return null; // No more triplets available
    }

    LocalDate to = startNegativeDate.minusDays(index);
    LocalDate mid = to.minusDays(1);
    LocalDate from = mid.minusDays(1);

    return new DateTriplet(from, mid, to);
  }

  /**
   * Inner class representing a triplet of dates.
   */
  public static class DateTriplet {
    private final LocalDate from;
    private final LocalDate mid;
    private final LocalDate to;

    public DateTriplet(LocalDate from, LocalDate mid, LocalDate to) {
      this.from = from;
      this.mid = mid;
      this.to = to;
    }

    public LocalDate getFrom() {
      return from;
    }

    public LocalDateTime getFromLocalDateTime() {
      return from.atStartOfDay();
    }

    public String getFromString() {
      return convertLocalDateToIsoDate(from);
    }

    public LocalDate getMid() {
      return mid;
    }

    public LocalDateTime getMidLocalDateTime() {
      return mid.atStartOfDay();
    }

    public String getMidString() {
      return convertLocalDateToIsoDate(mid);
    }

    public LocalDate getTo() {
      return to;
    }

    public LocalDateTime getToLocalDateTime() {
      return to.atStartOfDay();
    }

    public String getToString() {
      return convertLocalDateToIsoDate(to);
    }

    @Override
    public String toString() {
      return "DateTriplet{" +
          "from=" + convertLocalDateToIsoDate(from) +
          ", mid=" + convertLocalDateToIsoDate(mid) +
          ", to=" + convertLocalDateToIsoDate(to) +
          '}';
    }

    private String convertLocalDateToIsoDate(LocalDate localDate) {
      return localDate.format(DateTimeFormatter.ISO_DATE);
    }
  }
}
