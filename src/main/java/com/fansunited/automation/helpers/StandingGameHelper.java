package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.STANDING;
import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.standing.Meta;
import com.fansunited.automation.model.predictionapi.standing.request.CustomStandingGameRequest;
import com.fansunited.automation.model.predictionapi.standing.request.StandingResponse;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class StandingGameHelper {

  private static final Faker faker = new Faker();

  public static CustomStandingGameRequest createStandingGameRequest(
      int points, GameStatus status, int outcomeCount) {

    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var images = faker.internet().avatar();

    return CustomStandingGameRequest.builder()
        .meta(
            Meta.builder()
                .participants(
                    List.of(
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("A")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build(),
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("B")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build(),
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("C")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build()))
                .build())
        .type(STANDING.getValue())
        .outcomeCount(outcomeCount)
        .id(faker.internet().uuid())
        .title(faker.toString())
        .description(faker.toString())
        .rules(faker.toString())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .points(points)
        .predictionsCutoff(date)
        .status(status)
        .build();
  }


  public static StandingResponse createStandingGame(GameStatus status,int outcomeCount,int points)
      throws HttpException {

    var response =
        PredictionStandingBaseSetup.createCustomStandingGame(
            createStandingGameRequest(points,status,outcomeCount),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    return response.as(StandingResponse.class);

  }

}
