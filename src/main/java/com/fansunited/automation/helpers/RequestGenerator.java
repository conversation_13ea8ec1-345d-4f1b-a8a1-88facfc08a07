package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.ApiConstants.AuthRequirement.getRandomAuthRequirementValue;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.setUpImages;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.model.CommonStatus.INACTIVE;
import static com.fansunited.automation.model.loyaltyapi.activity.request.Context.createContextWithRandomData;
import static com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO.createBrandingDtoWithRandomData;
import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.ImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizOptionsRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizQuestionsRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizRequest;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrOption;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrPoint;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.model.minigamesapi.eitheror.request.EitherOrRequest;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;

public class RequestGenerator {
  private static final Faker faker = new Faker();
  private static final String DEFAULT_EMBED_CODE = "test";
  public static final String MAIN = "happy.jpg";
  public static final String MOBILE = "sad.jpg";

  /**
   * Creates a ClassicQuizRequest object populated with randomized test data using Faker library.
   * Generates quiz settings including title, language, branding, questions, and other
   * configurations.
   *
   * @return ClassicQuizRequest A fully populated Classic Quiz request object with test data
   * @throws HttpException If there is an error during object creation or data generation
   */
  public ClassicQuizRequest getClassicQuizRequest() throws HttpException {

    return ClassicQuizRequest.builder()
        .related(null)
        .title(faker.lorem().sentence(3))
        .language(UrlParamValues.Language.FR.getValue())
        .alternative_title(faker.lorem().word())
        .branding(createBrandingDtoWithRandomData())
        .status(INACTIVE)
        .authRequirement(getRandomAuthRequirementValue())
        .description(faker.lorem().sentence(6))
        .context(createContextWithRandomData())
        .customFields(new Fields(faker.lorem().word(), faker.lorem().word()))
        .labels(new Fields(faker.lorem().word(), faker.lorem().word()))
        .adContent("content")
        .rules("test")
        .type(getClassicQuizType())
        .embed_code(DEFAULT_EMBED_CODE)
        .time(5)
        .flags(createFlags())
        .images(createImages())
        .questions(createDefaultQuestions())
        .build();
  }

  private List<String> createFlags() {
    return List.of(
        new Faker().howIMetYourMother().toString(), new Faker().howIMetYourMother().toString());
  }

  private ImagesDto createImages() {
    return ImagesDto.builder().main("test0.jpg").mobile("test1.jpg").cover("test2.jpg").build();
  }

  private List<ClassicQuizQuestionsRequest> createDefaultQuestions() {
    return List.of(
        ClassicQuizQuestionsRequest.builder()
            .embed_code("Test")
            .question("What " + faker.lorem().sentence().toLowerCase() + "?")
            .options(
                List.of(
                    ClassicQuizOptionsRequest.builder()
                        .option("So so!")
                        .correct(true)
                        .images(GameImagesDto.builder().main("so so.jpg").mobile(MOBILE).build())
                        .build(),
                    ClassicQuizOptionsRequest.builder()
                        .option("Very good!")
                        .correct(false)
                        .images(GameImagesDto.builder().main(MAIN).mobile(MOBILE).build())
                        .build()))
            .build());
  }

  private String getClassicQuizType() throws HttpException {

    return ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS)
        .jsonPath()
        .getString("data.classic_quiz.types[0].id");
  }

  /**
   * This method creates Template request with random data
   *
   * @return TemplateRequest object
   */
  public TemplateRequest getTemplateRequest() {
    return TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(3)))
        .description(faker.lorem().paragraph(2))
        .build();
  }

  /**
   * Creates and returns an EitherOrRequest object populated with test data using Faker library. The
   * request includes randomized game parameters such as title, description, options, point system,
   * and game settings.
   *
   * @return EitherOrRequest A fully populated Either/Or game request object with test data
   */
  public EitherOrRequest getEitherOrRequest() {

    return EitherOrRequest.builder()
        .title(faker.name().title())
        .description(faker.lorem().paragraph(3))
        .related(null)
        .flags(List.of("flag1", "flag2"))
        .images(ImagesDto.builder().main("test1.jpg").mobile("test2.jpg").cover("test").build())
        .status(String.valueOf(INACTIVE))
        .authRequirement(getRandomAuthRequirementValue())
        .winning_condition(EitherOrWinningCondition.MORE)
        .branding(createBrandingDtoWithRandomData())
        .question("How are you@")
        .context(createContextWithRandomData())
        .points(
            (List.of(
                EitherOrPoint.builder().correct_steps(0).score(5).build(),
                EitherOrPoint.builder().correct_steps(5).score(10).build(),
                EitherOrPoint.builder().correct_steps(10).score(20).build())))
        .options(
            List.of(
                (EitherOrOption.builder()
                    .id("1")
                    .label("label1")
                    .images(GameImagesDto.builder().main("test.jpg").mobile(MOBILE).build())
                    .value(1)
                    .build()),
                (EitherOrOption.builder()
                    .id("2")
                    .label("label2")
                    .value(2)
                    .images(GameImagesDto.builder().main(MAIN).mobile(MOBILE).build())
                    .build()),
                (EitherOrOption.builder()
                    .id("3")
                    .value(3)
                    .label("label3")
                    .images(GameImagesDto.builder().main(MAIN).mobile(MOBILE).build())
                    .build()),
                (EitherOrOption.builder()
                    .id("4")
                    .value(5)
                    .label("label4")
                    .images(GameImagesDto.builder().main(MAIN).mobile(MOBILE).build())
                    .build()),
                (EitherOrOption.builder()
                    .id("5")
                    .value(5)
                    .label("label5")
                    .images(GameImagesDto.builder().main(MAIN).mobile(MOBILE).build())
                    .build())))
        .lives(3)
        .rules("no bugs")
        .time(3)
        .build();
  }

  public static CreateGameRequest getCreateGameRequest(GameType gameType) {
    List<Match> matchList = MatchGenerator.generateMatchesInFuture(2, 4);

    for (Match match : matchList) {
      cleanUpMatchIdList.add(match.getId());
    }

    Resolver.openMatchesForPredictions(matchList);

    var matchIds = matchList.stream().map(Match::getId).toList();
    var predictionsCutoff = generateFutureDate(2);
    var gameFixtureList = generateValidFixturesForGameType(gameType, matchIds);
    var images =
        setUpImages(faker.internet().url(), faker.internet().url(), faker.internet().url());
    var generateScheduleTime =
        Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusMinutes(5));

    return CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(generateDateTimeInIsoFormat(predictionsCutoff))
        .scheduleOpenAt(generateScheduleTime)
        .fixtures(gameFixtureList)
        .status(GameStatus.PENDING.getValue())
        .images(images)
        .branding(
            BrandingDTO.builder()
                .colors(
                    BrandingColorsDTO.builder()
                        .additionalColor(faker.color().hex())
                        .backgroundColor(faker.color().hex())
                        .primaryColor(faker.color().hex())
                        .contentColor(faker.color().hex())
                        .borderColor(faker.color().hex())
                        .secondaryColor(faker.color().hex())
                        .build())
                .urls(
                    BrandingUrlsDTO.builder()
                        .primaryUrl(faker.internet().url())
                        .privacyPolicyUrl(faker.internet().url())
                        .additionalUrl(faker.internet().url())
                        .build())
                .images(
                    BrandingImagesDTO.builder()
                        .additionalImage(faker.file().fileName())
                        .backgroundImage(faker.file().fileName())
                        .mainLogo(faker.file().fileName())
                        .mobileBackgroundImage(faker.file().fileName())
                        .build())
                .build())
        .adContent(faker.lorem().sentence(2))
        .labels(Fields.builder().label1(faker.lorem().word()).label2(faker.lorem().word()).build())
        .customFields(
            Fields.builder().label2(faker.lorem().word()).label1(faker.lorem().word()).build())
        .build();
  }
}
