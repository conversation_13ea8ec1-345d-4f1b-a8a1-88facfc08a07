package com.fansunited.automation.helpers;

import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import com.github.javafaker.Faker;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

/**
 * Helper class for creating custom event game requests for testing purposes.
 */
public class CustomEventGameHelper {

  private static final Faker faker = new Faker();

  /**
   * Creates a default custom event game request.
   *
   * @return A CreateCustomEventGameRequest with default values
   */
  public static CreateCustomEventGameRequest createDefaultCustomEventGameRequest() {
    return CreateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question(faker.lorem().sentence())
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question(faker.lorem().sentence())
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title(faker.lorem().sentence())
        .description(faker.lorem().sentence())
        .rules(faker.lorem().sentence())
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
        .outcome(false)
        .build();
  }

  /**
   * Creates an update custom event game request with modified fixtures.
   *
   * @return A CreateCustomEventGameRequest with updated values for testing updates
   */
  public static CreateCustomEventGameRequest updateCustomEventGameRequest() {
    return CreateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you today")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .outcome("fine")
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title("Updated Test Game")
        .description("Updated test description")
        .rules("Updated test rules")
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
        .outcome(false)
        .build();
  }
}
