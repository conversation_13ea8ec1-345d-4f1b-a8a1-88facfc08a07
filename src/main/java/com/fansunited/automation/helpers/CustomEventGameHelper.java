package com.fansunited.automation.helpers;

import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import com.github.javafaker.Faker;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

/**
 * Helper class for creating custom event game requests for testing purposes.
 */
public class CustomEventGameHelper {

  private static final Faker faker = new Faker();

  /**
   * Creates a default custom event game request.
   *
   * @return A CreateCustomEventGameRequest with default values
   */
  public static CreateCustomEventGameRequest createDefaultCustomEventGameRequest() {
    return CreateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .question("how are you")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id(faker.internet().uuid())
                    .outcome("fine")
                    .question("what is going on")
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title("test")
        .description("test")
        .rules("test")
        .images(null)
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusDays(10).toInstant()))
        .outcome(false)
        .build();
  }
}
