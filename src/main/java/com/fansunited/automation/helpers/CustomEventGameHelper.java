package com.fansunited.automation.helpers;

import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionCustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventPredictionRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateCustomEventPredictionRequest;
import com.github.javafaker.Faker;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

/**
 * Helper class for creating custom event game requests for testing purposes.
 */
public class CustomEventGameHelper {

  private static final Faker faker = new Faker();
  private static final String FREE_INPUT ="FREE_INPUT" ;

  /**
   * Creates a default custom event game request.
   *
   * @return A CreateCustomEventGameRequest with default values
   */
  public static CreateCustomEventGameRequest createDefaultCustomEventGameRequest() {
    return CreateCustomEventGameRequest.builder()
        .type("EVENT")
        .fixtures(
            List.of(
                CustomEventFixture.builder()
                    .id("1")
                    .question(faker.lorem().sentence())
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build(),
                CustomEventFixture.builder()
                    .id("2")
                    .question(faker.lorem().sentence())
                    .voidReason(null)
                    .points(1)
                    .outcomeType("FREE_INPUT")
                    .build()))
        .title(faker.lorem().sentence())
        .description(faker.lorem().sentence())
        .rules(faker.lorem().sentence())
        .images(Images.createImagesWithRandomData())
        .related(null)
        .customFields(null)
        .status(GameStatus.OPEN)
        .predictionsCutoff(Date.from(ZonedDateTime.now(UTC).plusMinutes(16).toInstant()))
        .build();
  }

  public static CreateCustomEventPredictionRequest participateInDefaultCustomEventGameRequest(String id, String id1) {
    return CreateCustomEventPredictionRequest.builder()
        .fixtures(
            List.of(
                PredictionCustomEventFixture.builder()
                    .id("1")
                    .predictionType(FREE_INPUT)
                    .prediction(true)

                    .build(),
                PredictionCustomEventFixture.builder()
                    .id("2")
                    .predictionType(FREE_INPUT)
                    .prediction(false)
                    .build()))
        .build();

  }
  
  public static UpdateCustomEventPredictionRequest updateCustomEventPredictionRequest() {
    return UpdateCustomEventPredictionRequest.builder()
        .fixtures(
            List.of(
                PredictionCustomEventFixture.builder()
                    .id("1")
                    .predictionType(FREE_INPUT)
                    .prediction(true)
                    .build(),
                PredictionCustomEventFixture.builder()
                    .id("2")
                    .predictionType(FREE_INPUT)
                    .prediction(true)
                    .build()))
        .build();
}
}
