package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.model.loyaltyapi.templates.RankType.FANTASY;

import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroupFilter;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroups;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.github.javafaker.Faker;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class TemplateRequestGenerator {

  private static final Faker faker = new Faker();

  public static TemplateRequest generateTemplateRequest(DateTripletHelper.DateTriplet dateTriplet) {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    return TemplateRequest.builder()
        .name(faker.name().title())
        .competitionIds(List.of(PREMIER_LEAGUE_COMP_ID))
        .groups(
            List.of(
                TemplateGroups.builder()
                    .groupId("1")
                    .label(faker.idNumber().valid())
                    .filters(
                        TemplateGroupFilter.builder()
                            .fromDate(dateTriplet.getFromString())
                            .toDate(dateTriplet.getToString())
                            .build())
                    .build(),TemplateGroups.builder()
                    .groupId("2")
                    .label(faker.idNumber().valid())
                    .filters(
                        TemplateGroupFilter.builder()
                            .fromDate(dateTriplet.getFromString())
                            .toDate(dateTriplet.getToString())
                            .build())
                    .build()))
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusDays(2)))
        .branding(
            BrandingDTO.builder()
                .colors(
                    BrandingColorsDTO.builder()
                        .additionalColor(faker.color().hex(true))
                        .backgroundColor(faker.color().hex(true))
                        .primaryColor(faker.color().hex(true))
                        .contentColor(faker.color().hex(true))
                        .borderColor(faker.color().hex(true))
                        .secondaryColor(faker.color().hex(true))
                        .build())
                .urls(
                    BrandingUrlsDTO.builder()
                        .primaryUrl(faker.internet().url())
                        .privacyPolicyUrl(faker.internet().url())
                        .additionalUrl(faker.internet().url())
                        .build())
                .images(
                    BrandingImagesDTO.builder()
                        .additionalImage(faker.internet().image())
                        .backgroundImage(faker.internet().image())
                        .mainLogo(faker.internet().image())
                        .mobileBackgroundImage(faker.internet().image())
                        .build())
                .build())
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusDays(3)))
        .markets(markets)
        .type(FANTASY.name())
        .labels(
            Fields.builder()
                .label1(faker.funnyName().name())
                .label2(faker.funnyName().name())
                .build())
        .customFields(
            Fields.builder()
                .label1(faker.funnyName().name())
                .label2(faker.funnyName().name())
                .build())
        .ad_content("<p>" + faker.lorem().word() + "</p>")
        .build();
  }
}
