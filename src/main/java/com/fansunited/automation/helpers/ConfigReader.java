package com.fansunited.automation.helpers;

import static com.fansunited.automation.helpers.ConfigReader.PropertyKey.FIREBASE_PATH_TO_SERVICE_ACCOUNT_PROFILE_PROJECT;
import static com.fansunited.automation.helpers.ConfigReader.PropertyKey.FIREBASE_WEB_API_KEY_CLIENTS_PROJECT;
import static com.fansunited.automation.helpers.ConfigReader.PropertyKey.FIREBASE_WEB_API_KEY_PROFILE_PROJECT;
import static com.fansunited.automation.helpers.Helper.isPortInUse;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ConfigReader {

  private static volatile ConfigReader configReader;
  private Properties properties;
  private static final Object lock = new Object();
  private static final Logger LOG = LoggerFactory.getLogger(ConfigReader.class);

  private ConfigReader() {
  }

  public static ConfigReader getInstance() {
    if (configReader == null) {
      synchronized (lock) {
        if (configReader == null) {
          configReader = new ConfigReader();
          return configReader;
        }
      }
    }
    return configReader;
  }

  public synchronized String getProperty(PropertyKey key) {
    if (properties == null) {
      var env = System.getProperty("env");

      if (env == null) { // If env is NOT specified fallback to local
        env = Environment.LOCAL.getValue();
      }

      properties = new Properties();
      loadProperties(env, properties);

      // Used when testing only some APIs locally
      if (!env.equals(Environment.LOCAL.getValue())) {
        var localEnvProps = new Properties();
        loadProperties(Environment.LOCAL.getValue(), localEnvProps);
        // this should be removed, because mixing env variables
        //replaceLoadedPropsForApisRunningLocally(localEnvProps);
      }
    }

    return properties.getProperty(key.getValue());
  }

  private void loadProperties(String env, Properties properties) {
    try (
        InputStream input = new FileInputStream(
            System.getProperty("user.dir") + "/src/main/resources/env/" + env + ".properties")) {
      properties.load(input);
    } catch (IOException ex) {
      ex.printStackTrace();
    }
  }

  public static String getCurrentTestEnvironment() {
    String currentTestEnvironment = System.getProperty("env");

    if (currentTestEnvironment == null) {
      currentTestEnvironment = "local";
    }
    return currentTestEnvironment;
  }

  @AllArgsConstructor
  public enum PropertyKey {

    VOTING_API_BASE_URL("votingApiBaseUrl"),
    VOTING_API_PORT("votingApiPort"),
    MINI_GAMES_API_BASE_URL("miniGamesApiBaseUrl"),
    MINI_GAMES_API_PORT("miniGamesApiPort"),
    DISCUSSION_API_BASE_URL("discussionApiBaseUrl"),
    DISCUSSION_API_PORT("discussionApiPort"),
    LEAGUES_API_BASE_URL("leaguesApiBaseUrl"),
    LEAGUES_API_PORT("leaguesApiPort"),
    PROFILE_API_BASE_URL("profileApiBaseUrl"),
    PROFILE_API_PORT("profileApiPort"),
    FOOTBALL_API_BASE_URL("footballApiBaseUrl"),
    FOOTBALL_API_PORT("footballApiPort"),
    PREDICTION_API_BASE_URL("predictionApiBaseUrl"),
    PREDICTION_API_PORT("predictionApiPort"),
    CLIENT_API_BASE_URL("clientApiBaseUrl"),
    CLIENT_API_PORT("clientApiPort"),
    REPORTING_API_BASE_URL("reportingApiBaseUrl"),
    REPORTING_API_PORT("reportingApiPort"),
    RESOLVER_API_BASE_URL("resolverApiBaseUrl"),
    RESOLVER_API_PORT("resolverApiPort"),
    LOYALTY_API_BASE_URL("loyaltyApiBaseUrl"),
    LOYALTY_API_PORT("loyaltyApiPort"),
    MOCK_API_BASE_URL("mockApiBaseUrl"),
    MOCK_API_PORT("mockApiPort"),
    FIREBASE_WEB_API_KEY_PROFILE_PROJECT("firebaseWebApiKeyProfileProject"),
    FIREBASE_WEB_API_KEY_CLIENTS_PROJECT("firebaseWebApiKeyClientsProject"),
    FIREBASE_PATH_TO_SERVICE_ACCOUNT_PROFILE_PROJECT("firebasePathToServiceAccountProfileProject"),
    GOOGLE_ENDPOINTS_API_KEY("googleEndpointsApiKey"),
    MYSQL_CONNECTION_URL("mysqlConnectionUrl"),
    MYSQL_USER("mysqlUser"),
    MYSQL_PASS("mysqlPass"),
    IDENTITY_TOOLKIT_PROFILE_URL("identityToolkitProfileUrl"),
    IDENTITY_TOOLKIT_CLIENT_URL("identityToolkitClientUrl"),
    USE_FIREBASE_EMULATOR("useFirebaseEmulator"),
    AUTH_USER_PLATFORM_OPERATOR("authUserPlatformOperator"),
    AUTH_USER_ADMIN_USER("authUserClientAdmin"),
    AUTH_USER_BILLING_USER("authUserBillingManager"),
    RINGIER_BASE_AUTH_USER("baseAuthUser"),
    RINGIER_BASE_AUTH_PASS("baseAuthPass");

    @Getter
    private final String value;

    public static List<PropertyKey> getPropertyKeysContainingApi(ApiLocalPort apiLocalPort) {
      return Arrays.stream(PropertyKey.values())
          .filter(propertyKey -> propertyKey.name().contains(apiLocalPort.name()))
          .collect(Collectors.toList());
    }
  }

  @AllArgsConstructor
  private enum ApiLocalPort {
    PROFILE_API(8010),
    PREDICTION_API(8040),
    FOOTBALL_API(8080),
    REPORTING_API(8020),
    MOCK_API(8030),
    RESOLVER_API(8050),
    CLIENT_API(8070),
    LOYALTY_API(8060),
    LEAGUES_API(9010),
    DISCUSSION_API(9020),
    ENGAGEMENT_CALCULATOR(8001);

    @Getter
    private final int value;

    public static ApiLocalPort of(int port) {
      for (ApiLocalPort apiLocalPort : values()) {
        if (apiLocalPort.getValue() == port) {
          return apiLocalPort;
        }
      }
      throw new IllegalArgumentException(
          port + " is not in the list of local APIs ports. List ports: " + Arrays.toString(
              Arrays.stream(ApiLocalPort.values()).map(ApiLocalPort::getValue).toArray()));
    }
  }

  @AllArgsConstructor
  private enum Environment {
    PROD("prod"),
    LOCAL("local"),
    STAGE("stage");

    @Getter
    private final String value;
  }

  private void replaceLoadedPropsForApisRunningLocally(Properties localEnvProps) {

    boolean hasApiRunningLocally = false;

    for (var port : ApiLocalPort.values()) {
      if (isPortInUse(port.getValue())) {
        hasApiRunningLocally = true;
        var propertyKeyList = PropertyKey.getPropertyKeysContainingApi(port);
        if (propertyKeyList.isEmpty()) {
          throw new RuntimeException("Could not find any property key containing: " + port.name());
        }
        LOG.info("Running "
            + port.name()
            + " tests locally, as local "
            + port.name()
            + " port("
            + port.getValue()
            + ") is open");
        propertyKeyList.forEach(propertyKey -> properties.replace(propertyKey.getValue(),
            localEnvProps.getProperty(propertyKey.getValue())));
      }
    }
    if (hasApiRunningLocally) {
      // Replace staging firebase and endpoint values with local.props values, otherwise tests won't work
      properties.replace(FIREBASE_WEB_API_KEY_PROFILE_PROJECT.getValue(),
          localEnvProps.getProperty(FIREBASE_WEB_API_KEY_PROFILE_PROJECT.getValue()));
      properties.replace(FIREBASE_WEB_API_KEY_CLIENTS_PROJECT.getValue(),
          localEnvProps.getProperty(FIREBASE_WEB_API_KEY_CLIENTS_PROJECT.getValue()));
      properties.replace(FIREBASE_PATH_TO_SERVICE_ACCOUNT_PROFILE_PROJECT.getValue(),
          localEnvProps.getProperty(FIREBASE_PATH_TO_SERVICE_ACCOUNT_PROFILE_PROJECT.getValue()));
    }
  }
}
