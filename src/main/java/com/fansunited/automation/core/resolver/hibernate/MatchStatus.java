package com.fansunited.automation.core.resolver.hibernate;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@ToString
@Getter
@Setter
@EqualsAndHashCode
@AllArgsConstructor @NoArgsConstructor
@Entity
@Table(name = "event_status")
public class MatchStatus {
  @Id
  private byte id;
  private String type;
  private String name;
  private String code;
}

