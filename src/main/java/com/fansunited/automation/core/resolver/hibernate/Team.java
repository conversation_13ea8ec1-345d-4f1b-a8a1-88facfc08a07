package com.fansunited.automation.core.resolver.hibernate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@AllArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@Entity
@Table(name = "team")
public class Team {
  @Id
  private String id;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "country_id")
  private Country country;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "asset_id")
  private Asset assets;

  @JsonIgnore
  private String name;

  @Column(name = "full_name")
  @JsonProperty(value = "full_name")
  private String fullName;

  @Column(name = "short_name")
  @JsonProperty(value = "short_name")
  private String shortName;

  private boolean national;
  private String code;
  private String gender;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinTable(
      name = "competition_team",
      joinColumns = {@JoinColumn(name = "team_id")},
      inverseJoinColumns = {@JoinColumn(name = "competition_id")}
  )
  private List<Competition> competitions;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinColumn(name = "team_id", referencedColumnName = "id")
  private List<Squad> squad;
}