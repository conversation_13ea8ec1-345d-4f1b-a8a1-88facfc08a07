package com.fansunited.automation.core.resolver.hibernate;

import com.fansunited.automation.helpers.DateFormatter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Table(name = "event")
@Entity
public class Match {

  @Id private String id;

  @Column(name = "kickoff_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime kickoffAt;

  @Column(name = "finished_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime finishedAt;

  @Column(name = "updated_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime updatedAt;

  @Column(name = "created_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime createdAt;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "status", referencedColumnName = "code")
  private MatchStatus status;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "home_team_id")
  private Team homeTeam;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "competition_id")
  private Competition competition;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "away_team_id")
  private Team awayTeam;

  @Column(name = "goals_ft_home")
  private Byte goalsFullTimeHome;

  @Column(name = "goals_ft_away")
  private Byte goalsFullTimeAway;

  @Column(name = "goals_ht_home")
  private Byte goalsHalfTimeHome;

  @Column(name = "goals_ht_away")
  private Byte goalsHalfTimeAway;

  @Column(name = "goals_et_home")
  private Byte goalsExtraTimeHome;

  @Column(name = "goals_et_away")
  private Byte goalsExtraTimeAway;

  @Column(name = "goals_agg_home")
  private Byte goalsAggregateHome;

  @Column(name = "goals_agg_away")
  private Byte goalsAggregateAway;

  @Column(name = "goals_pen_home")
  private Byte goalsPenaltyHome;

  @Column(name = "goals_pen_away")
  private Byte goalsPenaltyAway;

  @Column(name = "venue_name")
  private String venue;

  @Column(name = "referee_name")
  private String referee;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "event_id", referencedColumnName = "id")
  private List<MatchStats> stats;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinColumn(name = "event_id", referencedColumnName = "id")
  private List<MatchPlayer> players;

  @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "event_id", referencedColumnName = "id")
  private List<MatchTimeline> timeline;

  @Column(name = "lineups_confirmed")
  private Boolean lineupsConfirmed;

  @Column(name = "started_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime startedAt;

  private String minute;
}
