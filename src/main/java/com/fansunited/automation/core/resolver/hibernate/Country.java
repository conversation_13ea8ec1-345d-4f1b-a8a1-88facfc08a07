package com.fansunited.automation.core.resolver.hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@EqualsAndHashCode
@Entity
@Table(name = "country")
public class Country {
  @Id
  private String id;

  private String name;
  private String alias;

  @Column(name = "code")
  private String countryCode;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "asset_id")
  private Asset assets;
}