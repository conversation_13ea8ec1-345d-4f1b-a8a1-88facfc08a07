package com.fansunited.automation.core.resolver.hibernate;

import com.fansunited.automation.helpers.DateFormatter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Table(name = "event")
@Entity
public class Event {

  @Id
  private int id;

  @Column(name = "kickoff_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime kickoffAt;

  @Column(name = "finished_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime finishedAt;

  @Column(name = "home_team_id")
  private String homeTeamId;

  @Column(name = "away_team_id")
  private String awayTeamId;

  @Column(name = "status")
  private String status;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "json")
  private JsonCompetitionContext competitionContext;

  @Column(name = "venue_name")
  private String venueName;

  @Column(name = "referee_name")
  private String refereeName;

  @Column(name = "goals_ft_home")
  private int goalsFtHome;

  @Column(name = "goals_ft_away")
  private int goalsFtAway;

  @Column(name = "goals_ht_home")
  private int goalsHtHome;

  @Column(name = "goals_ht_away")
  private int goalsHtAway;

  @Column(name = "goals_et_home")
  private int goalsEtHome;

  @Column(name = "goals_et_away")
  private int goalsEtAway;

  @Column(name = "goals_agg_home")
  private int goalsAggHome;

  @Column(name = "goals_agg_away")
  private int goalsAggAway;

  @Column(name = "goals_pen_home")
  private int goalsPenHome;

  @Column(name = "goals_pen_away")
  private int goalsPenAway;

  @Column(name = "created_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime createdAt;

  @Column(name = "updated_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime updatedAt;

  @Column(name = "competition_id")
  private String competitionId;

  @Column(name = "enetpulse_ut")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime enetpulseUt;

  @Column(name = "lineups_confirmed")
  private int lineupsConfirmed;

  @Column(name = "started_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime startedAt;

  @Column(name = "minute")
  private int minute;

  @Column(name = "is_deleted")
  private int isDeleted;
}
