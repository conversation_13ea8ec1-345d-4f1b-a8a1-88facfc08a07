package com.fansunited.automation.core.resolver.hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@Data
@Builder
@EqualsAndHashCode
@AllArgsConstructor @NoArgsConstructor
@Entity
@Table(name = "event_player")
public class MatchPlayer {
  @Id
  private int id;

  @Column(name = "player_name")
  private String playerName;

  @Column(name = "position_x")
  private Byte positionX;

  @Column(name = "position_y")
  private Byte positionY;

  @Column(name = "shirt_number")
  private Short shirtNumber;

  @Column(name = "home_team")
  private boolean homeTeam;

  @Column(name = "updated_at")
  private Date updatedAt;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "player_id")
  private Player player;

  @Column(name = "event_id")
  private String eventId;

  private String type;
  private String subtype;
}
