package com.fansunited.automation.core.resolver.hibernate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JsonCompetitionContext {
    private String season_id;
    private String season_stage_id;
    private String competition_country;
    private boolean competition_season_stage_cup;
    private String competition_season_stage_name;
    private String competition_season_stage_round;
}
