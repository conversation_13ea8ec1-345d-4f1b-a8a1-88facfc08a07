package com.fansunited.automation.core.resolver.enums;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Getter
public enum TimelineMatchInputValues {

  /**
   * CORNERS_6225 = home team corners:6, home team extra time corners:2, away team corners:2, away team extra time corners:2
   * RCARD_1000 = home team red cards:1, home team extra time red cards:1, away team red cards:0, away team extra time red cards:0;
   * Timeline_6 = there are 6 timeline events for this match;
   * Goals_HT_31_FT_4112 =
   *        half time goal home team:3
   *        half time goal away team:1
   *        full time goal home team:4
   *        full time goal home extra time team:1
   *        full time goal away team:4
   *        full time goal away extra time team:1
   */
  CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010(
      6, 2, 2, 5,
      1, 0, 0, 0,
      3, 1,
      4, 1, 1, 2,
          1,1, 1,1
  );

  private int cornersHome;
  private int cornersHomeEt;
  private int cornersAway;
  private int cornersAwayEt;
  private int redCardsHome;
  private int redCardsHomeEt;
  private int redCardsAway;
  private int redCardsAwayEt;
  private int goalHTHomeTeam;
  private int goalHTAwayTeam;
  private int goalFTHomeTeam;
  private int goalETHomeTeam;
  private int goalFTAwayTeam;
  private int goalETAwayTeam;
  private int substitutionHomeTeam;
  private int substitutionHomeTeamEt;
  private int substitutionHomeAwayTeam;
  private int substitutionHomeAwayTeamEt;


  @Getter
  @AllArgsConstructor public
  enum GenerateMatchTimeLine {

    Goal_ValidPlayerId_10_TRUE_1(TimelineEventType.GOAL, VALID_PLAYER_ID, 10, true, 1,0),
    Goal_ValidPlayerId_15_FALSE_2(TimelineEventType.GOAL, VALID_PLAYER_ID, 15, false, 2,0),
    Goal_ValidPlayerId_20_TRUE_3(TimelineEventType.GOAL, VALID_PLAYER_ID, 20, true, 3,0),
    Goal_ValidPlayerId_51_TRUE_4(TimelineEventType.GOAL, VALID_PLAYER_ID, 51, true, 4,0),
    Goal_ValidPlayerId_55_TRUE_5(TimelineEventType.YELLOW_CARD, VALID_PLAYER_ID, 55, true, 5,0),
    Goal_ValidPlayerId_80_TRUE_6(TimelineEventType.RED_CARD, VALID_PLAYER_ID, 80, true, 6,0),
    Substitution_ValidPlayerId_50_TRUE_7(TimelineEventType.SUBSTITUTION, VALID_PLAYER_ID, 50, true, 7,0),
    Substitution_ValidPlayerId_60_TRUE_8(TimelineEventType.SUBSTITUTION, VALID_PLAYER_ID, 60, true, 8,0),
    Substitution_ValidPlayerId_70_TRUE_9(TimelineEventType.SUBSTITUTION, VALID_PLAYER_ID, 70, true, 9,0),
    Substitution_ValidPlayerId_90_TRUE_10(TimelineEventType.SUBSTITUTION, VALID_PLAYER_ID, 90, true, 10,0),
    Goal_ValidPlayerId_70_TRUE_11(TimelineEventType.GOAL, VALID_PLAYER_ID, 70, false, 11,0),;

    @Getter
    private TimelineEventType  timelineEventType;
    @Setter
    private String playerId;
    @Setter
    private int minute;
    private boolean isHomeTeam;
    private int sortOrder;
    private int fullTimeScore;


  }
}
