package com.fansunited.automation.core.resolver.hibernate;

import com.fansunited.automation.helpers.DateFormatter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Entity
@Table(name = "player_match_stats")
@JsonIgnoreProperties(ignoreUnknown = true)
public class PlayerMatchStats {

  @Column(name = "player_id", insertable = false, updatable = false)
  private String playerId;

  @EmbeddedId
  private PlayerStatsId id;

  @Column(name = "minutes_played")
  private Integer minutesPlayed;

  @Column(name = "yellow_cards")
  private Integer yellowCards;

  @Column(name = "red_cards")
  private Integer redCards;

  @Column(name = "goals")
  private Integer goals;

  @Column(name = "penalty_goals")
  private Integer penaltyGoals;

  @Column(name = "penalty_committed")
  private Integer penaltyCommitted;

  @Column(name = "penalty_won")
  private Integer penaltyWon;

  @Column(name = "penalty_missed")
  private Integer penaltyMissed;

  @Column(name = "own_goals")
  private Integer ownGoals;

  @Column(name = "assists")
  private Integer assists;

  @Column(name = "clean_sheets")
  private Integer cleanSheets;

  @Column(name = "shots")
  private Integer shots;

  @Column(name = "shots_on")
  private Integer shotsOn;

  @Column(name = "offsides")
  private Integer offsides;

  @Column(name = "fouls_committed")
  private Integer foulsCommitted;

  @Column(name = "tackles")
  private Integer tackles;

  @Column(name = "conceded_goals")
  private Integer concededGoals;

  @Column(name = "caught_ball")
  private Integer caughtBall;

  @Column(name = "saves")
  private Integer saves;

  @Column(name = "updated_at")
  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime updatedAt;
}
