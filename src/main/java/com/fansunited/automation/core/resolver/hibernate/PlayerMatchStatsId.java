package com.fansunited.automation.core.resolver.hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;

@Data
@Embeddable
public class PlayerMatchStatsId implements Serializable {
  @Column(name = "player_id")
  private String playerId;
  @Column(name = "event_id")
  private String eventId;
  public PlayerMatchStatsId() {
  }
  public PlayerMatchStatsId(String playerId, String eventId) {
    this.playerId = playerId;
    this.eventId = eventId;
  }
  public String getPlayerId() {
    return playerId;
  }
  public void setPlayerId(String playerId) {
    this.playerId = playerId;
  }
  public String getEventId() {
    return eventId;
  }
  public void setEventId(String eventId) {
    this.eventId = eventId;
  }
  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    PlayerMatchStatsId that = (PlayerMatchStatsId) o;
    return Objects.equals(playerId, that.playerId) &&
        Objects.equals(eventId, that.eventId);
  }
  @Override
  public int hashCode() {
    return Objects.hash(playerId, eventId);
  }
}
