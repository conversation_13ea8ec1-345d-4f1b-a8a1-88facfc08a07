package com.fansunited.automation.core.resolver.hibernate;

import com.fansunited.automation.helpers.DateFormatter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@Entity
@Table(name = "squad")
public class Squad implements Serializable {
  @Id
  private int id;

  @Column(name = "start_date")
  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO_DATE)
  private LocalDate startDate;

  @Column(name = "end_date")
  @JsonDeserialize(using = LocalDateDeserializer.class)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO_DATE)
  private LocalDate endDate;

  private boolean active;

  @Column(name = "shirt_number")
  private Short shirtNumber;

  private Boolean loan;

  @Column(name = "sort_number")
  private Long sortNumber;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "player_id")
  private Player player;

  @JsonIgnore
  @Column(name = "team_id")
  private String teamId;
}