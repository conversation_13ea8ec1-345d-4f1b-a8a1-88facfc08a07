package com.fansunited.automation.core.resolver;

import static com.fansunited.automation.helpers.DateFormatter.timeInUtc;

import com.fansunited.automation.core.apis.resolverapi.ResolverEndpoint;
import com.fansunited.automation.core.base.cleanup.MatchCleanup;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.core.resolver.hibernate.MatchPlayer;
import com.fansunited.automation.core.resolver.hibernate.MatchStatus;
import com.fansunited.automation.core.resolver.hibernate.PlayerMatchStats;
import com.fansunited.automation.utils.HibernateUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpStatus;
import org.hibernate.HibernateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CustomResolver {
  private static final Logger LOG = LoggerFactory.getLogger(CustomResolver.class);

  private CustomResolver() {}

  public static void openMatchesForPredictions(List<Match> matchList) {
    matchList.forEach(CustomResolver::openMatchForPredictions);
  }

  public static void openMatchForPredictions(Match match) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      session.persist(match);
      transaction.commit();
    }
    MatchCleanup.addAndMapItems(List.of(match));
  }

  public static void updateMatchToBeFinishedInThePast(String matchId, int minutesInThePast) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      var match = session.find(Match.class, matchId);
      match.setKickoffAt(timeInUtc(LocalDateTime.now().minusMinutes(minutesInThePast + 90L)));
      match.setFinishedAt(match.getKickoffAt().plusMinutes(90));
      match.setStatus(MatchGenerator.STATUS_FINISHED);
      session.persist(match);
      LOG.error(
          "updateMatchToBeFinishedInThePast: "
              + match.getKickoffAt().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
              + " / "
              + match.getFinishedAt().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
      transaction.commit();
    }
  }

  public static void updateMatchStats(PlayerMatchStats playerMatchStats) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      session.persist(playerMatchStats);
      session.find(PlayerMatchStats.class, playerMatchStats.getId());
      transaction.commit();
    }
  }

  public static void updateEventPlayer(Map<String, MatchPlayer> eventPlayer) {

    for (MatchPlayer player : eventPlayer.values()) {
      try (var session = HibernateUtils.getSessionFactory().openSession()) {
        var transaction = session.beginTransaction();
        session.persist(player);
        session.find(MatchPlayer.class, player.getId());
        transaction.commit();
      }
    }
  }

  public static void updateEventPlayer(MatchPlayer player) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      session.persist(player);
      session.find(MatchPlayer.class, player.getId());
      transaction.commit();
    } catch (HibernateException e) {
      throw new RuntimeException(
          "Database error while updating event player: " + player.getId(), e);
    } catch (Exception e) {
      throw new RuntimeException(
          "Unexpected error while updating event player: " + player.getId(), e);
    }
  }

  public static void updateMatchesToBeFinishedInThePast(
      List<Match> matchIds, int minutesInThePast) {
    matchIds.forEach(
        matchId -> updateMatchToBeFinishedInThePast(matchId.getId(), minutesInThePast));
  }

  public static void updateMatchIdsToBeFinishedInThePast(
      List<String> matchIds, int minutesInThePast) {
    matchIds.forEach(matchId -> updateMatchToBeFinishedInThePast(matchId, minutesInThePast));
  }

  public static void resolve(int calledTimes) throws InterruptedException {
    for (int i = 0; i < calledTimes + calledTimes; i++) {
      resolve();
    }
  }

  public static synchronized void resolve() throws InterruptedException {
    ResolverEndpoint.resolve().then().statusCode(HttpStatus.SC_OK);
    TimeUnit.MILLISECONDS.sleep(1000);
  }

  /**
   * This method updates the DataBase value of a match status column.
   *
   * @param matchId the id of the match which status column value will be updated
   * @param status the new value that will be set in the match status column
   */
  public static synchronized void updateMatchToAnotherStatus(String matchId, MatchStatus status) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      var match = session.find(Match.class, matchId);
      switch (status.getName()) {
        case "Postponed":
          match.setFinishedAt(null);
          match.setStatus(MatchGenerator.STATUS_POSTPONED);
          session.persist(match);
          transaction.commit();
          break;
        case "To Finish":
          match.setFinishedAt(null);
          match.setStatus(MatchGenerator.STATUS_TO_FINISH);
          session.persist(match);
          transaction.commit();
          break;
        case "Not started":
          match.setFinishedAt(null);
          match.setStatus(MatchGenerator.STATUS_NOT_STARTED);
          session.persist(match);
          transaction.commit();
          break;
        case "1st half":
          match.setFinishedAt(null);
          match.setStatus(MatchGenerator.STATUS_TO_LIVE);
          session.persist(match);
          transaction.commit();
          break;
        default:
          throw new IllegalArgumentException("Not recognized Match Status: " + status.getName());
      }
    }
  }

  public static synchronized void postponeAndMoveToPast(String matchId) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      var match = session.find(Match.class, matchId);
      match.setKickoffAt(timeInUtc(LocalDateTime.now().minusMinutes(360L)));
      match.setFinishedAt(null);
      match.setStatus(MatchGenerator.STATUS_POSTPONED);
      session.persist(match);
      transaction.commit();
    }
  }

  // todo: needs to be thread safe
  public static void updateMatchKickOffAndFinishDates(
      String matchId, LocalDateTime setKickOffAt, LocalDateTime setFinishedAt, MatchStatus status) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      var match = session.find(Match.class, matchId);
      match.setKickoffAt(setKickOffAt);
      match.setFinishedAt(setFinishedAt);
      match.setStatus(status);
      session.persist(match);
      LOG.error(
          "updateMatchToBeFinishedInThePast: "
              + match.getKickoffAt().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
              + " / "
              + match.getFinishedAt().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
      transaction.commit();
    }
  }

  public static synchronized void customGameResolve(String customGameType, String gameId)
      throws InterruptedException {
    ResolverEndpoint.customResolverResolve(customGameType, gameId)
        .then()
        .log()
        .all()
        .statusCode(HttpStatus.SC_OK);
    TimeUnit.MILLISECONDS.sleep(1000);
  }
}
