package com.fansunited.automation.core.base.discusionapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreatePostEndpoint.createPostForDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetPostByPostIdEndpoint.getSinglePost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.model.discussionapi.DiscussionType.COMMENT_SECTION;
import static com.fansunited.automation.model.discussionapi.DiscussionType.PRIVATE_LEAGUE;
import static com.fansunited.automation.model.discussionapi.ModerationType.USER;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint;
import com.fansunited.automation.core.apis.discussionapi.pinnedposts.DeletePinnedPostEndpoint;
import com.fansunited.automation.core.apis.discussionapi.pinnedposts.PinPostsEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import com.fansunited.automation.model.discussionapi.request.CreatePostRequest;
import com.fansunited.automation.model.discussionapi.response.DiscussionResponse;
import com.fansunited.automation.model.discussionapi.response.PostResponse;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import org.apache.http.HttpException;

public class DiscussionApiBaseTest extends AuthBase {

  public static DiscussionResponse createDiscussionForTests(boolean isPrivate)
      throws HttpException {
    Faker faker = new Faker();
    var createDiscussionRequest =
        CreateDiscussionRequest.builder()
            .id(faker.internet().uuid())
            .discussionType(isPrivate ? PRIVATE_LEAGUE : COMMENT_SECTION)
            .moderationType(USER)
            .discussionUrl(faker.lorem().word())
            .build();

    var createDiscussionResponse =
        CreateDiscussionEndpoint.createDiscussion(
            createDiscussionRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            null);

    return createDiscussionResponse.as(DiscussionResponse.class);
  }

  public static PostResponse createPostForDiscussionsTests() throws HttpException {

    var createPost = CreatePostRequest.builder().content(new Faker().internet().uuid()).build();

    var PostResponse =
        createPostForDiscussion(
            createPost,
            createDiscussionForTests(false).getData().getId(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    return PostResponse.as(PostResponse.class);
  }

  /**
   * The goal of this method is to pin posts for discussion.
   *
   * @param postIds List of post IDs to be added to pinned posts.
   * @return Response
   */
  public static Response pinPostForDiscussion(List<String> postIds) {
    String discussionId =
        getSinglePost(postIds.get(0)).getBody().jsonPath().get("data.discussion_id");

    return PinPostsEndpoint.pinPinnedPostsForDiscussion(postIds, discussionId);
  }

  /**
   * The goal of this method is to delete pinned posts from discussion.
   *
   * @param postIds List of post IDs to be deleted from pinned posts.
   * @return Response
   */
  public static Response deletePostsForDiscussion(List<String> postIds) {
    String discussionId =
        getSinglePost(postIds.get(0)).getBody().jsonPath().get("data.discussion_id");

    return DeletePinnedPostEndpoint.deletePinnedPostsForDiscussion(postIds, discussionId);
  }
}
