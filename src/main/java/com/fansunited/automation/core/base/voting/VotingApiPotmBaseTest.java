package com.fansunited.automation.core.base.voting;

import static com.fansunited.automation.core.apis.voting.potm.VoteForPotmEndpoint.putVoteForPotm;
import static com.fansunited.automation.core.resolver.MatchTimelineGenerator.generateMatchPlayer;
import static com.fansunited.automation.core.resolver.MatchTimelineGenerator.generateMatchPlayerNew;
import static com.fansunited.automation.core.resolver.Resolver.updateEventPlayer;
import static com.fansunited.automation.helpers.GenerateMatchTimeLineSubstitution.generateTimelineInputValues;
import static com.fansunited.automation.helpers.GenerateMatchTimeLineSubstitution.setupMatchTimelineForPotm;
import static com.fansunited.automation.helpers.PlayerMatchStatsGenerator.generatePlayerId;

import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.model.footballapi.players.enums.PlayerPosition;
import com.fansunited.automation.model.voting.potm.OwnVotesResponse;
import com.fansunited.automation.model.voting.potm.VoteForPotmRequest;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class VotingApiPotmBaseTest extends AuthBase {

  private OwnVotesResponse addPlayersAndVoteForMatch(Match match, String email, String type)
      throws HttpException {

    var playerId = generatePlayerId(match, PlayerPosition.DEFENDER);
    var matchPlayersMap = generateMatchPlayer(match, playerId, PlayerPosition.DEFENDER);

    var timelineInputValues = generateTimelineInputValues(List.of(playerId));
    var relatedPlayerId = generatePlayerId(match, PlayerPosition.MIDFIELDER);
    setupMatchTimelineForPotm(match, relatedPlayerId, timelineInputValues);

    // Set type for all players in the lineup
    matchPlayersMap.values().forEach(player -> player.setType(type));

    Resolver.openMatchForPredictions(match);
    match.setPlayers(new ArrayList<>(matchPlayersMap.values()));

    updateEventPlayer(matchPlayersMap);
    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    String voteForPlayer;
    if (type.equals("substitution")) {
      voteForPlayer = relatedPlayerId;
    } else {
      voteForPlayer = playerId;
    }
    var response = putVoteForPotm(match.getId(), new VoteForPotmRequest(voteForPlayer), email);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    return new OwnVotesResponse(match.getId(), voteForPlayer, null);
  }

  public List<OwnVotesResponse> addPlayersAndVoteForMatchesNew(
      List<Match> matches, String email, String type) throws HttpException {

    List<OwnVotesResponse> matchPlayers = new ArrayList<>();
    for (Match match : matches) {
      matchPlayers.add(addPlayerAndVote(match, email, type));
    }
    return matchPlayers;
  }

  private OwnVotesResponse addPlayerAndVote(Match match, String email, String type)
      throws HttpException {

    var playerId = generatePlayerId(match, PlayerPosition.DEFENDER);
    var matchPlayer = generateMatchPlayerNew(match, playerId);
    var timelineInputValues = generateTimelineInputValues(List.of(playerId));
    var relatedPlayerId = generatePlayerId(match, PlayerPosition.MIDFIELDER);

    setupMatchTimelineForPotm(match, relatedPlayerId, timelineInputValues);

    // Set type for lineups
    matchPlayer.setType(type);

    Resolver.openMatchForPredictions(match);
    match.setPlayers(List.of(matchPlayer));

    updateEventPlayer(matchPlayer);
    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    String voteForPlayer;
    if (type.equals("substitution")) {
      voteForPlayer = relatedPlayerId;
    } else {
      voteForPlayer = playerId;
    }
    var response = putVoteForPotm(match.getId(), new VoteForPotmRequest(voteForPlayer), email);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    return new OwnVotesResponse(match.getId(), voteForPlayer, null);
  }
}
