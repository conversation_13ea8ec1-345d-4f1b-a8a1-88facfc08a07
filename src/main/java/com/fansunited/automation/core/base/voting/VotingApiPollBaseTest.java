package com.fansunited.automation.core.base.voting;

import static com.fansunited.automation.constants.ApiConstants.DEFAULT_PAGE_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.CreateVotingEndpoint.createPoll;
import static com.fansunited.automation.core.apis.voting.poll.GetPollsEndpoint.getPolls;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.validators.VotingApiValidator.validateVotingResponseScOk;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import com.fansunited.automation.model.voting.poll.response.PollInstance;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class VotingApiPollBaseTest extends AuthBase {
  protected static final String PREMIER_LEAGUE = "Premier League";
  protected static final String TOP_SCORERS = "Top scorers";

  public PollRequest request = PollRequest.createPollRequestWithRandomData();

  protected Response createPollForTest() {
    Response response;
    try {
      response =
          createPoll(
              request,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON,
              FANS_UNITED_CLIENTS,
              null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }

    validateVotingResponseScOk(response);

    return response;
  }

  public static Response createCustomPoll(PollRequest pollRequest) throws HttpException {

    var response =
        createPoll(
            pollRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    return response;
  }

  /**
   * This method retrieves all polls from /v1/polls filtered by the given fields and returns them as
   * a list.
   *
   * @return List<PollInstance>
   * @throws HttpException exception
   */
  protected List<PollInstance> getAllPollsFilteredByFields(
      int limit,
      int minVotes,
      String startAfterId,
      List<String> entityIds,
      List<String> flags,
      ApiConstants.SortOrder sortOrder,
      CommonStatus status)
      throws HttpException {

    List<PollInstance> polls = new ArrayList<>();
    do {
      var response =
          getPolls(
              startAfterId,
              entityIds,
              flags,
              limit,
              minVotes,
              sortOrder,
              status,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON,
              FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
              getCurrentTestUser().getEmail());

      currentTestResponse.set(response);

      response.then().assertThat().statusCode(HttpStatus.SC_OK).body("data", is(notNullValue()));

      polls.addAll(response.jsonPath().getList("data", PollInstance.class));

      if (limit == DEFAULT_PAGE_LIMIT){
        startAfterId = response.jsonPath().getString("meta.pagination.next_page_starts_after");
      }

    } while (startAfterId != null);
    return polls;
  }

  public static void createCustomPolls(int count, PollRequest request) {

    for (int i = 0; i < count; i++) {
      try {
        createCustomPoll(request);
      } catch (HttpException e) {
        throw new RuntimeException(e);
      }
    }
 }
}