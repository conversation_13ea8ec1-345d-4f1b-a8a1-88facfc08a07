package com.fansunited.automation.core.base.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.PLATFORM_OPERATOR;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_PREDICTOR;
import static com.fansunited.automation.constants.JsonSchemasPath.ClientsApi.Endpoints.Clients.GET_CLIENTS_BY_ID_PREDICTOR_FEATURES_BASIC_SCHEMA;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.clientapi.ManageListsEndpoint.deleteClientListById;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.apis.clientapi.ManageListsEndpoint;
import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.EntityTypes;
import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.ListTypes;
import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.SortBy;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.features.response.FeaturesResponse;
import com.fansunited.automation.model.clientapi.features.response.PredictorFeature;
import com.fansunited.automation.model.clientapi.features.response.SuccessRateScope;
import com.fansunited.automation.model.clientapi.features.response.managelists.CreateListRequest;
import com.fansunited.automation.model.clientapi.features.response.managelists.Entities;
import com.fansunited.automation.model.clientapi.features.response.managelists.Entity;
import com.fansunited.automation.model.clientapi.features.response.managelists.Sort;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.userdata.UserData;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.AfterAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ClientApiBaseTest extends AuthBase {

  private static final Logger LOG = LoggerFactory.getLogger(ClientApiBaseTest.class);

  @AfterAll
  public static void afterAll()
      throws HttpException, IOException, ExecutionException, InterruptedException {
    LOG.info("Resetting features to default");
    updatePredictorFeatureToDefault();
    resetLoyalty();
  }

  private static void updatePredictorFeatureToDefault() {
    try {
      var ret = ClientFeaturesEndpoint.updateFeatures(
          FANS_UNITED_CLIENTS,
          PLATFORM_OPERATOR,
          PredictorFeature.builder()
              .enabled(true)
              .successRateScopes(new SuccessRateScope())
              .userDataConfig(UserData.builder().storeIp("true").build())
              .build(),
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          FEATURES_PREDICTOR);

      LOG.info("Features updated: {}", ret.prettyPrint());

    } catch (HttpException e) {
      e.printStackTrace();
    }
  }

  public static void resetLoyalty()
      throws HttpException, IOException, ExecutionException, InterruptedException {
    FirebaseHelper.deleteLoyaltyDocument(FANS_UNITED_PROFILE);
    ClientFeaturesEndpoint.getClientsByIdFeatures(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS).as(
        FeaturesResponse.class);
  }

  private static CreateListRequest createCustomListRequestWithRandomValues() {
    var request = createDynamicListRequestWithRandomValues();
    request.getEntities().getList().forEach(e -> e.setEntityId(UUID.randomUUID().toString()));
    request.getEntities().setSort(null);
    request.setType(ListTypes.CUSTOM);
    return request;
  }

  private static CreateListRequest createDynamicListRequestWithRandomValues() {
    var faker = new Faker();
    List<Entity> entitiesList = new ArrayList<>();
    for (var type : EntityTypes.values()) {
      entitiesList.add(Entity.builder().entityType(type).entityId(null).build());
    }

    return CreateListRequest.builder()
        .name(faker.lorem().sentence(2))
        .type(ListTypes.DYNAMIC)
        .entities(
            Entities.builder()
                .list(entitiesList)
                .sort(
                    Sort.builder()
                        .sortOrder(ApiConstants.SortOrder.ASC)
                        .sortBy(SortBy.UPDATED_AT)
                        .build())
                .build())
        .build();
  }

  /**
   * Creates a CreateListRequest object based on the provided list type.
   *
   * @param listType The type of list that determines what kind of request should be created
   * @return CreateListRequest object configured according to the provided list type
   */
  public static CreateListRequest createRequestByListType(ListTypes listType) {

    if (listType.equals(ListTypes.DYNAMIC)) {
      return createDynamicListRequestWithRandomValues();
    }
    return createCustomListRequestWithRandomValues();
  }

  /**
   * Creates a new list based on the specified list type.
   *
   * @param listTypes The type of list to be created
   * @return Response object containing the result of the list creation operation
   * @throws Exception if an error occurs during the list creation process
   */
  protected static Response createList(ListTypes listTypes) throws Exception {
    switch (listTypes) {
      case CUSTOM -> {
        return ManageListsEndpoint.createList(
            true,
            FANS_UNITED_CLIENTS,
            ADMIN_USER,
            createCustomListRequestWithRandomValues(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
      }
      case DYNAMIC -> {
        return ManageListsEndpoint.createList(
            true,
            FANS_UNITED_CLIENTS,
            ADMIN_USER,
            createDynamicListRequestWithRandomValues(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
      }
      default ->
          throw new IllegalArgumentException(
              "Unsupported list type provided: " + PredictionMarket.getValidMarkets());
    }
  }

  /**
   * Deletes an existing list identified by the provided list ID.
   *
   * @param listId Unique identifier of the list to be deleted
   * @return Response object containing the result of the delete operation
   * @throws Exception if an error occurs during the deletion process
   */
  protected static Response deleteList(String listId) throws Exception {
    return deleteClientListById(
        listId,
        true,
        FANS_UNITED_CLIENTS,
        ADMIN_USER,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }
}
