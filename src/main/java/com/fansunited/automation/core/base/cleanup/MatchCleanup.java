package com.fansunited.automation.core.base.cleanup;

import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.utils.HibernateUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MatchCleanup {
  private static List<String> cleanUpList = new ArrayList<>();
  private static final Logger LOG = LoggerFactory.getLogger(MatchCleanup.class);

  public static void addAndMapItems(List<Match> items) {
    var toAdd = items.stream().map(Match::getId).toList();
    synchronized (MatchCleanup.class) {
      cleanUpList.addAll(toAdd);
    }
  }

  public static void addItems(List<String> items) {
    synchronized (MatchCleanup.class) {
      cleanUpList.addAll(items);
    }
  }

  public static void cleanUp() {
    LOG.info("Starting cleanup of matches. Total matches to clean: {}", cleanUpList.size());
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();

      for (var matchId : cleanUpList) {
        try {
          LOG.debug("Attempting to remove match with ID: {}", matchId);
          var match = session.find(Match.class, matchId);
          if (match != null) {
            session.remove(match);
            LOG.debug("Successfully removed match with ID: {}", matchId);
          } else {
            LOG.warn("Match with ID: {} not found. Skipping removal.", matchId);
          }
        } catch (Exception e) {
          LOG.error("Failed to remove match with ID: {}. Continuing with the next item.", matchId,
              e);
        }
      }

      try {
        transaction.commit();
        LOG.info("Cleanup completed successfully.");
      } catch (Exception e) {
        LOG.error("Failed to commit the transaction during cleanup.", e);
        transaction.rollback();
        LOG.info("Transaction rolled back.");
      }
    } catch (Exception e) {
      LOG.error("Unexpected error occurred during cleanup.", e);
    } finally {
      synchronized (MatchCleanup.class) {
        cleanUpList.clear();
      }
      LOG.info("Cleanup list cleared.");
    }
  }
}
