package com.fansunited.automation.core.base.leaguesapi;

import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToIsoDate;
import static com.fansunited.automation.validators.LeaguesApiValidator.createPrivateLeagueRequest;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint;
import com.fansunited.automation.core.apis.leagueapi.membership.InviteEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fansunited.automation.model.leaguesapi.request.CreateLeagueRequest;
import com.fansunited.automation.model.leaguesapi.request.membership.BaseMembershipRequest;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import lombok.SneakyThrows;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;

public class LeagueBaseTest extends AuthBase {

  protected static CreateLeagueRequest leagueRequest;
  protected static UserRecord creator;
  protected static UserRecord admin;
  protected static Faker faker;
  protected LeagueData createLeagueResponse;
  protected String leagueId;

  @BeforeAll
  public static void createLeagueRequest()
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException {
    creator = createUser();
    admin = createUser();
    var template = LeagueBaseTest.createTemplateForLeague();
    var scoringStartAt = convertLocalDateToIsoDate(LocalDate.now());
    faker = new Faker();

    leagueRequest =
        createPrivateLeagueRequest(
            faker.funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "),
            LeagueType.PRIVATE,
            template.getId(),
            faker.lorem().paragraph(1),
            null,
            new ArrayList<>() {
              {
                add(admin.getUid());
              }
            },
            null,
            null,
            null,
            true,
            scoringStartAt);
  }

  @BeforeEach
  public void setUp() throws HttpException {

    createLeagueResponse =
        CreateLeagueEndpoint.createLeague(
                FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                AuthConstants.ENDPOINTS_API_KEY,
                CLIENT_AUTOMATION_ID,
                ContentType.JSON,
                null,
                leagueRequest,
                creator.getEmail())
            .as(LeagueData.class);
    leagueId = createLeagueResponse.getLeague().getId();
  }

  @SneakyThrows
  public static TemplateResponse createTemplateForLeague() {
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .build();
    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);
    return createTemplateResponse.as(TemplateResponse.class);
  }

  public static CreateLeagueRequest createLeagueWithTypeRequest(
      LeagueType leagueType, CreateLeagueRequest request) {
    request.setType(leagueType.getValue());
    return request;
  }

  public static Response inviteUsers(List<String> membersIds, String leagueId, String email) {
    var inviteMembershipRequest = BaseMembershipRequest.builder().profiles(membersIds).build();
    try {
      return InviteEndpoint.inviteUsersToLeague(leagueId, inviteMembershipRequest, email);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static Response inviteUser(UserRecord userRecord, String leagueId, String email) {
    var inviteMembershipRequest =
        BaseMembershipRequest.builder().profiles(List.of(userRecord.getUid())).build();
    try {
      return InviteEndpoint.inviteUsersToLeague(
          leagueId, inviteMembershipRequest, email);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
