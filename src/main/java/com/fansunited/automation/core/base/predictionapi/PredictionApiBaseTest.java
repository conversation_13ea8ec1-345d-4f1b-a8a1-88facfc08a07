package com.fansunited.automation.core.base.predictionapi;

  import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.TOP_X;
import static com.fansunited.automation.validators.PredictionApiValidator.validateCreatePredictionResponse;

import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.predictionapi.contest.CreateWinnersRequest;
import com.fansunited.automation.model.predictionapi.contest.UserListToSetWinners;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.github.javafaker.Faker;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeAll;

public class PredictionApiBaseTest extends AuthBase {
  @BeforeAll
  public static void beforeAll() {
    var matchList = MatchGenerator.generateMatchesInFutureInvalidCompetition(6, 16);
    Resolver.openMatchesForPredictions(matchList);
  }

  /**
   * Sets up a user as a winner for a game with predictions.
   *
   * <p>This method creates a game, generates predictions, inserts ranking data into BigQuery, syncs
   * the data with the user ranking materialized view, and finally sets the user as a winner in the
   * winners collection.
   *
   * @param userRecord The user record to set as a winner
   * @return The ID of the created game
   * @throws HttpException If an HTTP error occurs during API calls
   */
  public static String setWinnerWithPrediction(UserRecord userRecord) throws HttpException {
    var contestType = "GAME";
    var faker = new Faker();
    var gameInstance = createGameWithPredictions(userRecord);
    var gameId = createGameWithPredictions(userRecord).getId();

    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileId = userRecord.getUid();

    InsertBigQData.insertSingleRankEventWithFinishDate(
        LocalDateTime.now(),
        profileId,
        PredictionMarket.CORRECT_SCORE,
        TOP_X,
        30,
        gameId,
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate,
        LocalDateTime.now()
            .plusDays(1)
            .atOffset(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss 'UTC'")));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(
            TestSynchronizationHelper.USER_RANKING_GAME_MV, gameId);

    UserListToSetWinners userWinnersRequest =
        UserListToSetWinners.builder()
            .profile_id(profileId)
            .position("1")
            .note(faker.lorem().word())
            .tags(List.of(faker.lorem().word(), faker.lorem().word(), faker.lorem().word()))
            .build();

    var request =
        CreateWinnersRequest.builder()
            .contest_type(contestType)
            .contest_id(gameId)
            .description(faker.lorem().sentence(3))
            .user_list(List.of(userWinnersRequest))
            .build();

    WinnersEndpoint.setWinnersByContestType(
            request,
            CLIENT_AUTOMATION_ID,
            ENDPOINTS_API_KEY,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER,
            ContentType.JSON)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    return gameId;
  }

  /**
   * Adds a user to the excluded profile IDs list for a game with predictions. This method creates a
   *
   * <p>game with predictions and then updates the game to add the user's profile ID to the excluded
   * profile IDs list.
   *
   * @param userRecord The user record to exclude from the game
   * @return The ID of the created game
   * @throws HttpException If an HTTP error occurs during API calls
   */
  public static String setExcludedProfileIdsWithPrediction(UserRecord userRecord) throws HttpException {

    var gameId = createGameWithPredictions(userRecord).getId();

    var updatedGameRequest =
        UpdateGameRequest.builder().excludedProfileIds(List.of(userRecord.getUid())).build();

    updateGame(gameId, updatedGameRequest).then().assertThat().statusCode(HttpStatus.SC_OK);

    return gameId;
  }

  /**
   * Creates a game instance with predictions for the specified user.
   *
   * <p>This helper method generates match data, sets up match results, creates a game instance, and
   * generates predictions for all matches in the game.
   *
   * @param userRecord The user record for which to create predictions
   * @return The created game instance
   * @throws HttpException If an HTTP error occurs during API calls
   */
  public static GameInstance createGameWithPredictions(UserRecord userRecord) throws HttpException {
    var matchList = MatchGenerator.generateMatches(3, false);
    var matchesIdList = matchList.stream().map(Match::getId).toList();

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchesIdList, TOP_X, GameStatus.OPEN, ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);

    createPredictionsForMatches(gameInstance.getId(), matchesIdList, userRecord);

    return gameInstance;
  }

  /**
   * Creates predictions for a list of matches in a game for a specific user.
   *
   * <p>This helper method generates prediction fixtures for the correct score market for all
   * matches in the list, submits the prediction request, and validates the response.
   *
   * @param gameId The ID of the game for which to create predictions
   * @param matchesIdList List of match IDs to predict
   * @param userRecord The user record for which to create predictions
   * @throws HttpException If an HTTP error occurs during API calls
   */
  private static void createPredictionsForMatches(
      String gameId, List<String> matchesIdList, UserRecord userRecord) throws HttpException {
    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId ->
            predictions.addAll(
                generateValidSinglePredictionFixture(
                    PredictionMarket.CORRECT_SCORE, matchId, null)));

    var createPredictionRequest =
        CreatePredictionRequest.builder().fixtures(predictions).gameInstanceId(gameId).build();

    var predictionResponse =
        createPrediction(
            createPredictionRequest,
            null,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            userRecord.getEmail(),
            CLIENT_AUTOMATION_ID,
            ENDPOINTS_API_KEY,
            ContentType.JSON);
    validateCreatePredictionResponse(
        predictionResponse, createPredictionRequest, false, userRecord);
  }
}
