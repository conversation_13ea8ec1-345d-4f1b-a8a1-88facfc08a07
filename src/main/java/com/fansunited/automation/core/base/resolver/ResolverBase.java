package com.fansunited.automation.core.base.resolver;

import com.fansunited.automation.core.base.AuthBase;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;

public class ResolverBase extends AuthBase {
  //todo: make this private when ready with all tests
  public static List<String> cleanUpMatchIdList = new ArrayList<>();
  private final Object lock = new Object();

  @BeforeAll
  public static void init() {
  }

  @AfterAll
  public static void cleanUp() {
  }

  /**
   * The method cleans a list of matched from the DB.
   *
   * @param cleanUpMatchIdList the list of matches to be removed from the db
   */
  public static void cleanUp(List<String> cleanUpMatchIdList) {
  }
}
