package com.fansunited.automation.core.base;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.PROFILE_AVATAR_URL;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AuthBase extends BaseTest {

  private static final ThreadLocal<UserRecord> currentTestUser = new ThreadLocal<>();
  private static final Object lock = new Object();
  protected static final String EMPTY_LIST_MESSAGE = "Expected the list of actual methods to be non-empty, but it was empty or null.";
  protected static final String METHODS_MISMATCH_MESSAGE = "Not all actual methods are in the expected methods list.";
  private static final Logger LOG = LoggerFactory.getLogger(AuthBase.class);

  @BeforeEach
  public void createUserForNextTest()
      throws IOException, FirebaseAuthException, InterruptedException, ExecutionException {
    currentTestUser.set(createUser());
  }

  protected static boolean isUseStageEnvironment() {
    boolean condition = ConfigReader.getInstance()
        .getProperty(ConfigReader.PropertyKey.USE_FIREBASE_EMULATOR)
        .equals("true");
    if (condition) {
      LOG.info("Skipping this test because it does not need to be run in the local environment!");
    }
    return !condition;
  }

  public static UserRecord createUser()
      throws IOException, FirebaseAuthException, InterruptedException, ExecutionException {
    return createUser(null, new Faker().name().fullName(),
            PROFILE_AVATAR_URL,new Faker().funnyName().toString());
  }

  public static List<UserRecord> createUsers(int count)
      throws IOException, FirebaseAuthException, InterruptedException, ExecutionException {
    List<UserRecord> userList = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      userList.add(createUser());
    }
    return userList;
  }

  protected static UserRecord createUser(String displayName)
      throws IOException, FirebaseAuthException, InterruptedException, ExecutionException {

    return createUser(null, displayName,
            PROFILE_AVATAR_URL,new Faker().funnyName().toString());
  }

  public static UserRecord createUser(String email, String displayName, String avatarUrl, String nikName)
      throws FirebaseAuthException, IOException, InterruptedException, ExecutionException {
    Faker faker = new Faker();
    UserRecord.CreateRequest request = new UserRecord.CreateRequest()
        .setPassword(AuthConstants.DEFAULT_USER_PASS)
        .setPhoneNumber("+1" + faker.phoneNumber().cellPhone())
        .setDisabled(false)
        .setEmailVerified(false);

    if (avatarUrl != null) {
      request.setPhotoUrl(avatarUrl);
    }

    if (nikName != null) {
      request.setDisplayName(nikName);
    }

    if (displayName != null) {
      request.setDisplayName(displayName);
    }

    if (email != null) {
      request.setEmail(email);
    } else {
      var customGeneratedEmail = faker.internet().emailAddress();
      var emailParts = customGeneratedEmail.split("@");
      customGeneratedEmail = emailParts[0] + System.currentTimeMillis() + "@" + emailParts[1];
      request.setEmail(customGeneratedEmail);
    }

    return create(request);
  }

  public static UserRecord createUserWithoutNameAndEmail()
      throws IOException, InterruptedException {
    Faker faker = new Faker();
    UserRecord.CreateRequest request = new UserRecord.CreateRequest()
        .setPassword(AuthConstants.DEFAULT_USER_PASS)
        .setPhoneNumber("+1" + faker.phoneNumber().cellPhone())
        .setPhotoUrl(PROFILE_AVATAR_URL)
        .setDisabled(false)
        .setEmailVerified(false);

    return create(request);
  }

  private static UserRecord create(UserRecord.CreateRequest request)
      throws IOException, InterruptedException {
    UserRecord userRecord;
    try {
      // Emulator fails to create users if not synchronized
      synchronized (lock) {
        userRecord = FirebaseHelper.getFireBaseAuthInstance(
                FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE)
            .createUser(request);

        var userDocument = FirebaseHelper.getFirestoreCollection(
                FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, FirebaseHelper.PROFILE_COLLECTION)
            .document(userRecord.getUid());

        // Retry logic with capped backoff
        boolean exists = retryWithBackoff(() -> {
          try {
            return userDocument.get().get().exists();
          } catch (ExecutionException | InterruptedException e) {
            // Handle exceptions during Firestore fetch
            LOG.error("Error checking document existence", e);
            Thread.currentThread().interrupt(); // Preserve interrupt status
            return false; // Retry on failure
          }
        }, 60_000, 100);

        if (!exists) {
          throw new RuntimeException("User was NOT created in Firestore!");
        }
        return userRecord;
      }
    } catch (FirebaseAuthException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Retry logic with capped backoff.
   *
   * @param condition      The condition to check.
   * @param maxWaitMs      Maximum total wait time in milliseconds.
   * @param initialDelayMs Initial delay between retries in milliseconds.
   * @return True if the condition is met, otherwise false after max wait time.
   */
  private static boolean retryWithBackoff(Supplier<Boolean> condition, long maxWaitMs,
      long initialDelayMs)
      throws InterruptedException {
    long totalWait = 0;
    long delay = initialDelayMs;

    while (totalWait < maxWaitMs) {
      if (condition.get()) {
        return true;
      }
      TimeUnit.MILLISECONDS.sleep(delay);
      totalWait += delay;
      // Exponential backoff with cap at 1 second
      delay = Math.min(delay * 2, 1000);
    }
    return false;
  }

  public static UserRecord getCurrentTestUser() {
    if (currentTestUser.get() == null) {
      throw new RuntimeException(
          "Test class must extend AuthBase.class!");
    }
    return currentTestUser.get();
  }

  public static Response createStaffUser(){
    Faker faker = new Faker();
    var createStaffRequest =
        CreateStaffMember.builder()
            .email(faker.internet().emailAddress())
            .name(faker.name().firstName())
            .pass(AuthConstants.DEFAULT_USER_PASS)
            .roles(List.of("client_billing_manager"))
            .build();

      try {
          return StaffMemberEndpoint.createStaff(
                  FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
                  createStaffRequest,
                  CLIENT_AUTOMATION_ID);

      } catch (HttpException e) {
          throw new RuntimeException(e);
      }
  }

  public static Response createAdminUser() {
    Faker faker = new Faker();
    var createStaffRequest =
        CreateStaffMember.builder()
            .email(faker.internet().emailAddress())
            .name(faker.name().firstName())
            .pass(AuthConstants.DEFAULT_USER_PASS)
            .roles(List.of("client_admin"))
            .build();

    try {
      return StaffMemberEndpoint.createStaff(
          FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
          createStaffRequest,
          CLIENT_AUTOMATION_ID);

    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static UserRecord createUser(UserRecord.CreateRequest request)
      throws IOException, InterruptedException {
    return create(request);
  }
}
