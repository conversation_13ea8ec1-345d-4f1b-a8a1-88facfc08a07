package com.fansunited.automation.core.base.predictionapi;

import com.fansunited.automation.core.resolver.hibernate.MatchPlayer;
import com.fansunited.automation.core.resolver.hibernate.MatchTimeline;
import com.fansunited.automation.core.resolver.hibernate.PlayerMatchStats;
import com.fansunited.automation.utils.HibernateUtils;

public class FantasyCleanUpData {
  private FantasyCleanUpData() {}

  public static void cleanUpDataFromPlayerMatchStats(PlayerMatchStats playerMatchStats) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();
      var playerStats = session.find(PlayerMatchStats.class, playerMatchStats.getId());
      session.remove(playerStats);
      transaction.commit();
    }
  }

  public static void cleanUpDataFromEventPlayer(String eventId) {
    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();

      var query =
          session.createQuery("FROM MatchPlayer WHERE eventId = :eventId", MatchPlayer.class);
      query.setParameter("eventId", eventId);

      var playersToDelete = query.getResultList();
      playersToDelete.forEach(session::remove);

      transaction.commit();
    }
  }

  public static void cleanUpDataFromEventTimeline(String eventId) {

    try (var session = HibernateUtils.getSessionFactory().openSession()) {
      var transaction = session.beginTransaction();

      var query =
              session.createQuery("FROM MatchTimeline WHERE eventId = :eventId", MatchTimeline.class);
      query.setParameter("eventId", eventId);

      var eventToDelete = query.getResultList();
      eventToDelete.forEach(session::remove);

      transaction.commit();
    }
  }
}
