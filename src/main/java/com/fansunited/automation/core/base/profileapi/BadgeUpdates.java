package com.fansunited.automation.core.base.profileapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.core.apis.clientapi.ClientBadgesUpdateEndpoint.updateBadges;

import com.fansunited.automation.core.apis.loyaltyapi.enums.FeatureType;
import com.fansunited.automation.model.clientapi.features.response.Badge;
import com.fansunited.automation.model.clientapi.features.response.BadgeRewards;
import com.fansunited.automation.model.loyaltyapi.activity.request.BadgeRequirement;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.HttpStatus;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BadgeUpdates {

  private final String PREDICTOR_NEWBIE_ID = "predictor_newbie";
  private final String PREDICTOR_TOP_ID = "predictor_top";
  private final String PREDICTOR_GURU_ID = "predictor_guru";
  private final String PREDICTOR_TEAM_ID = "predictor_team";
  private final String PREDICTOR_COMPETITION_ID = "predictor_competition";
  private final int CORRECT_PREDICTIONS_NEWBIE = 1;
  private final int CORRECT_PREDICTIONS_TOP = 1;
  private final int CORRECT_PREDICTIONS_GURU = 1;
  private final int CORRECT_PREDICTIONS_TEAM = 0;
  private final int PREDICTIONS_MADE_TEAM = 10;
  private final int POINTS_TEAM = 1;
  private final int CORRECT_PREDICTIONS_COMPETITION = 0;
  private final int PREDICTIONS_MADE_COMPETITION = 0;
  private final int POINTS_COMPETITION = 0;
  private Integer correctPredictions;
  private Integer predictionsMade;
  private Integer points;
  private Integer gameParticipationCount;
  private String entityId;
  private String entityType;
  private String badgeId;
  private boolean badgeIsEnabled;
  private BadgeRequirement badgeRequirement;

  /**
   * This method updates badges by given BadgeRewards
   *
   * @param badgeRewards BadgeRewards
   */
  public void resetBadges(BadgeRewards badgeRewards) {
    var response = updateBadges(ADMIN_USER, badgeRewards);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
  }

  /**
   * This method updates only the Predictor badges
   *
   * @param badgeRewards BadgeRewards
   */
  public void resetPredictorBadges(BadgeRewards badgeRewards) {
    var bodyPredictorBadges = BadgeRewards.builder().predictor(badgeRewards.getPredictor()).build();

    var response = updateBadges(ADMIN_USER, bodyPredictorBadges);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
  }

  /**
   * This method updates only the TOP_X badges
   *
   * @param badgeRewards BadgeRewards
   */
  public void resetTopXBadges(BadgeRewards badgeRewards) {
    var bodyTopXBadges = BadgeRewards.builder().topX(badgeRewards.getTopX()).build();

    var response = updateBadges(ADMIN_USER, bodyTopXBadges);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
  }

  /**
   * This method updates only the Football badges
   *
   * @param badgeRewards BadgeRewards
   */
  public void resetFootballBadges(BadgeRewards badgeRewards) {
    var bodyFeaturePredictor = BadgeRewards.builder().football(badgeRewards.getFootball()).build();

    var response = updateBadges(ADMIN_USER, bodyFeaturePredictor);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
  }

  private Badge updateBadge() {
    if (gameParticipationCount == null) {
      gameParticipationCount = 0;
    }

    if (badgeRequirement == null) {
      badgeRequirement = new BadgeRequirement();
      badgeRequirement.setCorrectPredictions(correctPredictions);
      badgeRequirement.setPredictionsMade(predictionsMade);
      badgeRequirement.setPoints(points);
      badgeRequirement.setGameParticipationCount(gameParticipationCount);
      badgeRequirement.setEntityId(entityId);
      badgeRequirement.setEntityType(entityType);
    }

    Badge predictorBadge = new Badge();
    predictorBadge.setId(badgeId);
    predictorBadge.setEnabled(isBadgeIsEnabled());
    predictorBadge.setRequirements(badgeRequirement);

    return predictorBadge;
  }

  private Badge getPredictionNewbieBadge(int correctPredictions) {
    BadgeRequirement badgeRequirementNewbie = new BadgeRequirement();
    badgeRequirementNewbie.setCorrectPredictions(correctPredictions);

    Badge predictorNewbieBadge = new Badge();
    predictorNewbieBadge.setId(PREDICTOR_NEWBIE_ID);
    predictorNewbieBadge.setEnabled(true);
    predictorNewbieBadge.setRequirements(badgeRequirementNewbie);
    predictorNewbieBadge.setLabel("Newbie Predictor");
    predictorNewbieBadge.setAssets(null);
    predictorNewbieBadge.setDescription(null);
    predictorNewbieBadge.setFlags(null);

    return predictorNewbieBadge;
  }

  private Badge getPredictionTopBadge(int correctPredictions) {
    BadgeRequirement badgeRequirementPredictorTop = new BadgeRequirement();
    badgeRequirementPredictorTop.setCorrectPredictions(correctPredictions);

    Badge predictorTopBadge = new Badge();
    predictorTopBadge.setId(PREDICTOR_TOP_ID);
    predictorTopBadge.setEnabled(true);
    predictorTopBadge.setRequirements(badgeRequirementPredictorTop);
    predictorTopBadge.setLabel("Top X Regular");
    predictorTopBadge.setAssets(null);
    predictorTopBadge.setDescription(null);
    predictorTopBadge.setFlags(null);

    return predictorTopBadge;
  }

  private Badge getPredictionMatchQuizBadge(int correctPredictions) {
    BadgeRequirement badgeRequirementMatchQuiz = new BadgeRequirement();
    badgeRequirementMatchQuiz.setCorrectPredictions(correctPredictions);

    Badge predictorMatchQuizBadge = new Badge();
    predictorMatchQuizBadge.setId(PREDICTOR_GURU_ID);
    predictorMatchQuizBadge.setEnabled(true);
    predictorMatchQuizBadge.setRequirements(badgeRequirementMatchQuiz);
    predictorMatchQuizBadge.setLabel("Match Quiz Regular");
    predictorMatchQuizBadge.setAssets(null);
    predictorMatchQuizBadge.setDescription(null);
    predictorMatchQuizBadge.setFlags(null);

    return predictorMatchQuizBadge;
  }

  private Badge getPredictionTeamBadge() {
    BadgeRequirement badgeRequirementPredictorTeam = new BadgeRequirement();

    Badge predictorTeamBadge = new Badge();
    predictorTeamBadge.setId(PREDICTOR_TEAM_ID);
    predictorTeamBadge.setEnabled(true);
    predictorTeamBadge.setRequirements(badgeRequirementPredictorTeam);

    return predictorTeamBadge;
  }

  private Badge getPredictionCompetitionBadge() {
    BadgeRequirement badgeRequirementPredictorTeam = new BadgeRequirement();

    Badge predictorTeamBadge = new Badge();
    predictorTeamBadge.setId(PREDICTOR_COMPETITION_ID);
    predictorTeamBadge.setEnabled(true);
    predictorTeamBadge.setRequirements(badgeRequirementPredictorTeam);

    return predictorTeamBadge;
  }

  /**
   * This method enable all badges for given feature type
   * @param type enum FeatureType
   * @param badgeRewards BadgeRewards
   */
  public void enableBadgesForFeature(FeatureType type, BadgeRewards badgeRewards) {
    List<Badge> updatedBadges;
    switch (type) {
      case TOP_X -> {
        updatedBadges =
            badgeRewards.getTopX().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setTopX(updatedBadges);
      }
      case MATCH_QUIZ -> {
        updatedBadges =
            badgeRewards.getMatchQuiz().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setMatchQuiz(updatedBadges);
      }
      case GAMES -> {
        updatedBadges =
            badgeRewards.getGames().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setGames(updatedBadges);
      }
      case FOOTBALL -> {
        updatedBadges =
            badgeRewards.getFootball().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setFootball(updatedBadges);
      }
      case PREDICTOR -> {
        updatedBadges =
            badgeRewards.getPredictor().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setPredictor(updatedBadges);
      }
      case DISCUSSION -> {
        updatedBadges =
            badgeRewards.getDiscussions().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setDiscussions(updatedBadges);
      }
      case EITHER_OR -> {
        updatedBadges =
            badgeRewards.getEitherOr().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setEitherOr(updatedBadges);
      }
      case CLASSIC_QUIZZES -> {
        updatedBadges =
            badgeRewards.getClassicQuizzes().stream()
                .peek(badge -> badge.setEnabled(true))
                .collect(Collectors.toList());
        badgeRewards.setClassicQuizzes(updatedBadges);
      }
      default -> throw new IllegalArgumentException("Invalid argument type: " + type);
    }
    resetBadges(badgeRewards);
  }
}
