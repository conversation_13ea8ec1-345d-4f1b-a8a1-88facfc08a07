package com.fansunited.automation.core.base.profileapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionForUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getAllFixtures;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_PATTERN;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.bq.events.GameType.TOP_X;
import static com.fansunited.automation.validators.PredictionApiValidator.validateCreatePredictionResponse;
import static com.fansunited.automation.validators.RedisValidator.validateGameDoesNotExistInActiveGames;
import static com.fansunited.automation.validators.RedisValidator.validateMatchesDoesNotExistInActiveMatches;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientGetBadgesEndpoint;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.mockapi.MockEventEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.profileapi.BadgesByProfileIdEndpoint;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.clientapi.features.response.Badge;
import com.fansunited.automation.model.clientapi.features.response.BadgeRewards;
import com.fansunited.automation.model.clientapi.features.response.BaseBadge;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.model.loyaltyapi.ProfileBadgesResponse;
import com.fansunited.automation.model.loyaltyapi.activity.request.TopXMatchQuizRequirement;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.FullTimeOneXTwoPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OneXTwoOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultStatus;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.validators.RedisValidator;
import com.fansunited.automation.validators.ResolverValidator;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class BadgesBaseTest extends ProfileApiBaseTest {

  protected void generatePredictionEvents(
      String userId,
      PredictionType gameType,
      int points,
      int correctPredictions,
      int predictionsMade,
      PredictionType predictionType,
      int gameParticipationCount) {

    if (points > 0) {
      MockEventEndpoint.generateRankingEvent(LocalDateTime.now().minusDays(5),
          getCurrentTestUser().getUid(),
          PredictionMarket.FT_1X2, gameType, points, null, ResultOutcome.CORRECT,
          ResultStatus.SETTLED,
          MatchByIdEndpoint.getMatchDtoById("fb:m:158507"), 1, "1",
          LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    }

    if (correctPredictions > 0) {
      IntStream.range(0, correctPredictions)
          .parallel()
          .forEach(
              pred ->
                  MockEventEndpoint.generateRankingEvent(LocalDateTime.now().minusDays(5),
                      userId,
                      PredictionMarket.FT_1X2, gameType, 10, null, ResultOutcome.CORRECT,
                      ResultStatus.SETTLED,
                      MatchByIdEndpoint.getMatchDtoById("fb:m:158507"), 1, "1",
                      LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
          );
    }

    if (predictionsMade > 0) {
      IntStream.range(0, predictionsMade)
          .parallel()
          .forEach(
              pred ->
                  MockEventEndpoint.generatePredictionMadeEvent(
                      PredictionInstance.builder()
                          .id(UUID.randomUUID().toString())
                          .userId(userId)
                          .gameType(predictionType)
                          .fixtures(
                              PredictionsEndpoint.generateValidSinglePredictionFixture(
                                  PredictionMarket.FT_1X2,
                                  MatchesEndpoint.getRandomMatchDto().getId(),
                                  VALID_PLAYER_ID))
                          .createdAt(
                              LocalDateTime.now()
                                  .format(DateTimeFormatter.ofPattern(ISO8601_PATTERN)))
                          .build()));
    }

    if (gameParticipationCount > 0) {
      IntStream.range(0, gameParticipationCount)
          .parallel()
          .forEach(
              pred ->
                  MockEventEndpoint.generatePredictionMadeEvent(
                      PredictionInstance.builder()
                          .id(UUID.randomUUID().toString())
                          .userId(userId)
                          .gameType(predictionType)
                          .fixtures(
                              PredictionsEndpoint.generateValidSinglePredictionFixture(
                                  PredictionMarket.FT_1X2,
                                  MatchesEndpoint.getRandomMatchDto().getId(),
                                  VALID_PLAYER_ID))
                          .createdAt(
                              LocalDateTime.now()
                                  .format(DateTimeFormatter.ofPattern(ISO8601_PATTERN)))
                          .build()));
    }
  }

  protected List<String> getAllBadgesMatchingAchievedBadgeReqs(
      List<Badge> badgeList, Badge achievedBadge) {

    var filteredBadges =
        badgeList.stream().filter(badge -> !badge.getId().equals(achievedBadge.getId())).toList();

    if (filteredBadges.isEmpty()) {
      return List.of(achievedBadge.getId());
    }

    var allAchievedBadges =
        filteredBadges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getPoints()
                            <= achievedBadge.getRequirements().getPoints()
                        && badge.getRequirements().getPredictionsMade()
                            <= achievedBadge.getRequirements().getPredictionsMade()
                        && badge.getRequirements().getCorrectPredictions()
                            <= achievedBadge.getRequirements().getCorrectPredictions()
                        && badge.getRequirements().getGameParticipationCount()
                            <= achievedBadge.getRequirements().getGameParticipationCount())
            .map(Badge::getId)
            .collect(Collectors.toList());
    allAchievedBadges.add(achievedBadge.getId());
    return allAchievedBadges;
  }

  protected void generatePredictionEvents(
      String userId,
      int points,
      int correctPredictions,
      int predictionsMade,
      PredictionType predictionType,
      int gameParticipationCount,
      Match match) {

    if (points > 0) {
      InsertBigQData.generateCorrectPredictionEvent(
          LocalDateTime.now(),
          userId,
          UUID.randomUUID().toString(),
          predictionType,
          PredictionMarket.CORRECT_SCORE,
          points);
    }

    if (correctPredictions > 0) {
      IntStream.range(0, correctPredictions)
          .parallel()
          .forEach(
              pred ->
                  InsertBigQData.generateCorrectPredictionEvent(
                      LocalDateTime.now(),
                      userId,
                      UUID.randomUUID().toString(),
                      predictionType,
                      PredictionMarket.CORRECT_SCORE,
                      points));
    }

    if (predictionsMade > 0) {
      IntStream.range(0, predictionsMade)
          .parallel()
          .forEach(
              pred ->
                  InsertBigQData.insertSingleRankEvent(
                      LocalDateTime.now(),
                      userId,
                      PredictionMarket.CORRECT_SCORE,
                      GameType.SINGLE,
                      30,
                      null,
                      match,
                      1,
                      "1",
                      LocalDateTime.now()
                          .plusSeconds(2)
                          .format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
          );
    }

    if (gameParticipationCount > 0) {
      IntStream.range(0, gameParticipationCount)
          .parallel()
          .forEach(
              pred ->
                  InsertBigQData.generatePredictionMadeEvent(
                      PredictionInstance.builder()
                          .id(UUID.randomUUID().toString())
                          .userId(userId)
                          .gameType(predictionType)
                          .fixtures(
                              PredictionsEndpoint.generateValidSinglePredictionFixture(
                                  PredictionMarket.FT_1X2,
                                  match != null
                                      ? match.getId()
                                      : MatchesEndpoint.getRandomMatchDto().getId(),
                                  VALID_PLAYER_ID))
                          .createdAt(
                              LocalDateTime.now()
                                  .format(DateTimeFormatter.ofPattern(ISO8601_PATTERN)))
                          .build()));
    }
  }

  /**
   * This method creates MatchQuiz prediction
   *
   * @throws HttpException exception
   */
  public void generateMatchQuizPrediction(UserRecord user)
      throws HttpException, InterruptedException {
    var matchList = MatchGenerator.generateMatches(3, false);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(0, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(0, 4));
        });

    Resolver.openMatchesForPredictions(matchList);

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);
    var createGameResponse = GameEndpoint.getGameById(gameId);
    var gameObject = createGameResponse.as(GameInstance.class);
    var matchId = gameObject.getFixtures().get(0).getMatchId();
    var playerId = getRandomPlayerFromMatch(matchId);
    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameObject.getId())
            .build();

    var response = createPredictionForUser(createPredictionRequest, user.getEmail());

    validateCreatePredictionResponse(response, createPredictionRequest, false, user);

    var predictionInstance = response.as(PredictionInstance.class);
    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    Resolver.updateMatchToBeFinishedInThePast(matchId, 31);
    Resolver.resolve();
    Thread.sleep(5000);
  }

  /**
   * This method creates TopX prediction
   *
   * @throws HttpException exception
   */
  public void generateTopXPrediction(UserRecord user) throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(3, 3);
    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(0, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(0, 4));
        });

    Resolver.openMatchesForPredictions(matchList);

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream()
                    .map(com.fansunited.automation.core.resolver.hibernate.Match::getId)
                    .toList(),
                GameType.TOP_X,
                GameStatus.OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(match.getGoalsFullTimeHome())
                    .goalsAway(match.getGoalsFullTimeAway())
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    createPrediction(
        createPredictionRequest,
        null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        user.getEmail(),
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .log()
        .all()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Resolver.resolve(matchList.size());
    ResolverValidator.validateTopXGamePredictionsAreResolvedToWon(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);
    validateGameDoesNotExistInActiveGames(gameInstance.getId());
    validateMatchesDoesNotExistInActiveMatches(matchList);
  }

  /**
   * This method creates Single prediction
   *
   * @throws HttpException exception
   */
  public void generateSinglePrediction(
      String email, com.fansunited.automation.core.resolver.hibernate.Match match)
      throws HttpException, InterruptedException {

    match.setGoalsFullTimeHome((byte) 2);
    match.setGoalsFullTimeAway((byte) 0);

    Resolver.openMatchForPredictions(match);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(
                List.of(
                    FullTimeOneXTwoPredictionFixture.builder()
                        .matchId(match.getId())
                        .matchType(MatchType.FOOTBALL.getValue())
                        .prediction(OneXTwoOutcome.ONE)
                        .build()))
            .build();

    var predictionId =
        createPredictionForUser(createPredictionRequest, email)
            .as(PredictionInstance.class)
            .getId();

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    ResolverValidator.waitForPredictionToBeResolved(email);

    ResolverValidator.validateSinglePredictionIsResolved(
        PredictionsEndpoint.getOwnPredictionsForUser(email));
    RedisValidator.validateMatchesDoesNotExistInActiveMatches(List.of(match));
    RedisValidator.validatePredictionsDoesNotExistInProcessingPredictionsSet(List.of(predictionId));
  }

  public void verifyBadgesForMatchQuizGame(
      UserRecord user, Response statisticsResponse) throws HttpException {
    TopXMatchQuizRequirement badgeRequirement = new TopXMatchQuizRequirement();

    badgeRequirement.setPoints(
        statisticsResponse.then().extract().path("data.predictions.match_quiz.points"));
    badgeRequirement.setParticipationCount(
        statisticsResponse.then().extract().path("data.predictions.match_quiz.participations"));

    BadgeRewards badgeRewards = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class);

    List<Badge> allBadges = new ArrayList<>();
    allBadges.addAll(badgeRewards.getMatchQuiz());
    allBadges.addAll(badgeRewards.getGames());

    List<String> expectedAchievedBadges =
            allBadges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getPoints() <= badgeRequirement.getPoints()
                        && badge.getRequirements().getGameParticipationCount()
                            <= badgeRequirement.getParticipationCount())
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(user.getUid())
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .toList();

    assertTrue(actualAchievedBadges.containsAll(expectedAchievedBadges),
        "Actual badges do not contain all expected badges.");
  }

  public void verifyBadgesForTopXGames(UserRecord user, Response statisticsResponse)
      throws HttpException {
    TopXMatchQuizRequirement badgeRequirement = new TopXMatchQuizRequirement();

    badgeRequirement.setPoints(
        statisticsResponse.then().extract().path("data.predictions.top_x.points"));
    badgeRequirement.setParticipationCount(
        statisticsResponse.then().extract().path("data.predictions.top_x.participations"));

    var badges = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class).getTopX();

    var expectedAchievedBadges =
        badges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getPoints() <= badgeRequirement.getPoints()
                        && badge.getRequirements().getGameParticipationCount()
                            <= badgeRequirement.getParticipationCount()
                        && badge.getId().startsWith(TOP_X.name()))
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(user.getUid())
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .filter(bId -> bId.startsWith(TOP_X.name()))
            .toList();

    assertThat(expectedAchievedBadges, containsInAnyOrder(actualAchievedBadges.toArray()));
  }
}
