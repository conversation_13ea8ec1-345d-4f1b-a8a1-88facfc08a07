package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_MATCH_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import org.apache.http.HttpException;

public class RedisEndpoint extends BaseSetup {

  public static Response getActiveMatches() throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.REDIS_MATCHES);
  }

  public static Response getProcessingPredictions() throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.REDIS_PROCESSING_PREDICTIONS);
  }

  public static Response getPredictionsForMatch(String matchId) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .pathParam(PATH_PARAM_MATCH_ID, matchId)
        .when()
        .get(Endpoints.PredictionApi.REDIS_PREDICTIONS_BY_MATCH_ID);
  }

  public static Response getActiveGames() throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.REDIS_ACTIVE_GAMES);
  }

  public static Response getGameById(String gameId) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .when()
        .get(Endpoints.PredictionApi.REDIS_GAME_BY_ID);
  }

  public static Response getGames() throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.REDIS_GAMES);
  }

  @SneakyThrows public static Response clearAllRedis() {

    var requestSpecification =
        com.fansunited.automation.core.apis.resolverapi.BaseSetup.getRequiredRequestSpec(AuthConstants.REDIS_BASE_AUTH_USAGE, null, true, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.REDIS_CLEAR_ALL);
  }
}
