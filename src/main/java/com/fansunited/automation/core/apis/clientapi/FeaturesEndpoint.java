package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.ApiConstants.ClientApi.COMPETITIONS_WHITELIST_PROP;
import static com.fansunited.automation.constants.ApiConstants.ClientApi.FULL_COVERAGE_COMPETITIONS_WHITELIST_PROP;
import static com.fansunited.automation.constants.ApiConstants.ClientApi.MAX_FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.ClientApi.PREDICTOR_PROP;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.model.clientapi.features.response.WeightMultiplier.getDefaultLoyaltyWeightMultipliers;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint;
import com.fansunited.automation.model.clientapi.features.response.BadgeRewards;
import com.fansunited.automation.model.clientapi.features.response.FeaturesResponse;
import com.fansunited.automation.model.clientapi.features.response.LoyaltyActions;
import com.fansunited.automation.model.clientapi.features.response.LoyaltyRewards;
import com.fansunited.automation.model.clientapi.features.response.VotingBadge;
import com.fansunited.automation.model.clientapi.features.response.WeightMultiplier;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;

public class FeaturesEndpoint extends BaseSetup {

  public static int getTopXMaxFixtures() throws HttpException {
    return getClientFeatures(CLIENT_AUTOMATION_ID)
        .then()
        .extract()
        .response()
        .body()
        .jsonPath()
        .getInt("data." + GameType.TOP_X.getValue().toLowerCase() + "." + MAX_FIXTURES_PROP);
  }

  public static List<String> getNonWhitelistedCompetitions(GameType gameType) throws HttpException {
    var whiteListedCompetitions = getCompetitionsWhitelist(gameType);
    var allCompetitionsIdList = CompetitionsEndpoint.getCompetitionsIdList();

    var nonWhitelistedCompetitions = new ArrayList<String>();

    for (String competition : allCompetitionsIdList) {
      if (!whiteListedCompetitions.contains(competition)) {
        nonWhitelistedCompetitions.add(competition);
      }
    }

    return nonWhitelistedCompetitions;
  }

  public static List<String> getFullCoverageCompetitionsWhitelist() throws HttpException {

    return getClientFeatures(CLIENT_AUTOMATION_ID)
        .then()
        .extract()
        .response()
        .body()
        .jsonPath()
        .getList("data." + PREDICTOR_PROP + "." + FULL_COVERAGE_COMPETITIONS_WHITELIST_PROP);
  }

  public static List<String> getCompetitionsWhitelist(GameType gameType) throws HttpException {
    var response = getClientFeatures(CLIENT_AUTOMATION_ID);

    if (GameType.SINGLE.equals(gameType)) {
      return response
          .then()
          .extract()
          .jsonPath()
          .getList("data." + "predictor" + "." + FULL_COVERAGE_COMPETITIONS_WHITELIST_PROP);
    }
    return response
        .then()
        .extract()
        .jsonPath()
        .getList("data." + gameType.name().toLowerCase() + "." + COMPETITIONS_WHITELIST_PROP);
  }

  public static Response getClientFeatures(String clientId) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, null, false, null, CLIENT_AUTOMATION_ID, null,
            null, AuthConstants.ENDPOINTS_API_KEY, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .pathParam(UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.FEATURES);
  }

  public static Response getClientFeatures(String clientId, int apiVersion) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            apiVersion,
            null,
            false,
            null,
            CLIENT_AUTOMATION_ID,
            null,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification
        .pathParam(UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.FEATURES);
  }

  public static LoyaltyRewards setDefaultLoyaltyRewards() throws HttpException {

    return LoyaltyRewards.builder().badges(setDefaultLoyaltyRewardsBadges()).build();
  }

  public static BadgeRewards setDefaultLoyaltyRewardsBadges() throws HttpException {

    FeaturesResponse defaultLoyaltyBadges =
        FeaturesEndpoint.getClientFeatures(CLIENT_AUTOMATION_ID).as(FeaturesResponse.class);
    var badges = defaultLoyaltyBadges.getData().getLoyalty().getRewards().getBadges();

    return BadgeRewards.builder()
        .general(badges.getGeneral())
        .predictor(badges.getPredictor())
        .matchQuiz(badges.getMatchQuiz())
        .topX(badges.getTopX())
        .football(badges.getFootball())
        .voting(new VotingBadge()) //TODO: Replace 'new VotingBadge()' with 'badges.getVoting()' when the cache expires.
        .build();
  }

  /**
   * The method updates the default Loyalty Actions. The method uses different types of update.
   * It can update a parameter of an Action.
   * It can add new action (which currently 30.01.2023 is not allowed).
   * It can remove an action (which currently 30.01.2023 is not allowed).
   * It can duplicate an action (which currently 30.01.2023 is not allowed).
   * @param multiplier the Action that will be updated.
   * @param updateType the type of update that will be performed. The types are:
   *                   <br>"UPDATE", "ADD", "REMOVE", "DUPLICATE"</br>
   * @return LoyaltyActions object
   */
  public static LoyaltyActions updateDefaultLoyaltyActions(WeightMultiplier multiplier, String updateType) {

    List<WeightMultiplier> updatedLoyaltyWeightMultipliers;

    switch (updateType){
      case "UPDATE": updatedLoyaltyWeightMultipliers = getDefaultLoyaltyWeightMultipliers()
          .stream()
          .map(m -> m.getId().equals(multiplier.getId()) ? multiplier : m)
          .toList();
      break;
      case "ADD", "DUPLICATE":
        updatedLoyaltyWeightMultipliers = getDefaultLoyaltyWeightMultipliers();
        updatedLoyaltyWeightMultipliers.add(multiplier);
        break;
      case "REMOVE":
        updatedLoyaltyWeightMultipliers = getDefaultLoyaltyWeightMultipliers();
        updatedLoyaltyWeightMultipliers.removeIf(m -> m.getId().equals(multiplier.getId()));
        break;
      default: throw new IllegalArgumentException(String.format(
          "updateType '%s' is not recognized! Allowed values are:\n"
              + "UPDATE, ADD, REMOVE, DUPLICATE", updateType));
    }

    LoyaltyActions updatedLoyaltyActions = new LoyaltyActions();

    updatedLoyaltyActions.setValue(updatedLoyaltyWeightMultipliers);

    return updatedLoyaltyActions;
  }
}
