package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.ApiConstants.DiscussionsApi.SKIP_MODERATED_PROP;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetUsersPostEndpoint extends BaseSetup {

  public static Response getUsersOwnPost(
          String clientId,
          FansUnitedProject project,
          String email,
          String apiKey,
          ContentType contentType, String authToken, Boolean skipModerated)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey, contentType);

    if (skipModerated!= null && !skipModerated) {
      requestSpecification = requestSpecification.queryParam(SKIP_MODERATED_PROP, false);
    }

    return requestSpecification.when().get(Endpoints.DiscussionApi.GET_USERS_OWN_POSTS);
  }

  public static Response getUsersOwnPost(String email) {
    try {
      return getUsersOwnPost(
          CLIENT_AUTOMATION_ID,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          email,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON, null, null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static Response getUsersOwnPost(String email, boolean skipModerated) {
    try {
      return getUsersOwnPost(
          CLIENT_AUTOMATION_ID,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          email,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          null,
          skipModerated);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
