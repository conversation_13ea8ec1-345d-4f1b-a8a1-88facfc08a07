import java.util.HashMap;
import java.util.Set;

public class Test {
  public static void main(String[] args) {
    // Example arrays
    int[] arr = {5, 10, 15, 20};

    // Test with different arrays
    findSecondLargest(arr);
    printFibonacci(4);

    System.out.println(getSumOfAllDigits(123));

    characterCount("test");
    duplicateChars("teeest");
    reverseString("test");
    swapNumbers();
    reverseString("test");
    getCapitalLetInStr("tesT");
    sumTheArray();
  }

  public static void findSecondLargest(int[] arr) {
    System.out.print("Array: ");

    int largest = Integer.MIN_VALUE;
    int secondLargest = Integer.MIN_VALUE;

    // First pass to find the largest
    for (int num : arr) {
      if (num > largest) {
        largest = num;
      }
    }

    // Second pass to find the second largest
    for (int num : arr) {
      if (num > secondLargest && num < largest) {
        secondLargest = num;
      }
    }

    // Check if we found a second largest
    if (secondLargest == Integer.MIN_VALUE) {
      // This happens if all elements are equal
      System.out.println(
          "Second largest: No second largest element found (all elements are equal)");
    } else {
      System.out.println("Second largest: " + secondLargest);
    }
    System.out.println();
  }

  public static void printFibonacci(int n) {
    int[] fib = new int[n];
    fib[0] = 0;
    fib[1] = 1;
    fib[2] = 2;
    System.out.print("Fibonacci: " + fib[0] + " " + fib[1] + " " + fib[2] + " ");
    for (int i = 3; i < n; i++) {
      fib[i] = fib[i - 1] + fib[i - 2] + fib[i - 3];
    }
    System.out.print("Fibonacci: ");
    for (int i = 0; i < n; i++) {
      System.out.print(fib[i] + " ");
    }
    System.out.println();
  }

  public static int testSum(int number) {
    number = Math.abs(number);

    int sum = 0;

    System.out.println("Sum: " + number);
    while (number > 0) {
      sum += number % 10;
      number /= 10;
    }

    return sum;
  }

  public static int getSumOfAllDigits(int num) {

    // Declare variable sum which will store the sum of all digits.
    int sum = 0;

    // Run a while loop until the num becomes 0.
    while (num != 0) {
      int test = num % 10;
      sum = sum + test;
      num = num / 10;
    }
    return sum;
  }

  static void characterCount(String inputString) {

    // Creating a hashmap object.
    HashMap<Character, Integer> hash_map = new HashMap<>();
    char[] strArray = inputString.toCharArray();

    for (char c : strArray) {
      if (hash_map.containsKey(c)) {
        hash_map.put(c, hash_map.get(c) + 1);
      } else {
        hash_map.put(c, 1);
      }
    }
    // Print the hashmap object which gives the number of each character in String.
    System.out.println(hash_map);
  }

  static void duplicateChars(String inputString) {

    // Creating a hashmap object.
    HashMap<Character, Integer> hash_map = new HashMap<>();
    char[] strArray = inputString.toCharArray();

    for (char c : strArray) {
      if (hash_map.containsKey(c)) {
        hash_map.put(c, hash_map.get(c) + 1);
      } else {
        hash_map.put(c, 1);
      }
    }
    // Store the key values in a set and then get the number of each duplicate character.
    Set<Character> keys = hash_map.keySet();
    for (char c : keys) {
      if (hash_map.get(c) > 1) {
        System.out.println(c + "-->" + hash_map.get(c));
      }
    }
  }

  public static void swapNumbers() {

    int num1 = 10;
    int num2 = 20;
    num1 = num1 + num2;
    num2 = num1 - num2;
    num1 = num1 - num2;
    System.out.println("num1: " + num1 + " num2: " + num2);
  }

  public static void reverseString(String str) {
    String reversed = "";
    for (int i = str.length() - 1; i >= 0; i--) {
      reversed += str.charAt(i);
    }
    System.out.println(reversed);
  }

  public static void getCapitalLetInStr(String str){

    int stringCounter=0;

    for (int i = 0; i < str.length(); i++) {
      if (Character.isUpperCase(str.charAt(i))) {
        stringCounter++;
      }
    }
    System.out.println(stringCounter);



  }

  public static void sumTheArray(){

    int[] myArray= {1,2,3,4,5};
    int sum = 0;
    for (int i = 0; i < myArray.length; i++) {
      sum += myArray[i];
    }
    System.out.println(sum);
  }



  public class Vectors {
    public static int[] findShortest(int[][] vectors) {
      throw new UnsupportedOperationException("Waiting to be implemented");
    }

    public static void main(String[] args) {
      int[][] vectors = {
          new int[] { 1, 1, 1 },
          new int[] { 2, 2, 2 },
          new int[] { 3, 3, 3 }
      };

      int[] shortest = Vectors.findShortest(vectors);
      System.out.println("x: "+ shortest[0] + " y: "+ shortest[1] +" z: " + shortest[2]);
    }
  }
}








