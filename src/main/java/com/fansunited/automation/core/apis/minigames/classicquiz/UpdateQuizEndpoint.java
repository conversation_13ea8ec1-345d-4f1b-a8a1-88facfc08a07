package com.fansunited.automation.core.apis.minigames.classicquiz;

import static com.fansunited.automation.constants.UrlParamValues.MiniGamesApi.QUIZ_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class UpdateQuizEndpoint extends QuizBaseSetup {

  public static Response updateQuiz(String quizId,Object body,String clientId,
      String apiKey, ContentType contentType, FirebaseHelper.FansUnitedProject project,
      String email)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(QUIZ_ID,quizId)
        .when()
        .body(body)
        .put(Endpoints.MiniGamesApi.DELETE_QUIZ);
  }
}
