package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_DISCUSSION_ID;
import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PIN_POST_IDS;
import static com.fansunited.automation.core.apis.discussionapi.BaseSetup.getRequiredRequestSpec;
import static com.google.api.client.http.HttpMethods.DELETE;
import static com.google.api.client.http.HttpMethods.PATCH;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import org.apache.http.HttpException;

public class BasePinnedPostsEndpoint extends AuthBase {

  public static Response modifyPinnedPostsForDiscussion(
      List<String> pinPostIds,
      String discussionId,
      String clientId,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String apiKey,
      ContentType contentType,
      String authToken,
      String httpMethod)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey, contentType);

    requestSpecification.pathParams(PATH_PARAM_DISCUSSION_ID, discussionId);
    if (pinPostIds != null) {
      requestSpecification.queryParam(PIN_POST_IDS, String.join(",", pinPostIds));
    }

    if (httpMethod.equalsIgnoreCase(PATCH)) {
      return requestSpecification.when().patch(Endpoints.DiscussionApi.PINNED_POSTS_DISCUSSION);
    } else if (httpMethod.equalsIgnoreCase(DELETE)) {
      return requestSpecification.when().delete(Endpoints.DiscussionApi.PINNED_POSTS_DISCUSSION);
    } else {
      throw new IllegalArgumentException("Unsupported HTTP method: " + httpMethod);
    }
  }
}
