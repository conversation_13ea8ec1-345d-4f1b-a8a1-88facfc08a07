package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ClientByIdEndpoint extends BaseSetup {

  public static Response getClientsById(String clientId, FirebaseHelper.FansUnitedProject project,
      String apiKey, ContentType contentType)
      throws HttpException {

    return getClientById(null, true, ADMIN_USER, clientId, project,
        apiKey, contentType);
  }

  public static Response getClientById(String authToken, boolean isAuthRequired, String email,
      String clientId, FirebaseHelper.FansUnitedProject project, String apiKey,
      ContentType contentType) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, authToken, true,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, ADMIN_USER, clientId, null,
            apiKey, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.CLIENT_BY_ID);
  }
}
