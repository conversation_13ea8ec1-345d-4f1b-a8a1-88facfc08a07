package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class OptionsProfileEndpoint extends BaseSetup {

  public static Response optionsProfileApi(String endpoint) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            null,
            false,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification.when().options(endpoint);
  }
}
