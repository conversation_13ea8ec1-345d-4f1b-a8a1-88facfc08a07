package com.fansunited.automation.core.apis.mockapi;

import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.helpers.ConfigReader;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;

class BaseSetup {

  private static final String BASE_URI =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.MOCK_API_BASE_URL);
  private static final String PORT =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.MOCK_API_PORT);
  private static final String API_KEY = "hdEuGQnLn7d9A84g7hHwaF3ESdJVXCZq";

  protected static RequestSpecification getRequiredRequestSpec() {

    return requestWithoutAuth(BASE_URI, PORT)
        .contentType(ContentType.JSON)
        .queryParam(UrlParamValues.QUERY_PARAM_KEY, API_KEY);
  }

  protected static RequestSpecification getRequiredRequestSpecForAuditLog() {

    return requestWithoutAuth(BASE_URI, PORT)
        .contentType(ContentType.JSON)
        .queryParam(UrlParamValues.QUERY_PARAM_KEY, API_KEY)
        .queryParam(UrlParamValues.QUERY_PARAM_CLIENT_ID, "fans_united");
  }
}
