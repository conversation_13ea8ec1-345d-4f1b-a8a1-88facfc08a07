package com.fansunited.automation.core.apis.profileapi.leads;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.LEAD_ID_PATH;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.profileapi.BaseSetup;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetLeadByIdEndpoint extends BaseSetup {
  public static Response getLeadById(
          String id,
          String email,
          String clientId,
          String apiKey,
          ContentType contentType,
          boolean isAuthRequired,
          String jwtToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            jwtToken, isAuthRequired, email, null, clientId, apiKey, contentType);

    requestSpecification.pathParams(LEAD_ID_PATH, id);

    return requestSpecification.when().get(Endpoints.ProfileApi.LEADS_BY_ID);
  }
}
