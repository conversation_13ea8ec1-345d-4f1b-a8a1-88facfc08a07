package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class ProfileEndpoint extends BaseSetup {

  public static Response getCurrentTestUserProfileRequest() throws HttpException {

    return getCurrentTestUserProfileRequest(
        UrlParamValues.Language.EN.getValue(),
        CLIENT_AUTOMATION_ID,
        ContentType.JSON,
        AuthConstants.ENDPOINTS_API_KEY);
  }

  public static Response getCurrentTestUserProfileRequest(String clientId) throws HttpException {

    return getCurrentTestUserProfileRequest(
        UrlParamValues.Language.EN.getValue(),
        clientId,
        ContentType.JSON,
        AuthConstants.ENDPOINTS_API_KEY);
  }

  public static Response getProfileRequest(String email) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(
            null,
            true,
            email,
            UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification.when().get(Endpoints.ProfileApi.PROFILE);
  }

  public static Response getCurrentTestUserProfileRequest(
      String lang, String clientId, ContentType contentType) throws HttpException {

    return getCurrentTestUserProfileRequest(
        lang, clientId, contentType, AuthConstants.ENDPOINTS_API_KEY);
  }

  public static Response getCurrentTestUserProfileRequest(
      String lang, String clientId, ContentType contentType, String apiKey) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, true, null, lang, clientId, apiKey, contentType);

    return requestSpecification.when().get(Endpoints.ProfileApi.PROFILE);
  }

  public static Response getProfileRequestWithoutToken() throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            null,
            false,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification.when().get(Endpoints.ProfileApi.PROFILE);
  }

  public static Response getProfileRequestWithToken(
      String authToken, String lang, String clientId, ContentType contentType, String apiKey)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, true, null, lang, clientId, apiKey, contentType);

    return requestSpecification.when().get(Endpoints.ProfileApi.PROFILE);
  }

  public static Response updateCurrentTestUserProfile(Object body) throws HttpException {

    return updateProfileRequest(getCurrentTestUser().getEmail(), body);
  }

  public static Response updateCurrentTestUserProfile(
      Object body, String lang, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    return updateProfileRequest(
        getCurrentTestUser().getEmail(), body, lang, clientId, apiKey, contentType);
  }

  public static Response updateProfileRequestWithToken(
      String authToken,
      Object body,
      String lang,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, true, null, lang, clientId, apiKey, contentType);

    return requestSpecification.body(body).when().patch(Endpoints.ProfileApi.PROFILE);
  }

  public static Response updateProfileRequest(String email, Object body) throws HttpException {

    return updateProfileRequest(
        email,
        body,
        UrlParamValues.Language.EN.getValue(),
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response updateProfileRequest(
      String email,
      Object body,
      String lang,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, true, email, lang, clientId, apiKey, contentType);

    return requestSpecification.body(body).when().patch(Endpoints.ProfileApi.PROFILE);
  }

  public static Response deleteUser(String userId) throws HttpException {
    DeleteProfileEndpoint deleteProfileEndpoint =
        DeleteProfileEndpoint.builder()
            .project(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS)
            .email(ADMIN_USER)
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .userId(userId)
            .build();

    var response = deleteProfileEndpoint.deleteUserProfile();

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    return response;
  }

  public static Response setVerifiedUserRequest(
      String email,
      Object body,
      String clientId,
      String apiKey,
      ContentType contentType,String userId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, true, email, null, clientId, apiKey, contentType);

    return requestSpecification.body(body).pathParams(PATH_PARAM_USER_ID,userId).when().post(Endpoints.ProfileApi.VERIFIED_USER);
  }

  public static Response deleteOwnProfile(String email, String apiKey, String clientID, String authToken) throws HttpException {
    DeleteOwnProfileEndpoint deleteOwnProfileEndpoint =
        DeleteOwnProfileEndpoint.builder()
            .authToken(authToken)
            .project(FansUnitedProject.FANS_UNITED_PROFILE)
            .email(email)
            .clientId(clientID)
            .apiKey(apiKey)
            .build();

    return deleteOwnProfileEndpoint.deleteOwnProfile();
  }
}
