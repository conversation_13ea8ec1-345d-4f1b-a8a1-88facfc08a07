package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;

public class TopTeamsEndpoint extends BaseSetup {

  public static List<String> getTopTeamsIds() {
    return getTopTeams().then().extract().body().jsonPath().getList("data.id");
  }

  public static Response getTopTeams() {

    return getTopTeams(UrlParamValues.Language.EN.getValue(),
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getTopTeams(String lang, String clientId, String apiKey,
      ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .queryParam(QUERY_PARAM_CLIENT_ID,CLIENT_AUTOMATION_ID)
        .get(Endpoints.FootballApi.TOP_TEAMS);
  }
}