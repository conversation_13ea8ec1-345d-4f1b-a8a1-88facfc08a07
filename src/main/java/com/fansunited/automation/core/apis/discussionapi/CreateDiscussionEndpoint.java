package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class CreateDiscussionEndpoint extends BaseSetup {

  public static Response createDiscussion(Object body, String clientId,
      String apiKey, ContentType contentType, FirebaseHelper.FansUnitedProject project,
      String email, String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .body(body)
        .when()
        .post(Endpoints.DiscussionApi.CREATE_DISCUSSION);
  }

  public static Response createDiscussion(Object body){
      try {
          return createDiscussion(body, CLIENT_AUTOMATION_ID,
                  AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null, null);
      } catch (HttpException e) {
          throw new RuntimeException(e);
      }
  }
}
