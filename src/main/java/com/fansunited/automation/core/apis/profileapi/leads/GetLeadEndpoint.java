package com.fansunited.automation.core.apis.profileapi.leads;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CAMPAIGN_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CONTENT_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CONTENT_TYPE_PROP;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.profileapi.BaseSetup;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetLeadEndpoint extends BaseSetup {
  public static Response getLead(
      int limit,
      String campaignId,
      String contentTypeField,
      String contentId,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      boolean isAuthRequired,
      String jwtToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            jwtToken, isAuthRequired, email, null, clientId, apiKey, contentType);

    if (limit > 0) {
      requestSpecification.queryParam("limit", limit);
    }

    if (campaignId != null) {
      requestSpecification.queryParam(CAMPAIGN_ID_PROP, campaignId);
    }

    if (contentTypeField != null) {
      requestSpecification.queryParam(CONTENT_TYPE_PROP, contentTypeField);
    }

    if (contentId != null) {
      requestSpecification.queryParam(CONTENT_ID_PROP, contentId);
    }

    return requestSpecification.when().get(Endpoints.ProfileApi.LEADS);
  }
}
