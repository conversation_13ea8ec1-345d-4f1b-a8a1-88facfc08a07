package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.ApiConstants.DiscussionsApi.SKIP_MODERATED_PROP;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetPostsByUserIdEndpoint extends BaseSetup {

  public static Response getPostByUserIdPost(
      String userId,
      String clientId,
      FansUnitedProject project,
      String email,
      String apiKey,
      ContentType contentType,
      Boolean skipModerated)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    if (skipModerated != null && !skipModerated) {
      requestSpecification = requestSpecification.queryParam(SKIP_MODERATED_PROP, false);
    }

    return requestSpecification
        .pathParams(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.DiscussionApi.GET_POST_BY_USER_ID);
  }

  public static Response getPostByUserIdPost(String userId, String email) {
    try {
      return getPostByUserIdPost(
          userId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_PROFILE,
          email,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static Response getPostByUserIdPost(String userId, String email, boolean skipModerated) {
    try {
      return getPostByUserIdPost(
          userId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_PROFILE,
          email,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          skipModerated);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
