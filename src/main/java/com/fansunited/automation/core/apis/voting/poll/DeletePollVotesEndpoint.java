package com.fansunited.automation.core.apis.voting.poll;

import static com.fansunited.automation.constants.UrlParamValues.VotingApi.PATH_PARAM_POLL_ID;

import com.fansunited.automation.constants.Endpoints.VotingApi;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class DeletePollVotesEndpoint extends VotingBaseSetup {

  public static Response deletePollVotes(
      String pollId,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email,
      boolean isAuthRequired)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, isAuthRequired, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POLL_ID, pollId)
        .when()
        .delete(VotingApi.DELETE_POLLS_VOTES);
  }
}
