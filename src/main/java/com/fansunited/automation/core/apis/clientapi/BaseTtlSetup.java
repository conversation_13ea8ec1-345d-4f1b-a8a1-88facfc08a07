package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.QUERY_PARAM_ENDPOINT_KEY;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_API_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_ENDPOINT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

class BaseTtlSetup {

  public static final String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.CLIENT_API_BASE_URL);
  public static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.CLIENT_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(
      String authToken, boolean isAuthRequired, FirebaseHelper.FansUnitedProject project,
      String email, String clientId, String apiId, String endpoint, String endpointsApiKey,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification = requestWithAuth(project,
            AuthConstants.FansUnitedClientsProject.ADMIN_USER,
            AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification = requestWithAuth(project,
          email,
          AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_CLIENT_ID, clientId);
    }

    if (endpointsApiKey != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_ENDPOINT_KEY, endpointsApiKey);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (apiId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_API_ID, apiId);
    }

    if (endpoint != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ENDPOINT, endpoint);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }
    return requestSpecification;
  }
}
