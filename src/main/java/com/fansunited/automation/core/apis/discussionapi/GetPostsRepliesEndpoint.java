package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.ApiConstants.DiscussionsApi.SKIP_MODERATED_PROP;
import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_POST_ID;
import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.QUERY_PARAM_LIMIT;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetPostsRepliesEndpoint extends BaseSetup {

  public static Response getRepliesForDiscussionPost(
      String postId,
      String clientId,
      FansUnitedProject project,
      String email,
      String apiKey,
      ContentType contentType,
      int limit,
      Boolean skipModerated)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    if (skipModerated != null && !skipModerated) {
      requestSpecification = requestSpecification.queryParam(SKIP_MODERATED_PROP, false);
    }

    return requestSpecification
        .pathParams(PATH_PARAM_POST_ID, postId)
        .queryParam(QUERY_PARAM_LIMIT, limit)
        .when()
        .get(Endpoints.DiscussionApi.GET_REPLIES_POSTS);
  }
}
