package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.CUSTOM_LEADERBOARDS_ID;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.LEADERBOARD_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.predictionapi.customLeaderBoard.CustomLeaderBoardGameInfo;
import com.fansunited.automation.model.predictionapi.customLeaderBoard.CustomLeaderBoardRequest;
import com.fansunited.automation.model.predictionapi.customLeaderBoard.CustomLeaderboardResponse;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class CustomLeaderboardBaseSetup extends BaseSetup {

  private static String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(
      String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject,
      boolean isAuthRequired,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification =
            requestWithAuth(
                tokenForProject, ADMIN_USER, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification =
          requestWithAuth(tokenForProject, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  public static Response createCustomLeaderboard(
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .body(body)
        .post(Endpoints.PredictionApi.CREATE_CUSTOM_LEADERBOARD);
  }

  public static Response getCustomLeaderboard(
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      String leaderboardId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(LEADERBOARD_ID, leaderboardId)
        .get(Endpoints.PredictionApi.GET_CUSTOM_LEADERBOARD);
  }

  public static Response deleteCustomLeaderboard(
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      String leaderboardId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(LEADERBOARD_ID, leaderboardId)
        .delete(Endpoints.PredictionApi.GET_CUSTOM_LEADERBOARD);
  }

  public static Response getCustomLeaderboardRanking(
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      String leaderboardId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(CUSTOM_LEADERBOARDS_ID, leaderboardId)
        .get(Endpoints.PredictionApi.GET_CUSTOM_LEADERBOARD_RANKINGS);
  }

  public static CustomLeaderBoardRequest createCustomLeaderboardRequest(
   String standingGameId,
      GameType.CustomGameType standingGameType,
      String bracketId,
      GameType.CustomGameType bracketGameType,
      GameStatus status) {

    var faker = new Faker();
    var images = faker.internet().avatar();

    return CustomLeaderBoardRequest.builder()
        .customFields(Fields.builder().label2(faker.toString()).build())
        .adContent(faker.toString())
        .labels(Campaign.builder().label(faker.toString()).build())
        .customGameIds(
            List.of(
                CustomLeaderBoardGameInfo.builder()
                    .id(standingGameId)
                    .type(standingGameType)
                    .build(),
                (CustomLeaderBoardGameInfo.builder().id(bracketId).type(bracketGameType).build())))
        .title(faker.toString())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .description(faker.toString())
        .rules(faker.toString())
        .status(status)
        .build();
  }

  public static CustomLeaderBoardRequest createCustomLeaderboardRequestWithoutCustomGameIds(
      GameStatus status) {

    var faker = new Faker();
    var images = faker.internet().avatar();

    return CustomLeaderBoardRequest.builder()
        .customFields(Fields.builder().label2(faker.toString()).build())
        .adContent(faker.toString())
        .labels(Campaign.builder().label(faker.toString()).build())
        .title(faker.toString())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .description(faker.toString())
        .rules(faker.toString())
        .status(status)
        .build();
  }

  public static CustomLeaderboardResponse createLeaderboard(
      String standingGameId,
      GameType.CustomGameType standingGameType,
      String bracketId,
      GameType.CustomGameType bracketGameType,
      GameStatus status)
      throws HttpException {

    var response =
        createCustomLeaderboard(
            createCustomLeaderboardRequest(
               standingGameId, standingGameType, bracketId, bracketGameType, status),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    return response.as(CustomLeaderboardResponse.class);
  }

  public static CustomLeaderboardResponse createSimpleLeaderboard(GameStatus status)
      throws HttpException {

    var response =
        createCustomLeaderboard(
            createCustomLeaderboardRequestWithoutCustomGameIds(status),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    return response.as(CustomLeaderboardResponse.class);
  }

  public static CustomLeaderboardResponse deleteSimpleLeaderboard(GameStatus status)
      throws HttpException {

    var response =
        createCustomLeaderboard(
            createCustomLeaderboardRequestWithoutCustomGameIds(status),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    return response.as(CustomLeaderboardResponse.class);
  }


}
