package com.fansunited.automation.core.apis.minigames.eitherOr;

import static com.fansunited.automation.constants.UrlParamValues.MiniGamesApi.EITHER_OR_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ParticipateInTriviaGame extends EitherOrBaseSetup {

  public static Response participateInEitherOr(Object body,String triviaGameId, String clientId,
      FirebaseHelper.FansUnitedProject project, String email,
      String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(EITHER_OR_ID,triviaGameId )
        .body(body)
        .when()
        .post(Endpoints.MiniGamesApi.PLAY_IN_EITHER_OR);
  }

}
