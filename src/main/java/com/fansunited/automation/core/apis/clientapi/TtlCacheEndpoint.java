package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class TtlCacheEndpoint extends BaseTtlSetup {

  public static Response getCacheTtlConfig(String clientId,
      FirebaseHelper.FansUnitedProject project, String apiId, String endpoint,
      String endpointApiKey, String apiKey, ContentType contentType)
      throws HttpException {

    return getCacheTtlConfiguration(null, true, ADMIN_USER, clientId, project,
        apiId, endpoint, endpointApiKey, apiKey, contentType);
  }

  public static Response putCacheTtlConfig(String clientId, Object body,
      FirebaseHelper.FansUnitedProject project,
      String endpointApiKey, String apiKey, ContentType contentType)
      throws HttpException {

    return updateCacheTtlConfiguration(null, true, ADMIN_USER, body, clientId, project,
        endpointApiKey, apiKey, contentType);
  }

  public static Response getCacheTtlConfiguration(String authToken, boolean isAuthRequired,
      String email,
      String clientId, FirebaseHelper.FansUnitedProject project, String apiId, String endpoint,
      String endpointsApiKey, String apiKey,
      ContentType contentType) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(authToken, true,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiId,
            endpoint, endpointsApiKey, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.CACHE_TTL_CONFIG);
  }

  public static Response updateCacheTtlConfiguration(String authToken, boolean isAuthRequired,
      String email, Object body,
      String clientId, FirebaseHelper.FansUnitedProject project,
      String endpointsApiKey, String apiKey,
      ContentType contentType) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(authToken, true,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, ADMIN_USER, clientId, null, null,
            endpointsApiKey, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .body(body)
        .when()
        .patch(Endpoints.ClientApi.CACHE_TTL_CONFIG);
  }
}

