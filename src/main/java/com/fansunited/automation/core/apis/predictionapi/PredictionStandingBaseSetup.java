package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.STANDING_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.STANDING;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;
import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.standing.Meta;
import com.fansunited.automation.model.predictionapi.standing.request.CustomStandingGameRequest;
import com.fansunited.automation.model.predictionapi.standing.request.StandingResponse;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class PredictionStandingBaseSetup extends BaseSetup {

  private static String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(
      String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject,
      boolean isAuthRequired,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification =
            requestWithAuth(
                tokenForProject, ADMIN_USER, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification =
          requestWithAuth(tokenForProject, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  public static Response createCustomStandingGame(
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification.when().body(body).post(Endpoints.PredictionApi.STANDING_GAME);
  }

  public static Response updateCustomStandingGame(
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String gameId,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(STANDING_ID, gameId)
        .body(body)
        .put(Endpoints.PredictionApi.STANDING_GAME_BY_ID);
  }

  public static Response participateStandingGame(
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String standingGameId,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParams(STANDING_ID, standingGameId)
        .body(body)
        .post(Endpoints.PredictionApi.PARTICIPATE_IN_STANDING_GAME);
  }

  private static final Faker faker = new Faker();

  public static CustomStandingGameRequest createStandingGameRequest(
      int points, GameStatus status, int outcomeCount) {

    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var images = faker.internet().avatar();

    return CustomStandingGameRequest.builder()
        .meta(
            Meta.builder()
                .participants(
                    List.of(
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("A")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build(),
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("B")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build(),
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("C")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build()))
                .build())
        .type(STANDING.getValue())
        .outcomeCount(outcomeCount)
        .id(faker.internet().uuid())
        .title(faker.toString())
        .description(faker.toString())
        .rules(faker.toString())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .points(points)
        .predictionsCutoff(date)
        .status(status)
        .build();
  }

  public static CustomStandingGameRequest updateStandingGameRequest(
      int points, GameStatus status, int outcomeCount, String outcome) {

    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var images = faker.internet().avatar();

    return CustomStandingGameRequest.builder()
        .meta(
            Meta.builder()
                .participants(
                    List.of(
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("A")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build(),
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("B")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build(),
                        com.fansunited.automation.model.predictionapi.standing.Participant.builder()
                            .id("C")
                            .name(faker.toString())
                            .image(images)
                            .undecided(false)
                            .build()))
                .build())
        .outcomeCount(outcomeCount)
        .outcome(List.of(outcome))
        .id(faker.internet().uuid())
        .title(faker.toString())
        .description(faker.toString())
        .rules(faker.toString())
        .images(Images.builder().cover(images).main(images).mobile(images).build())
        .points(points)
        .type(STANDING.getValue())
        .predictionsCutoff(date)
        .status(status)
        .build();
  }

  public static StandingResponse createStandingGame(GameStatus status, int outcomeCount, int points)
      throws HttpException {

    var response =
        PredictionStandingBaseSetup.createCustomStandingGame(
            createStandingGameRequest(points, status, outcomeCount),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    return response.as(StandingResponse.class);
  }

  public static UpdateStandingResponse updateStandingGameStandingGame(GameStatus status, int outcomeCount, int points,String outcome)
      throws HttpException {

    var response =
        PredictionStandingBaseSetup.createCustomStandingGame(
            updateStandingGameRequest(points, status, outcomeCount,outcome),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    return response.as(UpdateStandingResponse.class);
  }
}
