package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.LIMIT_PARAM_MAX_VALUE_MATCHES;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_COMPETITIONS;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_COUNTRIES;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_MATCH_IDS;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_PAGE;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_FIELD;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_ORDER;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_TEAM_IDS;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_FROM_DATE;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_STATUS;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_TO_DATE;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.model.footballapi.matches.FootballMatchesData;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import org.apache.http.HttpException;

public class MatchesEndpoint extends BaseSetup {

  public static Match getRandomMatchDto(
      String fromDate, String toDate, String sortField, String sortOrder) {

    var matchesData = MatchesEndpoint.getMatchesWithFilterDate(fromDate, toDate,
            UrlParamValues.Language.EN.getValue(), -1, -1, sortField, sortOrder)
        .as(FootballMatchesData.class)
        .getData();

    return matchesData.get(new Random().nextInt(matchesData.size()));
  }

  public static Match getRandomMatchDto() {

    return MatchesEndpoint.getRandomMatchDto(null,
        generateDateTimeInIsoFormat(ZonedDateTime.now().plusDays(10)),
        ApiConstants.FootballApi.SortField.DATE.getValue(),
        ApiConstants.SortOrder.DESC.getValue());
  }

  public static Match getRandomFinishedMatchDto(int minusDays) {

    return MatchesEndpoint.getRandomMatchDto(null,
        generateDateTimeInIsoFormat(ZonedDateTime.now().minusDays(minusDays)),
        ApiConstants.FootballApi.SortField.DATE.getValue(),
        ApiConstants.SortOrder.DESC.getValue());
  }

  public static List<String> getFinishedMatchesIdList(List<String> competitionIds,
      int numberOfMatches) {

    String fromDate = generateDateTimeInIsoFormat(ZonedDateTime.now().minusYears(1));
    String toDate = generateDateTimeInIsoFormat(ZonedDateTime.now().minusHours(24));

    List<String> matchesIdList =
        getMatchesFromDateToDate(
                competitionIds,
                UrlParamValues.Language.EN.getValue(),
                fromDate,
                toDate,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON)
            .then()
            .extract()
            .body()
            .jsonPath()
            .getList("data." + ID_PROP);

    return matchesIdList.subList(0, numberOfMatches);
  }

  public static Response getMatchesFromDateToDate(
      List<String> competitionIds,
      String lang,
      String fromDate,
      String toDate,
      String apiKey,
      ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, CLIENT_AUTOMATION_ID, apiKey, contentType);

    return requestSpecification
        .queryParam(QUERY_PARAM_COMPETITIONS, String.join(",", competitionIds))
        .queryParam(QUERY_PARAM_FROM_DATE, fromDate)
        .queryParam(QUERY_PARAM_TO_DATE, toDate)
        .queryParam(PATH_PARAM_CLIENT_ID, CLIENT_AUTOMATION_ID)
        .queryParam(QUERY_PARAM_LIMIT, LIMIT_PARAM_MAX_VALUE_MATCHES)
        .when()
        .get(Endpoints.FootballApi.MATCHES);
  }

  public static String getSingleMatchIdAfterDate(
      List<String> competitionIds, String dateTimeInIsoFormat) {
    return getMatchesIdListAfterDate(competitionIds, dateTimeInIsoFormat, 1).get(0);
  }

  public static List<String> getMatchesIdListAfterDate(
      List<String> competitionIds, String dateTimeInIsoFormat, int numberOfMatches) {

    List<String> matchesIdList =
        getMatchesAfterDate(
                competitionIds, UrlParamValues.Language.EN.getValue(), dateTimeInIsoFormat)
            .then()
            .extract()
            .jsonPath()
            .getList("data." + ID_PROP);

    if (numberOfMatches < matchesIdList.size()) {
      return matchesIdList.subList(0, numberOfMatches);
    }
    return matchesIdList;
  }

  public static Response getMatchesAfterDate(
      List<String> competitionIds, String lang, String fromDate) {

    return getMatches(
        null,
        String.join(",", competitionIds),
        null,
        null,
        null,
        fromDate,
        null,
        LIMIT_PARAM_MAX_VALUE_MATCHES,
        -1,
        null,
        null,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesWithFilterDate(
      String fromDate,
      String toDate,
      String lang,
      int limit,
      int page,
      String sortField,
      String sortOrder) {

    return getMatches(
        null,
        null,
        null,
        null,
        null,
        fromDate,
        toDate,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesForTeamId(
      String teamId,
      String fromDate,
      String toDate,
      String lang,
      int limit,
      int page,
      String sortField,
      String sortOrder) {

    return getMatches(
        null,
        null,
        null,
        null,
        teamId,
        fromDate,
        toDate,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesWithFilterTeamIds(
      String teamIds, String lang, int limit, int page, String sortField, String sortOrder) {

    return getMatches(
        null,
        null,
        null,
        null,
        teamIds,
        null,
        null,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesWithFilterMatchIds(
      String matchIds, String lang, int limit, int page, String sortField, String sortOrder) {

    return getMatches(
        null,
        null,
        null,
        matchIds,
        null,
        null,
        null,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesWithFilterStatus(
      String status, String lang, int limit, int page, String sortField, String sortOrder) {

    return getMatches(
        null,
        null,
        status,
        null,
        null,
        null,
        null,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesWithFilterCompetitions(
      String competitionIds, String lang, int limit, int page, String sortField, String sortOrder) {

    return getMatches(
        null,
        competitionIds,
        null,
        null,
        null,
        null,
        null,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatchesWithFilterCountries(
      String countryIds, String lang, int limit, int page, String sortField, String sortOrder) {

    return getMatches(
        countryIds,
        null,
        null,
        null,
        null,
        null,
        null,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatches(String lang, String apiKey, ContentType contentType) {

    return getMatches(
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        -1,
        -1,
        null,
        null,
        lang,
        apiKey,
        contentType,
        null);
  }

  public static Response getMatches() {

    return getMatches(
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        -1,
        -1,
        null,
        null,
        UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatches(
      String lang, int limit, int page, String sortField, String sortOrder) {

    return getMatches(
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        limit,
        page,
        sortField,
        sortOrder,
        lang,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getMatches(
      String countries,
      String competitions,
      String status,
      String matchIds,
      String teamIds,
      String fromDate,
      String toDate,
      int limit,
      int page,
      String sortField,
      String sortOrder,
      String lang,
      String apiKey,
      ContentType contentType,
      String clientId) {

    var requestSpecification =
        getRequiredRequestSpec(lang, CLIENT_AUTOMATION_ID, apiKey, contentType);

    if (countries != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_COUNTRIES, countries);
    }
    if (competitions != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_COMPETITIONS, competitions);
    }
    if (status != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_STATUS, status);
    }
    if (matchIds != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_MATCH_IDS, matchIds);
    }
    if (teamIds != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TEAM_IDS, teamIds);
    }
    if (fromDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_FROM_DATE, fromDate);
    }
    if (toDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TO_DATE, toDate);
    }
    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }
    if (page != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_PAGE, page);
    }
    if (sortField != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_FIELD, sortField);
    }
    if (sortOrder != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_ORDER, sortOrder);
    }
    if (clientId != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, CLIENT_AUTOMATION_ID);
    }

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification.when().get(Endpoints.FootballApi.MATCHES);
  }

  /**
   * The method returns a list of matches. Each match has different kickoff date.
   *
   * @param gameType the type of game that will be utilizing the matches
   * @param hoursAhead how may hours ahead to be their start time
   * @param numberOfMatches how many matches to return
   * @return list of matches
   * @throws HttpException exception
   */
  public static List<Match> getUniqueMatchesWithDifferentKickoffDate(
      GameType gameType, int hoursAhead, int numberOfMatches) throws HttpException {

    Response response =
        getMatchesAfterDate(
            getCompetitionsWhitelist(gameType),
            UrlParamValues.Language.EN.getValue(),
            generateDateTimeInIsoFormat(generateFutureDate(hoursAhead)));

    List<Match> matches = new ArrayList<>();
    List<Match> allMatches = response.getBody().jsonPath().getList("data", Match.class);
    String kickoffTime = "";
    int count = 0;
    while (numberOfMatches > 0) {

      if (!kickoffTime.equals(String.valueOf(allMatches.get(count).getKickoffAt()))) {
        matches.add(allMatches.get(count));
        numberOfMatches--;
        kickoffTime = String.valueOf(allMatches.get(count).getKickoffAt());
      }
      count++;
    }

    return matches;
  }
}
