package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.PATH_PARAM_TEMPLATE_ID;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.QUERY_PARAM_PAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;
import org.apache.http.HttpException;

public class LeaderboardByIdEndpoint extends BaseSetup {

  // GET /v1/leaderboard/{templateId}
  public static Response getLeaderboardForTemplateWithId(String templateId, Integer page,
      Integer limit) throws HttpException {

    return getLeaderboardForTemplateWithId(templateId, CLIENT_AUTOMATION_ID, page, limit);
  }

  public static Response getLeaderboardForTemplateWithId(String templateId, String clientId,
      Integer page, Integer limit)
      throws HttpException {

    return getLeaderboardForTemplateWithId(templateId, page, limit, clientId,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, true);
  }

  public static Response getLeaderboardForTemplateWithIdNoCache(String templateId, String clientId,
      Integer page, Integer limit)
      throws HttpException {

    return getLeaderboardForTemplateWithId(templateId, page, limit, clientId,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, true);
  }

  public static Response getLeaderboardForTemplateWithId(String templateId, Integer page,
      Integer limit, String clientId,
      String apiKey,
      ContentType contentType, boolean avoidCache)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, true, ADMIN_USER, clientId, apiKey,
            contentType);

    if (page != null) {
      requestSpecification = requestSpecification.queryParam(
          QUERY_PARAM_PAGE, page);
    }

    if (limit != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (avoidCache) {
      // Avoid cache
      requestSpecification =
          requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));
    }

    return requestSpecification
        .pathParam(PATH_PARAM_TEMPLATE_ID, templateId)
        .when()
        .get(Endpoints.LoyaltyApi.LEADERBOARD_BY_ID);
  }
}
