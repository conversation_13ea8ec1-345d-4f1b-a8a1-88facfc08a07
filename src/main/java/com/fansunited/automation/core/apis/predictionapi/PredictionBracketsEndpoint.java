package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.BRACKET_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class PredictionBracketsEndpoint extends AuthBase {

  private static String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(
      String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject,
      boolean isAuthRequired,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification =
            requestWithAuth(
                tokenForProject, ADMIN_USER, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification =
          requestWithAuth(tokenForProject, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  public static Response predictionBracket(
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification.when().body(body).post(Endpoints.PredictionApi.BRACKET_GAME);
  }

  public static Response getPredictionBracketGame(
      String bracketId,
      FirebaseHelper.FansUnitedProject project,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, false, null, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParams(BRACKET_ID, bracketId)
        .get(Endpoints.PredictionApi.GET_BRACKET_GAME);
  }

  public static Response updateBracketGame(
      String bracketId,
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParams(BRACKET_ID, bracketId)
        .body(body)
        .put(Endpoints.PredictionApi.GET_BRACKET_GAME);
  }
  public static Response participateBracketGame(
      String bracketId,
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParams(BRACKET_ID, bracketId)
        .body(body)
        .post(Endpoints.PredictionApi.PARTICIPATE_IN_BRACKET_GAME);
  }
}
