package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.PATH_PARAM_ACTIVITY_ID;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.QUERY_PARAM_ACTION;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.QUERY_PARAM_PAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.loyaltyapi.enums.ActivityActionType;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.loyaltyapi.activity.request.ActivityRequest;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.loyaltyapi.activity.request.Content;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;

public class ActivitiesEndpoint extends BaseSetup {

  // DELETE /v1/activities/{id}

  public static Response deleteActivity(String activityId, String email) throws HttpException {
    return deleteActivity(activityId, email, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response deleteActivity(String activityId, String email, String clientId,
      String apiKey, ContentType contentType) throws HttpException {
    return deleteActivity(null, activityId, email,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, true, clientId,
        apiKey, contentType);
  }

  public static Response deleteActivity(String authToken, String activityId, String email,
      FirebaseHelper.FansUnitedProject project, boolean isAuthRequired,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project,
            isAuthRequired, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_ACTIVITY_ID, activityId)
        .queryParam(QUERY_PARAM_KEY,apiKey)
        .when()
        .delete(Endpoints.LoyaltyApi.ACTIVITY_BY_ID);
  }

  // GET /v1/activities

  public static Response getOwnActivities(String email, String filterAction) throws HttpException {
    return getOwnActivities(filterAction, email, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getOwnActivities(String filterAction, String email, String clientId,
      String apiKey,
      ContentType contentType) throws HttpException {
    return getOwnActivities(null, filterAction,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, true,
        email, clientId, apiKey, contentType);
  }

  public static Response getOwnActivities(String authToken, String filterAction,
      FirebaseHelper.FansUnitedProject project, boolean isAuthRequired, String email,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project,
            isAuthRequired, email, clientId, apiKey,
            contentType);

    if (filterAction != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ACTION, filterAction);
    }

    return requestSpecification
        .when()
        .queryParam(QUERY_PARAM_KEY,apiKey)
        .get(Endpoints.LoyaltyApi.ACTIVITIES);
  }

  // POST /v1/activities

  public static Response createActivity(String userEmail, String action)
      throws HttpException {

    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(ActivityActionType.LIKE.getValue());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(action)
        .context(context).build();

    return createActivity(body, userEmail, CLIENT_AUTOMATION_ID,AuthConstants.ENDPOINTS_API_KEY);
  }

  public static Response createActivity(Object body, String email, String clientId,  String apiKey)
      throws HttpException {
    return createActivity(body, email, clientId, apiKey,
        ContentType.JSON);
  }

  public static Response createActivity(Object body, String email, String clientId, String apiKey,
      ContentType contentType) throws HttpException {
    return createActivity(null, body, true, email,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, clientId, apiKey, contentType);
  }

  public static Response createActivity(String authToken, Object body, boolean isAuthRequired,
      String email, FirebaseHelper.FansUnitedProject tokenForProject, String clientId,
      String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, tokenForProject,
            isAuthRequired, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .body(body)
        .queryParam(QUERY_PARAM_KEY,apiKey)
        .when()
        .post(Endpoints.LoyaltyApi.ACTIVITIES);
  }

  public static Response getOwnActivities(String email, String filterAction, int pageNumber) throws HttpException {
    return getOwnActivities(null, filterAction, pageNumber, email, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        true, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getOwnActivities(String authToken, String filterAction, int pageNumber,
      String email, FirebaseHelper.FansUnitedProject project, boolean isAuthRequired,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project,
            isAuthRequired, email, clientId, apiKey,
            contentType);

    if (filterAction != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ACTION, filterAction);
    }

    if (filterAction != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }
    if (pageNumber > 0) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_PAGE, pageNumber);
    } else {
      throw new RuntimeException("page: Page cannot be zero or negative number.");
    }

    return requestSpecification
        .when()
        .get(Endpoints.LoyaltyApi.ACTIVITIES);
  }
}
