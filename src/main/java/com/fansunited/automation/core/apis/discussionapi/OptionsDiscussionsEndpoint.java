package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class OptionsDiscussionsEndpoint extends BaseSetup {

  public static Response optionsDiscussionApi(String endpoint) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            null,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            true,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification.when().options(endpoint);
  }
}
