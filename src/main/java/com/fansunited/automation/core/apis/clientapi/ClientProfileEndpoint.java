package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.QUERY_PARAM_ENDPOINT_KEY;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ClientProfileEndpoint extends BaseSetup {

  public static Response getProfile(String authToken, FirebaseHelper.FansUnitedProject project,
      boolean isAuthRequired, String email,
      String endpointsApiKey,
      String apiKey, ContentType contentType) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, authToken, true, project, email,
            null, null, apiKey, endpointsApiKey, contentType);

    if (endpointsApiKey != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_ENDPOINT_KEY, endpointsApiKey);
    }

    return requestSpecification
        .when()
        .get(Endpoints.ClientApi.GET_PROFILE_FOR_USER);
  }

  public static Response getProfile(String endpointApiKey, ContentType contentType)
      throws HttpException {
    return getProfile(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, true, ADMIN_USER,
        endpointApiKey, null,
        contentType);
  }
}

