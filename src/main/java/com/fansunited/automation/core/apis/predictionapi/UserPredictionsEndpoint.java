package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.QUERY_PARAM_GAME_TYPES;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_START_AFTER;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_STATUS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.HttpValues;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import io.restassured.http.ContentType;
import io.restassured.http.Header;
import io.restassured.response.Response;
import java.util.UUID;
import org.apache.http.HttpException;

public class UserPredictionsEndpoint extends BaseSetup {

  public static Response getPredictionsForSpecificUser(String userId)
      throws HttpException {

    return getPredictionsForSpecificUser(userId, null, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, -1, null);
  }

  public static Response getPredictionsForSpecificUser(String userId, String status)
      throws HttpException {

    return getPredictionsForSpecificUser(userId, status, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, -1, null);
  }

  public static Response getPredictionsForSpecificUserWithOriginHeader(String origin, String userId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null, null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .header(new Header(HttpValues.HeaderKey.ORIGIN, origin))
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS_BY_USER_ID);
  }

  public static Response getPredictionsForSpecificUser(String userId, String status,
      String clientId,
      String apiKey,
      ContentType contentType, int limit, String startAfter)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    if (status != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_STATUS, status);
    }

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS_BY_USER_ID);
  }

  public static Response getPredictionsForSpecificUser(String userId, GameType gameType, PredictionStatus status)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null, null, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    requestSpecification = requestSpecification
        .queryParam(QUERY_PARAM_GAME_TYPES, gameType.getValue())
        .queryParam(QUERY_PARAM_STATUS, status.getValue());

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS_BY_USER_ID);
  }
}
