package com.fansunited.automation.core.apis.reportingapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_TO_DATE;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_BYPASS_CACHE;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_FROM_DATE;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_GROUP_BY;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_MARKETS;
import static com.fansunited.automation.core.apis.reportingapi.BaseSetup.getRequiredRequestSpec;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.apache.http.HttpException;

@Builder
@AllArgsConstructor
public class ClientPredictionsEndpoint {
  private String clientId;
  private String apiKey;
  private ContentType contentType;
  private String authToken;
  private String email;
  private FirebaseHelper.FansUnitedProject project;
  private List<String> marketList;
  private GroupBy groupBy;
  private String fromDate;
  private String toDate;
  private Integer byPassCache;
  private boolean withoutAuth;

   public Response getClientPredictionCount() throws HttpException {

     var requestSpecification = getRequestSpecification();

     return requestSpecification
         .when()
         .get(Endpoints.ReportingApi.CLIENT_PREDICTIONS_FOR_PERIOD);
   }

  public Response getClientTotalPredictions() throws HttpException {

    var requestSpecification = getRequestSpecification();

    return requestSpecification
        .when()
        .get(Endpoints.ReportingApi.CLIENT_TOTAL_PREDICTIONS);
  }

   private RequestSpecification getRequestSpecification() throws HttpException {

     if(clientId == null) {
       clientId = CLIENT_AUTOMATION_ID;
     }

     if(apiKey == null) {
       apiKey = AuthConstants.ENDPOINTS_API_KEY;
     }

     if(contentType == null) {
       contentType = ContentType.JSON;
     }

     if(email == null) {
       if(!withoutAuth) {
         email = ADMIN_USER;
       }
     }

     if(project == null) {
       project = FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
     }

     var requestSpecification =
         getRequiredRequestSpec(authToken, project,
             email, clientId, apiKey, contentType);

     if(marketList != null){
       requestSpecification.queryParam(QUERY_PARAM_MARKETS, String.join(",", marketList));
     }

     if(groupBy != null){
       requestSpecification.queryParam(QUERY_PARAM_GROUP_BY, groupBy.getValue());
     }

     if(fromDate != null){
       requestSpecification.queryParam(QUERY_PARAM_FROM_DATE, fromDate);
     }

     if(toDate != null) {
       requestSpecification.queryParam(QUERY_PARAM_TO_DATE, toDate);
     }

     if(byPassCache != null) {
       requestSpecification.queryParam(QUERY_PARAM_BYPASS_CACHE, byPassCache);
     }

     return  requestSpecification;
   }
}
