package com.fansunited.automation.core.apis.minigames;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.minigames.classicquiz.QuizBaseSetup;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class OptionsMiniGamesEndpoint extends QuizBaseSetup {

  public static Response optionsMiniGamesApi(String endpoint) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            null,
            null,
            false,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification.when().options(endpoint);
  }
}
