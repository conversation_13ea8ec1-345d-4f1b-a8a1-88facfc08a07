package com.fansunited.automation.core.apis.resolverapi;

import com.fansunited.automation.constants.Endpoints;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ResolverEndpoint extends BaseSetup {

  public static Response resolve() {
    try {
      return getRequiredRequestSpec()
          .when()
          .get(Endpoints.ResolverApi.EXECUTE);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
