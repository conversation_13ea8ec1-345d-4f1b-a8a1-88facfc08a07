package com.fansunited.automation.core.apis.leagueapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class OptionsLeagueEndpoint extends BaseSetup {

  public static Response optionsLeaguesApi(String endpoint) throws HttpException {

    RequestSpecification requestSpecification =
        BaseSetup.getRequiredRequestSpec(
           null,
            AuthConstants.ENDPOINTS_API_KEY,
            CLIENT_AUTOMATION_ID,
            ContentType.JSON,
            null,
            null);

    return requestSpecification.when().options(endpoint);
  }
}
