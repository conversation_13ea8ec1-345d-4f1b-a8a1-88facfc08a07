package com.fansunited.automation.core.apis.reportingapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_EITHER_OR_IDS;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_FROM_DATE;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_GROUP_BY;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_PARTICIPATION_STATUS;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.QUERY_PARAM_TO_DATE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import org.apache.http.HttpException;

public class EitherOrParticipationEndpoint extends BaseSetup {

  public static Response getEitherOrParticipation(
      String clientId,
      String apiKey,
      String authToken,
      String email,
      FirebaseHelper.FansUnitedProject project,
      ContentType contentType,
      String fromDate,
      String toDate,
      GroupBy groupBy,
      String participationStatus,
      List<String> eitherOrIds)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, email, clientId, apiKey, contentType);

    if (fromDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_FROM_DATE, fromDate);
    }

    if (toDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TO_DATE, toDate);
    }

    if (groupBy != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_GROUP_BY, groupBy.getValue());
    }

    if (participationStatus != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_PARTICIPATION_STATUS, participationStatus);
    }

    if (eitherOrIds != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_EITHER_OR_IDS, String.join(",", eitherOrIds));
    }

    return requestSpecification.when().get(Endpoints.ReportingApi.EITHER_OR_PARTICIPATIONS);
  }

  public static Response getEitherOrParticipation(
      String fromDate,
      String toDate,
      GroupBy groupBy,
      String participationStatus,
      List<String> eitherOrIds)
      throws HttpException {

    return getEitherOrParticipation(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        null,
        ADMIN_USER,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        ContentType.JSON,
        fromDate,
        toDate,
        groupBy,
        participationStatus,
        eitherOrIds);
  }
}
