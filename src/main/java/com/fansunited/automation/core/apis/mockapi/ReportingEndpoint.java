package com.fansunited.automation.core.apis.mockapi;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import io.restassured.response.Response;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReportingEndpoint extends BaseSetup {

  private static final Logger LOG = LoggerFactory.getLogger(ReportingEndpoint.class);

  public static void truncateTables() {
    LOG.info("Truncating tables...");
    getRequiredRequestSpec().delete(Endpoints.MockApi.TRUNCATE);
    RedisEndpoint.clearAllRedis();
    try {
      TimeUnit.SECONDS.sleep(120); // Wait for truncate completion
    } catch (InterruptedException e) {
      LOG.error("Truncate sleep interrupted", e);
      Thread.currentThread().interrupt(); // Restore interrupted status
    }

    LOG.info("Truncate complete.");
  }

  public static Response truncateAuditLog() {
    LOG.info("Truncating tables...");
    var response = getRequiredRequestSpecForAuditLog()
        .delete(Endpoints.MockApi.TRUNCATE);
    if (response.getStatusCode() != HttpStatus.SC_OK) {
      response.then().log().all();
      throw new RuntimeException("Could NOT truncate tables");
    }
    try {
      TimeUnit.SECONDS.sleep(15); // Wait for truncate completion
    } catch (InterruptedException e) {
      LOG.error("Truncate sleep interrupted", e);
      Thread.currentThread().interrupt(); // Restore interrupted status
    }
    return response;
  }

}
