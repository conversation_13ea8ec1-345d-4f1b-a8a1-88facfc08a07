package com.fansunited.automation.core.apis.clientapi;

public class Vectors {
  /**
   * Finds the shortest vector from the given array of vectors.
   * The shortest vector is the one with the smallest magnitude (length).
   * For a vector, the magnitude is calculated as the square root of the sum of squares of its components.
   *
   * @param vectors Array of vectors, where each vector is represented as an array of integers
   * @return The shortest vector from the input array
   * @throws IllegalArgumentException if the input array is empty or null
   */
  public static int[] findShortest(int[][] vectors) {



    int[] shortestVector = vectors[0];
    double shortestMagnitude = calculateMagnitude(shortestVector);

    for (int i = 1; i < vectors.length; i++) {
      double currentMagnitude = calculateMagnitude(vectors[i]);
      if (currentMagnitude < shortestMagnitude) {
        shortestVector = vectors[i];
        shortestMagnitude = currentMagnitude;
      }
    }

    return shortestVector;
  }

  /**
   * Calculates the magnitude (length) of a vector.
   * The magnitude is the square root of the sum of squares of the vector's components.
   *
   * @param vector The vector whose magnitude is to be calculated
   * @return The magnitude of the vector
   */
  private static double calculateMagnitude(int[] vector) {
    double sumOfSquares = 0;
    for (int component : vector) {
      sumOfSquares += component * component;
    }
    return Math.sqrt(sumOfSquares);
  }

  public static void main(String[] args) {
    int[][] vectors = {
        new int[] { 1, 1, 1 },
        new int[] { 2, 2, 2 },
        new int[] { 3, 3, 3 }
    };

    int[] shortest = Vectors.findShortest(vectors);
    System.out.println("x: "+ shortest[0] + " y: "+ shortest[1] +" z: " + shortest[2]);
  }
}