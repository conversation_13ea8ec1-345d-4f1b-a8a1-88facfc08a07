package com.fansunited.automation.core.apis.clientapi;

public class Shipping {
  public static long minimalNumberOfPackages(long items, long availableLargePackages, long availableSmallPackages) {



    //int[] large= can be 5
    //int[] small= can hold only 1
    //int[] x= items

    long maxLarge = Math.min(availableLargePackages, items / 5);
    long maxSmall = items % 5;

    return items;
  }

  public static void main(String[] args) {
    System.out.println(minimalNumberOfPackages(items,availableLargePackages,availableSmallPackages));
  }
}
}
