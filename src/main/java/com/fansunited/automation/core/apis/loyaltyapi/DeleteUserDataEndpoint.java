package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.Endpoints.LoyaltyApi;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class DeleteUserDataEndpoint extends BaseSetup {

  public static Response deleteUserDataEndpoint(
      String token,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      Object body,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(
            token, project, isAuthRequired, email, clientId, apiKey, contentType);

    return requestSpecification.when().body(body).delete(LoyaltyApi.DELETE_USER_DATA);
  }

  public static Response deleteUserDataEndpoint(Object body) throws HttpException {
    return deleteUserDataEndpoint(
        null,
        true,
        FansUnitedProject.FANS_UNITED_CLIENTS,
        null,
        body,
        CLIENT_AUTOMATION_ID,
        ENDPOINTS_API_KEY,
        ContentType.JSON);
  }
}
