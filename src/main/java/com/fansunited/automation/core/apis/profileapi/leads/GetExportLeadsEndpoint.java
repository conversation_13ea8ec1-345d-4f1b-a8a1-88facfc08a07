package com.fansunited.automation.core.apis.profileapi.leads;


import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.profileapi.BaseSetup;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetExportLeadsEndpoint extends BaseSetup {

  public static Response getExportLeads(
      String fromDate,
      String toDate,
      String campaignId,
      String contentTypeField,
      String contentId,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      boolean isAuthRequired,
      String jwtToken)
      throws HttpException {

    return LeadsRequestBuilder.buildLeadsRequest(
        Endpoints.ProfileApi.EXPORT_LEADS,
        fromDate,
        toDate,
        campaignId,
        contentTypeField,
        contentId,
        email,
        clientId,
        apiKey,
        contentType,
        isAuthRequired,
        jwtToken);
  }
}
