package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_ENTITIES;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SCOPE;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SEARCH_FILTER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;

public class SearchEndpoint extends BaseSetup {

  public static Response search(String searchFilter, String scope) {

    return search(null, searchFilter, scope, UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response search(String entities, String searchFilter, String scope) {

    return search(entities, searchFilter, scope, UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response search(String entities, String searchFilter, String scope, String lang,
      String clientId, String apiKey, ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, clientId, apiKey, contentType);

    if (entities != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ENTITIES, entities);
    }
    if (searchFilter != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_SEARCH_FILTER, searchFilter);
    }
    if (scope != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SCOPE, scope);
    }

    // Avoid cache
    requestSpecification =
            requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .when()
        .get(Endpoints.FootballApi.SEARCH);
  }
}
