package com.fansunited.automation.core.apis.voting.poll;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_START_AFTER;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetUserVotesEndpoint extends VotingBaseSetup {

  public static Response getUserVotes(
      Integer limit,
      String startAfter,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email,
      boolean isAuthRequired)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, isAuthRequired, email, clientId, apiKey, contentType);

    if (limit != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }
    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    return requestSpecification.when().get(Endpoints.VotingApi.GET_OWN_VOTES);
  }

  public static Response getUserVotes(String email, boolean isAuthRequired) throws HttpException {
    return getUserVotes(
        null,
        null,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        email,
        isAuthRequired);
  }
}
