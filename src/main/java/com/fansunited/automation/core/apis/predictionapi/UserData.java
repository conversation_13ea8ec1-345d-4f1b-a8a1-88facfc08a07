package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_PREDICTION_IDS;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class UserData extends BaseSetup {

  public static Response getUserData( String clientId, String apiKey,
      ContentType contentType, int limit, String predictionIds) throws HttpException {

    RequestSpecification requestSpecification =
            getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiKey, contentType);

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
    if (predictionIds != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_PREDICTION_IDS, predictionIds);
    }

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.GET_USER_DATA);
  }
}
