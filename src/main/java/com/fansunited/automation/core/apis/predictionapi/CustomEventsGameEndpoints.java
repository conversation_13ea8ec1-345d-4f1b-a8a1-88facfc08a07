package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.PredictionApi.CUSTOM_EVENT_GAME;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.CUSTOM_EVENT_GAME_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.Endpoints;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class CustomEventsGameEndpoints extends BaseSetup {

  public static Response createCustomEventGame(
      String clientId, String apiKey,String email, ContentType contentType, Object body) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, FANS_UNITED_CLIENTS, email, clientId, apiKey, contentType);

    return requestSpecification.when().body(body).that().post(CUSTOM_EVENT_GAME);
  }

  public static Response getCustomEventGameById(
      String eventId, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(CUSTOM_EVENT_GAME_ID, eventId)
        .get(Endpoints.PredictionApi.CUSTOM_EVENT_GAME_BY_ID);
  }

  public static Response updateCustomEventGame(
      String eventId, String clientId, String apiKey, ContentType contentType, Object body)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(CUSTOM_EVENT_GAME_ID, eventId)
        .body(body)
        .put(Endpoints.PredictionApi.CUSTOM_EVENT_GAME_BY_ID);
  }

  public static Response getCustomEventGames(String clientId, String apiKey, ContentType contentType, String startAfter)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiKey, contentType);

    return requestSpecification.when().get(Endpoints.PredictionApi.CUSTOM_EVENT_GAME);
  }

}
