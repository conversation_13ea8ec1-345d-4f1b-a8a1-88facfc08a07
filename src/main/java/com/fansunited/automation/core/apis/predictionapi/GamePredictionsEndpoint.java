package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_START_AFTER;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class GamePredictionsEndpoint extends BaseSetup {

  public static Response getPredictionsForGame(String gameId) throws HttpException {

    return getPredictionsForGame(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, -1, null);
  }

  public static Response getPredictionsForGame(String gameId, String clientId, String apiKey,
      ContentType contentType, int limit, String startAfter) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS_FOR_GAME);
  }

}
