package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.LIMIT_PARAM_MAX_VALUE_TEAMS;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_COUNTRY_ID;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_GENDER;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_NAME;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_NATIONAL;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_PAGE;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SCOPE;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_FIELD;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_ORDER;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_TEAMS;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.model.footballapi.teams.FootballTeamsData;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TeamsEndpoint extends BaseSetup {

  public static List<String> extractTeamIdsFromResponseWithFilterScope(Response response,
      String scope, String lang, String sortField, String sortOrder) {

    var footballData = response.as(FootballTeamsData.class);
    var totalPages = footballData.getMeta().getPagination().getNumberOfPages();
    var limit = footballData.getMeta().getPagination().getItemsPerPage();
    var currentPage = footballData.getMeta().getPagination().getCurrentPage() + 1;

    var teamIds =
        new ArrayList<String>(response.then().extract().jsonPath().getList("data." + ID_PROP));

    for (int i = currentPage; i <= totalPages; i++) {
      var currResponse = getTeamsWithFilterScope(scope, lang, limit, i, sortField, sortOrder);
      teamIds.addAll(
          currResponse.then()
              .extract()
              .jsonPath()
              .getList("data." + ID_PROP));
    }
    return teamIds;
  }

  public static String getRandomTeamIdFromCompetition(String competitionId) {
    List<String> footballTeamsId =
        getTeamsWithFilterScope(competitionId, UrlParamValues.Language.EN.getValue(), -1, -1, null,
            null)
            .then()
            .extract()
            .body()
            .jsonPath()
            .get("data." + ID_PROP);

    return footballTeamsId.get(
        generateRandomNumber(0, footballTeamsId.size() - 1));
  }

  public static String getRandomTeamId() {

    List<String> footballTeamsId =
        getTeams(null, null, LIMIT_PARAM_MAX_VALUE_TEAMS, null, null, -1, null, null, null, null,
            UrlParamValues.Language.EN.getValue(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON)
            .then()
            .extract()
            .body()
            .jsonPath()
            .get("data." + ID_PROP);

    return footballTeamsId.get(
        generateRandomNumber(0, footballTeamsId.size() - 1));
  }

  public static List<String> getTeamsForCompetition(String competitionId) {

    var response =
        getTeamsWithFilterScope(competitionId, null, LIMIT_PARAM_MAX_VALUE_TEAMS, -1, null, null);

    var footballData = response.as(FootballTeamsData.class);
    var totalPages = footballData.getMeta().getPagination().getNumberOfPages();
    var currentPage = footballData.getMeta().getPagination().getCurrentPage() + 1;

    var teamIds =
        new ArrayList<String>(response.then().extract().jsonPath().getList("data." + ID_PROP));

    for (int i = currentPage; i <= totalPages; i++) {
      teamIds.addAll(
          getTeamsWithFilterScope(competitionId, null, LIMIT_PARAM_MAX_VALUE_TEAMS, i, null,
              null).then()
              .extract()
              .jsonPath()
              .getList("data." + ID_PROP));
    }
    return teamIds;
  }

  public static Response getTeamsWithFilterNational(boolean national, String lang, int limit,
      int page, String sortField, String sortOrder) {

    return getTeams(null, null, limit, null, national, page, null, sortField, sortOrder,
        null, lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeamsWithFilterScope(String scope, String lang, int limit, int page,
      String sortField, String sortOrder) {

    return getTeams(null, null, limit, null, null, page, null, sortField, sortOrder,
        scope, lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeamsWithFilterTeamIds(String teamIds, String lang, int limit, int page,
      String sortField, String sortOrder) {

    return getTeams(null, null, limit, null, null, page, teamIds, sortField, sortOrder,
        null, lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeamsWithFilterName(String name, String lang, int limit, int page,
      String sortField, String sortOrder) {

    return getTeams(null, null, limit, name, null, page, null, sortField, sortOrder,
        null, lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeamsWithFilterGender(String gender, String lang, int limit, int page,
      String sortField, String sortOrder) {

    return getTeams(null, gender, limit, null, null, page, null, sortField, sortOrder,
        null, lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeamsWithFilterCountry(String countryId, String lang, int limit,
      int page,
      String sortField, String sortOrder) {

    return getTeams(countryId, null, limit, null, null, page, null, sortField, sortOrder,
        null, lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeams(int limit, int page, String lang, String apiKey,
      ContentType contentType) {

    return getTeams(null, null, limit, null, null, page, null, null, null, null, lang, apiKey,
        contentType);
  }

  public static Response getTeams(String lang, String apiKey, ContentType contentType) {

    return getTeams(null, null, -1, null, null, -1, null, null, null, null, lang, apiKey,
        contentType);
  }

  public static Response getTeams() {

    return getTeams(null, null, -1, null, null, -1, null, null, null, null,
        UrlParamValues.Language.EN.getValue(), AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getTeams(String countryId, String gender,
      int limit, String name, Boolean national, int page, String teamIds,
      String sortField, String sortOrder, String scope, String lang,
      String apiKey,
      ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, null, apiKey, contentType);

    if (countryId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_COUNTRY_ID, countryId);
    }
    if (gender != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GENDER, gender);
    }
    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }
    if (name != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_NAME, name);
    }
    if (national != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_NATIONAL, national);
    }
    if (page != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_PAGE, page);
    }
    if (teamIds != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TEAMS, teamIds);
    }
    if (sortField != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_FIELD, sortField);
    }
    if (sortOrder != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_ORDER, sortOrder);
    }
    if (scope != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_SCOPE, scope);
    }

    // Avoid cache
    requestSpecification = requestSpecification.queryParam("dummy",
        UUID.randomUUID().toString().replace("-", ""));
    requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID,
        CLIENT_AUTOMATION_ID);

    return requestSpecification
        .when()
        .get(Endpoints.FootballApi.TEAMS);
  }
}
