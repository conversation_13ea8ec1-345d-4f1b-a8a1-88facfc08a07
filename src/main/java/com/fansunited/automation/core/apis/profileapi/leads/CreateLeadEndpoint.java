package com.fansunited.automation.core.apis.profileapi.leads;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.profileapi.BaseSetup;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class CreateLeadEndpoint extends BaseSetup {
  public static Response createLead(
      String email,
      Object body,
      String clientId,
      String apiKey,
      ContentType contentType,
      boolean isAuthRequired,
      String jwtToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            jwtToken, isAuthRequired, email, null, clientId, apiKey, contentType);

    return requestSpecification.body(body).when().post(Endpoints.ProfileApi.LEADS);
  }
}
