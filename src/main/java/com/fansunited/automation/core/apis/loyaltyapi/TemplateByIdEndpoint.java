package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.PATH_PARAM_TEMPLATE_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class TemplateByIdEndpoint extends BaseSetup {

  // PUT /v1/leaderboard/templates/{templateId}

  public static Response updateLeaderboardTemplateWithToken(String authToken, String templateId,
      Object body) throws HttpException {

    return updateLeaderboardTemplate(templateId, body, authToken, null, false, null,
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response updateLeaderboardTemplate(String templateId, Object body)
      throws HttpException {

    return updateLeaderboardTemplate(templateId, body, CLIENT_AUTOMATION_ID);
  }

  public static Response updateLeaderboardTemplate(String templateId, Object body, String clientId)
      throws HttpException {

    return updateLeaderboardTemplate(templateId, body, clientId, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response updateLeaderboardTemplate(String templateId, Object body, String clientId,
      String apikey, ContentType contentType) throws HttpException {

    return updateLeaderboardTemplate(templateId, body, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, true, ADMIN_USER, clientId,
        apikey,
        contentType);
  }

  public static Response updateLeaderboardTemplate(String templateId, Object body, String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject,
      boolean isAuthRequired, String email, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, tokenForProject, isAuthRequired, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_TEMPLATE_ID, templateId)
        .queryParam(QUERY_PARAM_KEY,apiKey)
        .body(body)
        .when()
        .put(Endpoints.LoyaltyApi.TEMPLATE_BY_ID);
  }

  // DELETE /v1/leaderboard/templates/{templateId}
  public static Response deleteLeaderboardTemplate(String templateId) throws HttpException {

    return deleteLeaderboardTemplate(templateId, CLIENT_AUTOMATION_ID);
  }

  public static Response deleteLeaderboardTemplate(String templateId, String clientId)
      throws HttpException {

    return deleteLeaderboardTemplate(templateId, clientId, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response deleteLeaderboardTemplate(String templateId, String clientId,
      String apikey, ContentType contentType) throws HttpException {

    return deleteLeaderboardTemplate(templateId, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, true, ADMIN_USER, clientId,
        apikey,
        contentType);
  }

  public static Response deleteLeaderboardTemplate(String templateId, String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject,
      boolean isAuthRequired, String email, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, tokenForProject, isAuthRequired, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_TEMPLATE_ID, templateId)
        .queryParam(QUERY_PARAM_KEY,apiKey)
        .when()
        .delete(Endpoints.LoyaltyApi.TEMPLATE_BY_ID);
  }
}
