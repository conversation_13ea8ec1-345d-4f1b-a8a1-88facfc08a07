package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_DISCUSSION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetDiscussionsEndpoint extends BaseSetup {

  public static Response getDiscussion(String discussionId, String clientId,
      FirebaseHelper.FansUnitedProject project, String email,
      String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_DISCUSSION_ID, discussionId)
        .when()
        .get(Endpoints.DiscussionApi.GET_DISCUSSION_BY_ID);
  }

  public static Response getDiscussion(String discussionId){
      try {
          return getDiscussion(discussionId,
                  CLIENT_AUTOMATION_ID, FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY,
                  ContentType.JSON);
      } catch (HttpException e) {
          throw new RuntimeException(e);
      }
  }
}
