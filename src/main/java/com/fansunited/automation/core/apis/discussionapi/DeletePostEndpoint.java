package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_POST_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class DeletePostEndpoint extends BaseSetup {

  public static Response deleteDiscussionsPost(String postId, String clientId,
      String apiKey, ContentType contentType, FirebaseHelper.FansUnitedProject project,
      String email, String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POST_ID, postId)
        .when()
        .delete(Endpoints.DiscussionApi.DELETE_USERS_POSTS);
  }
}
