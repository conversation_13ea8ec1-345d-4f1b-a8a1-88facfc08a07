package com.fansunited.automation.core.apis.voting.poll;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.PATH_PARAM_POLL_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints.VotingApi;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetUserVotesForPoll extends VotingBaseSetup {

  public static Response getUserVotesForPoll(
      String pollId,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email,
      boolean isAuthRequired)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, isAuthRequired, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POLL_ID, pollId)
        .when()
        .get(VotingApi.GET_OWN_VOTES_FOR_A_POLL);
  }

  public static Response getUserVotesForPoll(String email, String pollId) throws HttpException {
    return getUserVotesForPoll(
        pollId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        email,
        true);
  }
}
