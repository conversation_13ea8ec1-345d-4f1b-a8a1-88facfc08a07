package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LANG;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;
import static com.fansunited.automation.utils.RestAssuredUtils.baseAuthRequest;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import java.util.Map;
import org.apache.http.HttpException;

public class BaseSetup {

  private static final String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PROFILE_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PROFILE_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(
      String authToken,
      boolean isAuthRequired,
      String email,
      String lang,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification =
            requestWithAuth(FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                getCurrentTestUser().getEmail(),
                AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification =
          requestWithAuth(FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
              email,
              AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (lang != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LANG, lang);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  protected static RequestSpecification getRequiredRequestSpec(String specUsedFor, Map<String, String> baseAuth, boolean withBasicAuth,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    requestSpecification = baseAuthRequest(specUsedFor, baseAuth, withBasicAuth, baseUri, port);

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  protected static RequestSpecification getRequiredRequestSpec(String authToken,
      FirebaseHelper.FansUnitedProject project, String email, String clientId,
      String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (email == null) {
      if (authToken == null) {
        requestSpecification = requestWithoutAuth(baseUri, port);
      } else {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      }
    } else {
      requestSpecification =
          requestWithAuth(project, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }
}
