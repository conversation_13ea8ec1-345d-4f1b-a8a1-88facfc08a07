package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

class BaseSetup {

  private static String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_BASE_URL);
  private static String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.PREDICTION_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(String authToken,
      FirebaseHelper.FansUnitedProject project,
      String email, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (email == null) {
      if (authToken == null) {
        requestSpecification = requestWithoutAuth(baseUri, port);
      } else {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      }
    } else {
      requestSpecification =
          requestWithAuth(project, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  protected static RequestSpecification getRequiredRequestSpecWithHeaders(String authToken,
      FirebaseHelper.FansUnitedProject project,
      String email, String clientId, String apiKey, ContentType contentType, String deviceId)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (email == null) {
      if (authToken == null) {
        requestSpecification = requestWithoutAuth(baseUri, port);
      } else {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      }
    } else {
      requestSpecification =
          requestWithAuth(project, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }
    //if (deviceId != null) {
    //  requestSpecification = requestSpecification.header(HEADER_DEVICE_ID_PARAM, deviceId);
    //}

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  public Response createGame(CreateGameEndpoint createGameEndpoint){
    return createGameEndpoint.getRequestSpecification()
        .body(createGameEndpoint.getBody())
        .when()
        .post(Endpoints.PredictionApi.GAMES);
  }
}
