package com.fansunited.automation.core.apis.leagueapi.membership;

import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.QUERY_PARAM_INVITATION_CODE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.leagueapi.BaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class JoinEndpoint extends BaseSetup {

  public static Response joinUserToLeague(
      String email,
      String invitationCode,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String apiKey,
      String authToken,
      String clientId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(project, apiKey, clientId, contentType, authToken, email);
    return requestSpecification
        .queryParam(QUERY_PARAM_INVITATION_CODE, invitationCode)
        .when()
        .post(Endpoints.MembershipApi.JOIN_MEMBER);
  }

  public static Response joinUserToLeague(String email, String invitationCode) {
    return joinUserToLeague(email, invitationCode, AuthConstants.ENDPOINTS_API_KEY);
  }

  public static Response joinUserToLeague(String email, String invitationCode, String apiKey) {
    return joinUserToLeague(email, invitationCode, apiKey, null);
  }

  public static Response joinUserToLeague(
      String email, String invitationCode, String apiKey, String authToken) {
    return joinUserToLeague(email, invitationCode, apiKey, authToken, CLIENT_AUTOMATION_ID);
  }

  public static Response joinUserToLeague(
      String email, String invitationCode, String apiKey, String authToken, String clientId) {
    return joinUserToLeague(email, invitationCode, apiKey, authToken, clientId, ContentType.JSON);
  }

  public static Response joinUserToLeague(
      String email,
      String invitationCode,
      String apiKey,
      String authToken,
      String clientId,
      ContentType contentType) {
    try {
      return joinUserToLeague(
          email,
          invitationCode,
          contentType,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          apiKey,
          authToken,
          clientId);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
