package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.PLATFORM_OPERATOR;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class ClientEndpoint extends BaseSetup {

  public static Response getClientsWithApiKey(String endpointsApiKey)
      throws HttpException {
    return getClients(AuthConstants.ENDPOINTS_API_KEY, endpointsApiKey, ContentType.JSON,
        FANS_UNITED_CLIENTS,CLIENT_AUTOMATION_ID);
  }

  public static Response getClients(String endpointsApiKey, ContentType contentType)
      throws HttpException {
    return getClients(null, endpointsApiKey, contentType,
        FANS_UNITED_CLIENTS,CLIENT_AUTOMATION_ID);
  }

  public static Response getAllClients()
      throws HttpException {
    return getClients(null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
        FANS_UNITED_CLIENTS,null);
  }

  public static Response createClient(FirebaseHelper.FansUnitedProject project, Object body,
      String apiKey, ContentType contentType) throws HttpException {

    return postClient(body, null, true, project, PLATFORM_OPERATOR, apiKey, contentType);
  }

  public static Response updateClient(FirebaseHelper.FansUnitedProject project, Object body,
      String clientId,
      String apiKey, ContentType contentType) throws HttpException {
    return putClient(body, null, true, project, PLATFORM_OPERATOR, clientId, apiKey, contentType);
  }

  public static Response getClients(String apiKey, String endpointsApiKey, ContentType contentType,
      FirebaseHelper.FansUnitedProject project, String clientId)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, null, true, project, PLATFORM_OPERATOR , null, null,
            apiKey, endpointsApiKey, contentType);

    return requestSpecification
        .when()
        .get(Endpoints.ClientApi.CLIENTS);
  }

  public static Response postClient(
      Object body, String authToken, boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(1, authToken, true, project, email,
            null, null, null, apiKey, contentType);

    return requestSpecification
        .body(body)
        .when()
        .post(Endpoints.ClientApi.CLIENTS);
  }

  public static Response putClient(
      Object body, String authToken, boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email, String clientId,
      String apiKey, ContentType contentType) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(1, authToken, true, project, email,
            clientId, null, AuthConstants.ENDPOINTS_API_KEY, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .queryParam(apiKey)
        .body(body)
        .when()
        .put(Endpoints.ClientApi.CLIENT_BY_ID);
  }

  public static Response optionsClient(String endpoint) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1,
            null,
            true,
            FANS_UNITED_PROFILE,
            PLATFORM_OPERATOR,
            CLIENT_AUTOMATION_ID,
            null,
            ENDPOINTS_API_KEY,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    return requestSpecification.when().options(endpoint);
  }
}
