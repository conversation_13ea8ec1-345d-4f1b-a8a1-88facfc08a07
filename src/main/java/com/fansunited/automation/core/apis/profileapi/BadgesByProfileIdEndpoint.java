package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.PATH_PARAM_USER_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class BadgesByProfileIdEndpoint extends BaseSetup {

  public static Response getBadgesForProfile(String profileId) throws HttpException {
    return getBadgesForProfile(profileId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getBadgesForProfile(String userId, String clientId, String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification = getRequiredRequestSpec(null, false, null,
        null, clientId,
        apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.ProfileApi.PROFILE_BY_ID_BADGES);
  }
}
