package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;
import org.apache.http.HttpException;

public class GameEndpoint extends BaseSetup {

  // PATCH /v1/games/{gameId}

  public static Response updateGame(String gameId, Object body) throws HttpException {
    return updateGame(gameId, body, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response updateGame(String gameId, Object body, String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, null, null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .body(body)
        .when()
        .patch(Endpoints.PredictionApi.GAME_BY_ID);
  }

  public static Response updateGame(String gameId, Object body,
      FirebaseHelper.FansUnitedProject project, String email,
      String clientId,
      String apiKey, ContentType contentType) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .body(body)
        .when()
        .patch(Endpoints.PredictionApi.GAME_BY_ID);
  }

  // GET /v1/games/{gameId}

  public static Response getGameById(String gameId) throws HttpException {
    return getGameById(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getGameById(String gameId, String clientId,
      String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .when()
        .get(Endpoints.PredictionApi.GAME_BY_ID);
  }
}
