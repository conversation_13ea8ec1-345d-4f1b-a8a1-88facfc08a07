package com.fansunited.automation.core.apis.leagueapi.membership;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.leagueapi.BaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetInvitationsEndpoint extends BaseSetup {
  public static Response getInvitations(
      String type,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(project, apiKey, clientId, contentType, authToken, email);
    return requestSpecification.queryParam(type).when().get(Endpoints.MembershipApi.GET_INVITATION);
  }

  public static Response getInvitations(String authToken, String email, ContentType contentType)
      throws HttpException {

    return getInvitations(
        LeagueType.PRIVATE.getValue(),
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        contentType,
        authToken);
  }

  public static Response getInvitations(String type, String email) throws HttpException {

    return getInvitations(
        type,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response getInvitations(String apiKey, String type, String email, String clientId)
      throws HttpException {

    return getInvitations(
        type,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email,
            clientId,
        apiKey,
        ContentType.JSON,
        null);
  }
}
