package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.PredictionApi.PARTICIPATE_IN_CUSTOM_EVENT_GAME;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_PREDICTION_ID;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.CUSTOM_EVENT_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.QUERY_PARAM_PROFILE_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class PredictionCustomEventsEndpoints extends BaseSetup {

  public static Response participateInCustomEventGame(
      String clientId,
      String eventId,
      FirebaseHelper.FansUnitedProject project,
      String apiKey,
      String email,
      ContentType contentType,
      Object body)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(null, project, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(CUSTOM_EVENT_GAME_ID, eventId)
        .body(body)
        .that()
        .post(PARTICIPATE_IN_CUSTOM_EVENT_GAME);
  }

  public static Response getCustomEventPredictionById(
      String eventId,String userId, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .queryParam(QUERY_PARAM_PROFILE_ID, userId)
        .pathParam(CUSTOM_EVENT_GAME_ID, eventId)
        .get(Endpoints.PredictionApi.PARTICIPATE_IN_CUSTOM_EVENT_GAME);
  }

  public static Response updatePredictionInCustomEventGame(
      String clientId,
      String predictionId,
      String eventId,
      FirebaseHelper.FansUnitedProject project,
      String apiKey,
      String email,
      ContentType contentType,
      Object body)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, project, email, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(CUSTOM_EVENT_GAME_ID, eventId)
        .pathParam(QUERY_PARAM_PREDICTION_ID, predictionId)
        .body(body)
        .put(Endpoints.PredictionApi.UPDATE_CUSTOM_EVENT_GAME_PREDICTIONS);
  }

  public static Response getOwnPredictions(
      String clientId,
      FirebaseHelper.FansUnitedProject project,
      String apiKey,
      String email,
      ContentType contentTyp)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, project, email, clientId, apiKey, contentTyp);

    return requestSpecification.when().get(Endpoints.PredictionApi.CUSTOM_EVENT_GAME);
  }
}
