package com.fansunited.automation.core.apis.voting.poll;

import static com.fansunited.automation.constants.UrlParamValues.VotingApi.PATH_PARAM_POLL_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class UpdateVotingByIdEndpoint extends VotingBaseSetup {
  public static Response updatePollById(
      String pollId,
      Object body,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POLL_ID, pollId)
        .when()
        .body(body)
        .put(Endpoints.VotingApi.GET_POLL_BY_ID);
  }
}
