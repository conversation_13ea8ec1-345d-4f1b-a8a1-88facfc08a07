package com.fansunited.automation.core.apis.leagueapi.membership;

import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.PATH_PARAM_LEAGUE_ID;
import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.QUERY_PARAM_ACCEPT;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.leagueapi.BaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.leaguesapi.enums.UserInvitesChoice;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class AcceptEndpoint extends BaseSetup {

  private static String userChoice = UserInvitesChoice.getRandomValidUserChoice().getValue();

  public static Response acceptInvitation(
      String userChoice,
      String leagueId,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      String authToken)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(project, apiKey, clientId, contentType, authToken, email);
    return requestSpecification
        .pathParam(PATH_PARAM_LEAGUE_ID, leagueId)
        .queryParam(QUERY_PARAM_ACCEPT, userChoice)
        .when()
        .post(Endpoints.MembershipApi.ACCEPT_MEMBER);
  }

  public static Response acceptInvitation(String userChoice, String leagueId, String email)
      throws HttpException {
    return acceptInvitation(
        userChoice,
        leagueId,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        null);
  }

  public static Response acceptInvitation(
      String leagueId, String apyKey, String email, ContentType contentType) throws HttpException {

    return acceptInvitation(
        userChoice,
        leagueId,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email,
        CLIENT_AUTOMATION_ID,
        apyKey,
        contentType,
        null);
  }

  public static Response acceptInvitation(
          String leagueId, String apyKey, String email, ContentType contentType, String  authToken) throws HttpException {

    return acceptInvitation(
            userChoice,
            leagueId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            email,
            CLIENT_AUTOMATION_ID,
            apyKey,
            contentType,
            authToken);
  }

}
