package com.fansunited.automation.core.apis.resolverapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.baseAuthRequest;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import java.util.Map;
import org.apache.http.HttpException;

public class BaseSetup {

  private static final String BASE_URI =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.RESOLVER_API_BASE_URL);
  private static final String PORT =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.RESOLVER_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec() throws HttpException {

    return getRequiredRequestSpec(AuthConstants.REDIS_BASE_AUTH_USAGE, null, true, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static RequestSpecification getRequiredRequestSpec(String specUsedFor,
      Map<String, String> baseAuth, boolean withBasicAuth,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    requestSpecification = baseAuthRequest(specUsedFor, baseAuth, withBasicAuth, BASE_URI, PORT);

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }
}
