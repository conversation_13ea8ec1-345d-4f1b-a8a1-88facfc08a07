package com.fansunited.automation.core.apis.resolverapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_GAMEID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.baseAuthRequest;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import java.util.Map;
import org.apache.http.HttpException;

public class BaseSetup {

  private static final String BASE_URI =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.RESOLVER_API_BASE_URL);
  private static final String PORT =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.RESOLVER_API_PORT);

  private static final String CUSTOM_RESOLVER_URI =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.CUSTOM_RESOLVER_API_BASE_URL);
  private static final String CUSTOM_RESOLVER_PORT =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.CUSTOM_RESOLVER_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec() throws HttpException {

    return getRequiredRequestSpec(
        AuthConstants.REDIS_BASE_AUTH_USAGE,
        null,
        true,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static RequestSpecification getRequiredRequestSpec(
      String specUsedFor,
      Map<String, String> baseAuth,
      boolean withBasicAuth,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    requestSpecification = baseAuthRequest(specUsedFor, baseAuth, withBasicAuth, BASE_URI, PORT);

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }

  public static RequestSpecification getCustomRequiredRequestSpec(
      String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject,
      boolean isAuthRequired,
      String email,
      String clientId,
      ContentType contentType,String customGameId) throws HttpException {

    RequestSpecification customRequestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        customRequestSpecification = requestWithAuth(authToken, CUSTOM_RESOLVER_URI, CUSTOM_RESOLVER_PORT);
      } else {
        customRequestSpecification =
            requestWithAuth(
                tokenForProject, ADMIN_USER, AuthConstants.DEFAULT_USER_PASS, CUSTOM_RESOLVER_URI, CUSTOM_RESOLVER_PORT);
      }
    } else if (isAuthRequired) {
      customRequestSpecification =
          requestWithAuth(tokenForProject, email, AuthConstants.DEFAULT_USER_PASS, CUSTOM_RESOLVER_URI, CUSTOM_RESOLVER_PORT);
    } else {
      customRequestSpecification = requestWithoutAuth(CUSTOM_RESOLVER_URI, CUSTOM_RESOLVER_PORT);
    }

    if (clientId != null) {
      customRequestSpecification =
          customRequestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
    if (customGameId != null) {
      customRequestSpecification =
          customRequestSpecification.queryParam(PATH_PARAM_GAMEID, customGameId);
    }

    if (contentType != null) {
      customRequestSpecification = customRequestSpecification.contentType(contentType);
    }

    return customRequestSpecification;
  }

  protected static RequestSpecification customRequestSpecification(String gameId)
      throws HttpException {

    return getCustomRequiredRequestSpec(null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        true,
        null,
        CLIENT_AUTOMATION_ID,
        ContentType.JSON,
        gameId);
  }
}
