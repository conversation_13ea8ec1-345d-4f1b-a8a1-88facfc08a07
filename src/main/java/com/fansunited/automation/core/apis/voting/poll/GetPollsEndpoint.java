package com.fansunited.automation.core.apis.voting.poll;

import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_ENTITY_IDS;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_FLAGS;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_MIN_VOTES;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_SORT_ORDER;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_START_AFTER;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_STATUS;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.CommonStatus;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import org.apache.http.HttpException;

public class GetPollsEndpoint extends VotingBaseSetup {

  public static Response getPolls(
      String startAfter,
      List<String> entityIds,
      List<String> flags,
      int limit,
      int minVotes,
      ApiConstants.SortOrder sortOrder,
      CommonStatus status,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, false, email, clientId, apiKey, contentType);

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }
    if (entityIds != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ENTITY_IDS, entityIds);
    }
    if (flags != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_FLAGS, flags);
    }
    if (limit < 10) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }
    if (minVotes > 0) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_MIN_VOTES, minVotes);
    }
    if (sortOrder != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_ORDER, sortOrder);
    }
    if (status != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_STATUS, status);
    }

    return requestSpecification
        .queryParam("dummyParam", System.currentTimeMillis())
        .when()
        .get(Endpoints.VotingApi.GET_POLLS);
  }
}
