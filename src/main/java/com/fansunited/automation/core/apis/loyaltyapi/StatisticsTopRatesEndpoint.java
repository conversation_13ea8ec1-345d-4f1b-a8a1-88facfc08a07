package com.fansunited.automation.core.apis.loyaltyapi;


import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;
import org.apache.http.HttpException;

public class StatisticsTopRatesEndpoint extends BaseSetup {



  public static Response getStatisticsTopRates(String clientId, String apiKey,
      ContentType contentType)
      throws HttpException {
    return getStatisticsTop(clientId, null, null,
        false, null, apiKey, contentType);
  }

  public static Response getStatisticsTop(String clientId, String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject, boolean isAuthRequired, String email,
      String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, tokenForProject, isAuthRequired, email, clientId, apiKey,
            contentType);

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .when()
        //.queryParam(QUERY_PARAM_MINIMUM_PREDICTIONS,1) the line will be commented out until the FZ-3876 is fixed
        .get(Endpoints.LoyaltyApi.TOP_RATES_BY_USER_ID);
  }
}
