package com.fansunited.automation.core.apis.clientapi;

  public class Node {
    private Node leftChild, rightChild;

    public Node(Node leftChild, Node rightChild) {
      this.leftChild = leftChild;
      this.rightChild = rightChild;
    }

    public Node getLeftChild() {
      return this.leftChild;
    }

    public Node getRightChild() {
      return this.rightChild;
    }

    /**
     * Calculates the height of the tree rooted at this node.
     * The height is defined as the length of the longest path from this node to any leaf node.
     * A leaf node has a height of 0.
     * A null node has a height of -1 (to ensure a leaf node has height 0).
     *
     * @return The height of the tree rooted at this node
     */
    public int height() {
      // Base case: if this is a leaf node (both children are null)
      if (leftChild == null && rightChild == null) {
        return 0;
      }

      // Calculate the height of the left subtree
      int leftHeight = (leftChild == null) ? -1 : leftChild.height();

      // Calculate the height of the right subtree
      int rightHeight = (rightChild == null) ? -1 : rightChild.height();

      // The height of this node is 1 + the maximum height of its subtrees
      return 1 + Math.max(leftHeight, rightHeight);
    }

    public static void main(String[] args) {
      Node leaf1 = new Node(null, null);
      Node leaf2 = new Node(null, null);
      Node node = new Node(leaf1, null);
      Node root = new Node(node, leaf2);

      System.out.println(root.height());
    }
  }
