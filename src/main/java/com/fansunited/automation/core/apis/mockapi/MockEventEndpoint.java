package com.fansunited.automation.core.apis.mockapi;

import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_COUNTRY_ID;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_DATE;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_FIRST_GOAL;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_FOLLOW;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_GAME_INSTANCE_ID;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_GAME_TYPE;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_GENDER;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_GOLDEN_GOAL;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_MARKET;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_POINTS;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_PREDICTION_ID;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_PREDICTION_LAST_UPDATE;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_PROFILE_ID;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_RESULT_OUTCOME;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_RESULT_STATUS;
import static com.fansunited.automation.constants.UrlParamValues.MockApi.QUERY_PARAM_UPDATED_AT;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.profileapi.CountriesEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.helpers.PeriodGenerator;
import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultStatus;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.github.javafaker.Faker;
import io.restassured.response.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MockEventEndpoint extends BaseSetup {

  private static final Logger LOG = LoggerFactory.getLogger(MockEventEndpoint.class);

  /**
   * Generate registration events for the specified period
   *
   * @param eventsPerDateInPeriod long
   * @param fromDate LocalDate
   * @param toDate LocalDate
   * @param periodicity Periodicity
   * @return Map<LocalDate, Integer> Number of events generated for every date between the specified period
   */
  public static Map<LocalDate, Long> generateRegistrationEventsForPeriod(
      long eventsPerDateInPeriod,
      LocalDate fromDate,
      LocalDate toDate, Periodicity periodicity) {
    if (eventsPerDateInPeriod <= 0) {
      throw new IllegalArgumentException("Events per date in period must be greater than 0");
    }

    var period = PeriodGenerator.genPeriod(fromDate, toDate, periodicity);
    var periodEventsCountMap = new LinkedHashMap<LocalDate, Long>();

    switch (periodicity) {
      case DAY -> {
        period.stream()
            .parallel()
            .forEach(localDate -> {
              generateRegistrationEvents(eventsPerDateInPeriod,
                  convertLocalDateToDateTime(localDate));
              periodEventsCountMap.put(localDate, eventsPerDateInPeriod);
            });
        return periodEventsCountMap;
      }
      case WEEK -> {
        for (var week : period) {
          PeriodGenerator.genPeriod(week, week.plusDays(6), Periodicity.DAY).stream()
              .parallel()
              .forEach(localDate -> generateRegistrationEvents(eventsPerDateInPeriod,
                  convertLocalDateToDateTime(localDate)));
          periodEventsCountMap.put(week, 7 * eventsPerDateInPeriod);
        }
        return periodEventsCountMap;
      }
      case MONTH -> {
        for (var month : period) {
          PeriodGenerator.genPeriod(month, month.plusDays(month.lengthOfMonth() - 1),
                  Periodicity.DAY).stream()
              .parallel()
              .forEach(localDate ->
                  generateRegistrationEvents(eventsPerDateInPeriod,
                      convertLocalDateToDateTime(localDate)));
          periodEventsCountMap.put(month, month.lengthOfMonth() * eventsPerDateInPeriod);
        }
        return periodEventsCountMap;
      }
      case YEAR -> {
        for (var year : period) {
          PeriodGenerator.genPeriod(year, LocalDate.of(year.getYear(), 12, 31), Periodicity.DAY)
              .stream()
              .parallel()
              .forEach(localDate ->
                  generateRegistrationEvents(eventsPerDateInPeriod,
                      convertLocalDateToDateTime(localDate)));
          periodEventsCountMap.put(year, year.lengthOfYear() * eventsPerDateInPeriod);
        }
        return periodEventsCountMap;
      }
    }
    throw new IllegalArgumentException(
        "Available periods are: " + Arrays.stream(GroupBy.values()).toList());
  }

  /**
   * Generate registration events for the specified period
   *
   * @param eventsPerDate long
   * @param fromDate LocalDate
   * @param toDate LocalDate
   * @return Map<String, Long> Number of events generated for every country in the map
   */

  public static Map<String, Long> generateRegistrationEvents(
      long eventsPerDate, LocalDate fromDate, LocalDate toDate) {
    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var countryIdEventCountMap = new HashMap<String, Long>();

    dates.forEach(localDate -> {
      var countryIdEventsCountPair = generateRegistrationEvents(eventsPerDate,
          convertLocalDateToDateTime(localDate));
      var exists = countryIdEventCountMap.get(countryIdEventsCountPair.getLeft()) != null;
      if (!exists) {
        countryIdEventCountMap.putIfAbsent(countryIdEventsCountPair.getLeft(),
            countryIdEventsCountPair.getRight());
      } else {
        var eventsCount = countryIdEventCountMap.get(countryIdEventsCountPair.getLeft());
        countryIdEventCountMap.replace(countryIdEventsCountPair.getLeft(),
            eventsCount + countryIdEventsCountPair.getRight());
      }
    });

    return countryIdEventCountMap;
  }

  /**
   * @param count long
   * @param eventTimestamp LocalDateTime
   * @return Pair of country id and number of events generated for it
   */
  private static Pair<String, Long> generateRegistrationEvents(
      long count, LocalDateTime eventTimestamp) {
    if (count < 0) {
      throw new IllegalArgumentException("Events count must be greater than 0");
    }
    CountryProfile country;
    try {
      country = CountriesEndpoint.getRandomCountryDto();
    } catch (Exception e) {
      country = CountryProfile.builder()
          .id(ApiConstants.ProfileApi.COUNTRY_ID_BG)
          .name("Bulgaria")
          .assets(new Country.AssetsFlag(
              "https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
          .build();
    }
    for (int i = 0; i < count; i++) {
      var profile = RegistrationProfile.builder()
          .id(UUID.randomUUID().toString())
          .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
          .gender(ProfileData.Profile.Gender.getRandomGender().getValue())
          .country(country)
          .followersCount(0)
          .followingCount(0)
          .email(new Faker().internet().emailAddress())
          .avatar("http://noavatar.com").build();
      generateRegistrationEvent(eventTimestamp, profile, 100, null);
    }

    return Pair.of(country.getId(), count);
  }

  public static Response generateRegistrationEvent(LocalDateTime eventTimestamp,
      RegistrationProfile profile, int points, LocalDateTime profileUpdatedAt) {
    LOG.info("Generating registration event with timestamp: " + eventTimestamp.format(
        DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    var response = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_POINTS, points)
        .queryParam(QUERY_PARAM_DATE,
            eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_UPDATED_AT, profileUpdatedAt == null ? eventTimestamp.format(
            DateTimeFormatter.ofPattern(ISO8601_WITH_NANO))
            : profileUpdatedAt.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .body(profile)
        .post(Endpoints.MockApi.REGISTRATION);
    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException("Could not sent registration event!");
    }
    return response;
  }

  /**
   * Generate country events for the specified period
   *
   * @param eventsPerDate long
   * @param fromDate LocalDate
   * @param toDate LocalDate
   * @return Map<LocalDate, Integer> Number of events generated for every date between fromDate and toDate param
   */
  public static Map<LocalDate, Map<Long, String>> generateAddCountryEventsForPeriod(
      long eventsPerDate, LocalDate fromDate, LocalDate toDate) {
    if (eventsPerDate < 0) {
      throw new IllegalArgumentException("Events per date in period must be greater than 0");
    }
    var period = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);
    var periodEventsCountMap = new LinkedHashMap<LocalDate, Map<Long, String>>();

    period.stream()
        .parallel()
        .forEach(localDate -> {
          String countryId;
          try {
            countryId = CountriesEndpoint.getRandomCountryId();
          } catch (HttpException e) {
            countryId = ApiConstants.ProfileApi.COUNTRY_ID_BG;
          }
          generateAddCountryEvent(eventsPerDate, convertLocalDateToDateTime(localDate),
              countryId,
              UUID.randomUUID().toString(),
              100);
          periodEventsCountMap.put(localDate, Map.of(eventsPerDate, countryId));
        });
    return periodEventsCountMap;
  }

  public static void generateAddCountryEvent(long count, LocalDateTime eventTimestamp,
      String countryId, String profileId, int points) {

    for (int i = 0; i < count; i++) {
      generateAddCountryEvent(eventTimestamp, countryId, profileId, points);
    }
  }

  public static Response generateAddCountryEvent(LocalDateTime eventTimestamp, String countryId,
      String profileId, int points) {
    LOG.info("Generating country event with timestamp: " + eventTimestamp.format(
        DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    var response = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_POINTS, points)
        .queryParam(QUERY_PARAM_DATE,
            eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_COUNTRY_ID, countryId)
        .queryParam(QUERY_PARAM_PROFILE_ID, profileId)
        .post(Endpoints.MockApi.COUNTRY);
    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException(
          "Could not sent country event -> " + response.getBody().prettyPrint());
    }
    return response;
  }

  public static Response generateGenderEvent(LocalDateTime eventTimestamp, String gender,
      String profileId, int points) {
    var response = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_DATE,
            eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_GENDER, gender)
        .queryParam(QUERY_PARAM_PROFILE_ID, profileId)
        .queryParam(QUERY_PARAM_POINTS, points)
        .post(Endpoints.MockApi.GENDER);
    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException(
          "Could not sent country event -> " + response.getBody().prettyPrint());
    }
    return response;
  }

  public static void generateProfileEvents(List<RegistrationProfile> profileList,
      LocalDateTime createdAt, LocalDateTime updatedAt) {
    for (var profile : profileList) {
      generateProfileEvent(profile, createdAt, updatedAt);
    }
  }

  public static Response generateProfileEvent(RegistrationProfile profile, LocalDateTime createdAt,
      LocalDateTime updatedAt) {
    LOG.info("Generating profile event...");
    var response = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_DATE,
            createdAt.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_UPDATED_AT, updatedAt == null ? createdAt.format(
            DateTimeFormatter.ofPattern(ISO8601_WITH_NANO))
            : updatedAt.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .body(profile)
        .post(Endpoints.MockApi.PROFILE);
    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException("Could not sent profile event!");
    }
    return response;
  }

  public static void generateFollowEvents(int count, LocalDateTime eventTimestamp,
      ProfileData.Interest interest, boolean isFollow, int points) {
    if (count <= 0) {
      throw new IllegalArgumentException("Follow events count must be greater than 0");
    }
    for (int i = 0; i < count; i++) {
      generateFollowEvent(eventTimestamp, UUID.randomUUID().toString(), interest,
          isFollow, points);
    }
  }

  public static Response generateFollowEvent(LocalDateTime eventTimestamp, String profileId,
      ProfileData.Interest interest, boolean isFollow, int points) {
    LOG.info("Generating " + (isFollow ? "follow" : "unfollow") + " event...");
    var requestSpec = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_DATE,
            eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_PROFILE_ID, profileId)
        .queryParam(QUERY_PARAM_FOLLOW, isFollow)
        .queryParam(QUERY_PARAM_POINTS, points)
        .body(interest);

    var response = requestSpec
        .post(Endpoints.MockApi.INTEREST_FOLLOW);

    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException(
          "Could not sent " + (isFollow ? "follow" : "unfollow") + " interest event!");
    }
    return response;
  }

  public static Response generateRankingEvent(LocalDateTime eventTimestamp, String profileId,
      PredictionMarket predictionMarket,
      PredictionType predictionType, int points, String gameInstanceId, ResultOutcome resultOutcome,
      ResultStatus resultStatus, Match match, Integer goldenGoalMinute, String firstGoalMinute, String predictionLastUpdate) {
    LOG.info("Generating ranking event...");
    var requestSpec = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_DATE,
            eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_PROFILE_ID, profileId)
        .queryParam(QUERY_PARAM_MARKET, predictionMarket.getValue())
        .queryParam(QUERY_PARAM_GAME_TYPE, predictionType)
        .queryParam(QUERY_PARAM_POINTS, points)
        .queryParam(QUERY_PARAM_GAME_INSTANCE_ID, gameInstanceId)
        .queryParam(QUERY_PARAM_RESULT_OUTCOME, resultOutcome)
        .queryParam(QUERY_PARAM_RESULT_STATUS, resultStatus)
        .queryParam(QUERY_PARAM_GOLDEN_GOAL, goldenGoalMinute)
        .queryParam(QUERY_PARAM_FIRST_GOAL, firstGoalMinute)
        .queryParam(QUERY_PARAM_PREDICTION_LAST_UPDATE, predictionLastUpdate)
        .body(match);
    var response = requestSpec
        .post(Endpoints.MockApi.RANKINGS);

    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException("Could not sent ranking event!");
    }
    return response;
  }

  /**
   * Generate prediction made activity in BigQuery
   *
   * @param predictionInstance PredictionInstance
   * @return Response
   */

  public static Response generatePredictionMadeEvent(PredictionInstance predictionInstance) {
    LOG.info("Generating prediction made event...");
    var requestSpec = getRequiredRequestSpec()
        .body(predictionInstance);

    var response = requestSpec
        .post(Endpoints.MockApi.PREDICTION_MADE);

    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException("Could not sent prediction made event!");
    }
    return response;
  }

  /**
   * Generate correct prediction event
   *
   * @param eventTimestamp LocalDateTime
   * @param profileId String
   * @param predictionMarket PredictionMarket
   * @param points int
   * @param resultStatus ResultStatus
   * @return Response
   */

  public static Response generateCorrectPredictionEvent(LocalDateTime eventTimestamp,
      String profileId, String predictionId, PredictionType gameType, PredictionMarket predictionMarket,
      int points, ResultStatus resultStatus) {
    LOG.info("Generating correct prediction event...");
    var requestSpec = getRequiredRequestSpec()
        .queryParam(QUERY_PARAM_DATE,
            eventTimestamp.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .queryParam(QUERY_PARAM_PROFILE_ID, profileId)
        .queryParam(QUERY_PARAM_PREDICTION_ID, predictionId)
        .queryParam(QUERY_PARAM_GAME_TYPE, gameType)
        .queryParam(QUERY_PARAM_MARKET, predictionMarket.getValue())
        .queryParam(QUERY_PARAM_POINTS, points)
        .queryParam(QUERY_PARAM_CLIENT_ID,CLIENT_AUTOMATION_ID)
        .queryParam(QUERY_PARAM_RESULT_STATUS, resultStatus);

    var response = requestSpec
        .post(Endpoints.MockApi.CORRECT_PREDICTION);

    if (response.getStatusCode() != HttpStatus.SC_OK) {
      throw new RuntimeException("Could not sent prediction made event!");
    }
    return response;
  }

  public static void generateRankingEvent(LocalDateTime now, String profileIdFirstPlace, PredictionMarket correctScore, PredictionType topX, int i, String id,
      ResultOutcome incorrect, ResultStatus settled, Match matchDtoById) {
  }
}