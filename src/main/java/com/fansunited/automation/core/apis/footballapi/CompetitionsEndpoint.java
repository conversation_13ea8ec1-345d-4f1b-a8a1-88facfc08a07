package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_COUNTRY_ID;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_GENDER;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_NAME;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_FIELD;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_ORDER;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_TYPE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.model.footballapi.matches.Competition;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class CompetitionsEndpoint extends BaseSetup {

  public static Competition getRandomCompetitionDto() {

    var competitionList = getCompetitions().then()
        .extract()
        .body()
        .jsonPath()
        .getList("data", Competition.class);

    return competitionList.get(generateRandomNumber(0, competitionList.size() - 1));
  }

  public static String getRandomCompetitionIdsCommaSeparated(int compCount) {
    Set<String> competitionIds = new HashSet<>();
    int counter = 0;
    while (competitionIds.size() != compCount && counter < 100) {
      competitionIds.add(getRandomCompetitionId());
      counter++;
    }
    return String.join(",", competitionIds);
  }

  public static String getRandomCompetitionId() {

    List<String> competitionsIdList =
        getCompetitions().then().extract().body().jsonPath().get("data.id");

    return competitionsIdList.get(generateRandomNumber(0, competitionsIdList.size() - 1));
  }

  public static List<String> getFakeCompetitionIds(int count) {
    List<String> ret = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      ret.add("fb:c:" + generateRandomNumber(1, 9999999));
    }
    return ret;
  }

  public static List<String> getCompetitionsIdList() {

    return getCompetitions().then().extract().body().jsonPath().getList("data." + ID_PROP);
  }

  public static Response getCompetitions() {

    return getCompetitions(null, null, null, null, null, null,
        UrlParamValues.Language.EN.getValue(), AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getCompetitions(String lang, String apiKey,
      ContentType contentType) {

    return getCompetitions(null, null, null, null, null, null, lang, apiKey, contentType);
  }

  public static Response getCompetitionsWithFilters(String countryId, String name,
      String gender, String type, String sortField, String sortOrder) {

    return getCompetitions(countryId, name, gender, type, sortField, sortOrder,
        UrlParamValues.Language.EN.getValue(), AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  private static Response getCompetitions(String countryId, String name, String gender,
      String type, String sortField, String sortOrder, String lang, String apiKey,
      ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, CLIENT_AUTOMATION_ID, apiKey, contentType);

    if (countryId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_COUNTRY_ID, countryId);
    }
    if (name != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_NAME, name);
    }
    if (gender != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GENDER, gender);
    }
    if (type != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TYPE, type);
    }
    if (sortField != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_FIELD, sortField);
    }
    if (sortOrder != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_ORDER, sortOrder);
    }

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID,CLIENT_AUTOMATION_ID);

    return requestSpecification.when().get(Endpoints.FootballApi.COMPETITIONS);
  }
}
