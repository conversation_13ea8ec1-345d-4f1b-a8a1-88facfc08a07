package com.fansunited.automation.core.apis.voting.poll;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.PATH_PARAM_POLL_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetVotingByIdEndpoint extends VotingBaseSetup {

  public static Response getPollById(
      String pollId,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, false, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POLL_ID, pollId)
        .when()
        .get(Endpoints.VotingApi.GET_POLL_BY_ID);
  }

  public static Response getPollById(String pollId) throws HttpException {
    return getPollById(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            null);
  }
}
