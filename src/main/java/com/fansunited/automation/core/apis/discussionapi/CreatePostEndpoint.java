package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_DISCUSSION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class CreatePostEndpoint extends BaseSetup {

  public static Response createPostForDiscussion(Object body, String discussionId, String clientId,
                                                 FirebaseHelper.FansUnitedProject project, String email,
                                                 String apiKey, ContentType contentType, String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_DISCUSSION_ID, discussionId)
        .body(body)
        .when()
        .post(Endpoints.DiscussionApi.CREATE_POST);
  }

  public static Response createPostForDiscussion(Object body, String discussionId, String email) throws HttpException {
    return createPostForDiscussion( body,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);
  }
}
