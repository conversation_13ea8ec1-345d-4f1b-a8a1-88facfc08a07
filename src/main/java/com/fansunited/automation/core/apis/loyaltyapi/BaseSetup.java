package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.PATH_PARAM_TEMPLATE_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

class BaseSetup {

  private static final String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.LOYALTY_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.LOYALTY_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject, boolean isAuthRequired, String email,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification =
            requestWithAuth(tokenForProject, ADMIN_USER,
                AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification =
          requestWithAuth(tokenForProject, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
//if everything is working without that peace of code will remove it
    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }


  protected static RequestSpecification getRequiredRequestSpecDetailed(String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject, boolean isAuthRequired, String email,
      String clientId, String apiKey, ContentType contentType, String templateId)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (isAuthRequired && email == null) {
      if (authToken != null) {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      } else {
        requestSpecification =
            requestWithAuth(tokenForProject, ADMIN_USER,
                AuthConstants.DEFAULT_USER_PASS, baseUri, port);
      }
    } else if (isAuthRequired) {
      requestSpecification =
          requestWithAuth(tokenForProject, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    } else {
      requestSpecification = requestWithoutAuth(baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }
    if (templateId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_TEMPLATE_ID, templateId);
    }
    //if everything is working without that peace of code will remove it
    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }
}
