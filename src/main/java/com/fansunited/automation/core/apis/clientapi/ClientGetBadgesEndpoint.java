package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.PLATFORM_OPERATOR;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ClientGetBadgesEndpoint extends BaseSetup {
  public static Response getBadges() throws HttpException {
        return getBadges(null, true, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
                PLATFORM_OPERATOR, CLIENT_AUTOMATION_ID, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

    public static Response getBadges(
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String endpointsKey,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            2,
            authToken,
            isAuthRequired,
            project,
            email,
            clientId,
            null,
            endpointsKey,
            apiKey,
            contentType);

    return requestSpecification
        .when()
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .get(Endpoints.ClientApi.FEATURES_BADGES);
  }
}
