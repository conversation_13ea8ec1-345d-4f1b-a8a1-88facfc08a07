package com.fansunited.automation.core.apis.leagueapi.leagues;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.leagueapi.BaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fansunited.automation.model.leaguesapi.request.CreateLeagueRequest;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.http.HttpException;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateLeagueEndpoint extends BaseSetup {
  private String name;
  private String authToken;
  private FirebaseHelper.FansUnitedProject tokenForProject;
  private Object body;
  private String email;
  private String clientId;
  private String apiKey;
  private ContentType contentType;
  private List<String> administrators;
  private List<String> invites;
  private List<String> banned;
  private List<String> members;
  private List<String> pinnedPosts;
  private String description;
  private String invitationCode;
  private String templateId;
  private LeagueType type;
  private boolean usersCanInviteUsers;
  private ZonedDateTime scoringStartsAt;
  private CreateLeagueRequest createLeagueRequest;

  private RequestSpecification requestSpecification;

  public static Response createLeague(
      FirebaseHelper.FansUnitedProject fansUnitedProject,
      String endpointsApiKey,
      String clientAutomationId,
      ContentType contentType,
      String authToken,
      Object body,
      String email)
      throws HttpException {

    RequestSpecification requestSpecification =
        BaseSetup.getRequiredRequestSpec(
            fansUnitedProject, endpointsApiKey, clientAutomationId, contentType, authToken, email);

    return requestSpecification.body(body).when().post(Endpoints.LeaguesApi.CREATE_LEAGUE);
  }

  public static Response createLeague(Object body, String authToken, String email)
      throws HttpException {

    return createLeague(
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        AuthConstants.ENDPOINTS_API_KEY,
        CLIENT_AUTOMATION_ID,
        ContentType.JSON,
        authToken,
        body,
        email);
  }
}
