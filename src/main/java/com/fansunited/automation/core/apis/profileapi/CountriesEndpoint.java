package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;

public class CountriesEndpoint extends BaseSetup {

  public static Response getCountries() throws HttpException {

    return getCountries(UrlParamValues.Language.EN.getValue(), CLIENT_AUTOMATION_ID,
        ContentType.JSON);
  }

  public static Response getCountries(String lang, String clientId, ContentType contentType,
      String apiKey) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, false, null, lang, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .get(Endpoints.ProfileApi.COUNTRIES);
  }

  public static Response getCountries(String lang, String clientId, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, false, null, lang, clientId, AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .when()
        .get(Endpoints.ProfileApi.COUNTRIES);
  }

  public static String getRandomCountryId() throws HttpException {

    List<String> countriesIdList = getCountries(UrlParamValues.Language.EN.getValue(),
        CLIENT_AUTOMATION_ID, ContentType.JSON)
        .then()
        .extract()
        .body()
        .jsonPath()
        .get("data.id");

    return countriesIdList.get(generateRandomNumber(0, countriesIdList.size() - 1));
  }

  public static CountryProfile getRandomCountryDto() throws HttpException {

    List<CountryProfile> countryDtoList = getCountries(UrlParamValues.Language.EN.getValue(),
        CLIENT_AUTOMATION_ID, ContentType.JSON).body()
        .jsonPath()
        .getList("data", CountryProfile.class);

    return countryDtoList.get(generateRandomNumber(0, countryDtoList.size() - 1));
  }
}
