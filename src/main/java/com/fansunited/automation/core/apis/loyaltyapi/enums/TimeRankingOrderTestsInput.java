package com.fansunited.automation.core.apis.loyaltyapi.enums;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TimeRankingOrderTestsInput {

  /**
   * MQTX_FALSE_FG1_P1_120010_30p_GG10_P2_120000_30p_GG80_P3_120005_30p_GG18
   * MQTX - related to Match Quiz and Top X games
   * FALSE - time tiebreaker = false
   * FG1 - First Goal in 1st minute
   * P1 - Profile one
   * 120010 - prediction is made in 12:00:10 (note Initial Time is today 12 o'clock noon)
   * 30p - 30 points
   * GG10 - Golden Goal predicted for the 10th minute
   */

  MQTX_TRUE_P1_115959_30p_P2_120001_30p_P3_120002_30p(
      true,
      InitialTime.INITIAL_TIME.value,
      -1, 1, 2,
      30, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(-1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(2).toString() + ".000000Z"),

  MQTX_TRUE_P1_120001_30p_P2_120001_30p_P3_120002_30p(
      true,
      InitialTime.INITIAL_TIME.value,
      1, 1, 2,
      30, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(2).toString() + ".000000Z"),

  MQTX_TRUE_P1_120003_40p_P2_120001_30p_P3_120002_30p(
      true,
      InitialTime.INITIAL_TIME.value,
      3, 1, 2,
      40, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(3).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(2).toString() + ".000000Z"),

  MQTX_TRUE_P1_120003_40p_P2_120001_30p_P3_120001_30p(
      true,
      InitialTime.INITIAL_TIME.value,
      3, 1, 1,
      40, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.SECOND_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(3).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z"),

  MQTX_TRUE_P1_120001_30p_P2_120001_30p_P3_120001_30p(
      true,
      InitialTime.INITIAL_TIME.value,
      1, 1, 1,
      30, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(),
          PositionsOrder.FIRST_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z"),

  TX_TRUE_FG40_P1_120001_30p_GG41_P2_120001_30p_GG61_P3_115958_30p_GG1(
      true,
      InitialTime.INITIAL_TIME.value,
      1, 1, -2,
      30, 30, 30,
      "40",
      41, 61, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(-2).toString() + ".000000Z"),

  ТX_TRUE_FG40_P1_120001_30p_GG41_P2_120002_30p_GG41_P3_120001_30p_GG1(
      true,
      InitialTime.INITIAL_TIME.value,
      1, 2, 1,
      30, 30, 30,
      "40",
      41, 41, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(2).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z"),

  TX_TRUE_FG40_P1_120005_40p_GG61_P2_115958_30p_GG41_P3_120001_30p_GG41(
      true,
      InitialTime.INITIAL_TIME.value,
      5, -2, 1,
      40, 30, 30,
      "40",
      61, 41, 41,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(5).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(-2).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z"),

  TX_TRUE_FGnull_P1_120001_30p_GG41_P2_120001_30p_GG61_P3_120001_30p_GG1(
      true,
      InitialTime.INITIAL_TIME.value,
      1, 1, 1,
      30, 30, 30,
      null,
      41, 61, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(),
          PositionsOrder.FIRST_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z"),

  TX_TRUE_FGnull_P1_120001_30p_GG41_P2_120002_30p_GG61_P3_120003_30p_GG1(
      true,
      InitialTime.INITIAL_TIME.value,
      1, 2, 3,
      30, 30, 30,
      null,
      41, 61, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      InitialTime.INITIAL_TIME.value.plusSeconds(1).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(2).toString() + ".000000Z",
      InitialTime.INITIAL_TIME.value.plusSeconds(3).toString() + ".000000Z"),

  MQTX_FALSE_P1_115958_30p_P2_120001_30p_P3_120002_30p(
      false,
      InitialTime.INITIAL_TIME.value,
      -2, 1, 2,
      30, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(),
          PositionsOrder.FIRST_POSITION.getValue()),
      null, null, null),

  MQTX_FALSE_P1_120003_40p_P2_120001_30p_P3_120002_30p(
      false,
      InitialTime.INITIAL_TIME.value,
      3, 1, 2,
      40, 30, 30,
      null,
      1, 1, 1,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.SECOND_POSITION.getValue()),
      null, null, null),

  TX_FALSE_FG1_P1_120010_30p_GG10_P2_120005_30p_GG18_P3_120001_30p_GG80(
      false,
      InitialTime.INITIAL_TIME.value,
      10, 5, 1,
      30, 30, 30,
      "1",
      10, 18, 80,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      null, null, null),

  TX_FALSE_FG1_P1_120002_30p_GG18_P2_120002_30p_GG18_P3_120001_30p_GG50(
      false,
      InitialTime.INITIAL_TIME.value,
      2, 2, 1,
      30, 30, 30,
      "1",
      18, 18, 50,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(),
          PositionsOrder.THIRD_POSITION.getValue()),
      null, null, null);

  private boolean isTimeTiebreakerOn;
  private LocalDateTime initialTime;
  private Integer plusSecondsFromNowPlayer1;
  private Integer plusSecondsFromNowPlayer2;
  private Integer plusSecondsFromNowPlayer3;
  private int pointsPlayer1;
  private int pointsPlayer2;
  private int pointsPlayer3;
  private String firstGoal;
  private int goldenGoalPredictionPlayer1;
  private int goldenGoalPredictionPlayer2;
  private int goldenGoalPredictionPlayer3;
  private List<Integer> leaderboardPositionsOrder;
  private String lastUpdatedTimePlayer1;
  private String lastUpdatedTimePlayer2;
  private String lastUpdatedTimePlayer3;

  @AllArgsConstructor
  @Getter
  public enum PositionsOrder {
    FIRST_POSITION(1),
    SECOND_POSITION(2),
    THIRD_POSITION(3);

    private int value;
  }

  @AllArgsConstructor
  @Getter
  public enum InitialTime {
    INITIAL_TIME(LocalDate.parse(LocalDate.now().toString()).atStartOfDay().plusHours(12));

    private LocalDateTime value;
  }
}
