package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_EITHER_OR;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.features.response.EitherOrFeature;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ClientFeaturesEndpoint extends BaseSetup {

  public static Response getClientsByIdFeatures(String clientId, String apiKey,
      ContentType contentType,FirebaseHelper.FansUnitedProject project) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, null, true, project, ADMIN_USER, clientId, null, null, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.FEATURES);
  }

  //update match quiz,topX,loyalty,predictor
  public static Response updateFeatures(FirebaseHelper.FansUnitedProject project,
      String email, Object body, String clientId, String apiKey, ContentType contentType, String url)
      throws HttpException {
    return updateFeatures(null, true,project,email, null, body, clientId,
        null, apiKey, contentType, url);
  }


  public static Response updateFeaturesWithInvalidToken(Object body, String token, FirebaseHelper.FansUnitedProject project,
      String email, String clientId, String apiKey, ContentType contentType, String url)
      throws HttpException {
    return updateFeatures(token, true, project, email, null, body, clientId,
        null, apiKey, contentType, url);
  }

  public static Response updateFeaturesWithInvalidClientIdPathParam(
          String invalidClientID, EitherOrFeature body) throws HttpException {

    var requestSpecification =
            getRequiredRequestSpec(
                    1,
                    null,
                    true,
                    FANS_UNITED_CLIENTS,
                    null,
                    CLIENT_AUTOMATION_ID,
                    null,
                    null,
                    AuthConstants.ENDPOINTS_API_KEY,
                    ContentType.JSON);

    return requestSpecification
            .when()
            .body(body)
            .pathParam(PATH_PARAM_CLIENT_ID, invalidClientID)
            .patch(FEATURES_EITHER_OR);
  }

  public static Response updateFeatures(String token, boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project, String email, String userId, Object body,
      String clientId, String endpointsKey, String apiKey, ContentType contentType, String url)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, token, isAuthRequired, project, email, clientId, userId,
            endpointsKey, apiKey,
            contentType);

    return requestSpecification
        .when()
        .body(body)
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .patch(url);
  }

  public static Response getFootballFantasyFeaturesByClientId(
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(
            1, null, true, project, ADMIN_USER, clientId, null, null, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.FEATURES_FOOTBALL_FANTASY);
  }
}
