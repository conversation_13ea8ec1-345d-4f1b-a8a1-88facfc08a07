package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_EITHER_OR;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.features.response.EitherOrFeature;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ClientFeaturesEndpoint extends BaseSetup {

  public static Response getClientsByIdFeatures(String clientId, String apiKey,
      ContentType contentType,FirebaseHelper.FansUnitedProject project) throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, null, true, project, ADMIN_USER, clientId, null, null, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.FEATURES);
  }

  //update match quiz,topX,loyalty,predictor
  public static Response updateFeatures(FirebaseHelper.FansUnitedProject project,
      String email, Object body, String clientId, String apiKey, ContentType contentType, String url)
      throws HttpException {
    return updateFeatures(null, true,project,email, null, body, clientId,
        null, apiKey, contentType, url);
  }


  public static Response updateFeaturesWithInvalidToken(Object body, String token, FirebaseHelper.FansUnitedProject project,
      String email, String clientId, String apiKey, ContentType contentType, String url)
      throws HttpException {
    return updateFeatures(token, true, project, email, null, body, clientId,
        null, apiKey, contentType, url);
  }

  public static Response updateFeaturesWithInvalidClientIdPathParam(
          String invalidClientID, EitherOrFeature body) throws HttpException {

    var requestSpecification =
            getRequiredRequestSpec(
                    1,
                    null,
                    true,
                    FANS_UNITED_CLIENTS,
                    null,
                    CLIENT_AUTOMATION_ID,
                    null,
                    null,
                    AuthConstants.ENDPOINTS_API_KEY,
                    ContentType.JSON);

    return requestSpecification
            .when()
            .body(body)
            .pathParam(PATH_PARAM_CLIENT_ID, invalidClientID)
            .patch(FEATURES_EITHER_OR);
  }

  public static Response updateFeatures(String token, boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project, String email, String userId, Object body,
      String clientId, String endpointsKey, String apiKey, ContentType contentType, String url)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(1, token, isAuthRequired, project, email, clientId, userId,
            endpointsKey, apiKey,
            contentType);

    return requestSpecification
        .when()
        .body(body)
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .patch(url);
  }

  public static Response getFootballFantasyFeaturesByClientId(
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(
            1, null, true, project, ADMIN_USER, clientId, null, null, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.FEATURES_FOOTBALL_FANTASY);
  }
}

public class StringSwapper {
  public static void main(String[] args) {
    // Initial strings
    String str1 = "Hello";
    String str2 = "World";

    System.out.println("Before swapping:");
    System.out.println("str1: " + str1);
    System.out.println("str2: " + str2);

    // Swap the strings
    String temp = str1;
    str1 = str2;
    str2 = temp;

    System.out.println("\nAfter swapping:");
    System.out.println("str1: " + str1);
    System.out.println("str2: " + str2);
  }

  // Method to swap strings that can be called from other classes
  public static void swapStrings(String[] strings) {
    if (strings != null && strings.length >= 2) {
      String temp = strings[0];
      strings[0] = strings[1];
      strings[1] = temp;
    }
  }
}

public class ProductExceptSelf {
  public static void main(String[] args) {
    int[] nums = {1, 2, 3, 4};
    int[] result = productExceptSelf(nums);

    System.out.print("Input array: ");
    printArray(nums);

    System.out.print("Product except self: ");
    printArray(result);
  }

  // Function to calculate product of array except self
  public static int[] productExceptSelf(int[] nums) {
    int n = nums.length;
    int[] result = new int[n];

    // Calculate products of all elements to the left of each element
    int leftProduct = 1;
    for (int i = 0; i < n; i++) {
      result[i] = leftProduct;
      leftProduct *= nums[i];
    }

    // Multiply by products of all elements to the right of each element
    int rightProduct = 1;
    for (int i = n - 1; i >= 0; i--) {
      result[i] *= rightProduct;
      rightProduct *= nums[i];
    }

    return result;
  }

  // Helper method to print an array
  private static void printArray(int[] arr) {
    System.out.print("[");
    for (int i = 0; i < arr.length; i++) {
      System.out.print(arr[i]);
      if (i < arr.length - 1) {
        System.out.print(", ");
      }
    }
    System.out.println("]");
  }
}
