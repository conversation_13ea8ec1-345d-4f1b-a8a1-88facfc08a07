package com.fansunited.automation.core.apis.minigames.enums;

import java.util.Random;
import lombok.Getter;

@Getter
public enum EntityRelationship {

  TRANSLATED_TO("translatedTo"),
  TRANSLATED_FROM("translatedFrom"),
  RELATED_TO("relatedTo"),
  BLOCKS("blocks"),
  BLOCKED_BY("blockedBy"),
  NEXT_IN_SERIES("nextInSeries"),
  PREVIOUS_IN_SERIES("previousInSeries"),
  BONUS_POINTS_GAME("bonusPointsGame");

  private final String value;
  private static final Random RANDOM = new Random();

  EntityRelationship(String value) {
    this.value = value;
  }

  public static EntityRelationship random() {
    EntityRelationship[] types = values();
    return types[RANDOM.nextInt(types.length)];
  }
}
