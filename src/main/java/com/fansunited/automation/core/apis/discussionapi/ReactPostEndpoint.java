package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_POST_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ReactPostEndpoint extends BaseSetup {

  public static Response reactUsersPost(Object body, String postId, String clientId,
                                        String apiKey, ContentType contentType, FirebaseHelper.FansUnitedProject project,
                                        String email, String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POST_ID, postId)
        .body(body)
        .when()
        .put(Endpoints.DiscussionApi.REACT_ON_USERS_POST);
  }
}
