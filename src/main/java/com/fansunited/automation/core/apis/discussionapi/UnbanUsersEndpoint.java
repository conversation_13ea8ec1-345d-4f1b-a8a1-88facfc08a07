package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class UnbanUsersEndpoint extends BaseSetup {

  public static Response unbanUser(
      Object body,
      String userId,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String authToken)
      throws HttpException {
    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_USER_ID, userId)
        .body(body)
        .when()
        .delete(Endpoints.DiscussionApi.BAN_USER);
  }

  public static Response usersUnban(Object body, String userId) {
    try {
      return unbanUser(
          body,
          userId,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          FANS_UNITED_CLIENTS,
          null,
          null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
