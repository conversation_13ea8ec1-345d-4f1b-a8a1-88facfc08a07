package com.fansunited.automation.core.apis.voting.potm;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.PATH_PARAM_MATCH_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints.VotingApi;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;
import org.apache.http.HttpException;

public class GetPotmResultsEndpoint extends VotingBaseSetup {

  public static Response getPotmResults(
      String matchId,
      String clientId,
      String apiKey,
      ContentType contentType,
      FansUnitedProject project,
      boolean avoidCache)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, false, null, clientId, apiKey, contentType);
    if (avoidCache) {
      // Avoid cache
      requestSpecification =
          requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));
    }

    requestSpecification.pathParams(PATH_PARAM_MATCH_ID, matchId);

    return requestSpecification.when().get(VotingApi.GET_POTM_RESULTS);
  }

  public static Response getPotmResultsResponse(String matchId, boolean avoidCache)
      throws HttpException {
    return getPotmResults(
        matchId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        FANS_UNITED_CLIENTS,
        avoidCache);
  }
}
