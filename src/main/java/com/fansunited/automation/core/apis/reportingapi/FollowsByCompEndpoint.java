package com.fansunited.automation.core.apis.reportingapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_FROM_DATE;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_TO_DATE;
import static com.fansunited.automation.constants.UrlParamValues.ReportingApi.PATH_PARAM_COMP_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;
import org.apache.http.HttpException;

public class FollowsByCompEndpoint extends BaseSetup {

  public static Response getFollowsByComp(String compId, String fromDate, String toDate)
      throws HttpException {

    return getFollowsByComp(CLIENT_AUTOMATION_ID, compId, fromDate, toDate,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getFollowsByComp(String clientId, String compId, String fromDate,
      String toDate)
      throws HttpException {

    return getFollowsByComp(clientId, compId, fromDate, toDate, true, null, ADMIN_USER,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getFollowsByComp(String clientId, String compId, String fromDate,
      String toDate, String apiKey, ContentType contentType) throws HttpException {

    return getFollowsByComp(clientId, compId, fromDate, toDate, false, null, ADMIN_USER,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, apiKey,
        contentType);
  }

  public static Response getFollowsByComp(String clientId, String compId, String fromDate,
      String toDate, boolean shouldBypassCache, String authToken, String email,
      FirebaseHelper.FansUnitedProject project, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project,
            email, clientId, apiKey, contentType);

    if (fromDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_FROM_DATE, fromDate);
    }

    if (toDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TO_DATE, toDate);
    }

    // Assign dummy param to avoid caching, hence tests fails
    if (shouldBypassCache) {
      requestSpecification = requestSpecification.queryParam("dummy",
          UUID.randomUUID().toString().replace("-", ""));
    }

    return requestSpecification
        .pathParam(PATH_PARAM_COMP_ID, compId)
        .when()
        .get(Endpoints.ReportingApi.FOLLOWS_BY_COMP_ID_FOOTBALL);
  }
}
