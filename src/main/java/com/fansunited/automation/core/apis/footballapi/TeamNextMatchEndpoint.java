package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.FootballApi.PATH_PARAM_TEAM_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;

public class TeamNextMatchEndpoint extends BaseSetup {

  public static Response getNextMatchForTeamId(String teamId) {

    return getNextMatchForTeamId(teamId, UrlParamValues.Language.EN.getValue(), CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getNextMatchForTeamId(String teamId, String lang, String clientId,
      String apiKey, ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, clientId, apiKey, contentType);

    // Avoid cache
    requestSpecification =
            requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .pathParam(PATH_PARAM_TEAM_ID, teamId)
        .when()
        .get(Endpoints.FootballApi.TEAM_NEXT_MATCH);
  }
}
