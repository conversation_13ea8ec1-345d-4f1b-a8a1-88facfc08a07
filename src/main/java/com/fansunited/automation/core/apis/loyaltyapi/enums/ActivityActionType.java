package com.fansunited.automation.core.apis.loyaltyapi.enums;

import com.fansunited.automation.helpers.Helper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum ActivityActionType {
  LIKE("like"),
  DIS<PERSON>IKE("dislike"),
  COMMENT("comment"),
  SHARE("share"),
  INVALID("invalid"),
  INVALID_EMPTY(""),
  INVALID_NULL(null);

  @Getter
  private final String value;

  public static List<ActivityActionType> getOwnSocialActivityActions() {
    return List.of(LIKE, DISLIKE, COMMENT, SHARE);
  }

  public static List<ActivityActionType> getUserSocialActivityActions() {
    return List.of(LIKE, DISLIKE, COMMENT, SHARE);
  }

  public static List<ActivityActionType> getNonSocialActivityActions() {
    return Arrays.stream(ActivityActionType.values())
        .filter(activityActionType -> !getOwnSocialActivityActions().contains(activityActionType)
            && activityActionType != INVALID
            && activityActionType != INVALID_NULL
            && activityActionType != INVALID_EMPTY)
        .toList();
  }

  public static List<ActivityActionType> getAllActivities() {
    var activityActionTypeList = new ArrayList<ActivityActionType>();
    activityActionTypeList.addAll(getOwnSocialActivityActions());
    activityActionTypeList.addAll(getNonSocialActivityActions());
    return activityActionTypeList;
  }

  /**
   * The method generates a list of randomly ordered activities types.
   * The size of the list is based on <b>the numOfActivities</b> parameter
   *
   * @param numOfActivities
   * @return a list of randomly ordered activities types
   */
  public static List<ActivityActionType> getRandomizedActivities(int numOfActivities,
      boolean forOwnUser) {

    List<ActivityActionType> randomizedActivitiesList = new ArrayList<>();

    for (int i = 0; i < numOfActivities; i++) {
      if (forOwnUser) {
        randomizedActivitiesList.add(getAllActivities().get(
            Helper.generateRandomNumber(1, getAllActivities().size() - 1)
        ));
      } else {
        randomizedActivitiesList.add(getUserSocialActivityActions().get(
            Helper.generateRandomNumber(1, getUserSocialActivityActions().size() - 1)
        ));
      }
    }

    return randomizedActivitiesList;
  }
}
