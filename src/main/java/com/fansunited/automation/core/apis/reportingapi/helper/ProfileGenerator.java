package com.fansunited.automation.core.apis.reportingapi.helper;

import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.profileapi.CountriesEndpoint;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.github.javafaker.Faker;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class ProfileGenerator {

  private ProfileGenerator() {
  }

  public final static CountryProfile COUNTRY_DTO_BG =
      CountryProfile.builder().name("Bulgaria").id(ApiConstants.ProfileApi.COUNTRY_ID_BG).build();

  public static List<RegistrationProfile> generateProfiles(int count,
      List<ProfileData.Interest> interestList, CountryProfile countryProfile) {
    var profileList = new ArrayList<RegistrationProfile>();
    for (int i = 0; i < count; i++) {
      profileList.add(generateProfile(UUID.randomUUID().toString(), countryProfile, interestList,
          ProfileData.Profile.Gender.getRandomGender()));
    }
    return profileList;
  }

  public static List<RegistrationProfile> generateProfiles(int count,
      List<ProfileData.Interest> interestList) {
    var profileList = new ArrayList<RegistrationProfile>();
    for (int i = 0; i < count; i++) {
      profileList.add(generateProfile(UUID.randomUUID().toString(), COUNTRY_DTO_BG, interestList,
          ProfileData.Profile.Gender.getRandomGender()));
    }
    return profileList;
  }

  public static RegistrationProfile generateProfile() {
    return generateProfile(UUID.randomUUID().toString(), CountryProfile.builder()
        .id(ApiConstants.ProfileApi.COUNTRY_ID_BG)
        .name("Bulgaria")
        .build(), null, ProfileData.Profile.Gender.getRandomGender());
  }

  public static RegistrationProfile generateProfile(String id) {
    CountryProfile countryProfile;
    try {
      countryProfile = CountriesEndpoint.getRandomCountryDto();
    } catch (Exception e) {
      countryProfile = COUNTRY_DTO_BG;
    }

    return generateProfile(id, countryProfile, null, ProfileData.Profile.Gender.getRandomGender());
  }

  public static RegistrationProfile generateProfile(String id, CountryProfile countryProfile,
      List<ProfileData.Interest> interestList, ProfileData.Profile.Gender gender) {
    var builder = RegistrationProfile.builder();

    if (id != null) {
      builder.id(id);
    }
    if (countryProfile != null) {
      builder.country(countryProfile);
    }
    if (interestList != null) {
      builder.interests(interestList);
    }
    if (gender != null) {
      builder.gender(gender.getValue());
    }

    builder.name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "));
    builder.email(new Faker().internet().emailAddress());
    builder.avatar("http://noavatar.com");

    return builder.build();
  }
}
