package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ClientBadgesUpdateEndpoint extends BaseSetup {

  public static Response updateBadges(String email, Object body) {
    try {
      return updateBadges(
          null,
          true,
          FANS_UNITED_CLIENTS,
          email,
          null,
          body,
          CLIENT_AUTOMATION_ID,
          null,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static Response updateBadges(
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String userId,
      Object body,
      String clientId,
      String endpointsKey,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
                1, authToken,
                isAuthRequired,
            project,
            email,
            clientId,
            userId,
            endpointsKey,
            apiKey,
            contentType);

    return requestSpecification
        .when()
        .body(body)
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .put(Endpoints.ClientApi.FEATURES_BADGES);
  }
}
