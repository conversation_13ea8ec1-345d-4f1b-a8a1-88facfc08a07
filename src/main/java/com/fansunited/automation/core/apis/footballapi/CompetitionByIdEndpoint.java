package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.FootballApi.PATH_PARAM_COMPETITION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;

public class CompetitionByIdEndpoint extends BaseSetup {

  public static Response getCompetitionById(String id) {

    return getCompetitionById(id, UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getCompetitionById(String id, String lang, String apiKey,
      ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, null, apiKey, contentType);

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .pathParam(PATH_PARAM_COMPETITION_ID, id)
        .when()
        .queryParam(QUERY_PARAM_CLIENT_ID, CLIENT_AUTOMATION_ID)
        .get(Endpoints.FootballApi.COMPETITION_BY_ID);
  }
}
