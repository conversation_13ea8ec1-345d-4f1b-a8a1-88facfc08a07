package com.fansunited.automation.core.apis.profileapi.leads;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CAMPAIGN_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CONTENT_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CONTENT_TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FROM_DATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.TO_DATE_PROP;

import com.fansunited.automation.core.apis.profileapi.BaseSetup;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.HttpException;

class LeadsRequestBuilder extends BaseSetup {
    public static Response buildLeadsRequest(
            String endpoint,
            String fromDate,
            String toDate,
            String campaignId,
            String contentTypeField,
            String contentId,
            String email,
            String clientId,
            String apiKey,
            ContentType contentType,
            boolean isAuthRequired,
            String jwtToken) throws HttpException {

        var requestSpecification = getRequiredRequestSpec(
                jwtToken, isAuthRequired, email, null, clientId, apiKey, contentType);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put(CAMPAIGN_ID_PROP, campaignId);
        queryParams.put(CONTENT_TYPE_PROP, contentTypeField);
        queryParams.put(CONTENT_ID_PROP, contentId);
        queryParams.put(FROM_DATE_PROP, fromDate);
        queryParams.put(TO_DATE_PROP, toDate);

        queryParams.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .forEach(entry -> requestSpecification.queryParam(entry.getKey(), entry.getValue()));

        requestSpecification.header("accept", "application/json, text/plain, */*");
        return requestSpecification.when().get(endpoint);
    }
}
