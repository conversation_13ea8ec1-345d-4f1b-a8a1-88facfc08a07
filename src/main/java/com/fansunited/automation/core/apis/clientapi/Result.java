package com.fansunited.automation.core.apis.clientapi;

import java.io.*;
import java.math.*;
import java.security.*;
import java.text.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.*;
import java.util.regex.*;
import java.util.stream.*;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

class Result {

  /*
   * Complete the 'getMaximumXor' function below.
   *
   * The function is expected to return a STRING.
   * The function accepts following parameters:
   *  1. STRING s
   *  2. STRING t
   */

  public static String getMaximumXor(String s, String t) {
    // Convert binary strings to BigInteger for easier manipulation
    BigInteger numS = new BigInteger(s, 2);
    BigInteger numT = new BigInteger(t, 2);

    // Calculate XOR
    BigInteger xorResult = numS.xor(numT);

    // Convert back to binary string
    String result = xorResult.toString(2);

    // Ensure the result has the same length as the longer input string
    int maxLength = Math.max(s.length(), t.length());

    // Pad with leading zeros if necessary
    while (result.length() < maxLength) {
      result = "0" + result;
    }

    return result;
  }

}

public class Solution {
  public static void main(String[] args) throws IOException {
    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(System.in));
    BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(System.getenv("OUTPUT_PATH")));

    String s = bufferedReader.readLine();

    String t = bufferedReader.readLine();

    String result = Result.getMaximumXor(s, t);

    bufferedWriter.write(result);
    bufferedWriter.newLine();

    bufferedReader.close();
    bufferedWriter.close();
  }
}
