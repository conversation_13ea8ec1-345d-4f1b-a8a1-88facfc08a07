package com.fansunited.automation.core.apis.discussionapi.pinnedposts;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.discussionapi.BasePinnedPostsEndpoint;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.google.api.client.http.HttpMethods;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import org.apache.http.HttpException;

public class DeletePinnedPostEndpoint extends BasePinnedPostsEndpoint {
  public static Response deletePinnedPostsForDiscussion(
      List<String> pinPostIds,
      String discussionId,
      String clientId,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String apyKey,
      ContentType contentType,
      String authToken,
      String method) {
    try {
      return modifyPinnedPostsForDiscussion(
          pinPostIds,
          discussionId,
          clientId,
          project,
          email,
          apyKey,
          contentType,
          authToken,
          method);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static Response deletePinnedPostsForDiscussion(
      List<String> pinPostIds, String discussionId) {
    try {
      return modifyPinnedPostsForDiscussion(
          pinPostIds,
          discussionId,
          CLIENT_AUTOMATION_ID,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          null,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          null,
          HttpMethods.DELETE);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
