package com.fansunited.automation.core.apis.predictionapi;


import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class WinnersEndpoint extends BaseSetup {
  public static Response setWinnersByContestType(Object body, String clientId,
      String apiKey, FirebaseHelper.FansUnitedProject project, String email,
      ContentType contentType) throws HttpException {

    return setWinnersByContestId(body, clientId,
        apiKey, project, email, contentType);
  }

  public static Response getWinnersByContestId(String contestId, String clientId,
      String apiKey, FirebaseHelper.FansUnitedProject project, String email,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null,
            project,
            email, clientId, apiKey, contentType);

    return requestSpecification
        .queryParam("contest_id", contestId)
        .when()
        .log()
        .uri()
        .get(Endpoints.PredictionApi.WINNERS);
  }

  public static Response setWinnersByContestId(
      Object body,
      String clientId,
      String apiKey,
      FirebaseHelper.FansUnitedProject project,
      String email,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, email, clientId, apiKey, contentType);

    return requestSpecification.body(body).when().post(Endpoints.PredictionApi.WINNERS);
  }

  public static Response updateWinnersByContestId(Object body, String clientId,
      String apiKey, FirebaseHelper.FansUnitedProject project, String email,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null,
            project,
            email, clientId, apiKey, contentType);

    return requestSpecification
        .body(body)
        .when().log().uri()
        .put(Endpoints.PredictionApi.WINNERS);
  }
}
