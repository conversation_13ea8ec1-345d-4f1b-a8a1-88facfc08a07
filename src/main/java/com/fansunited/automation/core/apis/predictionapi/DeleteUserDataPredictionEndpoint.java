package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.Endpoints;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class DeleteUserDataPredictionEndpoint extends BaseSetup {

  public static Response deleteUserData(
      String clientId, String apiKey, ContentType contentType, Object body) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            null, FANS_UNITED_CLIENTS, ADMIN_USER, clientId, apiKey, contentType);

    return requestSpecification
        .when()
        .body(body)
        .that()
        .delete(Endpoints.PredictionApi.DELETE_USER_DATA);
  }
}
