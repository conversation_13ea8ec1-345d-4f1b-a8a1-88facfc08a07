package com.fansunited.automation.core.watchers;

import com.aventstack.extentreports.Status;
import com.fansunited.automation.core.apis.mockapi.ReportingEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.core.base.cleanup.MatchCleanup;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.utils.RestAssuredUtils;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import java.time.LocalDateTime;
import java.util.Optional;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.TestWatcher;
import org.junit.platform.engine.TestExecutionResult;
import org.junit.platform.launcher.TestExecutionListener;
import org.junit.platform.launcher.TestIdentifier;
import org.junit.platform.launcher.TestPlan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TestWatcherImpl implements TestWatcher, TestExecutionListener {

  private static boolean shouldTruncateTables;
  private static final Logger LOG = LoggerFactory.getLogger(TestWatcherImpl.class);

  @Override
  public void testDisabled(ExtensionContext context, Optional<String> reason) {
    TestWatcher.super.testDisabled(context, reason);
    BaseTest.getCurrentTest().log(Status.WARNING, reason.orElse("Test disabled"));
  }

  @Override
  public void testSuccessful(ExtensionContext context) {
    TestWatcher.super.testSuccessful(context);
  }

  @Override
  public void testAborted(ExtensionContext context, Throwable cause) {
    TestWatcher.super.testAborted(context, cause);
    BaseTest.getCurrentTest().log(Status.WARNING, cause);
  }

  @Override
  public void testFailed(ExtensionContext context, Throwable cause) {
    TestWatcher.super.testFailed(context, cause);
    BaseTest.getCurrentTest().log(Status.FAIL, cause);
  }

  @Override
  public void testPlanExecutionStarted(TestPlan testPlan) {
    TestExecutionListener.super.testPlanExecutionStarted(testPlan);
    RestAssuredUtils.setPropertyNamingStrategy(new PropertyNamingStrategies.SnakeCaseStrategy());
    RestAssuredUtils.enableLoggingOnTestFailure();
  }

  @Override
  public void testPlanExecutionFinished(TestPlan testPlan) {
    LOG.info("Afterall testPlanExecutionFinished at {}", LocalDateTime.now());
    TestExecutionListener.super.testPlanExecutionFinished(testPlan);
    cleanUp();
    LOG.info("cleanUp finished at {}",LocalDateTime.now());
  }

  private static void cleanUp() {
    FirebaseHelper.cleanUpFirebase(FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE);
    MatchCleanup.cleanUp();
  }

  private static void truncateBigQueryTables() {
    if (shouldTruncateTables) {
      ReportingEndpoint.truncateTables();
    }
  }

  public static void setShouldTruncateTables(boolean shouldTruncateTables) {
    TestWatcherImpl.shouldTruncateTables = shouldTruncateTables;
  }

  @Override public void executionSkipped(TestIdentifier testIdentifier, String reason) {
    TestExecutionListener.super.executionSkipped(testIdentifier, reason);
    LOG.info("Test skipped: {}", testIdentifier.getDisplayName());
  }

  @Override public void executionStarted(TestIdentifier testIdentifier) {
    TestExecutionListener.super.executionStarted(testIdentifier);
    LOG.info("Test started: {}", testIdentifier.getDisplayName());
  }

  @Override public void executionFinished(TestIdentifier testIdentifier,
      TestExecutionResult testExecutionResult) {
    TestExecutionListener.super.executionFinished(testIdentifier, testExecutionResult);
    if (testExecutionResult.getStatus() == TestExecutionResult.Status.SUCCESSFUL) {
      LOG.info("Test PASSED {}", testIdentifier.getDisplayName());
    } else {
      LOG.info("Test FAILED: {} {}", testExecutionResult, testIdentifier.getDisplayName());
    }
  }
}
