package com.fansunited.automation.model.footballapi.teams;

import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.footballapi.matches.Competition;
import com.fansunited.automation.model.footballapi.matches.stats.TeamPlayer;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class Team {
  private String id;
  private Country country;
  private AssetsLogo assets;
  private boolean national;
  private String code;
  private String gender;
  private String name;
  private String fullName;
  private String shortName;
  private List<TeamPlayer> squad;
  private List<Competition> competitions;
  private Boolean is_deleted;
  private Object colors;
  private boolean undecided;
}
