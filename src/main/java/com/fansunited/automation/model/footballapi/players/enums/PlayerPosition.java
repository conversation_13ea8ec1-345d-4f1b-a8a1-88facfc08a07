package com.fansunited.automation.model.footballapi.players.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlayerPosition {
  DEFENDER("defender"),
  MIDFIELD<PERSON>("midfielder"),
  FOR<PERSON><PERSON>("forward"),
  <PERSON><PERSON><PERSON><PERSON>("keeper"),
  CAPTA<PERSON>("captain"),
  VICE_CAPTAIN("vice_captain");
  private final String value;
}
