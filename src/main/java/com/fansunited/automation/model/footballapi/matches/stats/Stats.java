package com.fansunited.automation.model.footballapi.matches.stats;

import lombok.Data;

@Data
public class Stats {
  private Corners corners;
  private Cross<PERSON> crosses;
  private Offside offside;
  private ShotsOn shotsOn;
  private ThrowIn throwIn;
  private RedCards redCards;
  private ShotsOff shotsOff;
  private GoalKicks goalKicks;
  private Possession possession;
  private Treatments treatments;
  private YellowCards yellowCards;
  private ShotsBlocked shotsBlocked;
  private Substitutions substitutions;
  private CounterAttacks counterAttacks;
  private FoulsCommitted foulsCommitted;
  private Goals goals;
  private Goals_1h goals_1h;
  private  Goals_2h goals_2h;
  private  Goals_et goals_et;
  //private  Throw_in throw_in;
  private  Corners_1h corners_1h;
  private  Corners_2h corners_2h;
  private  Corners_et corners_et;
  private Crosses_1h crosses_1h;
  private  Crosses_2h crosses_2h;
  private  Crosses_et crosses_et;
  private  Offside_1h offside_1h;
  private  Offside_2h offside_2h;
  private  Offside_et offside_et;
  private  ShotsOn_1h shots_on_1h;
  private  ShotsOn_2h shots_on_2h;
  private  ShotsOn_et shots_on_et;
  private  ThrowIn_1h throw_in_1h;
  private  ThrowIn_2h throw_in_2h;
  private  ThrowIn_et throw_in_et;
  private  RedCards_1h red_cards_1h;
  private  PriredCards_2h prired_cards_2h;
  private  RedCards_et red_cards_et;
  private  ShotsOff_1h shots_off_1h;
  private  ShotsOff_2h shots_off_2h;
  private  ShotsOff_et shots_off_et;
  private  RedCards_2h red_cards_2h;
  private  GoalKicks_1h goal_kicks_1h;
  private  GoalKicks_2h goal_kicks_2h;
  private  GoalKicks_et goal_kicks_et;
  private  Possession_1h possession_1h;
  private  Possession_2h possession_2h;
  private  Possession_et possession_et;
  private  Treatments_1h treatments_1h;
  private  Treatments_2h treatments_2h;
  private  Treatments_et treatments_et;
  private  YellowCards_1h yellow_cards_1h;
  private  YellowCards_2 yellow_cards_2h;
  private  YellowCards_et yellow_cards_et;
  private  ShotsBlocked_1h shots_blocked_1h;
  private  ShotsBlocked_2h shots_blocked_2h;
  private  ShotsBlocked_et shots_blocked_et;
  private  Substitutions_1h substitutions_1h;
  private Substitutions_2h substitutions_2h;
  private Substitutions_et substitutions_et;
  private  CounterAttacks_1h counter_attacks_1h;
  private  CounterAttacks_2h counter_attacks_2h;
  private  CounterAttacks_et counter_attacks_et;
  private  FoulsCommitted_1h fouls_committed_1h;
  private  FoulsCommitted_2h fouls_committed_2h;
  private  FoulsCommitted_et fouls_committed_et;
}
