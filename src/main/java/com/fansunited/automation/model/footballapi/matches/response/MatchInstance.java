package com.fansunited.automation.model.footballapi.matches.response;

import com.fansunited.automation.model.footballapi.matches.Context;
import com.fansunited.automation.model.footballapi.matches.MatchStatus;
import com.fansunited.automation.model.footballapi.matches.ParameterViolations;
import com.fansunited.automation.model.footballapi.matches.PropertyViolations;
import com.fansunited.automation.model.footballapi.matches.ReturnValueViolations;
import com.fansunited.automation.model.footballapi.matches.scores.Scores;
import com.fansunited.automation.model.footballapi.teams.Team;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class MatchInstance {

  private String id;
  private LocalDateTime kickoffAt;
  private LocalDateTime updatedAt;
  private Team homeTeam;
  private Team awayTeam;
  private Boolean lineups_confirmed;
  private LocalDateTime startedAt;
  private String minute;
  private Scores scores;
  private Context context;
  private MatchStatus status;
  private PropertyViolations propertyViolations;
  private ParameterViolations parameterViolations;
  private ReturnValueViolations returnValueViolations;

}
