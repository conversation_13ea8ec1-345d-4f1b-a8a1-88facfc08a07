package com.fansunited.automation.model.footballapi.players;

import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.footballapi.matches.Competition;
import com.fansunited.automation.model.footballapi.teams.Team;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class Player {
  private String id;
  private Country country;
  private String birthDate;
  private String firstName;
  private String lastName;
  private String position;
  private AssetsHeadshot assets;
  private String name;
  private List<Team> teams;
  private Boolean is_deleted;
  private List<Competition> competitions;
}
