package com.fansunited.automation.model.discussionapi.request;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class UpdatePostRequest {
  private String content;

  @JsonIgnore
  private String replayId;

  @JsonIgnore
  private boolean ignoreReplayId;

  @JsonAnyGetter
  public Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (!ignoreReplayId) {
      additionalProperties.put("replayId", replayId);
    }
    return additionalProperties;
  }
}
