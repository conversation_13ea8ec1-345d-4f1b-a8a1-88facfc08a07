package com.fansunited.automation.model.discussionapi.request;

import static com.fansunited.automation.model.discussionapi.DiscussionType.COMMENT_SECTION;
import static com.fansunited.automation.model.discussionapi.ModerationType.USER;

import com.fansunited.automation.model.discussionapi.DiscussionType;
import com.fansunited.automation.model.discussionapi.ModerationType;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.github.javafaker.Faker;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class CreateDiscussionRequest {
  private String id;
  @JsonIgnore private DiscussionType discussionType;
  @JsonIgnore private boolean ignoreDiscussionType;
  @JsonIgnore private ModerationType moderationType;
  @JsonIgnore private boolean ignoreModerationType;
  @JsonIgnore private Context context;
  @JsonIgnore private boolean ignoreContext;
  private String discussionUrl;

  private String label;

  public CreateDiscussionRequest() {
    this.id = new Faker().internet().uuid();
    this.discussionType = COMMENT_SECTION;
    this.moderationType = USER;
  }

  @JsonAnyGetter
  public Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (!ignoreContext) {
      additionalProperties.put("context", context);
    }
    if (!ignoreModerationType) {
      additionalProperties.put("moderation_type", moderationType);
    }
    if (!ignoreDiscussionType) {
      additionalProperties.put("discussion_type", discussionType);
    }

    additionalProperties.put("discussion_url", discussionUrl);
    return additionalProperties;
  }
}
