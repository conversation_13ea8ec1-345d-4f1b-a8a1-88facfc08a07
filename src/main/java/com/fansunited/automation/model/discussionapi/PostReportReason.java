package com.fansunited.automation.model.discussionapi;

import java.util.Arrays;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PostReportReason {
  SPAM("SPAM"),
  NOT_ON_THE_SUBJECT("NOT_ON_THE_SUBJECT"),
  AD("AD"),
  COPYRIGHT("COPYRIGHT"),
  EXPLICIT_SEXUAL_CONTENT("EXPLICIT_SEXUAL_CONTENT"),
  HATEFUL_SPEECH("HATEFUL_SPEECH"),
  INSULT("INSULT"),
  OTHER("OTHER"),
  INVALID_REPORT_TYPE("INVALID_REPORT_TYPE");

  private final String value;

  public static PostReportReason getRandomReasonType() {
    var reportTypeList =
        Arrays.stream(values())
            .filter(reason -> !reason.toString().contains("INVALID"))
            .toList();
    var random = new Random();
    return reportTypeList.get(random.nextInt(reportTypeList.size()));
  }
}
