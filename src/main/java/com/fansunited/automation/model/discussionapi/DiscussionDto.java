package com.fansunited.automation.model.discussionapi;

import com.fansunited.automation.model.discussionapi.enums.DiscussionStatus;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiscussionDto {

  @JsonIgnore
  private boolean enabled;

  @JsonProperty("client_id")
  private String clientId;

  @JsonProperty("automatic_moderation")
  private Object automaticModeration;
  private String id;
  private DiscussionType discussionType;
  private ModerationType moderationType;
  private int postsCount;
  private String lastPostId;
  private Context context;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private String label;
  private List<PostDto> pinnedPosts;
  private DiscussionStatus status;
  @JsonProperty("custom_fields")
  private String customField;
  private boolean deleted;
  @JsonProperty("deleted_at")
  private Date deletedAt;
  @JsonProperty("deleted_by")
  private String deletedBy;
  @JsonProperty("discussion_url")
  private String discussionUrl;
}
