package com.fansunited.automation.model.voting.potm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.jackson.Jacksonized;

@Data
@AllArgsConstructor
@Jacksonized
@Builder
public class OwnVotesResponse {
  @JsonProperty("match_id")
  private String matchId;

  @JsonProperty("player_id")
  private String playerId;

  @JsonProperty("created_at")
  @EqualsAndHashCode.Exclude
  private String createdAt;
}
