package com.fansunited.automation.model.voting.poll;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.voting.entities.Context;
import com.fansunited.automation.core.apis.voting.entities.PollOption;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@SuperBuilder
public class PollDto {
    private String id;
    private String title;
    private String description;
    private ApiConstants.AuthRequirement authRequirement;
    private String type;
    private Images images;
    private CommonStatus status;
    private int totalVotes;
    private List<String> flags;
    private Map<String, String> customFields;
    private Map<String, String> labels;
    private String adContent;
    private Context context;
    private List<PollOption> options;
    private BrandingDTO branding;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String embedCode;
    private String rules;
    private String language;
    private String alternativeTitle;
    private List<RelatedDto> related;
}
