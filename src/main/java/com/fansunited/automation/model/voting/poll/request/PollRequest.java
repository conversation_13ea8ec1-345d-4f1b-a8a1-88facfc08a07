package com.fansunited.automation.model.voting.poll.request;

import static com.fansunited.automation.core.apis.voting.entities.PollOption.createOptionWithRandomData;
import static com.fansunited.automation.model.common.Images.createImagesWithRandomData;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.voting.entities.Context;
import com.fansunited.automation.core.apis.voting.entities.PollOption;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.github.javafaker.Faker;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class PollRequest {

  private String title;
  private String description;
  private ApiConstants.AuthRequirement authRequirement;
  private String type;
  private Images images;
  private CommonStatus status;
  private List<String> flags;
  private Map<String, String> customFields;
  private Map<String, String> labels;
  private String rules;
  private String adContent;
  private Context context;
  private String embedCode;
  private BrandingDTO branding;
  private List<PollOption> options;
  private String language;
  private String alternativeTitle;
  private List<RelatedDto> related;

  /**
   * This method creates a PollRequest object with predefined fields
   *
   * @return PollRequest obj
   */
  public static PollRequest createPollRequestWithRandomData() {
    Faker faker = new Faker();
    return PollRequest.builder()
        .title(faker.book().title())
        .related(null)
        .language("bg")
        .alternativeTitle("test")
        .description(faker.lorem().sentence(5))
        .authRequirement(ApiConstants.AuthRequirement.getRandomAuthRequirementValue())
        .type(null)
        .images(createImagesWithRandomData())
        .status(CommonStatus.ACTIVE)
        .flags(Arrays.asList(faker.lorem().word(), faker.lorem().word()))
        .customFields(Map.of(faker.lorem().word(), faker.lorem().word()))
        .labels(Map.of(faker.lorem().word(), faker.lorem().word()))
        .rules(faker.lorem().sentence(2))
        .adContent(faker.lorem().sentence(2))
        .context(Context.createContextWithRandomData())
        .embedCode(faker.lorem().sentence(2))
        .branding(BrandingDTO.createBrandingDtoWithRandomData())
        .options(List.of(createOptionWithRandomData(), createOptionWithRandomData()))
        .build();
  }
}
