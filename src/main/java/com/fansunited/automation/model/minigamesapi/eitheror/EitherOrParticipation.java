package com.fansunited.automation.model.minigamesapi.eitheror;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EitherOrParticipation {
  private String instanceId;
  private String eitherOrId;
  private LocalDateTime gameStartedAt;
  private int currentStep;
  private int currentStreak;
  private int currentLives;
  private int currentPoints;
  private int personalBest;
  private List<EitherOrParticipationPair> steps;
}
