package com.fansunited.automation.model.minigamesapi.classicquiz;

import static com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO.createBrandingColorsDTOWithRandomData;
import static com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO.createDefaultBrandingImagesDTO;
import static com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO.createDefaultBrandingUrlsDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

@Builder
@Data
@Jacksonized
@AllArgsConstructor
@NoArgsConstructor
public class BrandingDTO {
  private BrandingColorsDTO colors;
  private BrandingUrlsDTO urls;
  private BrandingImagesDTO images;

  public static BrandingDTO createBrandingDtoWithRandomData() {
    return BrandingDTO.builder()
        .colors(createBrandingColorsDTOWithRandomData())
        .urls(createDefaultBrandingUrlsDTO())
        .images(createDefaultBrandingImagesDTO())
        .build();
  }
}
