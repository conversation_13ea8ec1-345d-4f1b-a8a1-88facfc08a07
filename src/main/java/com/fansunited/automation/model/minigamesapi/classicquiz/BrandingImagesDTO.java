package com.fansunited.automation.model.minigamesapi.classicquiz;

import com.github.javafaker.Faker;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized

public class BrandingImagesDTO {
  private String mainLogo;
  private String mobileLogo;
  private String backgroundImage;
  private String mobileBackgroundImage;
  private String additionalImage;

  public static BrandingImagesDTO createDefaultBrandingImagesDTO(){
    Faker faker = new Faker();
    return BrandingImagesDTO.builder()
        .mainLogo(faker.company().logo())
        .mobileLogo(faker.company().logo())
        .backgroundImage(faker.company().logo())
        .mobileBackgroundImage(faker.company().logo())
        .additionalImage(faker.company().logo())
        .build();
  }
}
