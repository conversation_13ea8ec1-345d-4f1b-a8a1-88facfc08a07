package com.fansunited.automation.model.minigamesapi.classicquiz;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.model.predictionapi.games.Fields;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ClassicQuizDto extends ClassicQuizPublicDto {

  private List<ClassicQuizQuestionsDto> questions;
  private Context context;
  private String average_score;
  private String perfect_score;
  private String id;
  private String title;
  private String description;
  private ImagesDto images;
  private CommonStatus status;
  private ApiConstants.AuthRequirement authRequirement;
  private List<String> flags;
  private String type;
  private int participationCount;
  private int time;
  private Fields customFields;
  private Fields labels;
  private String rules;
  private String adContent;
  private String created_at;
  private String updated_at;
  private int questions_count;
  private BrandingDTO branding;
  private String embed_code;
  private String scored;
  private int points;
  private int max_attempts;
  private String language;
  private String alternative_title;
  private List<RelatedDto> related;
}
