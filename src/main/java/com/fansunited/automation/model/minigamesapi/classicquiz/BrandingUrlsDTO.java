package com.fansunited.automation.model.minigamesapi.classicquiz;

import com.github.javafaker.Faker;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class BrandingUrlsDTO {
  private String primaryUrl;
  private String secondaryUrl;
  private String privacyPolicyUrl;
  private String termsAndConditionsUrl;
  private String additionalUrl;

  public static BrandingUrlsDTO createDefaultBrandingUrlsDTO() {
    Faker faker = new Faker();
    return BrandingUrlsDTO.builder()
        .primaryUrl(faker.internet().url())
        .secondaryUrl(faker.internet().url())
        .privacyPolicyUrl(faker.internet().url())
        .termsAndConditionsUrl(faker.internet().url())
        .additionalUrl(faker.internet().url())
        .build();
  }
}
