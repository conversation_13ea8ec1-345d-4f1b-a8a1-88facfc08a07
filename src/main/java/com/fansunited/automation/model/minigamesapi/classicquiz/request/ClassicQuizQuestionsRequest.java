package com.fansunited.automation.model.minigamesapi.classicquiz.request;

import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ClassicQuizQuestionsRequest {

  private String question;
  private GameImagesDto images;
  private List<ClassicQuizOptionsRequest> options;
  private String embed_code;
  private Boolean correct;
  private String explanation;
}
