package com.fansunited.automation.model.minigamesapi.eitheror;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class EitherOr {
    private String id;
    private String title;
    private String status;
    private ApiConstants.AuthRequirement authRequirement;
    private String description;
    private String rules;
    private Images images;
    private List<String> flags;
    private EitherOrWinningCondition winning_condition;
    private int lives;
    private int time;
    private int correct_steps;
    private List<EitherOrPoint> points;
    private List<EitherOrOption> options;
    private List<EitherOrPair> pairs;
    private LocalDateTime created_at;
    private LocalDateTime updated_at;
    private boolean enabled;
    private Fields labels;
    private Fields customFields;
    private Object types;
    private String type;
    private String ad_content;
    private Context context;
    private String client_id;
    private String question;
    private BrandingDTO branding;
    private List<RelatedDto> related;

}