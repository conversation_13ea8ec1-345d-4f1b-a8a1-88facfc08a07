package com.fansunited.automation.model.loyaltyapi.templates.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.loyaltyapi.templates.RankType;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.response.ValidRelatedEntity;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class TemplateRequest {
  @JsonIgnore
  private String name;
  @JsonIgnore
  private boolean ignoreName;

  @JsonIgnore
  private String fromDate;
  @JsonIgnore
  private boolean ignoreFromDate;

  @JsonIgnore
  private String toDate;
  @JsonIgnore
  private boolean ignoreToDate;

  @JsonIgnore
  private List<String> markets;
  @JsonIgnore
  boolean ignoreMarkets;

  @JsonIgnore
  private List<String> teamIds;
  @JsonIgnore
  private boolean ignoreTeamIds;

  @JsonIgnore
  private List<String> matchIds;
  @JsonIgnore
  private boolean ignoreMatchIds;

  @JsonIgnore
  private List<String> gameIds;
  @JsonIgnore
  private boolean ignoreGameIds;

  @JsonIgnore
  private List<String> gameTypes;
  @JsonIgnore
  private boolean ignoreGameTypes;

  @JsonIgnore
  private List<String> competitionIds;
  @JsonIgnore
  private boolean ignoreCompetitionIds;

  @JsonIgnore
  private String rules;
  @JsonIgnore
  private boolean ignoreRules;

  @JsonIgnore
  private List<String> flags;
  @JsonIgnore
  private boolean ignoreFlags;

  @JsonIgnore
  private String description;
  private boolean ignoreDescription;
  @JsonIgnore
  private List<String> excludedProfileIds;
  private boolean ignoreExcludedProfileIds;
  private Images images;
  private List<ValidRelatedEntity> related;
  private Fields labels;
  private Fields customFields;
  private String ad_content;
  private List<TemplateGroups> groups;
  private BrandingDTO branding;

  private String type;
  @JsonIgnore
  private boolean ignoreType;

  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = new HashMap<String, Object>();
    if (!ignoreName) {
      props.put("name", name);
    }
    if (!ignoreMarkets) {
      props.put("markets", markets);
    }
    if (!ignoreTeamIds) {
      props.put("team_ids", teamIds);
    }
    if (!ignoreMatchIds) {
      props.put("match_ids", matchIds);
    }
    if (!ignoreGameIds) {
      props.put("game_ids", gameIds);
    }
    if (!ignoreGameTypes) {
      props.put("game_types", gameTypes);
    }
    if (!ignoreCompetitionIds) {
      props.put("competition_ids", competitionIds);
    }
    if (!ignoreFromDate) {
      props.put("from_date", fromDate);
    }
    if (!ignoreToDate) {
      props.put("to_date", toDate);
    }
    if (!ignoreRules) {
      props.put("rules", rules);
    }
    if (!ignoreFlags) {
      props.put("flags", flags);
    }
    if (!ignoreDescription) {
      props.put("description", description);
    }
    if (!ignoreExcludedProfileIds) {
      props.put("excluded_profile_ids", excludedProfileIds);
    }

      props.put("type", Objects.requireNonNullElse(type, RankType.CUSTOM));

    return props;
  }
}
