package com.fansunited.automation.model.loyaltyapi.activity.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BadgeRequirement {
  @JsonProperty("predictions_made")
  private int predictionsMade;
  @JsonProperty("correct_predictions")
  private int correctPredictions;
  private int points;
  @JsonProperty("game_participation_count")
  private int gameParticipationCount;
  @JsonProperty("total_discussion_points")
  private int totalDiscussionPoints;
  @JsonProperty("posts_count")
  private int postsCount;
  @JsonProperty("post_points")
  private int postPoints;
  @JsonProperty("reactions_count")
  private int reactionsCount;
  @JsonProperty("reaction_points")
  private int reactionsPoints;
  @JsonProperty("entity_id")
  private String entityId;
  @JsonProperty("entity_type")
  private String entityType;
}
