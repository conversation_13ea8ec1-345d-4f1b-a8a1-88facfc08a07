package com.fansunited.automation.model.loyaltyapi.activity.request;

import static com.fansunited.automation.model.loyaltyapi.activity.request.Content.createContentWithRandomData;
import static com.fansunited.automation.model.loyaltyapi.activity.request.Tag.createTagRequestWithRandomData;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Context {
  private Content content;
  private List<Tag> tags;
  @JsonIgnore
  public boolean ignoreCampaign;
  @JsonIgnore
  private Campaign campaign;

  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = new HashMap<String, Object>();
    if (!ignoreCampaign) {
      props.put("campaign", campaign);
    }
    return props;
  }

  public static Context createContextWithRandomData() {
    return Context.builder()
        .tags(List.of(createTagRequestWithRandomData(), createTagRequestWithRandomData()))
        .content(createContentWithRandomData())
        .campaign(new Campaign())
        .build();
  }

}