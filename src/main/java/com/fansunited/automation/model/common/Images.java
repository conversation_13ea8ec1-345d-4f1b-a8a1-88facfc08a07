package com.fansunited.automation.model.common;

import com.github.javafaker.Faker;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Jacksonized
public class Images {

  private String main;
  private String cover;
  private String mobile;

  public static Images createImagesWithRandomData() {
    Faker faker = new Faker();
    return Images.builder()
        .main(faker.lorem().word())
        .cover(faker.lorem().word())
        .mobile(faker.lorem().word())
        .build();
  }
}
