package com.fansunited.automation.model.predictionapi.games.response;

import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * The type Prediction result response.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class PredictionResult {
  private String matchId;
  private ResultOutcome outcome;
}
