package com.fansunited.automation.model.predictionapi.standing.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.bracket.RelatedEntity;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.standing.Meta;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Builder
@Getter
@Setter
@AllArgsConstructor
public class CustomStandingGameRequest {

  private String id;
  private String title;
  private String description;
  private String rules;
  private Images images;
  private Integer points;
  @JsonProperty ("game_type")
  private String gameType;
  private String type;
  private List<RelatedEntity> related;

  @JsonProperty("custom_fields")
  private Map<String, String> customFields;

  private GameStatus status;

  @JsonProperty("predictions_cutoff")
  private String predictionsCutoff;

  private Meta meta;

  @JsonProperty("outcome_count")
  private Integer outcomeCount;

  private List<String> outcome;

}
