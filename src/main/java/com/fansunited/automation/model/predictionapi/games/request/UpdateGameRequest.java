package com.fansunited.automation.model.predictionapi.games.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.response.ValidRelatedEntity;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class UpdateGameRequest {

  @JsonIgnore
  private String title;

  @JsonIgnore
  private String description;

  @JsonIgnore
  private Images images;

  @JsonIgnore
  private String rules;

  @JsonIgnore
  private List<String> flags;

  @JsonIgnore
  private String status;

  @JsonIgnore
  private String predictionsCutoff;

  @JsonIgnore
  private List<GameFixture> fixtures;
  @JsonIgnore
  private String scheduleOpenAt;
  @JsonIgnore
  private List<String> excludedProfileIds;
  @JsonIgnore
  private List<ValidRelatedEntity> related;
  @JsonIgnore
  private String adContent;
  @JsonIgnore
  private Fields labels;
  @JsonIgnore
  private Fields customFields;

  @JsonAnyGetter
  public Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (title != null) {
      additionalProperties.put("title", title);
    }
    if (description != null) {
      additionalProperties.put("description", description);
    }
    if (status != null) {
      additionalProperties.put("status", status);
    }
    if (predictionsCutoff != null) {
      additionalProperties.put("predictions_cutoff", predictionsCutoff);
    }
    if (fixtures != null) {
      additionalProperties.put("fixtures", fixtures);
    }
    if (scheduleOpenAt != null) {
      additionalProperties.put("schedule_open_at", scheduleOpenAt);
    }
    if (rules != null) {
      additionalProperties.put("rules", rules);
    }
    if (flags != null) {
      additionalProperties.put("flags", flags);
    }
    if (images != null) {
      additionalProperties.put("images", images);
    }
    if (excludedProfileIds != null) {
      additionalProperties.put("excluded_profile_ids", excludedProfileIds);
    }
    if (related != null) {
      additionalProperties.put("related", related);
    }
    if (adContent != null) {
      additionalProperties.put("adContent", adContent);
    }
    if (labels != null) {
      additionalProperties.put("labels", labels);
    }
    if (customFields != null) {
      additionalProperties.put("labels", customFields);
    }
    return additionalProperties;
  }
}
