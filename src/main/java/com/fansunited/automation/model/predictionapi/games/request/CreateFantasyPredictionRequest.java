package com.fansunited.automation.model.predictionapi.games.request;

import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerPerformancePredictionFixture;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateFantasyPredictionRequest {
  private List<PlayerPerformancePredictionFixture> fixtures;
  private String template_id;
  private String group_id;
}
