package com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Arrays;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum HalfTimeFullTimeOutcome {
  @JsonProperty("1/1")
  ONE_ONE("1/1"),

  @JsonProperty("1/2")
  ONE_TWO("1/2"),

  @JsonProperty("1/x")
  ONE_DRAW("1/x"),

  @JsonProperty("x/1")
  DRAW_ONE("x/1"),

  @JsonProperty("x/2")
  DRAW_TWO("x/2"),

  @JsonProperty("x/x")
  DRAW_DRAW("x/x"),

  @JsonProperty("2/1")
  TWO_ONE("2/1"),

  @JsonProperty("2/x")
  TWO_DRAW("2/x"),

  @JsonProperty("2/2")
  TWO_TWO("2/2"),

  @JsonProperty("1")
  INVALID("1");

  @Getter
  private final String value;

  public static HalfTimeFullTimeOutcome getRandomHalfTimeFullTimeOutcome() {
    var validOutcomes =
        Arrays.stream(values()).filter(value -> value != HalfTimeFullTimeOutcome.INVALID).toList();
    var random = new Random();
    return validOutcomes.get(random.nextInt(validOutcomes.size()));
  }
}
