package com.fansunited.automation.model.predictionapi.contest;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateWinnersRequest {
  
  private String contest_id;
  private String contest_type;
  private String description;
  private List<UserListToSetWinners> user_list;
  @JsonIgnore
  private boolean ignoreDescription;
  @JsonIgnore
  private boolean ignoreUserList;

  @JsonAnyGetter
  public Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (!ignoreDescription) {
      additionalProperties.put("description", description);
    }
    return additionalProperties;
  }
}