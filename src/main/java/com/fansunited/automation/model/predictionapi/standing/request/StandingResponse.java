package com.fansunited.automation.model.predictionapi.standing.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.bracket.RelatedEntity;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.standing.Meta;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Data
@Getter
@Builder
@AllArgsConstructor
@Jacksonized
public class StandingResponse {
  private String id;
  private String title;
  private String description;
  private String rules;
  private Images images;
  private Integer points;

  @JsonProperty("game_type")
  private String gameType;

  private String type;
  private List<RelatedEntity> related;

  @JsonProperty("custom_fields")
  private Map<String, String> customFields;

  private GameStatus status;

  @JsonProperty("predictions_cutoff")
  private String predictionsCutoff;

  private Meta meta;

  @JsonProperty("outcome_count")
  private Integer outcomeCount;

  @JsonProperty("created_at")
  private String createdAt;

  @JsonProperty("updated_at")
  private String updatedAt;

  private List<String> outcome;
}
