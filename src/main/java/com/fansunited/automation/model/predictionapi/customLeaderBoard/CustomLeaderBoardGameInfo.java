package com.fansunited.automation.model.predictionapi.customLeaderBoard;

import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Getter
@Setter
@AllArgsConstructor
@Jacksonized
public class CustomLeaderBoardGameInfo {
  private String id;
  private GameType.CustomGameType type;
  @JsonProperty("created_at")
  private String createdAt;
}
