package com.fansunited.automation.model.predictionapi.games;

import com.fansunited.automation.model.footballapi.matches.MatchStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GameFixture {
  private String matchId;
  private String matchType;
  private String market;
  private MatchStatus match_status;

}
