package com.fansunited.automation.model.predictionapi.games.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.GameTiebreaker;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
@Builder
@Getter
public class CreateGameRequest {

  @JsonIgnore private String title;
  @JsonIgnore private boolean ignoreTitle;

  @JsonIgnore private String description;
  @JsonIgnore private boolean ignoreDescription;
  private List<String> excludedProfileIds;
  private String type;
  private String status;

  @JsonIgnore private String predictionsCutoff;
  @JsonIgnore private boolean ignorePredictionsCutoff;

  private List<GameFixture> fixtures;
  private Images images;
  private String scheduleOpenAt;
  private GameTiebreaker tiebreaker;
  private String adContent;
  private Fields labels;
  private Fields customFields;
  private BrandingDTO branding;
  private List<RelatedDto> related;

  @JsonAnyGetter
  public Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (!ignoreTitle) {
      additionalProperties.put("title", title);
    }
    if (!ignoreDescription) {
      additionalProperties.put("description", description);
    }
    if (!ignorePredictionsCutoff) {
      additionalProperties.put("predictions_cutoff", predictionsCutoff);
    }
    return additionalProperties;
  }
}
