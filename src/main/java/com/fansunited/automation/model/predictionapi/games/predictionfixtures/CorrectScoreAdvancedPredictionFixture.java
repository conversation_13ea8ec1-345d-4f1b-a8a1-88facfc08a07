package com.fansunited.automation.model.predictionapi.games.predictionfixtures;

import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CorrectScoreAdvancedPredictionFixture extends PredictionFixture {
  private final PredictionMarket market = PredictionMarket.CORRECT_SCORE_ADVANCED;

  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private int goalsHome;
  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private int goalsAway;
  @JsonIgnore
  private boolean ignoreGoals;

  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = super.getProperties();
    if (!ignoreGoals) {
      props.put("goals_home", goalsHome);
      props.put("goals_away", goalsAway);
    }
    return props;
  }
}
