package com.fansunited.automation.model.predictionapi.customLeaderBoard;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Builder
@AllArgsConstructor
@Data
@Getter
@Jacksonized
public class CustomLeaderboardResponse {
  private String id;

  private String title;

  String description;

  private GameStatus status;

  private String rules;

  private Images images;

  private List<String> flags;

  @JsonProperty("custom_fields")
  private Fields customFields;

  private BrandingDTO branding;

  @JsonProperty("custom_game_ids")
  private List<CustomLeaderBoardGameInfo> customGameIds;

  private Campaign labels;

  @JsonProperty("ad_content")
  private String adContent;

  @JsonProperty("created_at")
  private String createdAt;

  @JsonProperty("updated_at")
  private String updatedAt;
}
