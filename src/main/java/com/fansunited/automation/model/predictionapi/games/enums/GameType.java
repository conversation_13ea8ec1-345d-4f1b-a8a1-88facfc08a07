package com.fansunited.automation.model.predictionapi.games.enums;

import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum GameType {
  TOP_X("TOP_X"),
  MATCH_QUIZ("MATCH_QUIZ"),
  IN<PERSON><PERSON><PERSON>("INVALID"),
  SINGLE("SINGLE"),
  INVALID_EMPTY(""),
  INVALID_NULL(null);

  @Getter
  private final String value;

  public static List<GameType> getValidGameTypes() {
    return Arrays.stream(GameType.values())
        .filter(market -> market != INVALID
            && market != INVALID_EMPTY
            && market != INVALID_NULL)
        .toList();
  }
}


