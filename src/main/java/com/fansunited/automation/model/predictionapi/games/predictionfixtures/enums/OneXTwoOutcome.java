package com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Arrays;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum OneXTwoOutcome {
  @JsonProperty("1")
  ONE("1"),

  @JsonProperty("x")
  DRAW("x"),

  @JsonProperty("2")
  TWO("2"),

  @JsonProperty("0")
  INVALID("0");

  @Getter
  private final String value;

  public static OneXTwoOutcome getRandomOneXTwoOutcome() {
    var validOutcomes =
        Arrays.stream(values()).filter(value -> value != OneXTwoOutcome.INVALID).toList();
    var random = new Random();
    return validOutcomes.get(random.nextInt(validOutcomes.size()));
  }
}