package com.fansunited.automation.model.predictionapi.games.predictionfixtures;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomEventFixture {

  private String id;

  private String question;

  private String  status;

  @JsonProperty("void_reason")
  private String voidReason;

  private Integer points;

  private String outcome;

  @JsonProperty("outcome_type")
  private String outcomeType;

  @JsonProperty("valid_outcomes")
  private NumberValidOutcomes validOutcomes;
}
