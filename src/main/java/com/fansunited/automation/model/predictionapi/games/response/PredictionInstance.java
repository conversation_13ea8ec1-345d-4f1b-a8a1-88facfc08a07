package com.fansunited.automation.model.predictionapi.games.response;

import com.fansunited.automation.model.predictionapi.games.Tiebreaker;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class PredictionInstance {
  private String id;
  private String gameInstanceId;
  private PredictionType gameType;
  private Integer wager;
  private Integer totalFixtures;
  private Integer settledFixtures;
  private String userId;
  private List<? extends PredictionFixture> fixtures;
  private PredictionStatus status;
  private Tiebreaker tiebreaker;
  private int points;
  private String createdAt;
  private String updatedAt;
  private String system_last_kickoff;
  private Object error;
  private String templateId;
  private String groupId;
  private String profile_last_updated_at;
}

