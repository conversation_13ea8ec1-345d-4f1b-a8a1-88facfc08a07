package com.fansunited.automation.model.predictionapi.bracket.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.bracket.Fixture;
import com.fansunited.automation.model.predictionapi.bracket.Meta;
import com.fansunited.automation.model.predictionapi.bracket.RelatedEntity;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
@Builder
@Getter
public class UpdateBracketRequest {
  
  private String title;
  private String description;
  private String rules;
  private Images images;
  private Integer points;
  private List<RelatedEntity> related;
  @JsonProperty("custom_fields")
  private Fields customFields;
  private GameStatus status;
  @JsonProperty("predictions_cutoff")
  private String predictionsCutoff;
  private Meta meta;
  private List<Fixture> fixtures;
}
