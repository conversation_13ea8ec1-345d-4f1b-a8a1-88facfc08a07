package com.fansunited.automation.model.predictionapi.bracket;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Getter
@Setter
@Builder
public class CustomBracketFixture {
  @JsonProperty("match_id")
  private String matchId;
  @JsonProperty("participant_one")
  private String participantOne;
  @JsonProperty("participant_two")
  private String participantTwo;
  private String winner;
}
