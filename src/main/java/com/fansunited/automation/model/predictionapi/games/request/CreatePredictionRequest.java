package com.fansunited.automation.model.predictionapi.games.request;

import com.fansunited.automation.model.predictionapi.games.Tiebreaker;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreatePredictionRequest {
  private String gameInstanceId;
  private List<? extends PredictionFixture> fixtures;
  private String scheduleOpenAt;
  @JsonIgnore
  private Tiebreaker tiebreaker;

  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = new HashMap<String, Object>();
    if (tiebreaker != null) {
      props.put("tiebreaker", tiebreaker);
    }
    if (scheduleOpenAt != null) {
      props.put("schedule_open_at", scheduleOpenAt);
    }
    return props;
  }
}
