package com.fansunited.automation.model.predictionapi.games.response;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.EventGameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CreateCustomEventGameResponse {

  private GameType type;

  private List<CustomEventFixture> fixtures;

  private String title;

  private String description;

  private String rules;

  private Images images;

  private List<RelatedDto> related;

  @JsonProperty("custom_fields")
  private Fields customFields;

  private EventGameStatus status;

  @JsonProperty("predictions_cutoff")
  private String predictionsCutoff;

  private Boolean outcome;

  //@JsonProperty("valid_outcomes")
  //private BooleanValidOutcomes validOutcomes;
}
