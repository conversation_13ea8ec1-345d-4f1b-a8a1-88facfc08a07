package com.fansunited.automation.model.predictionapi.games.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.EventGameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class CreateCustomEventGameRequest {

  private GameType type;

  private List<CustomEventFixture> fixtures;

  private String title;

  private String description;

  private String rules;

  private Images images;

  private List<RelatedDto> related;

  @JsonProperty("custom_fields")
  private Fields customFields;

  private EventGameStatus status;

  @JsonProperty("predictions_cutoff")
  private String predictionsCutoff;

  private Boolean outcome;

  //@JsonProperty("valid_outcomes")
  //private BooleanValidOutcomes validOutcomes;
}
