package com.fansunited.automation.model.predictionapi.predictions;


import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum AdvancedPredictions {

  CORRECT_RESULT(Map.of(AdvancedPredictionDifference.CORRECT_DIFF, "CORRECT_RESULT")),
  CORRECT_HOME_TEAM(Map.of(AdvancedPredictionDifference.INCORRECT_DIFF, "CORRECT_HOME_TEAM")),
  CORRECT_HOME_TEAM_WITH_CORRECT_NEGATIVE_DIFF(
      Map.of(AdvancedPredictionDifference.CORRECT_NEGATIVE_DIFF,
          "CORRECT_HOME_TEAM_WITH_CORRECT_NEGATIVE_DIFF")),
  CORRECT_HOME_TEAM_AND_OUTCOME(
      Map.of(AdvancedPredictionDifference.INCORRECT_DIFF, "CORRECT_HOME_TEAM_AND_OUTCOME")),

  CORRECT_AWAY_TEAM(Map.of(AdvancedPredictionDifference.INCORRECT_DIFF, "CORRECT_AWAY_TEAM")),
  CORRECT_AWAY_TEAM_WITH_CORRECT_NEGATIVE_DIFF(
      Map.of(AdvancedPredictionDifference.CORRECT_NEGATIVE_DIFF,
          "CORRECT_AWAY_TEAM_WITH_CORRECT_NEGATIVE_DIFF")),
  CORRECT_AWAY_TEAM_AND_OUTCOME(
      Map.of(AdvancedPredictionDifference.INCORRECT_DIFF, "CORRECT_AWAY_TEAM_AND_OUTCOME")),
  CORRECT_DIFF_AND_OUTCOME(
      Map.of(AdvancedPredictionDifference.CORRECT_DIFF, "CORRECT_DIFF_AND_OUTCOME")),
  INCORRECT_RESULT(
      Map.of(AdvancedPredictionDifference.CORRECT_DIFF, "INCORRECT_RESULT"));

  @Getter
  private final Map<AdvancedPredictionDifference, String> value;

  public enum AdvancedPredictionDifference {
    CORRECT_DIFF,
    CORRECT_NEGATIVE_DIFF,
    INCORRECT_DIFF;
  }
}
