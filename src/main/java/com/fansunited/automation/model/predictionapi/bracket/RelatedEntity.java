package com.fansunited.automation.model.predictionapi.bracket;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Getter
@Jacksonized
public class RelatedEntity {
  @JsonProperty("entity_id")
  String entityId;

  @JsonProperty("entity_type")
  String entityType;

  @JsonProperty("entity_relationship")
  String entityRelationship;
}
