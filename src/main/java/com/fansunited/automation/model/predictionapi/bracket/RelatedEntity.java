package com.fansunited.automation.model.predictionapi.bracket;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Getter
@Jacksonized
public class RelatedEntity {
    @JsonProperty("entity_id")
    String entity_id;
    @JsonProperty("entity_type")
    String entity_type;
    @JsonProperty("entity_relationship")
    String entity_relationship;

    }