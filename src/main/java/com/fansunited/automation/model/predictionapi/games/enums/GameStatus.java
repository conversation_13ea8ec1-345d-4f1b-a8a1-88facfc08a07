package com.fansunited.automation.model.predictionapi.games.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum GameStatus {
  /**
   * the game has been manually canceled by a staff member
   */
  CANCELED("CANCELED"),
  /**
   * does not accept entries, matches are over, resolving is done
   */
  SETTLED("SETTLED"),
  /**
   * does not accept entries, matches are over, resolving is in progress
   */
  CLOSED("CLOSED"),
  /**
   * does not accept entries
   */
  LIVE("LIVE"),
  /**
   * accepts entries
   */
  OPEN("OPEN"),
  /**
   * game is still not opened for users
   */
  PENDING("PENDING"),
  /**
   * invalid game status, used for validation
   */
  INVALID("INVALID"),
  /**
   * invalid game status, used for validation
   */
  INVALID_EMPTY("");

  @Getter
  private final String value;

  public static List<GameStatus> getValidStatuses() {
    return Arrays.stream(values())
        .filter(value -> value != GameStatus.INVALID && value != GameStatus.INVALID_EMPTY)
        .toList();
  }

  public static String listToCommaSeparated(List<GameStatus> statuses) {
    var statusList = new ArrayList<String>();

    for (GameStatus status : statuses) {
      statusList.add(status.getValue());
    }
    return String.join(",", statusList);
  }
}
