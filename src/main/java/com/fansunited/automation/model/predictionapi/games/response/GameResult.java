package com.fansunited.automation.model.predictionapi.games.response;

import com.fansunited.automation.model.predictionapi.games.Tiebreaker;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * The type Game result response.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class GameResult {
  private String userId;
  private int points;
  private List<PredictionResult> results;
  private Tiebreaker tiebreaker;
}
