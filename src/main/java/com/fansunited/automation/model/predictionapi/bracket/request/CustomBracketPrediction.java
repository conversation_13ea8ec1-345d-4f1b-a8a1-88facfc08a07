package com.fansunited.automation.model.predictionapi.bracket.request;

import com.fansunited.automation.model.predictionapi.bracket.BracketTieBreaker;
import com.fansunited.automation.model.predictionapi.bracket.CustomBracketFixture;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
@Builder
@Getter
public class CustomBracketPrediction {
  private List<CustomBracketFixture> fixtures;
  private BracketTieBreaker tiebreaker;
}
