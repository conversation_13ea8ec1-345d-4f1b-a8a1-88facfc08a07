package com.fansunited.automation.model.predictionapi.games.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum EventGameStatus {
  /** the game is active  */
  ACTIVE(" ACTIVE"),
  /** does not accept entries, matches are over, resolving is done */
  SETTLED("SETTLED"),
/** does not accept entries, matches are over, resolving is in progress */
  VOID("VOID");

  @Getter private final String value;
}
