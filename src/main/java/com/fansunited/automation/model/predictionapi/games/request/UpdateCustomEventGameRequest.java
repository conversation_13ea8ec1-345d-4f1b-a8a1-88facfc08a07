package com.fansunited.automation.model.predictionapi.games.request;

import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Jacksonized
public class UpdateCustomEventGameRequest {

  private String type;

  private List<CustomEventFixture> fixtures;

  private String title;

  private String description;

  private String rules;

  private Images images;

  private List<RelatedDto> related;

  @JsonProperty("custom_fields")
  private Fields customFields;

  private GameStatus status;

  @JsonProperty("predictions_cutoff")
  private Date predictionsCutoff;

  private Boolean outcome;

  @JsonProperty("valid_outcomes")
  private Object validOutcomes;

}
