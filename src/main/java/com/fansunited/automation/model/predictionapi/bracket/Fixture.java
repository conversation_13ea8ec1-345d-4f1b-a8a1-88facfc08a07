package com.fansunited.automation.model.predictionapi.bracket;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
@Getter
@Builder
public class Fixture {
  @JsonProperty("match_id")
  private String matchId;

  @JsonProperty("participant_one")
  private String participantOne;

  @JsonProperty("participant_two")
  private String participantTwo;

  private String winner;

  @JsonProperty("home_participant")
  private String home_participant;

  @JsonProperty("start_date")
  private String start_date;

  private Score score;
}
