package com.fansunited.automation.model.profileapi.follow;

import com.fansunited.automation.model.common.Meta;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Data
public class ProfileFollowingsData {
  private Meta meta;
  @JsonProperty("data")
  private List<FollowingProfile> profileFollowings;

  @Data
  @EqualsAndHashCode
  @Getter
  public static class FollowingProfile {
    private String avatar;
    private String followingId;
    private String name;
    private String profileId;
    private String nickname;
    private String created_at;
    private String error;
  }
}
