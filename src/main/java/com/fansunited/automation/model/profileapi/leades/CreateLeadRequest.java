package com.fansunited.automation.model.profileapi.leades;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@AllArgsConstructor
@NoArgsConstructor
@XmlRootElement
public class CreateLeadRequest {

  String fullName;
  String firstName;
  String lastName;
  String email;
  String gender;
  String country;
  String phoneCountryCode;
  String phoneNumber;
  String campaignId;
  String campaignName;
  String contentType;
  String contentId;
  String contentName;
  Map<String, String> customFields;
}
