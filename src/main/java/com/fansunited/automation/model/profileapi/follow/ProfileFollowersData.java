package com.fansunited.automation.model.profileapi.follow;

import com.fansunited.automation.model.common.Meta;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class ProfileFollowersData {
  private Meta meta;
  @JsonProperty("data")
  private List<ProfileFollower> profileFollowers;

  @Data
  @EqualsAndHashCode
  public static class ProfileFollower {
    private String avatar;
    private String followerId;
    private String name;
    private String profileId;
    private String nickname;
  }
}