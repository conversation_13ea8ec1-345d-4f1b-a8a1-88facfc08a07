package com.fansunited.automation.model.profileapi.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProfileData {
  @JsonProperty("data")
  private Profile profile;

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Profile {

    private String id;
    private String name;
    @JsonProperty("firstName")
    private String firstName;
    @JsonProperty("lastName")
    private String lastName;
    private String avatar;
    private Profile.Gender gender;
    private Object country;
    private String birthDate;
    private List<Interest> interests;
    private Integer followingCount;
    private Integer followersCount;
    private String nickname;
    private String sub;
    private String email;
    private String createdAt;
    private Boolean verified;
    private String staffMember;
    private Boolean anonymous;

    @AllArgsConstructor
    public enum Gender {

      MALE("male"),
      FEMALE("female"),
      UNSPECIFIED("unspecified");

      @Getter
      @JsonValue
      private final String value;

      @Override
      public String toString() {
        return this.value;
      }

      public static Gender getRandomGender() {
        var genderList = Arrays.stream(values()).toList();
        var random = new Random();
        return genderList.get(random.nextInt(genderList.size()));
      }
    }
  }

  @Data
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class DeletedProfile {

    private String id;
    private String name;
    private String avatar;
    private DeletedProfile.Gender gender;
    private Object country;
    private String birthDate;
    private List<Interest> interests;
    private Integer followingCount;
    private Integer followersCount;
    private String nickname;
    private String createdAt;
    private Boolean verified;
    private String staffMember;
    private Boolean anonymous;

    @AllArgsConstructor
    public enum Gender {

      MALE("male"),
      FEMALE("female"),
      UNSPECIFIED("unspecified"),
      EMPTY_STRING("");

      @Getter
      @JsonValue
      private final String value;

      @Override
      public String toString() {
        return this.value;
      }

      public static Gender getRandomGender() {
        var genderList = Arrays.stream(values())
            .filter(gender -> gender != EMPTY_STRING && gender != UNSPECIFIED)
            .toList();
        var random = new Random();
        return genderList.get(random.nextInt(genderList.size()));
      }
    }
  }

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Interest {

    private Boolean favourite;
    private String id;
    private String source;
    private String type;
  }
}