package com.fansunited.automation.model.reportingapi.predictions.allpredictions;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;

@Data
@Getter
public class PredictionsByMarket {
  private long all;
  @JsonProperty("FT_1X2")
  private long FT_1X2;
  @JsonProperty("HT_1X2")
  private long HT_1X2;
  @JsonProperty("BOTH_TEAMS_SCORE")
  private long BOTH_TEAMS_SCORE;
  @JsonProperty("OVER_GOALS_0_5")
  private long OVER_GOALS_0_5;
  @JsonProperty("OVER_GOALS_1_5")
  private long OVER_GOALS_1_5;
  @JsonProperty("OVER_GOALS_2_5")
  private long OVER_GOALS_2_5;
  @JsonProperty("OVER_GOALS_3_5")
  private long OVER_GOALS_3_5;
  @JsonProperty("OVER_GOALS_4_5")
  private long OVER_GOALS_4_5;
  @JsonProperty("OVER_GOALS_5_5")
  private long OVER_GOALS_5_5;
  @JsonProperty("OVER_GOALS_6_5")
  private long OVER_GOALS_6_5;
  @JsonProperty("OVER_CORNERS_6_5")
  private long OVER_CORNERS_6_5;
  @JsonProperty("OVER_CORNERS_7_5")
  private long OVER_CORNERS_7_5;
  @JsonProperty("OVER_CORNERS_8_5")
  private long OVER_CORNERS_8_5;
  @JsonProperty("OVER_CORNERS_9_5")
  private long OVER_CORNERS_9_5;
  @JsonProperty("OVER_CORNERS_10_5")
  private long OVER_CORNERS_10_5;
  @JsonProperty("OVER_CORNERS_11_5")
  private long OVER_CORNERS_11_5;
  @JsonProperty("OVER_CORNERS_12_5")
  private long OVER_CORNERS_12_5;
  @JsonProperty("OVER_CORNERS_13_5")
  private long OVER_CORNERS_13_5;
  @JsonProperty("DOUBLE_CHANCE")
  private long DOUBLE_CHANCE;
  @JsonProperty("HT_FT")
  private long HT_FT;
  @JsonProperty(" PLAYER_SCORE")
  private long PLAYER_SCORE;
  @JsonProperty("PLAYER_YELLOW_CARD")
  private long PLAYER_YELLOW_CARD;
  @JsonProperty("PLAYER_RED_CARD")
  private long PLAYER_RED_CARD;
  @JsonProperty("RED_CARD_MATCH")
  private long RED_CARD_MATCH;
  @JsonProperty("PENALTY_MATCH")
  private long PENALTY_MATCH;
  @JsonProperty("PLAYER_SCORE_FIRST_GOAL")
  private long PLAYER_SCORE_FIRST_GOAL;
  @JsonProperty("CORNERS_MATCH")
  private long CORNERS_MATCH;
  @JsonProperty("CORRECT_SCORE")
  private long CORRECT_SCORE;
  @JsonProperty("CORRECT_SCORE_HT")
  private long CORRECT_SCORE_HT;
  @JsonProperty("CORRECT_SCORE_ADVANCED")
  private long CORRECT_SCORE_ADVANCED;
  @JsonProperty("PLAYER_SCORE_HATTRICK")
  private long PLAYER_SCORE_HATTRICK;
  @JsonProperty("PLAYER_SCORE_TWICE")
  private long PLAYER_SCORE_TWICE;

  private String unspecified;

  private long getValue;
}
