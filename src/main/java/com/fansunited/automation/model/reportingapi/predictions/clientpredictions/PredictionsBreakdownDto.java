package com.fansunited.automation.model.reportingapi.predictions.clientpredictions;

import com.fansunited.automation.model.reportingapi.predictions.allpredictions.PredictionsByMarket;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

@Data
public class PredictionsBreakdownDto {
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "UTC")
  private LocalDate date;
  private PredictionsByMarket predictions;
}
