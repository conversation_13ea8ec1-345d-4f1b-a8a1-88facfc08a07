package com.fansunited.automation.model.reportingapi.users.registeredusers;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Data;

/**

 * The type mau vs registrations Breakdown dto.

 */

@Data @Builder
public class MauVsRegistrationsBreakdownDto {
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "UTC")

  private LocalDate date;
  private Long activeUsers;
  private Long registrations;

}
