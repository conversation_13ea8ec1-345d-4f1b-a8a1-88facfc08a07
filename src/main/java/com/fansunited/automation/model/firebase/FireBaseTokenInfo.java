package com.fansunited.automation.model.firebase;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)
public class FireBaseTokenInfo {
  private String kind;
  private String localId;
  private String email;
  private String displayName;
  private String idToken;
  private Boolean registered;
  private String profilePicture;
  private String refreshToken;
  private String expiresIn;
}