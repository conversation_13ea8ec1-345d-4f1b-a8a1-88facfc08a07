package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BadgeRewards {
  private List<Badge> general;
  private List<Badge> predictor;

  @JsonProperty("top_x")
  private List<Badge> topX;

  @JsonProperty("match_quiz")
  private List<Badge> matchQuiz;

  @JsonProperty("classic_quizzes")
  private List<Badge> classicQuizzes;

  private List<Badge> discussions;
  @JsonProperty("either_or")
  private List<Badge> eitherOr;
  private List<Badge> football;
  private List<Badge> games;
  private VotingBadge voting;
}
