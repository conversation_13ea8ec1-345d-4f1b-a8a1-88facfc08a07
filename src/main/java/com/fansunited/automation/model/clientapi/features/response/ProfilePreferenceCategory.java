package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class ProfilePreferenceCategory {

  private String id;

  @JsonProperty("display_name")
  private String displayName;
}
