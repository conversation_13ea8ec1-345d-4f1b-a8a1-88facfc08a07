package com.fansunited.automation.model.clientapi.features.response;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfilePreferencesFeature{

    private boolean enabled;
    private List<ProfilePreference> preferences;
    private List<ProfilePreferenceCategory> categories;
    private Date createdAt;
    private Date updatedAt;
}
