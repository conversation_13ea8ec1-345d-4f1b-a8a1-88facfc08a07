package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.Multipliers;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

/** FantasyFootballFeature entity for jfirebase. */
@Data
@Builder
@AllArgsConstructor
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class FootballFantasyFeature {

  private Boolean enabled;
  private Multipliers multipliers;

  private List<String> competitions_whitelist;
  private Boolean add_points_to_profileTotal;
  private Players players;
  private Coefficients coefficients;

  private String clientId;
  @JsonIgnore
  private boolean ignoreClientId;

  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = new HashMap<String, Object>();
    if (!ignoreClientId) {
      props.put("client_id", clientId);
    }
    return props;
  }
}
