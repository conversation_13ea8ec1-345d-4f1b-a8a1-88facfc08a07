package com.fansunited.automation.model.clientapi.features.response.managelists;

import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.ListTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreateListFeature {
  private String id;

  @JsonProperty("client_id")
  private String clientId;

  private String name;
  private ListTypes type;
  private Entities entities;

  @JsonProperty("created_at")
  private String createdAt;

  @JsonProperty("updated_at")
  private String updatedAt;

  @JsonProperty("created_by")
  private String createdBy;
}
