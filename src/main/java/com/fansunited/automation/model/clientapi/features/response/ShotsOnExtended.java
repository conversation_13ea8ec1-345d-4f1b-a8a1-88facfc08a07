package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShotsOnExtended {
    @JsonProperty("1_shots_on")
    private int oneShotOn;
    @JsonProperty("2_shots_on")
    private int twoShotsOn;
    @JsonProperty("3_shots_on")
    private int threeShotsOn;
    @JsonProperty("4_shots_on")
    private int fourShotsOn;
    @JsonProperty("5_shots_on")
    private int fiveShotsOn;
    @JsonProperty("6_shots_on")
    private int sixShotsOn;
    @JsonProperty("7_shots_on")
    private int sevenShotsOn;
    @JsonProperty("8_shots_on")
    private int eightShotsOn;
    @JsonProperty("9_shots_on")
    private int nineShotsOn;
    @JsonProperty("10_shots_on_or_more")
    private int tenShotsOnOrMore;
}
