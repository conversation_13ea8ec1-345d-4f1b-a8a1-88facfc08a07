package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.model.loyaltyapi.activity.request.BadgeRequirement;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Badge extends BaseBadge{
  private BadgeRequirement requirements;
  private String label;
  private AssetsURL assets;
  private String description;
  private List<String> flags;
}
