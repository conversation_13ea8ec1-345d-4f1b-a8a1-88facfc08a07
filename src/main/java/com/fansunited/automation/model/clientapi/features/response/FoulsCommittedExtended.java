package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FoulsCommittedExtended {
    @JsonProperty("1_fouls_committed")
    private int oneFoulCommitted;
    @JsonProperty("2_fouls_committed")
    private int twoFoulsCommitted;
    @JsonProperty("3_fouls_committed")
    private int threeFoulsCommitted;
    @JsonProperty("4_fouls_committed")
    private int fourFoulsCommitted;
    @JsonProperty("5_fouls_committed_or_more")
    private int fiveFoulsCommittedOrMore;
}
