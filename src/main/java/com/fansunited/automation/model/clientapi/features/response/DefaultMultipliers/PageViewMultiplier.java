package com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers;

import com.fansunited.automation.model.clientapi.features.response.WeightMultiplier;
import java.util.List;
import lombok.Data;

@Data
public class PageViewMultiplier {

  private static String id = "PAGE_VIEW";
  private static int weight = 1;
  private static int multiplier = 1;
  private static List<String> conditions;

  public static WeightMultiplier returnAsWeightMultiplier(){
    WeightMultiplier weightMultiplier = new WeightMultiplier();
    weightMultiplier.setId(id);
    weightMultiplier.setWeight(weight);
    weightMultiplier.setMultiplier(multiplier);
    weightMultiplier.setConditions(conditions);

    return weightMultiplier;
  }
}