package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.model.discussionapi.DiscussionDto;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOr;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class Features {

  private PredictorFeature predictor;
  private TopXFeature top_x;
  private MatchQuizFeature match_quiz;
  private FootballFantasyFeature football_fantasy;
  private LoyaltyFeature loyalty;
  private ExternalPoints external_points;
  private Webhook webhook;
  private DiscussionDto discussions;
  private Object engagement;
  private ClassicQuizFeatureResponse classic_quiz;
  private EitherOr either_or;
  private PollFeature poll;
  private PotmFeature potm;
  private ProfilePreferencesFeature profilePreferences;
}
