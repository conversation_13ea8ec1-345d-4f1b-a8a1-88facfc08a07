package com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Getter
@Setter
@Jacksonized
public class Multipliers {

  private int captain;
  @JsonProperty("vice_captain")
  private int viceCaptain;
}
