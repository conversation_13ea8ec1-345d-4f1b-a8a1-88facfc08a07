package com.fansunited.automation.model.clientapi.features.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

/**
 * An entity for success rates cope in predictor feature.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Jacksonized
public class SuccessRateScope {
  private List<String> competitions;
  private List<String> teams;
  private List<Markets> markets;
}
