package com.fansunited.automation.model.clientapi.features.response;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class Badges {
    private List<Badge> badges;

    public Badge getBadgeById(String id) {
        return badges.stream()
                .filter(badge -> badge.getId().equals(id))
                .findFirst()
                .orElse(null);
    }
}
