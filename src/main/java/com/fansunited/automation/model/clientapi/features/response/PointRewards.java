package com.fansunited.automation.model.clientapi.features.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointRewards {
  private List<General> general;
  private List<Comments> comments;
  private List<PointMultiplier> discussion_post;
  private List<PointMultiplier> topX;
  private List<PointMultiplier> matchQuiz;
  private List<PointMultiplier> predictor;
  private List<PointMultiplier> activity;
  private Voting voting;
}
