package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@AllArgsConstructor
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoyaltyFeature {
  private LoyaltyActions actions;
  private String client_id;
  private LoyaltyConditions conditions;
  private boolean enabled;
  private String id;
  private LoyaltyRewards rewards;
}
