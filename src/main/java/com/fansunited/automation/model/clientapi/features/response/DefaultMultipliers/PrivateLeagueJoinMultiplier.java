package com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers;

import com.fansunited.automation.model.clientapi.features.response.WeightMultiplier;
import java.util.List;
import lombok.Data;

@Data
public class PrivateLeagueJoinMultiplier {

  private static String id = "PRIVATE_LEAGUE_JOIN";
  private static int weight = 3;
  private static int multiplier = 1;
  private static List<String> conditions;

  public static WeightMultiplier returnAsWeightMultiplier() {
    WeightMultiplier weightMultiplier = new WeightMultiplier();
    weightMultiplier.setId(id);
    weightMultiplier.setWeight(weight);
    weightMultiplier.setMultiplier(multiplier);
    weightMultiplier.setConditions(conditions);

    return weightMultiplier;
  }
}
