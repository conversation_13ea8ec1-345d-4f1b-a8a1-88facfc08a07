package com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers;

import com.fansunited.automation.model.clientapi.features.response.WeightMultiplier;
import java.util.List;
import lombok.Data;

@Data
public class EitherOrParticipationMultiplier {

  private static String id = "EITHER_OR_PARTICIPATION";
  private static int weight = 5;
  private static int multiplier = 1;
  private static List<String> conditions;

  public static WeightMultiplier returnAsWeightMultiplier() {
    WeightMultiplier weightMultiplier = new WeightMultiplier();
    weightMultiplier.setId(id);
    weightMultiplier.setWeight(weight);
    weightMultiplier.setMultiplier(multiplier);
    weightMultiplier.setConditions(conditions);

    return weightMultiplier;
  }
}
