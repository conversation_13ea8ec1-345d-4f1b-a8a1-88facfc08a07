package com.fansunited.automation.model.clientapi.features.response;

/**
 * The enum Markets.
 **/
public enum Markets {
  FT_1X2,
  HT_1X2,
  HT_FT,
  BOTH_TEAMS_SCORE,
  CORRECT_SCORE,
  CORRECT_SCORE_HT,
  CORRECT_SCORE_ADVANCED,
  DOUBLE_CHANCE,
  PLAYER_SCORE,
  PLAYER_SCORE_TWICE,
  PLAYER_SCORE_HATTRICK,
  PLAYER_YELLOW_CARD,
  PLAYER_RED_CARD,
  RED_CARD_MATCH,
  PENALTY_MATCH,
  /**
   * @deprecated  Separated on sub markets this value is for deserialization of old documents.
   */
  @Deprecated
  OVER_GOALS,
  OVER_GOALS_0_5,
  OVER_GOALS_1_5,
  OVER_GOALS_2_5,
  OVER_GOALS_3_5,
  OVER_GOALS_4_5,
  OVER_GOALS_5_5,
  OVER_GOALS_6_5,
  /**
   * @deprecated Separated on sub markets this value is for deserialization of old documents.
   */
  @Deprecated
  OVER_CORNERS,
  OVER_CORNERS_6_5,
  OVER_CORNERS_7_5,
  OVER_CORNERS_8_5,
  OVER_CORNERS_9_5,
  OVER_CORNERS_10_5,
  OVER_CORNERS_11_5,
  OVER_CORNERS_12_5,
  OVER_CORNERS_13_5,
  PLAYER_SCORE_FIRST_GOAL,
  CORNERS_MATCH;

  /**
   * Documentation for markets.
   **/
  public static class Documentation {
    public static final String FT_1X2 = "FT_1X2";
    public static final String HT_1X2 = "HT_1X2";
    public static final String BOTH_TEAMS_SCORE = "BOTH_TEAMS_SCORE";
    public static final String OVER_GOALS = "OVER_GOALS";
    public static final String OVER_CORNERS = "OVER_CORNERS";
    public static final String DOUBLE_CHANCE = "DOUBLE_CHANCE";
    public static final String HT_FT = "HT_FT";
    public static final String PLAYER_SCORE = "PLAYER_SCORE";
    public static final String CANCELED = "CANCELED";
    public static final String PLAYER_YELLOW_CARD = "PLAYER_YELLOW_CARD";
    public static final String PLAYER_RED_CARD = "PLAYER_RED_CARD";
    public static final String RED_CARD_MATCH = "RED_CARD_MATCH";
    public static final String PENALTY_MATCH = "PENALTY_MATCH";
    public static final String PLAYER_SCORE_FIRST_GOAL = "PLAYER_SCORE_FIRST_GOAL";
    public static final String CORNERS_MATCH = "CORNERS_MATCH";
    public static final String CORRECT_SCORE = "CORRECT_SCORE";
    public static final String CORRECT_SCORE_HT = "CORRECT_SCORE_HT";
    public static final String CORRECT_SCORE_ADVANCED = "CORRECT_SCORE_ADVANCED";
    public static final String PLAYER_SCORE_HATTRICK = "PLAYER_SCORE_HATTRICK";
    public static final String PLAYER_SCORE_TWICE = "PLAYER_SCORE_TWICE";

    public static final String OVER_GOALS_0_5 = "OVER_GOALS_0_5";
    public static final String OVER_GOALS_1_5 = "OVER_GOALS_1_5";
    public static final String OVER_GOALS_2_5 = "OVER_GOALS_2_5";
    public static final String OVER_GOALS_3_5 = "OVER_GOALS_3_5";
    public static final String OVER_GOALS_4_5 = "OVER_GOALS_4_5";
    public static final String OVER_GOALS_5_5 = "OVER_GOALS_5_5";
    public static final String OVER_GOALS_6_5 = "OVER_GOALS_6_5";
    public static final String OVER_CORNERS_6_5 = "OVER_CORNERS_6_5";
    public static final String OVER_CORNERS_7_5 = "OVER_CORNERS_7_5";
    public static final String OVER_CORNERS_8_5 = "OVER_CORNERS_8_5";
    public static final String OVER_CORNERS_9_5 = "OVER_CORNERS_9_5";
    public static final String OVER_CORNERS_10_5 = "OVER_CORNERS_10_5";
    public static final String OVER_CORNERS_11_5 = "OVER_CORNERS_11_5";
    public static final String OVER_CORNERS_12_5 = "OVER_CORNERS_12_5";
    public static final String OVER_CORNERS_13_5 = "OVER_CORNERS_13_5";

    private Documentation() {
    }
  }
}
