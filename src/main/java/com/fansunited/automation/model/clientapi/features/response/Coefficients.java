package com.fansunited.automation.model.clientapi.features.response;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

/**
 * Coefficients entity for jfirebase.
 */
@Data
@Builder
@AllArgsConstructor
@Jacksonized
public class Coefficients {

  @JsonProperty("assists")
  private Integer assists;

  @JsonProperty("shots")
  private Integer shots;

  @JsonProperty("offsides")
  private Integer offsides;

  @JsonProperty("tackles")
  private Integer tackles;

  @JsonProperty("saves")
  private Integer saves;

  @JsonProperty("minutes_equal_or_over_60")
  private Integer minutesEqualOrOver60;

  @JsonProperty("minutes_under_60")
  private Integer minutesUnder60;

  @JsonProperty("yellow_cards")
  private Integer yellowCards;

  @JsonProperty("red_cards")
  private Integer redCards;

  @JsonProperty("goals_goalkeeper")
  private Integer goalsGoalkeeper;

  @JsonProperty("goals_defender")
  private Integer goalsDefender;

  @JsonProperty("goals_midfielder")
  private Integer goalsMidfielder;

  @JsonProperty("goals_forwards")
  private Integer goalsForwards;

  @JsonProperty("penalty_goals")
  private Integer penaltyGoals;

  @JsonProperty("own_goals")
  private Integer ownGoals;

  @JsonProperty("assists_extended")
  private AssistsExtended assistsExtended;

  @JsonProperty("clean_sheets_goalkeeper")
  private Integer cleanSheetsGoalkeeper;

  @JsonProperty("clean_sheets_defender")
  private Integer cleanSheetsDefender;

  @JsonProperty("clean_sheets_midfielder")
  private Integer cleanSheetsMidfielder;

  @JsonProperty("clean_sheets_forwards")
  private Integer cleanSheetsForwards;

  @JsonProperty("shots_on")
  private Integer shotsOn;

  @JsonProperty("shots_on_extended")
  private ShotsOnExtended shotsOnExtended;

  @JsonProperty("fouls_committed")
  private Integer foulsCommitted;

  @JsonProperty("fouls_committed_extended")
  private FoulsCommittedExtended foulsCommittedExtended;

  @JsonProperty("penalty_committed")
  private Integer penaltyCommitted;

  @JsonProperty("penalty_won")
  private Integer penaltyWon;

  @JsonProperty("penalty_missed")
  private Integer penaltyMissed;

  @JsonProperty("tackles_extended")
  private TacklesExtended tacklesExtended;

  @JsonProperty("conceded_goals")
  private Integer concededGoals;

  @JsonProperty("conceded_goals_extended")
  private ConcededGoalsExtended concededGoalsExtended;

  @JsonProperty("caught_ball")
  private Integer caughtBall;

  @JsonProperty("saves_extended")
  private SavesExtended savesExtended;

}