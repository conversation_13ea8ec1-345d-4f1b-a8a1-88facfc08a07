package com.fansunited.automation.model.clientapi.features.response.managelists;

import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.EntityTypes;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Entity {
  @JsonProperty("entity_type")
  private EntityTypes entityType;

  @JsonProperty("entity_id")
  private String entityId;
}
