package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Players implements Serializable {

  private int total;

  @JsonProperty("min_goalkeepers")
  private int minGoalkeepers;

  @JsonProperty("max_goalkeepers")
  private int maxGoalkeepers;

  @JsonProperty("min_defenders")
  private int minDefenders;

  @JsonProperty("max_defenders")
  private int maxDefenders;

  @JsonProperty("min_midfielders")
  private int minMidfielders;

  @JsonProperty("max_midfielders")
  private int maxMidfielders;

  @JsonProperty("min_strikers")
  private int minStrikers;

  @JsonProperty("max_strikers")
  private int maxStrikers;
}
