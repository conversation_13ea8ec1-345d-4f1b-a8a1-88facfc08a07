package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TacklesExtended {

    @JsonProperty("1_tackles")
    private int oneTackle;
    @JsonProperty("2_tackles")
    private int twoTackles;
    @JsonProperty("3_tackles")
    private int threeTackles;
    @JsonProperty("4_tackles")
    private int fourTackles;
    @JsonProperty("5_tackles_or_more")
    private int fiveTacklesOrMore;
}
