package com.fansunited.automation.model.clientapi;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateStaffMember {

  @JsonIgnore
  private String email;
  @JsonIgnore
  private boolean ignoreEmail;

  @JsonIgnore
  private String name;
  @JsonIgnore
  private boolean ignoreName;
  @JsonIgnore
  private String pass;
  @JsonIgnore
  private boolean ignorePass;

  private List<String> roles;

  @JsonAnyGetter
  public Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (!ignoreEmail) {
      additionalProperties.put("email", email);
    }
    if (!ignoreName) {
      additionalProperties.put("name", name);
    }
    if (!ignorePass) {
      additionalProperties.put("pass", pass);
    }
    return additionalProperties;
  }
}
