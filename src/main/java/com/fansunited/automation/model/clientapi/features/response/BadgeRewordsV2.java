package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class BadgeRewordsV2 {
    private List<Badge> general;
    private List<Badge> predictor;
    @JsonProperty("top_x")
    private List<TopXBadge> topX;
    @JsonProperty("match_quiz")
    private List<Badge> matchQuiz;
    @JsonProperty("classic_quizzes")
    private List<Badge> classicQuizzes;
    private List<Badge> discussions;
    private List<Badge> eitherOr;
    private List<Badge> football;
    private List<Badge> games;
    private List<PredictorBadge> predictorV2;
}
