package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConcededGoalsExtended {

    @JsonProperty("1_goals")
    private int oneGoal;
    @JsonProperty("2_goals")
    private int twoGoals;
    @JsonProperty("3_goals")
    private int threeGoals;
    @JsonProperty("4_goals")
    private int fourGoals;
    @JsonProperty("5_goals_or_more")
    private int fiveGoalsOrMore;
}
