package com.fansunited.automation.model.clientapi.features.response.managelists;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.SortBy;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Sort {
  @JsonProperty("sort_order")
  private ApiConstants.SortOrder sortOrder;

  @JsonProperty("sort_by")
  private SortBy sortBy;
}
