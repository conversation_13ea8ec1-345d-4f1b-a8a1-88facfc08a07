package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MatchQuizFeature {
  private List<PredictionMarket> defaultMarkets;
  private List<String> competitionsWhitelist;
  private List<AssetsURL> assets;
  private boolean enabled;
  private String clientId;
  private ApiConstants.AuthRequirement authRequirement;
}
