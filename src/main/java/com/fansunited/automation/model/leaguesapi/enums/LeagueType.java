package com.fansunited.automation.model.leaguesapi.enums;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LeagueType {
  PRIVATE("PRIVATE"),
  ONE_VS_ONE("ONE_VS_ONE"),
  INVALID_EMPTY(""),
  INVALID_NULL(null);

  private final String value;

  public static LeagueType getRandomValidLeagueType() {
    var leagueTypeList =
        Arrays.stream(values())
            .filter(leagueType -> !leagueType.name().startsWith("INVALID"))
            .toList();
    var random = new Random();
    return leagueTypeList.get(random.nextInt(leagueTypeList.size()));
  }

  static Stream<ArrayList<String>> provideData() {
    return Stream.of(null, new ArrayList<>());
  }
}
