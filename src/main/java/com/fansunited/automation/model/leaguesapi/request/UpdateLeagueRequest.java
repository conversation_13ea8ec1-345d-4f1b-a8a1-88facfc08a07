package com.fansunited.automation.model.leaguesapi.request;

import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fansunited.automation.model.leaguesapi.response.League;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UpdateLeagueRequest {

  private String id;
  private String name;
  private List<String> administrators;
  private List<String> invites;
  private List<String> banned;
  private List<String> members;
  private String description;
  private String invitationCode;
  private List<String> pinnedPosts;
  private String scoringStartsAt;
  private String templateId;
  private LeagueType type;
  private boolean usersCanInviteUsers;

  public void setPropertiesByLeagueRequest(League league) {
    id = league.getId();
    type = league.getType();
    usersCanInviteUsers = league.isUsersCanInviteUsers();

    if (league.getName() != null) {
      this.name = league.getName();
    }
    if (league.getAdministrators() != null) {
      this.administrators = league.getAdministrators();
    }
    if (league.getInvites() != null) {
      this.invites = league.getInvites();
    }
    if (league.getBanned() != null) {
      this.banned = league.getBanned();
    }
    if (league.getMembers() != null) {
      this.members = league.getMembers();
    }
    if (league.getDescription() != null) {
      this.description = league.getDescription();
    }
    if (league.getInvitationCode() != null) {
      this.invitationCode = league.getInvitationCode();
    }
    if (league.getPinnedPosts() != null) {
      this.pinnedPosts = league.getPinnedPosts();
    }
    if (league.getScoringStartsAt() != null) {
      this.scoringStartsAt = league.getScoringStartsAt();
    }
    if (league.getTemplateId() != null) {
      this.templateId = league.getTemplateId();
    }
  }
}
