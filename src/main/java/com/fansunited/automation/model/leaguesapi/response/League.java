package com.fansunited.automation.model.leaguesapi.response;

import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class League {
    private String id;
    private String name;
    private LeagueType type;
    private List<String> members;
    private List<String> administrators;
    private List<String> banned;
    private List<String> invites;
    private String description;
    @JsonProperty("users_can_invite_users")
    private boolean usersCanInviteUsers;
    @JsonProperty("invitation_code")
    private String invitationCode;
    @JsonProperty("template_id")
    private String templateId;
    @JsonProperty("pinned_posts")
    private List<String> pinnedPosts;
    @JsonProperty("scoring_starts_at")
    private String scoringStartsAt;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("past_templates")
    private List<String> pastTemplates;
    @JsonProperty("members_count")
    private int membersCount;
}
