package com.fansunited.automation.model.leaguesapi.request;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateLeagueRequest {

  private String name;
  private List<String> administrators;
  private List<String> invites;
  private List<String> banned;
  private List<String> members;
  private String description;
  private String invitationCode;
  private String pinnedPosts;
  private String scoringStartsAt;
  private String templateId;
  private String type;
  private boolean usersCanInviteUsers;
  private String id;
}