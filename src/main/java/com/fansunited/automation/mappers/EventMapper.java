package com.fansunited.automation.mappers;

import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;

import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.bq.events.SampleEvent;
import com.fansunited.automation.helpers.bq.excluded.SimpleExcluded;
import com.fansunited.automation.helpers.bq.profile.Profile;
import com.fansunited.automation.helpers.bq.rank.RankItem;
import com.fansunited.automation.helpers.bq.template.SimpleTemplate;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.github.javafaker.Faker;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

public class EventMapper {
  public static final String REGISTRATION_CONTEXT = "REGISTRATION";
  public static final String ACTIVITY_REGISTRATION = "REGISTRATION";
  public static final String ACTIVITY_PREDICTION_MADE = "PREDICTION_MADE";
  public static final String CREATE_ACTION = "CREATE";
  private static final String CATEGORY_USERS = "USERS";
  public static final String PROPERTY_PROFILE_ID = "profile_id";
  public static final String PROPERTY_EXCLUDED_PROFILE_IDS = "excluded_profile_ids";
  public static final String PROPERTY_ID = "id";
  public static final String PROFILE_TABLE = "profile";
  public static final String TEMPLATE_TABLE = "template";
  public static final String EXCLUDED_TABLE = "excluded";
  public static final String EVENT_TABLE = "event";
  public static final String CLIENT_TEST = "client_test";
  private static final String UPDATE_ACTION = "UPDATE";
  private static final String COUNTRY_CONTEXT = "COUNTRY";
  private static final String PROPERTY_COUNTRY_ID = "country_id";
  public static final String INTERESTS_CATEGORY = "INTERESTS";
  public static final String FOLLOW_ACTION = "FOLLOW";
  public static final String UNFOLLOW_ACTION = "UNFOLLOW";
  public static final String CORRECT_PREDICTION = "CORRECT_PREDICTION";
  public static final String FAVOURITE_ACTION = "FAVOURITE";
  public static final String UNFAVOURITE_ACTION = "UNFAVOURITE";
  private static final String FOLLOWERS_CATEGORY = "FOLLOWERS";
  public static final String CONTEXT_PROFILE = "profile";
  private static final String PROPERTY_BIRTHDATE = "birthdate";
  private static final String PROPERTY_GENDER = "gender";
  private static final String GENDER_CONTEXT = "gender";
  private static final String BIRTHDATE_CONTEXT = "birthdate";
  private static final String PROPERTY_AVATAR = "avatar";
  private static final String AVATAR_CONTEXT = "avatar";

  private static final String PREDICTOR_CATEGORY = "predictor";
  private static final String PREDICTION_API_SOURCE = "prediction-api";
  private static final String PREDICTOR_CONTEXT = "predictor";
  private static final String PROPERTY_PREDICTION_ID = "prediction_id";
  private static final String CORRECT_PREDICTION_ACTION = "correct_prediction";
  private static final String RESETTLED_ACTION = "RESETTLED_ACTION";
  private static final String PREDICTION_RESOLVER_SOURCE = "prediction-resolver";
  public static final String RANK_TABLE = "rank";
  private static final String FOOTBALL_SOURCE = "football";
  private static final String TEAM_TYPE = "team";
  private static final String PLAYER_TYPE = "player";

  private BQMapperImpl bqMapper = new BQMapperImpl();

  public SampleEvent profileToEvent(LocalDateTime eventTimestamp,
      RegistrationProfile profile, int points) {

    return SampleEvent.builder()
        .eventId(UUID.randomUUID().toString())
        .timestamp(eventTimestamp != null ? eventTimestamp.format(
            DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)) :
            LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)))
        .profileId(profile.getId())
        .category(CATEGORY_USERS)
        .context(REGISTRATION_CONTEXT)
        .action(CREATE_ACTION)
        .property(PROPERTY_PROFILE_ID)
        .value(profile.getId())
        .label(profile.getName())
        .extra(profile.getGender())
        .activity(ACTIVITY_REGISTRATION)
        .points((long) points)
        .build();
  }

  public SampleEvent profileToEvent(RegistrationProfile profile, int points) {

    return SampleEvent.builder()
        .eventId(UUID.randomUUID().toString())
        .timestamp(profile.getUpdatedAt())
        .profileId(profile.getId())
        .category(CATEGORY_USERS)
        .context(REGISTRATION_CONTEXT)
        .action(CREATE_ACTION)
        .property(PROPERTY_PROFILE_ID)
        .value(profile.getId())
        .label(profile.getName())
        .extra(profile.getGender())
        .activity(ACTIVITY_REGISTRATION)
        .points((long) points)
        .build();
  }

  public Profile profileToProfileEvent(LocalDateTime eventTimestamp,
      RegistrationProfile registrationProfile, LocalDateTime updatedAt) {

    var profile = bqMapper.profileToProfileEvent(registrationProfile);

    if (registrationProfile == null) {
      String currentTime = Instant.now()
          .atZone(ZoneOffset.UTC)
          .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"));
      profile.setCreatedAt(currentTime);
    } else {
      String eventTime = eventTimestamp.atZone(ZoneOffset.UTC)
          .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"));
      profile.setCreatedAt(eventTime);
    }

    if (updatedAt == null) {
      String currentTime = Instant.now()
          .atZone(ZoneOffset.UTC)
          .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"));
      profile.setUpdatedAt(currentTime);
    } else {
      String eventTime = updatedAt.atZone(ZoneOffset.UTC)
          .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"));
      profile.setUpdatedAt(eventTime);
    }

    return profile;
  }

  public Profile profileToProfileEvent(RegistrationProfile registrationProfile) {

    var profile = bqMapper.profileToProfileEvent(registrationProfile);

    profile.setCreatedAt(registrationProfile.getUpdatedAt());
    profile.setUpdatedAt(registrationProfile.getUpdatedAt());

    return profile;
  }

  public SampleEvent profileDataInterestToEvent(LocalDateTime eventTimestamp, String profileId,
      ProfileData.Interest profileDataInterest, boolean isFollow, int points) {
    return SampleEvent.builder()
        .eventId(UUID.randomUUID().toString())
        .timestamp(eventTimestamp != null ? eventTimestamp.atZone(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"))
            : LocalDateTime.now().toString())
        .profileId(profileId)
        .category(INTERESTS_CATEGORY)
        .action(isFollow ? FOLLOW_ACTION : UNFOLLOW_ACTION)
        .property(profileDataInterest.getType() + "_id")
        .value(profileDataInterest.getId())
        .extra(profileDataInterest.getFavourite() + "")
        .context(profileDataInterest.getSource())
        .points((long) points)
        .source("profile-api")
        .weight(0L)
        .build();
  }

  public Profile profileDataInterestToProfileEvent(LocalDateTime eventTimestamp, String profileId,
      ProfileData.Interest profileDataInterest) {
    return Profile.builder()
        .id(profileId)
        .interests(List.of(bqMapper.profileDataInterestToEvent(profileDataInterest)))
        .createdAt(eventTimestamp != null ? eventTimestamp.atZone(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"))
            : LocalDateTime.now().toString())
        .updatedAt(eventTimestamp != null ? eventTimestamp.atZone(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'"))
            : LocalDateTime.now().toString())
        .build();
  }

  public SampleEvent mapCorrectPredictionToEvent(LocalDateTime eventTimestamp,
      String profileId, String predictionId, PredictionType gameType,
      PredictionMarket predictionMarket, int points) {

    return SampleEvent.builder()
        .eventId(UUID.randomUUID().toString())
        .timestamp(eventTimestamp.atZone(ZoneOffset.UTC)
        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS 'UTC'")))
        .profileId(profileId)
        .source(PREDICTION_RESOLVER_SOURCE)
        .category(PREDICTOR_CATEGORY)
        .context(gameType.toString())
        .property(PROPERTY_PREDICTION_ID)
        .value(predictionId)
        .extra(predictionMarket.toString())
        .label(null)
        .points((long) points)
        .action(CORRECT_PREDICTION)
        .activity(CORRECT_PREDICTION)
        .weight(0L)
        .build();
  }

  public SampleEvent predictionMade(PredictionInstance predictionInstance) {
    return SampleEvent.builder()
        .eventId(UUID.randomUUID().toString())
        .timestamp(predictionInstance.getCreatedAt())
        .profileId(predictionInstance.getUserId())
        .source(PREDICTION_API_SOURCE)
        .category(PREDICTOR_CATEGORY)
        .context(predictionInstance.getGameType().getValue())
        .property(PROPERTY_PREDICTION_ID)
        .value(predictionInstance.getId())
        .extra(predictionInstance.getGameType().toString())
        .label(null)
        .points((long) predictionInstance.getPoints())
        .action(ACTIVITY_PREDICTION_MADE)
        .activity(ACTIVITY_PREDICTION_MADE)
        .weight(0L)
        .build();
  }

  public SampleEvent predictionMadeForMarket(PredictionInstance predictionInstance, String market) {
    return SampleEvent.builder()
        .eventId(UUID.randomUUID().toString())
        .timestamp(predictionInstance.getCreatedAt())
        .profileId(predictionInstance.getUserId())
        .source(PREDICTION_API_SOURCE)
        .category(PREDICTOR_CATEGORY)
        .context(predictionInstance.getGameType().getValue())
        .property(PROPERTY_PREDICTION_ID)
        .value(predictionInstance.getId())
        .extra(market)
        .label(null)
        .points((long) predictionInstance.getPoints())
        .action(ACTIVITY_PREDICTION_MADE)
        .activity(ACTIVITY_PREDICTION_MADE)
        .weight(0L)
        .build();
  }

  public RankItem rankItem(LocalDateTime eventTimestamp, String profileId,
      PredictionMarket predictionMarket,
      GameType predictionType, int points, String gameInstanceId,
      Match match, Integer goldenGoalMinute, String firstGoalMinute, String predictionLastUpdate) {
    var ret = RankItem.builder()
        .timestamp(eventTimestamp.toString())
        .profileId(profileId)
        .market(predictionMarket.toString())
        .gameType(predictionType.toString())
        .points(String.valueOf(points))
        .gameId(gameInstanceId)
        .matchId(match.getId())
        .homeTeamId(match.getHomeTeam().getId())
        .awayTeamId(match.getAwayTeam().getId())
        .competitionId(match.getContext().getCompetition().getId())
        .goldenGoalMinute(goldenGoalMinute)
        .firstGoalMinute(firstGoalMinute)
        .predictionLastUpdated(predictionLastUpdate)
        .build();
    if (match.getFinishedAt() != null) {
      ret.setDateFinished(match.getFinishedAt().toString());
    }
    return  ret;
  }

  public SimpleTemplate templateItem(List<String> excludedProfileIds, String type){
    var dateTriplet = DateTripletHelper.getInstance().getNext();

    return SimpleTemplate.builder()
        .fromDate(dateTriplet.getToLocalDateTime().toString())
        .toDate(dateTriplet.getFromLocalDateTime().toString())
        .type(type)
        .name(new Faker().company().name())
        .excludedProfileIds(excludedProfileIds.get(0))
        .build();
  }

  public SimpleExcluded excludedItem(String excludedProfileId){
    return SimpleExcluded.builder().excludedProfileIds(excludedProfileId)
        .gameId(UUID.randomUUID().toString()).build();
  }

  public Match hibernateMatchToAutomationMatch(com.fansunited.automation.core.resolver.hibernate.Match hibernateMatch) {
    var ret = new Match();
    ret.setId(hibernateMatch.getId());

    var homeTeam = new com.fansunited.automation.model.footballapi.teams.Team();
    homeTeam.setId(hibernateMatch.getHomeTeam().getId());
    ret.setHomeTeam(homeTeam);

    var awayTeam = new com.fansunited.automation.model.footballapi.teams.Team();
    awayTeam.setId(hibernateMatch.getAwayTeam().getId());
    ret.setAwayTeam(awayTeam);

    var context = new com.fansunited.automation.model.footballapi.matches.Context();
    var competition = new com.fansunited.automation.model.footballapi.matches.Competition();
    competition.setId(hibernateMatch.getCompetition().getId());
    context.setCompetition(competition);
    ret.setContext(context);

    ret.setFinishedAt(hibernateMatch.getFinishedAt());

    return ret;
  }
}
