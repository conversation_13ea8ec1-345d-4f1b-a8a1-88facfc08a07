package com.fansunited.automation.mappers;

import com.fansunited.automation.helpers.bq.profile.Interest;
import com.fansunited.automation.helpers.bq.profile.Profile;
import com.fansunited.automation.model.minigamesapi.classicquiz.ClassicQuizOptionsDto;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import lombok.Generated;
import org.mapstruct.Mapper;

/**
 * Mapper for {@link ClassicQuizOptionsDto}.
 */
@Mapper(componentModel = "default")
@Generated
public interface BQMapper {
  Profile profileToProfileEvent(RegistrationProfile profile);
  Interest profileDataInterestToEvent(ProfileData.Interest interest);
}
