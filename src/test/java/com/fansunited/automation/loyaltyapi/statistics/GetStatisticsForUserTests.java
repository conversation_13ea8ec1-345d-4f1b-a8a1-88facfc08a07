package com.fansunited.automation.loyaltyapi.statistics;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.OVERALL_PERCENT;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.POINTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.SUCCESS_RATES;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.TestGroups.STAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.setPartiallyCorrectPredictionsForAllMarkets;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToObject;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsByUserIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.GeneralActivityId;
import com.fansunited.automation.core.apis.loyaltyapi.enums.Tier;
import com.fansunited.automation.core.apis.mockapi.MockEventEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.MatchStatsGenerator;
import com.fansunited.automation.core.resolver.MatchTimelineGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.enums.TimelineEventType;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.clientapi.features.response.FeaturesResponse;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultStatus;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import com.fansunited.automation.validators.RedisValidator;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Loyalty Api - GET /v1/users/{profile_id}/statistics endpoint happy path tests")
public class GetStatisticsForUserTests extends AuthBase {

  @ParameterizedTest(name = "Verify getting statistics for user id and validate they are placed in the respective tier based on earned points. Tier: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = Tier.class)
  public void getStatisticsForUser(Tier tier) throws Exception {
    var features =
        FeaturesEndpoint.getClientFeatures(CLIENT_AUTOMATION_ID).as(FeaturesResponse.class);

    var registrationPoints = features.getData()
        .getLoyalty()
        .getRewards()
        .getPoints()
        .getGeneral()
        .stream()
        .filter(point -> point.getId().equalsIgnoreCase(
            GeneralActivityId.REGISTRATION.getValue())).findFirst().get().getPoints();

    var points = features.getData()
        .getLoyalty()
        .getRewards()
        .getTiers()
        .stream()
        .filter(tierReward -> tierReward.getId().equalsIgnoreCase(tier.name()))
        .findFirst().get().getPoints();

    if (tier != Tier.BRONZE) { // User starts from bronze tier upon registration
      var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));
      MockEventEndpoint.generateRankingEvent(
          LocalDateTime.now().minusDays(5),
          getCurrentTestUser().getUid(),
          PredictionMarket.FT_1X2,
          PredictionType.SINGLE,
          points,
          null,
          ResultOutcome.CORRECT,
          ResultStatus.SETTLED,
          MatchByIdEndpoint.getMatchDtoById("fb:m:158469"),
          1,
          "1",
          predictionLastUpdate);
    }

    if(Helper.generateRandomNumber(1, 2) % 2 == 0){
      deleteUser(getCurrentTestUser().getUid());
    }

    BigQueryHelper.waitForEventsToBeSaved(60);

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateStatisticsResponse(response, getCurrentTestUser().getUid(),
        points + registrationPoints, tier);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users/{userId}/statistics response returned by the server is cached for 1h")
  public void verifyStatisticsForUserResponseIsCached() throws HttpException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @DisplayName("Verify prediction for MATCH_QUIZ has success rate partially correct")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  // todo: needs to be rewritten to store data in sql mvs (for the success rate part only)
  @Disabled("FZ-2878")
  public void validateSuccessRateForUser()
      throws HttpException, InterruptedException {

    var markets = PredictionMarket.getValidMarkets();
    var match = MatchGenerator.generateMatch();
    match.setTimeline(new ArrayList<>());
    match.setStats(List.of(
        MatchStatsGenerator.generateEmptyMatchStats(match.getId(), false),
        MatchStatsGenerator.generateEmptyMatchStats(match.getId(), true)
    ));
    match.getStats().get(0).getStatistics().setCorners((byte) 6);
    match.getStats().get(1).getStatistics().setCorners((byte) 2);
    match.getStats().get(0).getStatistics().setRed_cards((byte) 1);
    match.getStats().get(1).getStatistics().setRed_cards((byte) 0);
    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 10, true, (byte) 1));
    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 20, true, (byte) 3));
    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 51, true, (byte) 4));
    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(),
            TimelineEventType.YELLOW_CARD,
            VALID_PLAYER_ID, (short) 55, true, (byte) 5));
    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.RED_CARD,
            VALID_PLAYER_ID, (short) 80, true, (byte) 6));
    match.setGoalsHalfTimeHome((byte) 3);
    match.setGoalsHalfTimeAway((byte) 1);
    match.setGoalsFullTimeHome((byte) 4);
    match.setGoalsFullTimeAway((byte) 1);

    Resolver.openMatchForPredictions(match);

    var gameInstance = GamesEndpoint.createMatchQuizGameForMarkets(match.getId(), GameStatus.OPEN,
            markets, match.getKickoffAt().atZone(ZoneId.of("UTC")))
        .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setPartiallyCorrectPredictionsForAllMarkets(markets, match, predictionFixtures);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);



    var otherMatch = MatchGenerator.generateMatch();
    otherMatch.setTimeline(new ArrayList<>());
    otherMatch.setStats(List.of(
        MatchStatsGenerator.generateEmptyMatchStats(otherMatch.getId(), false),
        MatchStatsGenerator.generateEmptyMatchStats(otherMatch.getId(), true)
    ));
    otherMatch.getStats().get(0).getStatistics().setCorners((byte) 6);
    otherMatch.getStats().get(1).getStatistics().setCorners((byte) 2);
    otherMatch.getStats().get(0).getStatistics().setRed_cards((byte) 1);
    otherMatch.getStats().get(1).getStatistics().setRed_cards((byte) 0);
    otherMatch.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(otherMatch.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 10, true, (byte) 1));
    otherMatch.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(otherMatch.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 20, true, (byte) 3));
    otherMatch.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(otherMatch.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 51, true, (byte) 4));
    otherMatch.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(otherMatch.getId(),
            TimelineEventType.YELLOW_CARD,
            VALID_PLAYER_ID, (short) 55, true, (byte) 5));
    otherMatch.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(otherMatch.getId(), TimelineEventType.RED_CARD,
            VALID_PLAYER_ID, (short) 80, true, (byte) 6));
    otherMatch.setGoalsHalfTimeHome((byte) 3);
    otherMatch.setGoalsHalfTimeAway((byte) 1);
    otherMatch.setGoalsFullTimeHome((byte) 4);
    otherMatch.setGoalsFullTimeAway((byte) 1);

    Resolver.openMatchForPredictions(otherMatch);

    var otherGameInstance = GamesEndpoint.createMatchQuizGameForMarkets(otherMatch.getId(), GameStatus.OPEN,
            markets, otherMatch.getKickoffAt().atZone(ZoneId.of("UTC")))
        .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(otherGameInstance.getId());

    var predictionFixtures1 = new ArrayList<PredictionFixture>();

    setPartiallyCorrectPredictionsForAllMarkets(markets, otherMatch, predictionFixtures1);


    var createNewPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(otherGameInstance.getId()).fixtures(predictionFixtures1).build();

    PredictionsEndpoint.createPrediction(createNewPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);


    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Thread.sleep(1000);
    Resolver.updateMatchToBeFinishedInThePast(otherMatch.getId(), 31);

    BigQueryHelper.waitForEventsToBeSaved(140);

    Resolver.resolve();

    BigQueryHelper.waitForEventsToBeSaved(60);

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + POINTS_PROP, notNullValue())
        .body(("data." + "success_rates" + "." + OVERALL_PERCENT) , equalToObject("57") );

  }

  @Test
  @DisplayName("Verify loyalty api return 200 when the profile has no activities")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  public void validateSuccessRateForUserWithoutEvents()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            createUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(("data." + SUCCESS_RATES + "." + OVERALL_PERCENT), anyOf(equalTo(0), nullValue()));
  }
}
