package com.fansunited.automation.loyaltyapi.statistics;

import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsByUserIdEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - GET /v1/users/{profile_id}/statistics endpoint validation tests")
public class GetStatisticsForUserValidationTests extends AuthBase {

  @ParameterizedTest(name = "Verify statistics for user cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void getStatisticsForUserWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            getCurrentTestUser().getUid(),
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting statistics for user with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getStatisticsForUserWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(clientId, getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Disabled("WIP - FZ-3396")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-3396")})
  @DisplayName("Verify API returns NOT_FOUND when getting statistics for user with invalid user id")
  public void getStatisticsForUserWithInvalidUserId() throws HttpException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            "asxxvf3vaJEUr1sdr3q");

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting statistics for user with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getStatisticsForUserWithNonSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            getCurrentTestUser().getUid(),
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify statistics for user are fetched in JSON format if content type is NOT specified")
  public void getStatisticsForUserWithoutSpecifyingContentType() throws HttpException {

    var response =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID,
            getCurrentTestUser().getUid(),
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
