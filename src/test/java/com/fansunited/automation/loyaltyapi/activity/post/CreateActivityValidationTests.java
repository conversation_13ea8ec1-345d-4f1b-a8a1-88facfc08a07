package com.fansunited.automation.loyaltyapi.activity.post;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_TEAM_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.STAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProviderUser;
import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.loyaltyapi.ActivitiesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.ActivityActionType;
import com.fansunited.automation.core.base.loyaltyapi.ActivityBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.loyaltyapi.activity.request.ActivityRequest;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.loyaltyapi.activity.request.Content;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIfEnvironmentVariable;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - POST /v1/activity endpoint validation tests")
public class CreateActivityValidationTests extends ActivityBaseTest {

  @ParameterizedTest(name = "Verify activity cannot be created with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void createActivityWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(ActivityActionType.LIKE.getValue());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setIgnoreCampaign(true);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify activity cannot be created with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProviderUser.class)
  public void createActivityWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType("article");
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setIgnoreCampaign(true);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(argumentsHolder.getJwtToken(), body, true, null, null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify activity cannot be created without JWT token")
  public void createActivityWithoutJwtToken() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType("article");
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setIgnoreCampaign(true);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(null, body, false, null, null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }
  @Disabled("WIP FZ-1639")
  @DisabledIfEnvironmentVariable(named = "CLOUD_PROJECT", matches = "fans-united-stage")
  @Test
  @Tags({@Tag(STAGE), @Tag(REGRESSION)})
  @DisplayName("Verify clients cannot create activities")
  public void createActivityByClient() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(ActivityActionType.LIKE.getValue());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(null, body, true, ADMIN_USER,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify user from one project cannot create activity in another project")
  public void createActivityInAnotherProject() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(ActivityActionType.LIKE.getValue());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_PRODUCTION_TESTING_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify social activity: {arguments} cannot be created with empty value for content id field")
  @MethodSource("socialActivities")
  @Tags({@Tag(REGRESSION)})
  public void createSocialActivityWithEmptyContentId(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId("");
    content.setType(UUID.randomUUID().toString());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify social activity: {arguments} cannot be created with null value for content id field")
  @MethodSource("socialActivities")
  @Tags({@Tag(REGRESSION)})
  public void createSocialActivityWithNullContentId(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(null);
    content.setType(UUID.randomUUID().toString());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify social activity: {arguments} cannot be created with empty value for content type field")
  @MethodSource("socialActivities")
  @Tags({@Tag(REGRESSION)})
  public void createSocialActivityWithEmptyContentType(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("");
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify social activity: {arguments} cannot be created with null value for content type field")
  @MethodSource("socialActivities")
  @Tags({@Tag(REGRESSION)})
  public void createSocialActivityWithNullContentType(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType(null);
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify social activity: {arguments} cannot be created with null value for content object")
  @MethodSource("socialActivities")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1641")})
  public void createSocialActivityWithNullValueForContentObject(
      ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var context = new Context();
    context.setContent(null);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify activity cannot be created with invalid activity action type. Activity action type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createActivityWithInvalidActivityAction(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("Test article label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);
    response
        .then()
        .log().body();

    ErrorValidator.validateErrorResponse(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @ParameterizedTest(name = "Verify activity can be created with empty value for content label field. Activity action type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createActivityWithEmptyLabel(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify activity can be created with null value for content label field. Activity action type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createActivityWithNullLabel(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel(null);
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify activity cannot be created with null value for campaign id. Activity action type: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createActivityWithNullCampaignId(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("Test article label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(null);
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify activity cannot be created with empty value for campaign id. Activity action type: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createActivityWithEmptyCampaignId(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("Test article label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId("");
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @Test()
  @DisplayName("Verify activity can be created with content object set to null for non social activity types.")
  @Tag(REGRESSION)
  public void createNonSocialActivityWithoutContentObject()
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var context = new Context();
    context.setContent(null);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel(null);
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action("add_avatar")
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify activity cannot be created with empty array value for tags field for activity: {arguments}")
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  @Tag(REGRESSION)
  public void createSocialActivityWithEmptyTags(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType(UUID.randomUUID().toString());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(List.of());

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify activity cannot be created with null value for tags field for activity: {arguments}")
  @EnumSource(value = ActivityActionType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  @Tag(REGRESSION)
  public void createActivityWithNullValueForTags(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType(UUID.randomUUID().toString());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var context = new Context();
    context.setContent(content);
    context.setTags(null);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify activity cannot be created with invalid tag team id for source football")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_TEAM_ID)
  @NullAndEmptySource
  public void createActivityWithInvalidTagTeamId(String teamId)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(teamId).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @ParameterizedTest(name = "Verify activity cannot be created with invalid tag player id for source football")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_PLAYER_ID)
  @NullAndEmptySource
  public void createActivityWithInvalidTagPlayerId(String playerId)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.PLAYER.getType())
        .id(playerId).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, List.of(SC_NOT_FOUND, SC_BAD_REQUEST));
  }

  @ParameterizedTest(name = "Verify activity cannot be created with invalid tag competition id for source football")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_PLAYER_ID)
  @NullAndEmptySource
  public void createActivityWithInvalidTagCompId(String competitionId)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.PLAYER.getType())
        .id(INVALID_PLAYER_ID).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, List.of(SC_NOT_FOUND, SC_BAD_REQUEST));
  }

  @ParameterizedTest(name = "Verify activity cannot be created with invalid tag sport id for source sport")
  @Tag(REGRESSION)
  @ValueSource(strings = "Skate board")
  @NullAndEmptySource
  public void createActivityWithInvalidTagSportId(String sportId)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.SPORT.getSource())
        .type(ApiConstants.ProfileApi.Interest.SPORT.getSource())
        .id(sportId).build());

    var content = new Content();
    content.setId(UUID.randomUUID().toString());
    content.setType("article");
    content.setLabel("label");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);

    var campaign = new Campaign();
    campaign.setId(UUID.randomUUID().toString());
    campaign.setLabel("Carlsberg 2022");
    context.setCampaign(campaign);

    var body = ActivityRequest.builder()
        .action(ActivityActionType.LIKE.getValue())
        .context(context).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @ParameterizedTest(name = "Verify activity cannot be created with null value for context object. Activity action type: {arguments}")
  @Tag(REGRESSION)
  @MethodSource("socialActivities")
  public void createActivityWithNullContextObject(ActivityActionType activityActionType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var body = ActivityRequest.builder()
        .action(activityActionType.getValue())
        .context(null).build();

    var response =
        ActivitiesEndpoint.createActivity(body, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  static Stream<Arguments> socialActivities() {
    var arguments = new ArrayList<Arguments>();
    ActivityActionType.getOwnSocialActivityActions()
        .forEach(activityActionType -> arguments.add(Arguments.of(activityActionType)));
    return arguments.stream();
  }

  static Stream<Arguments> nonSocialActivities() {
    var arguments = new ArrayList<Arguments>();
    ActivityActionType.getNonSocialActivityActions()
        .forEach(activityActionType -> arguments.add(Arguments.of(activityActionType)));

    if (arguments.isEmpty()) {
      throw new IllegalStateException("No non-social activity actions found!");
    }
    return arguments.stream();
  }
}
