package com.fansunited.automation.loyaltyapi.activity.get;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.TOTAL_ITEMS_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.LoyaltyErrorCode.INVALID_LIMIT_ERROR_MESSAGE;
import static com.fansunited.automation.constants.JsonSchemasPath.LoyaltyApi.Endpoints.Activity.GET_OWN_ACTIVITIES_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_PROFILE_ID;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItems;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.loyaltyapi.ActivitiesByUserIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.ActivitiesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.ActivityActionType;
import com.fansunited.automation.core.base.loyaltyapi.ActivityBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.ArrayList;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - GET /v1/users/{userId}/activities endpoint validation tests")
public class GetActivitiesForUserValidationTests extends ActivityBaseTest {

  @ParameterizedTest(name = "Verify activities for user cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getActivitiesForUserWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), null,
            CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting activities for user with action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getActivitiesForUserWithInvalidClientId(String clientId)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), null,
            clientId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
 @Tag(REGRESSION)
  @DisplayName("Verify API returns NOT_FOUND when getting activities for user with invalid id")
  public void getActivitiesForUserWithInvalidId()
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(INVALID_PROFILE_ID, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting activities for user with invalid action filter")
  public void getOwnActivitiesWithInvalidActionFilter() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), "INVALID");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns all activities when getting activities for user with empty action filter")
  public void getActivitiesForUserWithEmptyActionFilter() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var activityActionTypeList = ActivityActionType.getOwnSocialActivityActions();
    var activityIdList = new ArrayList<String>();
    activityActionTypeList.forEach(activityActionType ->
        {
          try {
            var activityId = ActivitiesEndpoint.createActivity(user.get().getEmail(),
                    activityActionType.getValue())
                .then()
                .statusCode(HttpStatus.SC_OK)
                .extract()
                .body()
                .jsonPath()
                .getString(ID_PROP);
            activityIdList.add(activityId);
          } catch (HttpException e) {
            throw new RuntimeException("Could not create activity...");
          }
        }
    );
    BigQueryHelper.waitForEventsToBeSaved(60); //Waiting for the Newly created Activities to be saved in BigQuery

    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), "");
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_OWN_ACTIVITIES_SCHEMA))
        .body("meta.pagination." + TOTAL_ITEMS_PROP, equalTo(activityIdList.size()))
        .body("data." + ID_PROP, hasItems(activityIdList.toArray()));
  }

  @Disabled("WIP - FZ-1640")
  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting activities for user with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-1640")})
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getActivitiesForUserWithNonSupportedContentType(ContentType contentType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), null,
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify activities for user are fetched in JSON format if content type is NOT specified")
  public void getActivitiesForUserWithoutSpecifyingContentType() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), null,
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }

  @ParameterizedTest(name = "Verify pagination limit for get activities should be {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"> 0", "< 101"} )
  public void verifyActivitiesCanNotBeRetrievedWithInvalidLimit(String limit) throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    String limitParam = limit.substring(2);

    var response =
        ActivitiesByUserIdEndpoint.getActivitiesForUser(user.get().getUid(), null, 1, limitParam,
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(INVALID_LIMIT_ERROR_MESSAGE));
  }
}
