package com.fansunited.automation.loyaltyapi.activity.delete;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.ID_PROP;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.emptyString;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.core.apis.loyaltyapi.ActivitiesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.ActivityActionType;
import com.fansunited.automation.core.base.loyaltyapi.ActivityBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty Api - DELETE /v1/activity/{id} endpoint happy path tests")
public class DeleteActivityTests extends ActivityBaseTest {

  @Disabled("WIP FZ-1639")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE) ,@Tag(DISABLED),@Tag("FZ-1639")})
  @DisplayName("Verify deletion of activity")
  public void deleteActivity() throws HttpException, InterruptedException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var createActivityResponse =
        ActivitiesEndpoint.createActivity(user.get().getEmail(),
            ActivityActionType.LIKE.getValue());

    createActivityResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var activityId = createActivityResponse.then().extract().body().jsonPath().getString(ID_PROP);

    BigQueryHelper.waitForEventsToBeSaved(60);

    var deleteActivityResponse =
        ActivitiesEndpoint.deleteActivity(activityId, user.get().getEmail());

    currentTestResponse.set(deleteActivityResponse);

    deleteActivityResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(emptyString());

    ActivitiesEndpoint.getOwnActivities(user.get().getEmail(), ActivityActionType.LIKE.getValue())
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }
}
