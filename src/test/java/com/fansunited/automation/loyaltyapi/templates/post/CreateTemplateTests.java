package com.fansunited.automation.loyaltyapi.templates.post;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_COVER;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_MAIN;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_MOBILE;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.setUpImages;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatches;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.apis.footballapi.TopCompetitionsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroupFilter;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroups;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.LocalDate;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - POST /v1/leaderboard/templates endpoint happy path tests")
public class CreateTemplateTests extends TemplateBaseTest {

  @ParameterizedTest(name="Verify custom template creation with markets {arguments}")
  @ValueSource(strings = {"FANTASY","CUSTOM","PRIMARY"})
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyCustomTemplateCreationWithMarkets(String type) throws HttpException {

    var matches=generateMatches(2,false);
    Resolver.openMatchesForPredictions(matches);

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .competitionIds(List.of("fb:c:3"))
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .branding(
                BrandingDTO.builder()
                    .colors(
                        BrandingColorsDTO.builder()
                            .additionalColor("test")
                            .backgroundColor("")
                            .primaryColor("")
                            .contentColor("")
                            .borderColor("")
                            .secondaryColor("")
                            .build())
                    .urls(
                        BrandingUrlsDTO.builder()
                            .primaryUrl("тест")
                            .privacyPolicyUrl("тест")
                            .additionalUrl("тест")
                            .build())
                    .images(
                        BrandingImagesDTO.builder()
                            .additionalImage("тест")
                            .backgroundImage("тест")
                            .mainLogo("тест")
                            .mobileBackgroundImage("тест")
                            .build())
                    .build())
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusDays(1)))
            .markets(markets)
            .type(type)
            .labels(Fields.builder().label1("test").label2("test1_?123!?/").build())
            .customFields(Fields.builder().label2("123test").label2("tesgf!/_").build())
            .ad_content("<p>test</p>")
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with markets and period definition")
  public void verifyCustomTemplateCreationWithMarketsAndDate() throws HttpException {

    var markets =
        List.of(
            PredictionMarket.OVER_CORNERS_6_5.getValue(),
            PredictionMarket.OVER_GOALS_0_5.getValue());

    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusYears(1)))
            .markets(markets)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation without markets(null)")
  public void verifyCustomTemplateCreationWithoutMarkets() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with teams")
  public void verifyCustomTemplateCreationWithTeams() throws HttpException {

    var teamIds = TeamsEndpoint.getTeamsForCompetition(PREMIER_LEAGUE_COMP_ID);

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();

    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .teamIds(teamIds)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with teams and specific markets")
  public void verifyCustomTemplateCreationWithTeamsAndMarkets() throws HttpException {

    var markets =
        List.of(
            PredictionMarket.FT_1X2.getValue(),
            PredictionMarket.CORRECT_SCORE.getValue(),
            PredictionMarket.PLAYER_SCORE.getValue());

    var teamIds = TeamsEndpoint.getTeamsForCompetition(PREMIER_LEAGUE_COMP_ID);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();

    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .markets(markets)
            .teamIds(teamIds)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with game ids")
  public void verifyCustomTemplateCreationWithGameIds() throws HttpException {

    var gameIds = createGames(GameType.TOP_X, 4);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();

    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .gameIds(gameIds)
            .groups(
                List.of(
                    TemplateGroups.builder()
                        .groupId(new Faker().funnyName().toString())
                        .label("test")
                        .filters(
                            TemplateGroupFilter.builder()
                                .matchIds(null)
                                .fromDate(fromDateString)
                                .toDate(toDateString)
                                .build())
                        .build()))
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with team ids")
  public void verifyCustomTemplateCreationWithTeamIds() throws HttpException {

    var teamIds = TeamsEndpoint.getTeamsForCompetition(PREMIER_LEAGUE_COMP_ID).subList(0, 3);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .teamIds(teamIds)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with match ids")
  public void verifyCustomTemplateCreationWithMatchIds() throws HttpException {

    var matchIds = MatchesEndpoint.getFinishedMatchesIdList(List.of(PREMIER_LEAGUE_COMP_ID), 20);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .matchIds(matchIds)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with competition ids")
  public void verifyCustomTemplateCreationWithCompetitionIds() throws HttpException {

    var competitionIds = TopCompetitionsEndpoint.getTopCompetitionIds();
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .competitionIds(competitionIds)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with game types")
  public void verifyCustomTemplateCreationWithGameTypes() throws HttpException {

    var gameTypes = List.of(GameType.TOP_X.getValue(), GameType.MATCH_QUIZ.getValue());
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .gameTypes(gameTypes)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with teams and competitions")
  public void verifyCustomTemplateCreationWithTeamsAndCompetitions() throws HttpException {

    var teamIds = TeamsEndpoint.getTeamsForCompetition(PREMIER_LEAGUE_COMP_ID);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .competitionIds(List.of(PREMIER_LEAGUE_COMP_ID))
            .teamIds(teamIds)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with competitions and markets")
  public void verifyCustomTemplateCreationWithCompetitionsAndMarkets() throws HttpException {

    var competitionIds = TopCompetitionsEndpoint.getTopCompetitionIds();
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .competitionIds(competitionIds)
            .markets(List.of(PredictionMarket.OVER_CORNERS_13_5.getValue()))
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with competitions, markets and period definition")
  public void verifyCustomTemplateCreationWithCompetitionsAndMarketsAndDate() throws HttpException {

    var competitionIds = TopCompetitionsEndpoint.getTopCompetitionIds();
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .competitionIds(competitionIds)
            .markets(List.of(PredictionMarket.OVER_GOALS_6_5.getValue()))
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with rules and flags fields defined")
  public void verifyCustomTemplateCreationWithRulesAndFlags() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var flags = new Faker().lorem().words(Helper.generateRandomNumber(1, 3));
    flags.add(
        """
             <script>alert("123")</script>"
             "<script>alert(document.cookie)</script>"
             "<html><a>"Testing"</a></html>""");
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .rules(new Faker().regexify("[A-Za-z0-9$&+,:;=?@#|'<>.^*()%!-]{10,40}"))
            .flags(flags)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("Ticket FZ-1217")})
  @DisplayName("Verify custom template creation with description")
  public void verifyCustomTemplateCreationWithDescription() throws HttpException {

    var description = new Faker().lorem().characters(10000);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .description(description)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplates()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, hasItem(templateResponse.getId()));
  }

  // Images
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom template creation with images")
  public void verifyCustomTemplateCreationWithImages() throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var mainImageUrl = "https://example.com/imageurl";
    var coverImageUrl = "https://example.com/imagefolder/imageurl";
    var mobileImageUrl = "https://example.com/imageurl";
    var images = setUpImages(mainImageUrl, coverImageUrl, mobileImageUrl);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(fromDateString)
            .toDate(toDateString)
            .markets(markets)
            .images(images)
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    TemplatesEndpoint.getLeaderboardTemplatesByTemplateId(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, templateResponse.getId())
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + ID_PROP, equalTo(templateResponse.getId()))
        .body("data." + IMAGES_PROP + "." + IMAGES_MAIN, equalTo(mainImageUrl))
        .body("data." + IMAGES_PROP + "." + IMAGES_COVER, equalTo(coverImageUrl))
        .body("data." + IMAGES_PROP + "." + IMAGES_MOBILE, equalTo(mobileImageUrl));
  }
}
