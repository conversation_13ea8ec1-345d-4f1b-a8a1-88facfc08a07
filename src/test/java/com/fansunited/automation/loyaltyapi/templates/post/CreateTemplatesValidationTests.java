package com.fansunited.automation.loyaltyapi.templates.post;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_TEAM_ID;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.INVALID_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.setUpImages;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProviderUser;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.validators.ErrorValidator;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - POST /v1/leaderboard/templates endpoint validation tests")
public class CreateTemplatesValidationTests extends TemplateBaseTest {

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when creating template with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void createTemplateWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .markets(markets)
        .build();

    var response =
        TemplatesEndpoint.createLeaderboardTemplate(templateRequest, CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProviderUser.class)
  public void createTemplateWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var response =
        TemplatesEndpoint.createLeaderboardTemplateWithToken(argumentsHolder.getJwtToken(),
            TemplateRequest.builder()
                .name(new Faker().name().title())
                .fromDate(fromDateString)
                .toDate(toDateString)
                .build());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid client id. Client id: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void createTemplateWithInvalidClientId(String clientId) throws HttpException {
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var response =
        TemplatesEndpoint.createLeaderboardTemplate(
            TemplateRequest.builder()
                .name(new Faker().name().title())
                .fromDate(fromDateString)
                .toDate(toDateString)
                .build(),
            clientId);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response,
        List.of(HttpStatus.SC_FORBIDDEN, HttpStatus.SC_BAD_REQUEST));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify templates cannot be created by clients from different project")
  public void createTemplateForAnotherClient() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var response =
        TemplatesEndpoint.createLeaderboardTemplate(
            TemplateRequest.builder()
                .name(new Faker().name().title())
                .fromDate(fromDateString)
                .toDate(toDateString)
                .build(),
            CLIENT_PRODUCTION_TESTING_ID);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify template cannot be created with invalid content type")
  public void createTemplateWithInvalidContentType() throws HttpException {

    var response =
        TemplatesEndpoint.createLeaderboardTemplate("", CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.TEXT);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users cannot create templates")
  public void createTemplateByEndUser()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var user = createUser();
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var response =
        TemplatesEndpoint.createLeaderboardTemplate(
            TemplateRequest.builder()
                .name(new Faker().name().title())
                .fromDate(fromDateString)
                .toDate(toDateString)
                .build(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, null, true, user.getEmail(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions cannot create templates")
  public void createTemplateByUserWithInsufficientPermissions() throws HttpException {
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var response =
        TemplatesEndpoint.createLeaderboardTemplate(
            TemplateRequest.builder()
                .name(new Faker().name().title())
                .fromDate(fromDateString)
                .toDate(toDateString)
                .build(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, null, true, BILLING_USER,
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid from_date format. From date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void createTemplateWithInvalidFromDateFormat(String fromDate) throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .fromDate(fromDate)
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusYears(1)))
        .name(new Faker().name().title())
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid to_date format. To date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void createTemplateWithInvalidToDateFormat(String toDate) throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(toDate)
        .name(new Faker().name().title())
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify template cannot be created if one of the date fields is null. Null field: to_date")
  public void createTemplateWithNullToDateFormat() throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(null)
        .name(new Faker().name().title())
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify template cannot be created if one of the date fields is null. Null field: from_date")
  public void createTemplateWithNullFromDateFormat() throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .fromDate(null)
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .name(new Faker().name().title())
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with null or empty name. Name: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void createTemplateWithInvalidName(String name) throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue());

    var templateRequest = TemplateRequest.builder()
        .name(name)
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify template cannot be created without name field")
  public void createTemplateWithoutNameField() throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue());

    var templateRequest = TemplateRequest.builder()
        .ignoreName(true)
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid market. Market: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "INVALID")
  @NullAndEmptySource
  public void createTemplateWithInvalidMarket(String market) throws HttpException {

    var markets = new ArrayList<>(Collections.singleton(market));

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid match ids. Match id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "fb:m:15812751761838561864682135")
  @NullAndEmptySource
  public void createTemplateWithInvalidMatchId(String match) throws HttpException {

    var matchList = new ArrayList<>(Collections.singleton(match));

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .matchIds(matchList)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid competition ids. Competition id: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = "fb:c:15812751761838561864682135")
  @NullAndEmptySource
  public void createTemplateWithInvalidCompId(String comp) throws HttpException {

    var compList = new ArrayList<>(Collections.singleton(comp));

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .competitionIds(compList)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid game ids. Game id: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = INVALID_GAME_ID)
  @NullAndEmptySource
  public void createTemplateWithInvalidGameId(String gameId) throws HttpException {

    var gameIds = new ArrayList<>(Collections.singleton(gameId));

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .gameIds(gameIds)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid team ids. Game id: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = INVALID_TEAM_ID)
  @NullAndEmptySource
  public void createTemplateWithInvalidTeamId(String teamId) throws HttpException {

    var teamIds = new ArrayList<>(Collections.singleton(teamId));

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .teamIds(teamIds)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify template cannot be created with invalid game type. Game type: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "INVALID")
  @NullAndEmptySource
  public void createTemplateWithInvalidGameType(String gameType) throws HttpException {

    var gameTypes = new ArrayList<>(Collections.singleton(gameType));

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .gameTypes(gameTypes)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify template can be created without all fields but 'name', 'from_date' and 'to_date'")
  public void createTemplateWithoutAllFieldsButNameAndDate() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .ignoreCompetitionIds(true)
        .ignoreGameIds(true)
        .ignoreGameTypes(true)
        .ignoreMarkets(true)
        .ignoreMatchIds(true)
        .ignoreTeamIds(true)
        .ignoreFlags(true)
        .ignoreRules(true)
        .ignoreDescription(true)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    var templateResponse = response.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);
  }

  @ParameterizedTest(name = "Verify template cannot be created with increased time span duration > an year. From date: {arguments}, to date: {arguments}")
  @Tag(REGRESSION)
  @CsvSource({"2022-01-01, 2023-01-02", "2019-02-28, 2020-02-29"})
  public void createTemplateWithInvalidTimeSpan(String fromDate, String toDate)
      throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .fromDate(fromDate)
        .toDate(toDate)
        .name(new Faker().name().title())
        .markets(markets)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Disabled("WIP - FZ-1547")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-1547")})
  @DisplayName("Verify template cannot be created when description symbols > 10001")
  public void createTemplateWithInvalidDescriptionSymbolSize() throws HttpException {

    var description =
        new Faker().lorem().characters(10003);

    var templateRequest = TemplateRequest.builder()
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .name(new Faker().name().title())
        .description(description)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  // Images
  @Disabled("WIP - FZ-3400")
  @ParameterizedTest(name = "Verify creation of a template when {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-3400")})
  @CsvSource({
    "htt://invalidimageurl, https://example.com/imageurl^±˚, https: // example.com/ imageurl.jpg",
    "https://example.com/imageurl^±˚,htt://invalidimageurl, https: // example.com/ imageurl.jpg",
    "htt://invalidimageurl,https: // example.com/ imageurl.jpg, https://example.com/imageurl^±˚"
  })
  public void createTemplateWithInvalidImageUrlValues(
      String mainInvalidValue, String coverInvalidVale, String mobileInvalidValue)
      throws HttpException, IllegalArgumentException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var images = setUpImages(mainInvalidValue, coverInvalidVale, mobileInvalidValue);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .markets(markets)
        .images(images)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message",anyOf(is(ApiErrorCodes.INVALID_IMAGE_URL_FOR_STAGE), is(ApiErrorCodes.INVALID_IMAGE_EXTENSION)))
        .body("error.status", equalTo(ApiErrorCodes.STATUS_VALIDATION_ERROR));
  }
  @Test
  @Tag(REGRESSION)
  @DisplayName("Validate gameId limit to 50")
  public void validateGameIdLimit() throws HttpException {

    var gameIds= createGames(GameType.TOP_X,51);
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .gameIds(gameIds)
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);
response
    .then()
    .assertThat()
    .statusCode(HttpStatus.SC_BAD_REQUEST)
    .body("error.message",equalTo("gameIds: game_ids cannot contain more than 50 IDs."));
}
}
