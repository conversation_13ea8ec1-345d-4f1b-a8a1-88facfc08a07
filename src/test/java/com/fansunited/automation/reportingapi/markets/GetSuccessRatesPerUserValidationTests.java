package com.fansunited.automation.reportingapi.markets;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Markets.GET_SUCCESS_RATE_PER_CLIENT_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.reportingapi.SuccessRateEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDate;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/success-rates/football/markets validation tests")
public class GetSuccessRatesPerUserValidationTests extends ReportingApiBaseTest {

  @ParameterizedTest(name = "Verify success rate cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getSuccessRatesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting success rate with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getSuccessRateWithInvalidClientId(String clientId) throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(clientId,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_BAD_REQUEST), is(HttpStatus.SC_FORBIDDEN)))
        .equals(anyOf(is(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT),
            is(ApiErrorCodes.ClientErrorCodes.FORBIDDEN)));
  }

  @ParameterizedTest(name = "Verify success rate endpoint cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getSuccessRateWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRatePerClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            argumentsHolder.getJwtToken(), null, null,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users do not have access to /v1/success-rates/football/markets endpoint")
  public void getSuccessRateWithEndUserToken()
      throws IOException, ExecutionException, FirebaseAuthException,
      InterruptedException, HttpException {

    var createUser = AuthBase.createUser();

    var response =
        SuccessRateEndpoint.getSuccessRatePerClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), null,
            createUser.getEmail(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions do not have access to success rate reports")
  public void getSuccessRateByClientWithInsufficientPermissions()
      throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRatePerClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), null,
            BILLING_USER, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting success rate with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getSuccessRateWithNonSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting success rate with invalid 'from_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getSuccessRateWithInvalidFromDate(String fromDate)
      throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID, fromDate,
            LocalDate.now().toString(), FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting success rate with invalid 'to_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getSuccessRateWithInvalidToDate(String toDate)
      throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), toDate,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting success rate with invalid date range")
  public void getSuccessRateWithInvalidDateRange() throws HttpException {

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID,
            LocalDate.now().plusDays(7).toString(), LocalDate.now().toString(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns success rate for period -> 1 month subtracted by 'to_date' param, when 'from_date' query param is null or empty. Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getSuccessRateWithEmptyFromDate(String from) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30).toString();
    var toDate = LocalDate.now().toString();

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID, from, toDate,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_SUCCESS_RATE_PER_CLIENT_SCHEMA));

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("meta.from_date", equalTo(fromDate))
        .body("meta.to_date", equalTo(toDate));
  }

  @ParameterizedTest(name =
      "Verify API returns success rate for period -> from_date to current date - 1 day, when 'to_date' query param is null or empty. Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getSuccessRateWithEmptyToDate(String to) throws HttpException {

    var fromDate = LocalDate.now().minusMonths(1).toString();
    var toDate = LocalDate.now().minusDays(1).toString();

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID, fromDate, to,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_SUCCESS_RATE_PER_CLIENT_SCHEMA));

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("meta.from_date", equalTo(fromDate))
        .body("meta.to_date", equalTo(toDate));
  }

  @ParameterizedTest(name =
      "Verify API returns success rate for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty. Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getSuccessRateEmptyFromToDate(String fromTo) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30).toString();
    var toDate = LocalDate.now().minusDays(1).toString();

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID, fromTo, fromTo,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_SUCCESS_RATE_PER_CLIENT_SCHEMA));

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("meta.from_date", equalTo(fromDate))
        .body("meta.to_date", equalTo(toDate));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify top success rate data is still fetched in JSON format if content type is NOT specified")
  public void getSuccessRateWithoutSpecifyingContentType()
      throws HttpException {

    var response = SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID,
        LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
