package com.fansunited.automation.reportingapi.participations;

import static com.fansunited.automation.constants.ApiErrorCodes.ClientErrorCodes.INVALID_CLIENT_ID_STATUS;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.reportingapi.EitherOrParticipationEndpoint.getEitherOrParticipation;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/either-or/participation/breakdown endpoint validation tests")
public class GetParticipationBreakdownVerificationTests extends ReportingApiBaseTest {

  @ParameterizedTest(
      name =
          "Verify participation breakdown cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getParticipationBreakdownWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        getEitherOrParticipation(
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            null,
            ADMIN_USER,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ContentType.JSON,
            null,
            null,
            null,
            null,
            null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting participation breakdown with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getParticipationBreakdownWithInvalidClientId(String clientId) throws HttpException {

    var response =
        getEitherOrParticipation(
            clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            ADMIN_USER,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ContentType.JSON,
            null,
            null,
            null,
            null,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_BAD_REQUEST), is(HttpStatus.SC_FORBIDDEN)));

    String errorStatus = response.then().extract().path("error.status");

    Assertions.assertTrue(
        INVALID_CLIENT_ID_STATUS.equals(errorStatus) || "forbidden".equals(errorStatus),
        "Error status should be either 'invalid_client_id' or 'forbidden'");
  }
}
