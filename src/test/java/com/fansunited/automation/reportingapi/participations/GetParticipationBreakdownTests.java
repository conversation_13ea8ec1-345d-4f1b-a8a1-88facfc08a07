package com.fansunited.automation.reportingapi.participations;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.ParticipateInTriviaGame.participateInEitherOr;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.updateProfileRequest;
import static com.fansunited.automation.core.apis.reportingapi.EitherOrParticipationEndpoint.getEitherOrParticipation;
import static com.fansunited.automation.core.base.AuthBase.createUsers;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.createTriviaGameForTests;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.eitherOrParticipation;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.validators.ReportingValidator.verifyEitherOrParticipationsResponse;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.model.minigamesapi.eitheror.response.EitherOrResponse;
import com.fansunited.automation.model.profileapi.profile.ProfileData.DeletedProfile.Gender;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.CacheValidator.CachePeriod;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/either-or/participation breakdown endpoint happy path tests")
@SuppressWarnings("unchecked")
public class GetParticipationBreakdownTests extends ReportingApiBaseTest {

  private List<UserRecord> usersStartGameOnly = new ArrayList<>();
  private List<UserRecord> usersStartGameAndParticipateOnly = new ArrayList<>();
  private List<UserRecord> usersFinishedGame = new ArrayList<>();
  private EitherOrResponse eitherOrGame;
  private static final LocalDate currentDate = LocalDate.now();

  @BeforeEach
  @SuppressWarnings("unchecked")
  public void beforeAll()
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {
    /* create Lists of users
       usersStartGameOnly with gender MALE users that will start only
       usersStartGameAndParticipateOnly with gender FEMALE users that will start and participate
       usersFinishedGame with gender UNSPECIFIED users that will start, participate and finish
    */
    usersStartGameOnly = createUsers(generateRandomNumber(6, 10));
    usersStartGameAndParticipateOnly = createUsers(generateRandomNumber(4, 6));
    usersFinishedGame = createUsers(generateRandomNumber(2, 4));
    eitherOrGame = createTriviaGameForTests(EitherOrWinningCondition.MORE);

    var gender = new JSONObject();
    gender.put(GENDER_PROP, Gender.MALE.getValue());

    for (UserRecord user : usersStartGameOnly) {
      updateProfileRequest(user.getEmail(), gender);
    }

    gender.put(GENDER_PROP, Gender.FEMALE.getValue());
    for (UserRecord user : usersStartGameAndParticipateOnly) {
      updateProfileRequest(user.getEmail(), gender);
    }
  }

  @Test
  @DisplayName("Participate in the trivia game and verify the participation breakdown")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyTriviaGameParticipationBreakdown() throws HttpException {
    String eitherOrId = eitherOrGame.getData().getId();
    // Participate with users who only start the game
    participateTriviaGame(eitherOrId, "started", usersStartGameOnly);
    // Participate with users who start and play but not finished the game
    participateTriviaGame(eitherOrId, "participated", usersStartGameAndParticipateOnly);
    // Participate with users who finished the game
    participateTriviaGame(eitherOrId, "finished", usersFinishedGame);

    String fromDate = currentDate.format(DateTimeFormatter.ISO_DATE);

    var responseParticipationStarted =
        getEitherOrParticipation(fromDate, fromDate, GroupBy.DAY, "started", List.of(eitherOrId));

    var expectedParticipationCount =
        usersStartGameOnly.size()
            + usersStartGameAndParticipateOnly.size()
            + usersFinishedGame.size();

    verifyEitherOrParticipationsResponse(
        responseParticipationStarted,
        expectedParticipationCount,
        fromDate,
        fromDate,
        GroupBy.DAY,
        List.of(eitherOrId));

    var responseParticipationFinished =
        getEitherOrParticipation(fromDate, fromDate, GroupBy.DAY, "finished", List.of(eitherOrId));

    verifyEitherOrParticipationsResponse(
        responseParticipationFinished,
        usersFinishedGame.size(),
        fromDate,
        fromDate,
        GroupBy.DAY,
        List.of(eitherOrId));
  }

  @Test
  @DisplayName("Verify participation breakdown in list of games")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void testParticipationsBreakdownForMultipleGames()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    List<String> gameIds = new ArrayList<>();
    List<UserRecord> maleUsersStartGame;
    List<UserRecord> fmaleUsersStartGameAndParticipate;
    List<UserRecord> unspecifiedUsersFinished;
    int expectedParticipationCount = 0;
    int expectedFinishedParticipationCount = 0;

    for (int i = 0; i < 3; i++) {

      var gameId = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();
      gameIds.add(gameId);

      maleUsersStartGame = createUsers(generateRandomNumber(3, 6));
      fmaleUsersStartGameAndParticipate = createUsers(generateRandomNumber(1, 3));
      unspecifiedUsersFinished = createUsers(generateRandomNumber(1, 2));
      expectedParticipationCount +=
          maleUsersStartGame.size()
              + fmaleUsersStartGameAndParticipate.size()
              + unspecifiedUsersFinished.size();

      expectedFinishedParticipationCount += unspecifiedUsersFinished.size();

      var gender = new JSONObject();
      gender.put(GENDER_PROP, Gender.MALE.getValue());

      for (UserRecord user : maleUsersStartGame) {
        updateProfileRequest(user.getEmail(), gender);
      }

      gender.put(GENDER_PROP, Gender.FEMALE.getValue());
      for (UserRecord user : fmaleUsersStartGameAndParticipate) {
        updateProfileRequest(user.getEmail(), gender);
      }

      // Participate with users who only start the game
      participateTriviaGame(gameId, "started", maleUsersStartGame);
      // Participate with users who start and play but not finished the game
      participateTriviaGame(gameId, "participated", fmaleUsersStartGameAndParticipate);
      // Participate with users who finished the game
      participateTriviaGame(gameId, "finished", unspecifiedUsersFinished);
      // Currently, we count only unique users

    }
    String fromDate = currentDate.format(DateTimeFormatter.ISO_DATE);

    var responseParticipationStarted =
        getEitherOrParticipation(fromDate, fromDate, GroupBy.DAY, "started", gameIds);

    currentTestResponse.set(responseParticipationStarted);

    verifyEitherOrParticipationsResponse(
        responseParticipationStarted,
        expectedParticipationCount,
        fromDate,
        fromDate,
        GroupBy.DAY,
        gameIds);

    var responseParticipationFinished =
        getEitherOrParticipation(fromDate, fromDate, GroupBy.DAY, "finished", gameIds);

    verifyEitherOrParticipationsResponse(
        responseParticipationFinished,
        expectedFinishedParticipationCount,
        fromDate,
        fromDate,
        GroupBy.DAY,
        gameIds);
  }

  @ParameterizedTest(
      name = "Verify EitherOr participations breakdown with participation_status = {0}")
  @ValueSource(strings = {"started", "finished"})
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyParticipationStatusParameter(String participationStatus)
      throws HttpException {
    String eitherOrId =
        createTriviaGameForTests(EitherOrWinningCondition.MORE)
            .getData()
            .getId(); // Participate with users who only start the game

    participateTriviaGame(eitherOrId, "started", usersStartGameOnly);
    // Participate with users who start and play but not finished the game
    participateTriviaGame(eitherOrId, "participated", usersStartGameAndParticipateOnly);
    // Participate with users who finished the game
    participateTriviaGame(eitherOrId, "finished", usersFinishedGame);

    String fromDate = currentDate.format(DateTimeFormatter.ISO_DATE);

    Response response =
        getEitherOrParticipation(
            fromDate, fromDate, GroupBy.DAY, participationStatus, List.of(eitherOrId));

    // Verify common response aspects
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    if ((participationStatus.equals("started"))) {

      int expectedParticipationCount =
          usersStartGameOnly.size()
              + usersStartGameAndParticipateOnly.size()
              + usersFinishedGame.size();

      verifyEitherOrParticipationsResponse(
          response,
          expectedParticipationCount,
          fromDate,
          fromDate,
          GroupBy.DAY,
          List.of(eitherOrId));
    } else {
      verifyEitherOrParticipationsResponse(
          response, usersFinishedGame.size(), fromDate, fromDate, GroupBy.DAY, List.of(eitherOrId));
    }
  }

  @ParameterizedTest(name = "Verify EitherOr participations breakdown with groupBy = {0}")
  @EnumSource(GroupBy.class)
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyGroupByParameter(GroupBy groupBy) throws HttpException {

    String fromDate = currentDate.minusMonths(1).toString();
    String toDate = currentDate.toString();

    Response response = getEitherOrParticipation(fromDate, toDate, groupBy, null, null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.grouped_by", equalTo(groupBy.getValue()));

    // Additional verifications based on groupBy value
    switch (groupBy) {
      case DAY -> verifyDailyBreakdown(response);
      case WEEK -> verifyWeeklyBreakdown(response);
      case MONTH -> verifyMonthlyBreakdown(response);
      case YEAR -> verifyYearlyBreakdown(response);
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify /GET/v1/either-or/participations response returned by the server is cached for 480 min")
  public void verifyGetStatisticsBreakdownResultIsCached() throws HttpException {

    var response = getEitherOrParticipation(null, null, null, null, null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    CacheValidator.validateCacheExpirationDate(response, CachePeriod.EIGHT_HOURS);
  }

  // Helper methods

  private Response participate(String eitherOrId, UserRecord user) throws HttpException {

    var response =
        participateInEitherOr(
            "",
            eitherOrId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            user.getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    response.then().assertThat().statusCode(200);
    return response;
  }

  private void participateTriviaGame(
      String eitherOrIdString, String participationStatus, List<UserRecord> users)
      throws HttpException {

    switch (participationStatus) {
      case "started" -> {
        for (UserRecord user : users) {
          participate(eitherOrIdString, user);
        }
      }
      case "participated" -> {
        for (UserRecord user : users) {

          var response = participate(eitherOrIdString, user);
          var answer = response.jsonPath().get("data.steps[0].option_two.id").toString();
          var pair = response.jsonPath().get("data.steps[0].pair_id").toString();

          var participateResponse =
              participateInEitherOr(
                  eitherOrParticipation(answer, false, pair, 3F),
                  eitherOrIdString,
                  CLIENT_AUTOMATION_ID,
                  FANS_UNITED_PROFILE,
                  user.getEmail(),
                  AuthConstants.ENDPOINTS_API_KEY,
                  ContentType.JSON);

          participateResponse.then().statusCode(200);
        }
      }
      case "finished" -> {
        for (UserRecord user : users) {
          var response = participate(eitherOrIdString, user);
          var answer = response.jsonPath().get("data.steps[0].option_two.id").toString();
          var pair = response.jsonPath().get("data.steps[0].pair_id").toString();
          var participateResponse =
              participateInEitherOr(
                  eitherOrParticipation(answer, false, pair, 3F),
                  eitherOrIdString,
                  CLIENT_AUTOMATION_ID,
                  FANS_UNITED_PROFILE,
                  user.getEmail(),
                  AuthConstants.ENDPOINTS_API_KEY,
                  ContentType.JSON);

          int biggerOptionId;
          for (int i = 1; i < 45; i++) {
            String optionOneIdStr =
                participateResponse.jsonPath().get("data.steps[" + i + "].option_one.id");
            String optionTwoIdStr =
                participateResponse.jsonPath().get("data.steps[" + i + "].option_two.id");

            int optionOneId = Integer.parseInt(optionOneIdStr);
            int optionTwoId = Integer.parseInt(optionTwoIdStr);

            biggerOptionId = Math.max(optionOneId, optionTwoId);

            participateResponse =
                participateInEitherOr(
                    eitherOrParticipation(
                        String.valueOf(biggerOptionId),
                        false,
                        participateResponse.jsonPath().get("data.steps[" + i + "].pair_id"),
                        3F),
                    eitherOrIdString,
                    CLIENT_AUTOMATION_ID,
                    FANS_UNITED_PROFILE,
                    user.getEmail(),
                    AuthConstants.ENDPOINTS_API_KEY,
                    ContentType.JSON);
            participateResponse.then().statusCode(200).log().body();
          }
        }
      }
      default ->
          throw new IllegalArgumentException(
              "Invalid participation status: " + participationStatus);
    }
  }

  /**
   * Verifies the breakdown data for daily grouping
   *
   * @param response The API response
   */
  private void verifyDailyBreakdown(Response response) {
    // Get the actual from_date and to_date from the response
    String actualFromDate = response.jsonPath().getString("meta.from_date");
    String actualToDate = response.jsonPath().getString("meta.to_date");

    // Parse the dates
    LocalDate actualFromLocalDate = LocalDate.parse(actualFromDate);
    LocalDate actualToLocalDate = LocalDate.parse(actualToDate);

    // Calculate the expected number of days between the dates (inclusive)
    long expectedDays = ChronoUnit.DAYS.between(actualFromLocalDate, actualToLocalDate) + 1;

    // Get the breakdown data
    List<Object> breakdown = response.jsonPath().getList("data.breakdown");

    // Verify the number of entries matches the number of days
    Assertions.assertEquals(
        expectedDays,
        breakdown.size(),
        "Number of breakdown entries should match the number of days between "
            + actualFromDate
            + " and "
            + actualToDate
            + " (inclusive)");

    // Verify that each entry in the breakdown represents consecutive days
    verifyConsecutiveDates(response, breakdown, 1);

    // Verify the first and last dates in the breakdown match the from_date and to_date
    verifyFirstAndLastDates(response, breakdown, actualFromDate, actualToDate);
  }

  /**
   * Verifies the breakdown data for weekly grouping
   *
   * @param response The API response
   */
  private void verifyWeeklyBreakdown(Response response) {
    // Get the actual from_date and to_date from the response
    String actualFromDate = response.jsonPath().getString("meta.from_date");
    String actualToDate = response.jsonPath().getString("meta.to_date");

    // Parse the dates
    LocalDate actualFromLocalDate = LocalDate.parse(actualFromDate);
    LocalDate actualToLocalDate = LocalDate.parse(actualToDate);

    // Verify the breakdown data
    List<Object> breakdown = response.jsonPath().getList("data.breakdown");

    // Check if we have at least one week in the breakdown
    Assertions.assertFalse(breakdown.isEmpty(), "Should have at least one week in breakdown");

    // Verify that each entry in the breakdown represents a week
    verifyConsecutiveDates(response, breakdown, 7);

    // Verify that the API adjusts the from_date to the start of the week
    // and to_date to the end of a week if needed
    DayOfWeek fromDayOfWeek = actualFromLocalDate.getDayOfWeek();
    DayOfWeek toDayOfWeek = actualToLocalDate.getDayOfWeek();

    // The API might adjust dates to week boundaries, so we check if they're on expected days
    Assertions.assertEquals(
        DayOfWeek.MONDAY, fromDayOfWeek, "From date should be adjusted to start of week (Monday)");

    // The to_date might be adjusted to the end of a week or kept as is
    if (toDayOfWeek != actualToLocalDate.getDayOfWeek()) {
      Assertions.assertEquals(
          DayOfWeek.SUNDAY, toDayOfWeek, "If adjusted, to_date should be the end of week (Sunday)");
    }
  }

  /**
   * Verifies the breakdown data for monthly grouping
   *
   * @param response The API response
   */
  private void verifyMonthlyBreakdown(Response response) {
    // Check for monthly grouping
    // Expect 1-2 records in breakdown (depending if 30 days span 1 or 2 months)
    List<Object> breakdown = response.jsonPath().getList("data.breakdown");
    Assertions.assertTrue(
        !breakdown.isEmpty() && breakdown.size() <= 2, "Should have 1-2 months in breakdown");
  }

  /**
   * Verifies the breakdown data for yearly grouping
   *
   * @param response The API response
   */
  private void verifyYearlyBreakdown(Response response) {
    // Check for yearly grouping
    // Expect 1 record in breakdown (30 days are within a single year)
    List<Object> breakdown = response.jsonPath().getList("data.breakdown");
    Assertions.assertEquals(1, breakdown.size(), "Should have exactly one year in breakdown");
  }

  /**
   * Verifies that dates in the breakdown are consecutive with the expected interval
   *
   * @param response The API response
   * @param breakdown The breakdown data list
   * @param expectedDaysBetween The expected number of days between consecutive entries
   */
  private void verifyConsecutiveDates(
      Response response, List<Object> breakdown, int expectedDaysBetween) {
    for (int i = 0; i < breakdown.size() - 1; i++) {
      String actualCurrentDate = response.jsonPath().getString("data.breakdown[" + i + "].date");
      String nextDate = response.jsonPath().getString("data.breakdown[" + (i + 1) + "].date");

      LocalDate current = LocalDate.parse(actualCurrentDate);
      LocalDate next = LocalDate.parse(nextDate);

      // Calculate the days between consecutive entries
      long daysBetween = ChronoUnit.DAYS.between(current, next);

      // Verify that consecutive entries have the expected interval
      Assertions.assertEquals(
          expectedDaysBetween,
          daysBetween,
          "Days between consecutive entries should be exactly "
              + expectedDaysBetween
              + ", but found "
              + daysBetween
              + " days between "
              + actualCurrentDate
              + " and "
              + nextDate);
    }
  }

  /**
   * Verifies that the first and last dates in the breakdown match the expected dates
   *
   * @param response The API response
   * @param breakdown The breakdown data list
   * @param expectedFirstDate The expected first date
   * @param expectedLastDate The expected last date
   */
  private void verifyFirstAndLastDates(
      Response response,
      List<Object> breakdown,
      String expectedFirstDate,
      String expectedLastDate) {
    if (!breakdown.isEmpty()) {
      String firstBreakdownDate = response.jsonPath().getString("data.breakdown[0].date");
      String lastBreakdownDate =
          response.jsonPath().getString("data.breakdown[" + (breakdown.size() - 1) + "].date");

      Assertions.assertEquals(
          expectedFirstDate, firstBreakdownDate, "First breakdown date should match from_date");
      Assertions.assertEquals(
          expectedLastDate, lastBreakdownDate, "Last breakdown date should match to_date");
    }
  }
}
