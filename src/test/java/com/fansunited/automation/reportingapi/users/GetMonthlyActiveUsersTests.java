package com.fansunited.automation.reportingapi.users;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.SortField.DATE;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.BREAKDOWN_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.USERS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_MANTLY_ACTIVE_USERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isoDatesAreInDescendingOrder;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.core.apis.reportingapi.ActiveMonthlyUsersEndpoint;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/users/mau endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetMonthlyActiveUsersTests extends ReportingApiBaseTest {

  /**
   * Story
   * As a client, I want to know the number of monthly active users (MAU) I have.
   * <p>
   * Acceptance criteria
   * - Report with a number of unique users who were active in particular month
   * - Breakdown of users per month
   * <p>
   * Definition of MAU:
   * Any user that has registered an event in the platform
   * (can be a pageview activity, updated their profile, placed a single prediction or participated in a game).
   * If we have recorded an event for a user that uses their valid token, this is counted as an active user.
   */

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1513"), @Tag("FZ-1752")})
  @DisplayName("Verify getting monthly active users grouped by month")
  public void getRegisteredUsersByMonth()
      throws Exception {

    InsertBigQData.insertProfiles(10);
    InsertBigQData.insertEvents(10, "PREDICTION_MADE","SINGLE","predictor","PREDICTION_MADE");
    InsertBigQData.insertMauEventWithDate(30);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.MONTHLY_ACTIVE_USERS_MV,
            "dummy", 1);

    var response = ActiveMonthlyUsersEndpoint.builder().build().getMonthlyActiveUsers();

    currentTestResponse.set(response);

    response
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MANTLY_ACTIVE_USERS_SCHEMA))
        .body("data.breakdown.size()", is(greaterThan(2)))
        .body("data." + BREAKDOWN_PROP + "." + DATE, is(notNullValue()))
        .body("data." + BREAKDOWN_PROP + "." + DATE, isoDatesAreInDescendingOrder())
        .body("data." + BREAKDOWN_PROP + "." + USERS_PROP, is(notNullValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users response returned by the server is NOT cached")
  public void verifyGetRegisteredUsersResponseIsCached()
      throws HttpException {

    var response = ActiveMonthlyUsersEndpoint.builder().build().getMonthlyActiveUsers();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.EIGHT_HOURS);
  }
}
