package com.fansunited.automation.reportingapi.users;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_USERS_PER_COUNTRY_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.reportingapi.UsersPerCountryEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.reportingapi.users.userspercountry.UsersPerCountryDto;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDate;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

public class GetUsersPerCountryValidationTests extends ReportingApiBaseTest {

  @ParameterizedTest(name = "Verify users per country cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getUsersPerCountryWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting users per country with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getRegisteredUsersWithInvalidClientId(String clientId) throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(clientId,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_BAD_REQUEST),is(HttpStatus.SC_FORBIDDEN)))
        .equals(anyOf(is(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT),is(ApiErrorCodes.ClientErrorCodes.FORBIDDEN)));
  }


  @ParameterizedTest(name = "Verify users per country cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getUsersPerCountryWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true,
            argumentsHolder.getJwtToken(), null, null,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users do not have access to /v1/users/countries endpoint")
  public void getUsersPerCountryWithEndUserToken()
      throws IOException, ExecutionException, FirebaseAuthException,
      InterruptedException, HttpException {

    var createUser = AuthBase.createUser();

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true, null,
            createUser.getEmail(), FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions do not have access to users per country reports")
  public void getUsersPerCountryByClientWithInsufficientPermissions()
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            true, null, BILLING_USER,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting users per country with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getUsersPerCountryWithNonSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting users per country with invalid date range")
  public void getUsersPerCountryWithInvalidDateRange() throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(LocalDate.now().plusDays(7).toString(),
            LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting users per country with invalid 'from_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getUsersPerCountryWithInvalidFromDate(String fromDate)
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate, LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting users per country with invalid 'to_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getUsersPerCountryWithInvalidToDate(String toDate)
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(
            LocalDate.now().minusDays(7).toString(), toDate);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns users per country for period -> 1 month subtracted by 'to_date' param, when 'from_date' query param is null or empty"
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getUsersPerCountryWithEmptyFromDate(String from) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(from, toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
  }

  @ParameterizedTest(name =
      "Verify API returns users per country for period -> from_date to current date - 1 day, when 'to_date' query param is null or empty"
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getUsersPerCountryWithEmptyToDate(String to) throws HttpException {

    var fromDate = LocalDate.now().minusMonths(2);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(), to);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
  }

  @ParameterizedTest(name =
      "Verify API returns users per country for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty"
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getUsersPerCountryWithEmptyFromToDate(String fromTo) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromTo, fromTo);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify users per country data is still fetched in JSON format if content type is NOT specified")
  public void getUsersPerCountryWithoutSpecifyingContentType()
      throws HttpException {

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(CLIENT_AUTOMATION_ID,
        LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
        AuthConstants.ENDPOINTS_API_KEY,
        null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
