package com.fansunited.automation.reportingapi.users;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_MONTHLY_ACTIVE_VS_USERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.isA;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.ActiveMonthlyUsersEndpoint;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.core.base.reportingapi.TruncateTablesHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.time.LocalDate;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/users/mauvsreg endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetMonthlyActiveUsersVSTests extends ReportingApiBaseTest {

  @AfterAll
  public static void afterAll() {
    // this should not be done in separate test classes, but in base test only. Unfortunately, some
    // tests in reporting api need this to be run once more
    TruncateTablesHelper.executeTruncateTables();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1844")})
  @DisplayName("Verify getting monthly active vs users  grouped by month")
  public void getRegisteredUsersByMonth()
      throws Exception {

    LocalDate dayFromDate = LocalDate.now().minusYears(2).minusDays(20);
    LocalDate dayToDate =  LocalDate.now().minusYears(2);
    InsertBigQData.generateRegistrationEventsForPeriod(1, dayFromDate, dayToDate,
        Periodicity.DAY);
    InsertBigQData.insertMauEventWithDate(30);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.MONTHLY_ACTIVE_USERS_MV,
            "dummy", 1);

    waitForBreakdownData();

    var response = ActiveMonthlyUsersEndpoint.builder().build().getMonthlyActiveUsersVsReg();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        // First month validations
        .body("data.breakdown[0].registrations", greaterThanOrEqualTo(21))
        .body("data.breakdown[0].active_users", is(notNullValue()))

        // Second month validations
        .body("data.breakdown[1].registrations", greaterThanOrEqualTo(1)) // Adjust condition if needed
        .body("data.breakdown[1].active_users", is(notNullValue()))

        // Ensure all registrations and active_users are integers or valid
        .body("data.breakdown.registrations", everyItem(isA(Integer.class)))
        .body("data.breakdown.active_users", everyItem(isA(Integer.class)))

        // Check the descending order based on registration values
        .body("data.breakdown[0].registrations", greaterThanOrEqualTo(response.jsonPath().getInt("data.breakdown[1].registrations")));
  }

  public void waitForBreakdownData() {
    await()
        .atMost(300, TimeUnit.SECONDS)
        .pollInterval(10, TimeUnit.SECONDS)
        .until(() -> {
          // Call the endpoint
          Response response =
              ActiveMonthlyUsersEndpoint.builder().build().getMonthlyActiveUsersVsReg();
          currentTestResponse.set(response);

          // Validate initial response details (HTTP status and schema)
          response
              .then()
              .assertThat()
              .statusCode(HttpStatus.SC_OK)
              .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
                  GET_MONTHLY_ACTIVE_VS_USERS_SCHEMA));

          // Check if data.breakdown is not empty
          return !response.jsonPath().getList("data.breakdown").isEmpty();
        });
  }

    @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users/mauvsreg response returned by the server is cached for eight hours")
  public void verifyGetRegisteredUsersResponseIsCached()
      throws HttpException {

    var response = ActiveMonthlyUsersEndpoint.builder().build().getMonthlyActiveUsersVsReg();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.EIGHT_HOURS);
  }
}
