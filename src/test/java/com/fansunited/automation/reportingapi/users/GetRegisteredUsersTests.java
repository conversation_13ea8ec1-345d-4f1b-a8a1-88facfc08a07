package com.fansunited.automation.reportingapi.users;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_ACQUIRED_USERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.exparity.hamcrest.date.LocalDateMatchers.sameOrAfter;
import static org.exparity.hamcrest.date.LocalDateMatchers.sameOrBefore;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.UsersEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.core.base.reportingapi.TruncateTablesHelper;
import com.fansunited.automation.customexceptions.IncorrectDateBreakdownException;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.reportingapi.users.registeredusers.UsersPerDateDto;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Locale;
import java.util.Map;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/users endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetRegisteredUsersTests extends ReportingApiBaseTest {

  @AfterAll
  public static void afterAll() {
    // this should not be done in separate test classes, but in base test only. Unfortunately, some
    // tests in reporting api need this to be run once more
    TruncateTablesHelper.executeTruncateTables();
  }

  private static Map<LocalDate, Long> eventMap;

  private static final LocalDate dayFromDate = LocalDate.now().minusYears(1).minusDays(40);
  private static final LocalDate dayToDate = LocalDate.now().minusDays(40);

  private static final LocalDate weekFromDate = LocalDate.now().minusWeeks(20);
  private static final LocalDate weekToDate = LocalDate.now().minusWeeks(15);

  private static final LocalDate monthFromDate = LocalDate.now().minusMonths(10);
  private static final LocalDate monthToDate = LocalDate.now().minusMonths(5);

  private static final LocalDate yearFromDate = LocalDate.now().minusYears(1).minusDays(40);
  private static final LocalDate yearToDate = LocalDate.now().minusDays(40);

  @BeforeAll
  public static void setup() {
    eventMap = InsertBigQData.generateRegistrationEventsForPeriod(1, dayFromDate, dayToDate,
        Periodicity.DAY);
    BigQueryHelper.waitForEventsToBeSaved(10);  // Wait for events to be saved to BigQuery
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by day")
  public void getRegisteredUsersByDay() throws HttpException, InterruptedException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        dayFromDate.toString(), dayToDate.toString(), GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);
    usersPerDateDto.getData()
        .getBreakdown()
        .forEach(dateBreakdownDto -> MatcherAssert.assertThat(dateBreakdownDto.getDate(),
            allOf(sameOrAfter(dayFromDate), sameOrBefore(dayToDate))));

    var totalUsers = usersPerDateDto.getData().getTotal().getAll();

    for (var date : usersPerDateDto.getData().getBreakdown()) {
      var registeredUsersForDate = eventMap.get(date.getDate());
      if (registeredUsersForDate == null) {
        throw new IncorrectDateBreakdownException(eventMap.keySet().stream().toList(),
            date.getDate());
      }
      MatcherAssert.assertThat(
          "All registered users for day "
              + date.getDate()
              + " returned by the API do NOT match expected count",
          date.getUsers().getAll(),
          equalTo(registeredUsersForDate));
    }

    MatcherAssert.assertThat(
        "Total registered users for the whole period returned by the API do NOT match expected count",
        usersPerDateDto.getData().getTotal().getAll(),
        equalTo(totalUsers));

    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerDateDto.getMeta().getGroupedBy(),
        equalTo(GroupBy.DAY.getValue()));

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerDateDto.getMeta().getFromDate(),
        equalTo(dayFromDate));

    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerDateDto.getMeta().getToDate(),
        equalTo(dayToDate));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by week")
  public void getRegisteredUsersByWeek()
      throws HttpException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        weekFromDate.toString(), weekToDate.toString(), GroupBy.WEEK.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    LocalDate startOfWeekFromDate = weekFromDate.with(
        TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    LocalDate startOfWeekToDate = weekToDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    LocalDate endOfWeekToDate = weekToDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

    var usersPerDateDto = response.as(UsersPerDateDto.class);
    usersPerDateDto.getData().getBreakdown().forEach(dateBreakdownDto ->
        assertThat("Date " + dateBreakdownDto.getDate() + " is outside the expected weekly range",
            dateBreakdownDto.getDate(),
            allOf(sameOrAfter(startOfWeekFromDate), sameOrBefore(startOfWeekToDate))
        )
    );

    var totalUsers = usersPerDateDto.getData().getTotal().getAll();

    for (var date : usersPerDateDto.getData().getBreakdown()) {
      assertThat("Users for week " + date.getDate() + " do not match",
          date.getUsers().getAll(), equalTo(7L));
    }

    MatcherAssert.assertThat(
        "Total registered users for the whole period returned by the API do NOT match expected count",
        usersPerDateDto.getData().getTotal().getAll(),
        equalTo(totalUsers));

    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerDateDto.getMeta().getGroupedBy(),
        equalTo(GroupBy.WEEK.getValue()));

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerDateDto.getMeta().getFromDate(),
        equalTo(startOfWeekFromDate));

    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerDateDto.getMeta().getToDate(),
        equalTo(endOfWeekToDate));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by month")
  public void getRegisteredUsersByMonth()
      throws HttpException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        monthFromDate.toString(), monthToDate.toString(), GroupBy.MONTH.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);
    usersPerDateDto.getData()
        .getBreakdown()
        .forEach(dateBreakdownDto -> MatcherAssert.assertThat(dateBreakdownDto.getDate(),
            allOf(sameOrAfter(monthFromDate.withDayOfMonth(1)),
                sameOrBefore(monthToDate.withDayOfMonth(1)))));

    var totalUsers = usersPerDateDto.getData().getTotal().getAll();

    for (var date : usersPerDateDto.getData().getBreakdown()) {
      // Calculate the number of days in the current month
      LocalDate breakdownDate = date.getDate();
      int expectedDaysInMonth = YearMonth.of(breakdownDate.getYear(), breakdownDate.getMonth()).lengthOfMonth();

      // Cast expectedDaysInMonth to long
      assertThat("Users for month " + breakdownDate + " do not match",
          date.getUsers().getAll(), equalTo((long) expectedDaysInMonth));
    }

    MatcherAssert.assertThat(
        "Total registered users for the whole period returned by the API do NOT match expected count",
        usersPerDateDto.getData().getTotal().getAll(),
        equalTo(totalUsers));

    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerDateDto.getMeta().getGroupedBy(),
        equalTo(GroupBy.MONTH.getValue()));

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerDateDto.getMeta().getFromDate(),
        equalTo(monthFromDate.withDayOfMonth(1)));

    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerDateDto.getMeta().getToDate(),
        equalTo(monthToDate.withDayOfMonth(monthToDate.getMonth().length(monthToDate.isLeapYear()))));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by year")
  public void getRegisteredUsersByYear()
      throws HttpException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        yearFromDate.toString(), yearToDate.toString(), GroupBy.YEAR.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);
    usersPerDateDto.getData()
        .getBreakdown()
        .forEach(dateBreakdownDto -> MatcherAssert.assertThat(dateBreakdownDto.getDate(),
            allOf(sameOrAfter(yearFromDate.withMonth(1).withDayOfMonth(1)),
                sameOrBefore(yearToDate.withMonth(1).withDayOfMonth(1)))));

    var totalUsers = usersPerDateDto.getData().getTotal().getAll();

    for (var date : usersPerDateDto.getData().getBreakdown()) {
      LocalDate breakdownDate = date.getDate();
      int expectedDaysInYear;

      if (breakdownDate.equals(yearFromDate.withDayOfYear(1))) {
        // First year - count days from yearFromDate to December 31
        expectedDaysInYear = (int) ChronoUnit.DAYS.between(yearFromDate, LocalDate.of(yearFromDate.getYear(), 12, 31).plusDays(1));
      } else if (breakdownDate.equals(yearToDate.withDayOfYear(1))) {
        // Last year - count days from January 1 to yearToDate
        expectedDaysInYear = (int) ChronoUnit.DAYS.between(LocalDate.of(yearToDate.getYear(), 1, 1), yearToDate.plusDays(1));
      } else {
        // Full year in between - use 365 or 366 days based on whether it's a leap year
        expectedDaysInYear = YearMonth.of(breakdownDate.getYear(), 1).lengthOfYear();
      }

      // Assert that the number of users matches the expected days
      assertThat("Users for year " + breakdownDate + " do not match",
          date.getUsers().getAll(), equalTo((long) expectedDaysInYear));
    }

    MatcherAssert.assertThat(
        "Total registered users for the whole period returned by the API do NOT match expected count",
        usersPerDateDto.getData().getTotal().getAll(),
        equalTo(totalUsers));

    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerDateDto.getMeta().getGroupedBy(),
        equalTo(GroupBy.YEAR.getValue()));

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerDateDto.getMeta().getFromDate(),
        equalTo(yearFromDate.withMonth(1).withDayOfMonth(1)));

    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerDateDto.getMeta().getToDate(),
        equalTo(yearToDate.withMonth(12).withDayOfMonth(31)));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users response returned by the server is NOT cached")
  public void verifyGetRegisteredUsersResponseIsNotCached()
      throws HttpException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
        GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateResponseIsCached(response);
  }
}
