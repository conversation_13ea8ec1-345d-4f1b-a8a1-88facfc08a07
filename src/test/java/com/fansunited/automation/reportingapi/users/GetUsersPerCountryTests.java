package com.fansunited.automation.reportingapi.users;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_USERS_PER_COUNTRY_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.profileapi.CountriesEndpoint;
import com.fansunited.automation.core.apis.reportingapi.UsersPerCountryEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.fansunited.automation.model.reportingapi.users.userspercountry.CountryBreakdownDto;
import com.fansunited.automation.model.reportingapi.users.userspercountry.UsersPerCountryDto;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/users/country endpoint happy path tests")
public class GetUsersPerCountryTests extends ReportingApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-772")})
  @DisplayName("Verify getting users per country")
  public void getUsersPerCountry() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(6);
    var toDate = LocalDate.now();

    var events =
        InsertBigQData.generateRegistrationEvents(generateRandomNumber(1, 5), fromDate, toDate);

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    currentTestResponse.set(response);

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    events.keySet().forEach(countryId -> {
      var optional = usersPerCountryDto.getData()
          .getBreakdown()
          .stream()
          .filter(countryBreakdownDto -> countryId.equals(countryBreakdownDto.getId()))
          .findFirst();
      if (optional.isEmpty()) {
        throw new AssertionError(
            countryId + " is not present in country breakdown list: " + usersPerCountryDto.getData()
                .getBreakdown()
                .stream()
                .map(CountryBreakdownDto::getId)
                .toList());
      } else {
        MatcherAssert.assertThat("Incorrect users returned by the API for country: " + countryId,
            optional.get().getUsers().getAll(), equalTo(events.get(optional.get().getId())));
      }
    });

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerCountryDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-784")})
  @DisplayName("Verify events with same profile id are not counted multiple times when getting users per country for period")
  public void getUsersPerCountryDuplicateProfileId() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(6);
    var toDate = LocalDate.now();

    var profileId = UUID.randomUUID().toString();
    var profileCreatedAt = convertLocalDateToDateTime(fromDate);
    var profileUpdatedAt = profileCreatedAt;

    var profile = RegistrationProfile.builder()
        .id(profileId)
        .name(new Faker().funnyName().name())
        .gender("unspecified")
        .country(CountryProfile.builder()
            .id(ApiConstants.ProfileApi.COUNTRY_ID_BG)
            .name("Bulgaria")
            .assets(new Country.AssetsFlag(
                "https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
            .build())
        .followersCount(0)
        .followingCount(0)
        .email(new Faker().internet().emailAddress())
        .avatar("http://noavatar.com").build();

    InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, null);

    var eventsCount = 3;
    for (int i = 0; i < eventsCount; i++) {
      // Simulate profile update
      profileUpdatedAt = profileUpdatedAt.plusMinutes(1);
      profile.setUpdatedAt(profileUpdatedAt.format(
          DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
      InsertBigQData.insertSingleProfile(profileCreatedAt, profile,
          100, profileUpdatedAt);
    }

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    currentTestResponse.set(response);

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("API returned incorrect country breakdown list size",
        usersPerCountryDto.getData().getBreakdown().size(), equalTo(1));
    MatcherAssert.assertThat("API returned incorrect country id",
        usersPerCountryDto.getData().getBreakdown().get(0).getId(),
        equalTo(profile.getCountry().getId()));
    MatcherAssert.assertThat("API counts events with same profile id as distinct users",
        usersPerCountryDto.getData().getTotal().getAll(), equalTo(1));

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerCountryDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-784")})
  @DisplayName("Verify API counts only latest profile's country")
  public void getUsersPerCountryWithUpdatedCountry() throws HttpException {

    var fromDate = LocalDate.now().minusDays(6);
    var toDate = LocalDate.now();

    var profileId = UUID.randomUUID().toString();
    var profileCreatedAt = convertLocalDateToDateTime(fromDate);
    var profileUpdatedAt = profileCreatedAt;

    var profile = RegistrationProfile.builder()
        .id(profileId)
        .name(new Faker().funnyName().name())
        .gender("unspecified")
        .country(CountriesEndpoint.getRandomCountryDto())
        .followersCount(0)
        .followingCount(0)
        .email(new Faker().internet().emailAddress())
        .avatar("http://noavatar.com").build();

    InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, null);

    var eventsCount = 3;
    for (int i = 0; i < eventsCount; i++) {
      // Simulate profile update
      profile.setCountry(CountriesEndpoint.getRandomCountryDto());
      profileUpdatedAt = profileUpdatedAt.plusMinutes(1);
      profile.setUpdatedAt(profileUpdatedAt.format(
          DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
      InsertBigQData.insertSingleProfile(profileCreatedAt, profile,
          100, profileUpdatedAt);
    }

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    currentTestResponse.set(response);

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("API returned incorrect country breakdown list size",
        usersPerCountryDto.getData().getBreakdown().size(), equalTo(1));
    MatcherAssert.assertThat("API returned incorrect country id",
        usersPerCountryDto.getData().getBreakdown().get(0).getId(),
        equalTo(profile.getCountry().getId()));
    MatcherAssert.assertThat("API counts events with same profile id as distinct users",
        usersPerCountryDto.getData().getTotal().getAll(), equalTo(1));

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerCountryDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users/country response returned by the server is NOT cached")
  public void verifyGetRegisteredUsersPerCountryResponseIsNotCached()
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(
            LocalDate.now().minusDays(1).toString(),
            LocalDate.now().toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.EIGHT_HOURS);
  }
}