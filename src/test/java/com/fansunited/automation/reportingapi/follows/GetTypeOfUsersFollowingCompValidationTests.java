package com.fansunited.automation.reportingapi.follows;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Follows.GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.reportingapi.FollowsByCompEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.reportingapi.users.userspercountry.UsersPerCountryDto;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDate;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/follows/football/{competitionId} endpoint validation tests")
public class GetTypeOfUsersFollowingCompValidationTests extends ReportingApiBaseTest {

  @ParameterizedTest(name = "Verify type of users following a competition cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getTypeOfUsersFollowingCompWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(CLIENT_AUTOMATION_ID, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting type of users following a competition with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getTypeOfUsersFollowingCompWithInvalidClientId(String clientId) throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(clientId, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_BAD_REQUEST),is(HttpStatus.SC_FORBIDDEN)))
        .equals(anyOf(is(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT),is(ApiErrorCodes.ClientErrorCodes.FORBIDDEN)));
  }

  @ParameterizedTest(name = "Verify type of users following a competition cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getTypeOfUsersFollowingCompWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(CLIENT_AUTOMATION_ID, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true,
            argumentsHolder.getJwtToken(), null, null,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users do not have access to /v1/follows/football/{competitionId} endpoint")
  public void getTypeOfUsersFollowingCompWithEndUserToken()
      throws IOException, ExecutionException, FirebaseAuthException,
      InterruptedException, HttpException {

    var createUser = AuthBase.createUser();

    var response =
        FollowsByCompEndpoint.getFollowsByComp(CLIENT_AUTOMATION_ID, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true, null,
            createUser.getEmail(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions do not have access to type of user following a competition reports")
  public void getTypeOfUsersFollowingCompByClientWithInsufficientPermissions()
      throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(CLIENT_AUTOMATION_ID, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true, null,
            BILLING_USER, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns 404 when getting type of users following a competition with invalid competition id")
  public void getTypeOfUsersFollowingCompWithInvalidCompId() throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp("fb:12418512512123",
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting type of users following a competition with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getTypeOfUsersFollowingCompWithNonSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(CLIENT_AUTOMATION_ID, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting type of users following a competition with invalid 'from_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getTypeOfUsersFollowingCompWithInvalidFromDate(String fromDate)
      throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(PREMIER_LEAGUE_COMP_ID, fromDate,
            LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting type of users following a competition with invalid 'to_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getTypeOfUsersFollowingCompWithInvalidToDate(String toDate)
      throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(7).toString(), toDate);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting type of users following a competition with invalid date range")
  public void getTypeOfUsersFollowingCompWithInvalidDateRange() throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().plusDays(7).toString(), LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns type of users following a competition for period -> 2021-01-01 to current date, when 'from_date' query param is null or empty. Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getTypeOfUsersFollowingCompWithEmptyFromDate(String from) throws HttpException {

    var fromDate = LocalDate.of(2021, 1, 1);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        FollowsByCompEndpoint.getFollowsByComp(PREMIER_LEAGUE_COMP_ID, from, toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA));

    currentTestResponse.set(response);

    var followsPerComp = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerComp.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerComp.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerComp.getMeta().getGroupedBy(), equalTo(GroupBy.COUNTRY));
  }

  @ParameterizedTest(name =
      "Verify API returns type of users following a competition for period -> from_date to current date, when 'to_date' query param is null or empty. Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getTypeOfUsersFollowingCompWithEmptyToDate(String to) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now();

    var response =
        FollowsByCompEndpoint.getFollowsByComp(PREMIER_LEAGUE_COMP_ID, fromDate.toString(), to);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA));

    currentTestResponse.set(response);

    var followsPerComp = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerComp.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerComp.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerComp.getMeta().getGroupedBy(), equalTo(GroupBy.COUNTRY));
  }

  @ParameterizedTest(name =
      "Verify API returns type of users following a competition for period -> 2021-01-01 to current date, when 'from_date' and 'to_date' query params are null or empty. Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getTypeOfUsersFollowingCompWithEmptyFromToDate(String fromTo) throws HttpException {

    var fromDate = LocalDate.of(2021, 1, 1);
    var toDate = LocalDate.now();

    var response =
        FollowsByCompEndpoint.getFollowsByComp(PREMIER_LEAGUE_COMP_ID, fromTo, fromTo);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA));

    currentTestResponse.set(response);

    var followsPerComp = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerComp.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerComp.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerComp.getMeta().getGroupedBy(), equalTo(GroupBy.COUNTRY));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify type of users following a competition data is still fetched in JSON format if content type is NOT specified")
  public void getTypeOfUsersFollowingCompWithoutSpecifyingContentType()
      throws HttpException {

    var response =
        FollowsByCompEndpoint.getFollowsByComp(CLIENT_AUTOMATION_ID, PREMIER_LEAGUE_COMP_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
