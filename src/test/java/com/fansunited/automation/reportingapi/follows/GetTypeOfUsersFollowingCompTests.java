package com.fansunited.automation.reportingapi.follows;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.ALL_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.BREAKDOWN_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.USERS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Follows.GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.reportingapi.helper.InterestGenerator.generateFootballInterest;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.FollowsByCompEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.apis.reportingapi.helper.ProfileGenerator;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.PeriodGenerator;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.model.reportingapi.users.userspercountry.UsersPerCountryDto;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Reporting Api - GET /v1/follows/football/{competitionId} endpoint happy path tests")
public class GetTypeOfUsersFollowingCompTests extends ReportingApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-772")})
  @DisplayName("Verify getting type of users following a competition")
  public void getFollowsByComp() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var fromDate = dateTriplet.getFrom();
    var toDate = dateTriplet.getTo();

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var compId = PREMIER_LEAGUE_COMP_ID;
    var countryDto = ProfileGenerator.COUNTRY_DTO_BG;

    var compFollowPair = Pair.of(compId, new AtomicInteger());

    dates.stream().parallel().forEach(date -> {
      var profileCount = 3;
      var profiles = ProfileGenerator.generateProfiles(profileCount,
          List.of(
              generateFootballInterest(ApiConstants.ProfileApi.Interest.Football.COMPETITION, true,
                  compId)), countryDto);
      InsertBigQData.generateProfileEvents(profiles,
          convertLocalDateToDateTime(date), convertLocalDateToDateTime(date));
      compFollowPair.getRight().getAndAdd(profileCount);
    });

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        FollowsByCompEndpoint.getFollowsByComp(compId, fromDate.toString(), toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA))
        .body("data." + BREAKDOWN_PROP, hasSize(1))
        .body("data." + BREAKDOWN_PROP + "[0]." + ID_PROP, equalTo(countryDto.getId()))
        .body("data." + BREAKDOWN_PROP + "[0]." + NAME_PROP, equalTo(countryDto.getName()))
        .body("data." + BREAKDOWN_PROP + "[0]." + USERS_PROP + "." + ALL_PROP,
            equalTo(Integer.valueOf(String.valueOf(compFollowPair.getRight()))));

    var followsPerComp = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerComp.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerComp.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerComp.getMeta().getGroupedBy(), equalTo(GroupBy.COUNTRY));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify events with same profile id are not counted multiple times when getting type of users following a competition")
  public void getFollowsByCompSameProfileId() throws HttpException, InterruptedException {

    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var fromDate = dateTriplet.getFrom();
    var toDate = dateTriplet.getTo();

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var compId = PREMIER_LEAGUE_COMP_ID;
    var countryDto = ProfileGenerator.COUNTRY_DTO_BG;

    var profileId = UUID.randomUUID().toString();
    var profileCreatedAt = convertLocalDateToDateTime(dates.get(0));

    dates.stream().parallel().forEach(date -> {
      var profile = ProfileGenerator.generateProfile(profileId, countryDto, List.of(
          generateFootballInterest(ApiConstants.ProfileApi.Interest.Football.COMPETITION, true,
              compId)), ProfileData.Profile.Gender.UNSPECIFIED);


      InsertBigQData.generateProfileEvent(profile, profileCreatedAt, convertLocalDateToDateTime(date));
    });

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        FollowsByCompEndpoint.getFollowsByComp(compId, fromDate.toString(), toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA))
        .body("data." + BREAKDOWN_PROP, hasSize(1))
        .body("data." + BREAKDOWN_PROP + "[0]." + ID_PROP, equalTo(countryDto.getId()))
        .body("data." + BREAKDOWN_PROP + "[0]." + NAME_PROP, equalTo(countryDto.getName()))
        .body("data." + BREAKDOWN_PROP + "[0]." + USERS_PROP + "." + ALL_PROP,
            equalTo(1));

    var followsPerComp = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerComp.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerComp.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerComp.getMeta().getGroupedBy(), equalTo(GroupBy.COUNTRY));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns type of users following a competition data gathered only from the specified date range")
  public void getFollowsByCompOutsideDateRange() throws HttpException, InterruptedException {
    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var fromDate = dateTriplet.getFrom();
    var toDate = dateTriplet.getTo();
    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);
    var compId = PREMIER_LEAGUE_COMP_ID;
    var countryDto = ProfileGenerator.COUNTRY_DTO_BG;
    var compFollowPair = Pair.of(compId, new AtomicInteger());

    dates.stream().parallel().forEach(date -> {
      var profileCount = 3;
      var profiles = ProfileGenerator.generateProfiles(profileCount,
          List.of(
              generateFootballInterest(ApiConstants.ProfileApi.Interest.Football.COMPETITION, true,
                  compId)), countryDto);

      InsertBigQData.generateProfileEvents(profiles,
          convertLocalDateToDateTime(date), convertLocalDateToDateTime(date));
      compFollowPair.getRight().getAndAdd(profileCount);
    });

    // Generate events before from date
    var profiles = ProfileGenerator.generateProfiles(2,
        List.of(
            generateFootballInterest(ApiConstants.ProfileApi.Interest.Football.COMPETITION, true,
                compId)), countryDto);

    InsertBigQData.generateProfileEvents(profiles,
        convertLocalDateToDateTime(dates.get(0).minusDays(7)),
        convertLocalDateToDateTime(dates.get(0).minusDays(7)));

    // Generate events after to date
    profiles = ProfileGenerator.generateProfiles(2,
        List.of(
            generateFootballInterest(ApiConstants.ProfileApi.Interest.Football.COMPETITION, true,
                compId)), countryDto);

    InsertBigQData.generateProfileEvents(profiles,
        convertLocalDateToDateTime(dates.get(dates.size() - 1).plusDays(7)),
        convertLocalDateToDateTime(dates.get(dates.size() - 1).plusDays(7)));

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        FollowsByCompEndpoint.getFollowsByComp(compId, fromDate.toString(), toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_FOLLOWS_BY_COMP_ID_FOOTBALL_SCHEMA))
        .body("data." + BREAKDOWN_PROP, hasSize(1))
        .body("data." + BREAKDOWN_PROP + "[0]." + ID_PROP, equalTo(countryDto.getId()))
        .body("data." + BREAKDOWN_PROP + "[0]." + NAME_PROP, equalTo(countryDto.getName()))
        .body("data." + BREAKDOWN_PROP + "[0]." + USERS_PROP + "." + ALL_PROP,
            equalTo(Integer.valueOf(String.valueOf(compFollowPair.getRight()))));


    var followsPerComp = response.as(UsersPerCountryDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerComp.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerComp.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerComp.getMeta().getGroupedBy(), equalTo(GroupBy.COUNTRY));
  }
}
