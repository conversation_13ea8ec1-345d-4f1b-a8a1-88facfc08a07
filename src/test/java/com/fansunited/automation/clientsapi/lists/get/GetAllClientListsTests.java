package com.fansunited.automation.clientsapi.lists.get;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ManageListsEndpoint;
import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.ListTypes;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Client Api - GET /v1/clients/{client_id}/lists endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetAllClientListsTests extends ClientApiBaseTest {
  private static final List<String> expectedListIds = new ArrayList<>();

  @BeforeAll
  public static void createLists() throws Exception {

    var dynamicListId = createList(ListTypes.DYNAMIC).jsonPath().getString("data.id");
    var customListId = createList(ListTypes.CUSTOM).jsonPath().getString("data.id");
    expectedListIds.add(dynamicListId);
    expectedListIds.add(customListId);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify that retrieving all lists via the API works as expected")
  public void getAllClientListsTest() throws HttpException {
    List<String> actualListIds = new ArrayList<>();
    String startAfterId = null;
    do {
      var response =
          ManageListsEndpoint.getClientLists(
              startAfterId,
              true,
              FANS_UNITED_CLIENTS,
              ADMIN_USER,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON);

      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .contentType(ContentType.JSON)
          .body("data", is(notNullValue()));

      currentTestResponse.set(response);
      actualListIds.addAll(response.getBody().jsonPath().getList("data.id"));

      startAfterId = response.jsonPath().getString("meta.pagination.next_page_starts_after");

    } while (startAfterId != null);

    assertThat(
        "Actual list does not contain all expected IDs",
        expectedListIds,
        everyItem(is(in(actualListIds))));
  }
}
