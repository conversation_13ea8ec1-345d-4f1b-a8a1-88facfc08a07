package com.fansunited.automation.clientsapi.staff.get;

import static com.fansunited.automation.constants.ApiErrorCodes.ClientErrorCodes.CAN_NOT_GET_STAFF_INFO;
import static com.fansunited.automation.constants.ApiErrorCodes.ClientErrorCodes.INVALID_CLIENT_ID_STATUS;
import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.SPECIAL_CHARACTER;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.WHITE_SPACES;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("GET /v1/clients/{client_id}/staff/{user_id} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetStaffByClientAndUserIdValidationTests extends BaseTest {

  @ParameterizedTest(name = "Verify clients cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getStaffByIdWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        StaffMemberEndpoint.getStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting staff members with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getStaffByIdWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    var createStaff =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CreateStaffMember.builder()
                .email(new Faker().internet().emailAddress())
                .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
                .pass(new Faker().internet().password())
                .roles(List.of("client_editor"))
                .build(), CLIENT_AUTOMATION_ID);

    createStaff
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var userId = createStaff.then().extract().body().jsonPath().getString("data.id");

    var response = StaffMemberEndpoint.getStaffMemberByUserAndClientId(
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        CLIENT_AUTOMATION_ID, userId, AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify staff by client id and user id endpoint are still fetched in JSON format if content type is NOT specified")
  public void getStaffByIdWithoutSpecifyingContentType() throws HttpException {

    var createStaff =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CreateStaffMember.builder()
                .email(new Faker().internet().emailAddress())
                .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
                .pass(new Faker().internet().password())
                .roles(List.of("client_editor"))
                .build(), CLIENT_AUTOMATION_ID);

    createStaff
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var userId = createStaff.then().extract().body().jsonPath().getString("data.id");

    FirebaseHelper.getUserTokenMap().clear();

    var response = StaffMemberEndpoint.getStaffMemberByUserAndClientId(
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        CLIENT_AUTOMATION_ID, userId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body("data", not(empty()));
  }

  @ParameterizedTest(name = "Verify that can get staff members with invalid client id : {arguments} but user id is valid")
  @Tag(REGRESSION)
  @ValueSource(strings = {INVALID_CLIENT_ID, SPECIAL_CHARACTER, WHITE_SPACES})
  public void getStaffMemberValidationTestsWithInvalidClientId(String clientId)
      throws HttpException {

    var createStaffRequest = CreateStaffMember.builder()
        .email(new Faker().internet().emailAddress())
        .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
        .pass(new Faker().internet().password())
        .roles(List.of("client_editor"))
        .build();

    var response =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            createStaffRequest, CLIENT_AUTOMATION_ID);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    var userId = response.then().extract().body().jsonPath().getString("data.id");

    FirebaseHelper.getUserTokenMap()
        .clear(); // Same user is use in fans united clients and fans united automation project, hence it will reuse wrong token

    var getStaffMembers =
        StaffMemberEndpoint.getStaffMemberByUserAndClientId(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, clientId, userId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(getStaffMembers);

    switch (clientId) {
      case INVALID_CLIENT_ID, SPECIAL_CHARACTER, WHITE_SPACES ->
          getStaffMembers.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
        default -> throw new IllegalArgumentException("Unexpected client_id: " + clientId);
    }
  }

  @ParameterizedTest(name = "Verify that can get staff members with invalid user id : {arguments} but client id is valid")
  @Tag(REGRESSION)
  @ValueSource(strings = {INVALID_CLIENT_ID, WHITE_SPACES, SPECIAL_CHARACTER})
  public void getStaffMemberValidationTestsWithInvalidUserId(String userId)
      throws HttpException {

    var createStaffRequest = CreateStaffMember.builder()
        .email(new Faker().internet().emailAddress())
        .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
        .pass(new Faker().internet().password())
        .roles(List.of("client_editor"))
        .build();

    var response =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            createStaffRequest, CLIENT_AUTOMATION_ID);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var getStaffMembers =
        StaffMemberEndpoint.getStaffMemberByUserAndClientId(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, CLIENT_AUTOMATION_ID,
            userId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(getStaffMembers);

    switch (userId) {
      case SPECIAL_CHARACTER, WHITE_SPACES ->
          getStaffMembers
              .then()
              .assertThat()
              .statusCode(HttpStatus.SC_BAD_REQUEST)
              .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
      case INVALID_CLIENT_ID ->
          getStaffMembers
              .then()
              .assertThat()
              .statusCode(HttpStatus.SC_NOT_FOUND)
              .body("error.status", equalTo(CAN_NOT_GET_STAFF_INFO));
      default -> throw new IllegalArgumentException("Unexpected client_id: " + userId);
    }
  }

  @ParameterizedTest(name = "Verify that can get staff members with invalid client id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {INVALID_CLIENT_ID, WHITE_SPACES, SPECIAL_CHARACTER})
  public void getClientByIdValidationTestsForPathParams(String clientId)
      throws HttpException {

    var getStaffMembers =
        StaffMemberEndpoint.getStaffMemberByUserAndClientId(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, clientId, "userId",
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(getStaffMembers);

    switch (clientId) {
      case SPECIAL_CHARACTER, WHITE_SPACES ->
          getStaffMembers
              .then()
              .assertThat()
              .statusCode(HttpStatus.SC_BAD_REQUEST)
              .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
      case INVALID_CLIENT_ID ->
          getStaffMembers
              .then()
              .assertThat()
              .statusCode(HttpStatus.SC_BAD_REQUEST)
              .body("error.status", equalTo(INVALID_CLIENT_ID_STATUS));
      default -> throw new IllegalArgumentException("Unexpected clientId: " + clientId);
    }
  }
}
