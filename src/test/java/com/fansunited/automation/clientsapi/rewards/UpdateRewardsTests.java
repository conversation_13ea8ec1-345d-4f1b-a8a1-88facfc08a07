package com.fansunited.automation.clientsapi.rewards;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_REWARDS;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.ActivityActionType;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.Point;
import com.fansunited.automation.model.clientapi.features.response.Rewards;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Client Api - GET /v1/clients/{clientId}/feature/external_points endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateRewardsTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1843")})
  @DisplayName("Verify client can update external_points")
  public void updatesRewards() throws HttpException {

    var rewardBody =
        Rewards.builder()
            .enabled(true)
            .points(
                List.of(
                    Point.builder()
                        .points(20)
                        .id(ActivityActionType.DISLIKE.getValue())
                        .maxCount(23456)
                        .build(),
                    Point.builder()
                        .points(4)
                        .id(ActivityActionType.LIKE.getValue())
                        .maxCount(23456)
                        .build()))
            .build();

    var updateLoyaltyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            rewardBody,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_REWARDS);

    currentTestResponse.set(updateLoyaltyFeaturesResponse);

    updateLoyaltyFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);
  }
}
