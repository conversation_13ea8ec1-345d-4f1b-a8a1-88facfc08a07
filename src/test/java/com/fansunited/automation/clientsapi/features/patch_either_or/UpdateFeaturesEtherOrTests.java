package com.fansunited.automation.clientsapi.features.patch_either_or;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_CLASSIC_QUIZ;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.BasicObject;
import com.fansunited.automation.model.clientapi.features.response.EitherOrFeature;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Client Api - GET /v1/clients/{clientId}/feature/classic_quiz endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFeaturesEtherOrTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update classic_quiz features")
  public void clientCanUpdateQuizFeatures() throws HttpException {

    String idTest = "id_test";
    String labelTest = "label_test";

    EitherOrFeature quizFeatureBody =
        EitherOrFeature.builder()
            .types(List.of(BasicObject.builder().label(labelTest).id(idTest).build()))
            .build();

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            quizFeatureBody,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_CLASSIC_QUIZ);

    updateQuizFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.types[0].id", equalTo(idTest))
        .body("data.types[0].label", equalTo(labelTest));

    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON,
                FANS_UNITED_CLIENTS);

    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .body("data.classic_quiz.types[0].id", equalTo(idTest))
        .body("data.classic_quiz.types[0].label", equalTo(labelTest));
  }
}
