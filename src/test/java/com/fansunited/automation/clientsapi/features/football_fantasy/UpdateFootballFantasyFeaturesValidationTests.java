package com.fansunited.automation.clientsapi.features.football_fantasy;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_FANTASY_GAME;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static java.util.List.of;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.BasicObject;
import com.fansunited.automation.model.clientapi.features.response.ClassicQuizFeature;
import com.fansunited.automation.model.clientapi.features.response.Coefficients;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.Multipliers;
import com.fansunited.automation.model.clientapi.features.response.FootballFantasyFeature;
import com.fansunited.automation.model.clientapi.features.response.Players;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName(
    "Client Api - PATCH /v1/clients/{clientId}/feature/classic_quiz endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFootballFantasyFeaturesValidationTests extends ClientApiBaseTest {

  public static String label = new Faker().animal().toString();

  public static String id = new Faker().idNumber().toString();

  @Test
  @DisplayName("Verify that billing manager can not update football fantasy features")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void billingManagerCantUpdateFantasyFeatures() throws HttpException {

    FootballFantasyFeature fantasyFeature =
        FootballFantasyFeature.builder()
            .enabled(true)
            .coefficients(Coefficients.builder().assists(1).caughtBall(1).build())
            .add_points_to_profileTotal(true)
            .competitions_whitelist(List.of(PREMIER_LEAGUE_COMP_ID, BUNDESLIGA_COMP_ID))
            .players(Players.builder().maxDefenders(1).minGoalkeepers(1).build())
            .build();

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            BILLING_MANAGER_EMAIL,
            fantasyFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_FANTASY_GAME);

    currentTestResponse.set(updateFantasyFeaturesResponse);

    updateFantasyFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @DisplayName("Check whitelist validation mechanism")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void checkWhitelistValidationMechanism() throws HttpException {

    FootballFantasyFeature fantasyFeature =
        FootballFantasyFeature.builder()
            .enabled(true)
            .coefficients(Coefficients.builder().assists(1).caughtBall(1).build())
            .add_points_to_profileTotal(true)
            .multipliers(Multipliers.builder()
                    .captain(1)
                .viceCaptain(1)
                .build())
            .competitions_whitelist(of(INVALID_ID))
            .players(
                Players.builder()
                    .total(11)
                    .minDefenders(1)
                    .maxDefenders(5)
                    .minGoalkeepers(1)
                    .maxGoalkeepers(1)
                    .minMidfielders(1)
                    .maxMidfielders(5)
                    .minStrikers(1)
                    .maxStrikers(3)
                    .build())
            .build();

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            fantasyFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_FANTASY_GAME);

    currentTestResponse.set(updateFantasyFeaturesResponse);

    updateFantasyFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body(
            "error.message",
            equalTo("One or more competitions whitelist Id's are not in the default Id's."));
  }

  @ParameterizedTest(name = "Verify client can not {arguments} action")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  @EmptySource
  public void clientWithWrongIdCanNotUpdateFantasyFeatures(String invalidData)
      throws HttpException {

    FootballFantasyFeature fantasyFeature =
        FootballFantasyFeature.builder()
            .enabled(true)
            .coefficients(Coefficients.builder().assists(1).caughtBall(1).build())
            .add_points_to_profileTotal(true)
            .competitions_whitelist(List.of(invalidData))
            .players(Players.builder().maxDefenders(1).minGoalkeepers(1).build())
            .build();

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            fantasyFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_FANTASY_GAME);

    currentTestResponse.set(updateFantasyFeaturesResponse);

    updateFantasyFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update fantasy features with no body ")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void cantUpdateFantasyFeaturesWithNoBody() throws HttpException {

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            "null",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_FANTASY_GAME);

    currentTestResponse.set(updateFantasyFeaturesResponse);

    updateFantasyFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update fantasy features with not specified content type")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void cantUpdateFantasyFeaturesWithNotSpecifiedContentType() throws HttpException {

    ClassicQuizFeature quizFeature =
        ClassicQuizFeature.builder()
            .types(of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            quizFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            FEATURES_FANTASY_GAME);

    currentTestResponse.set(updateFantasyFeaturesResponse);

    updateFantasyFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @ParameterizedTest(
      name = "Verify API returns BAD_REQUEST when update fantasy features. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  public void updateFantasyFeaturesInvalidClientId(String clientId) throws HttpException {

    ClassicQuizFeature quizFeature =
        ClassicQuizFeature.builder()
            .types(of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            quizFeature,
            clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_FANTASY_GAME);

    currentTestResponse.set(updateFantasyFeaturesResponse);

    ErrorValidator.validateErrorResponse(
        updateFantasyFeaturesResponse, List.of(HttpStatus.SC_BAD_REQUEST));
  }
}
