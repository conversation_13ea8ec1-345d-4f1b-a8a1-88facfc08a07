package com.fansunited.automation.clientsapi.features.preferences;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_PROFILE_PREFERENCES;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.ProfilePreferencesHelper.createDefaultProfilePreferencesFeature;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.ProfilePreferencesFeature;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName(
    "Client Api - PATCH /v1/clients/{clientId}/feature/profile_preferences endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateProfilePreferencesValidationTests extends ClientApiBaseTest {

  public static String label = new Faker().animal().toString();

  public static String id = new Faker().idNumber().toString();

  public final Faker faker = new Faker();

  @Test
  @DisplayName("Verify that billing manager can not update profile preferences feature")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void billingManagerCantUpdatePreferencesFeatures() throws HttpException {

    ProfilePreferencesFeature preferencesFeature = createDefaultProfilePreferencesFeature(true);

    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            BILLING_MANAGER_EMAIL,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);
    currentTestResponse.set(updatePreferencesFeaturesResponse);

    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @DisplayName("Verify that can not update profile preferences feature with wrong body")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void wrongBodyPreferencesFeature() throws HttpException {

    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            "{null}",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);
    currentTestResponse.set(updatePreferencesFeaturesResponse);

    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update profile preferences feature with null body")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void nullBodyPreferencesFeature() throws HttpException {

    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            "",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);
    currentTestResponse.set(updatePreferencesFeaturesResponse);

    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update profile preferences feature with null body")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void nullContentTypePreferencesFeature() throws HttpException {

    ProfilePreferencesFeature preferencesFeature = createDefaultProfilePreferencesFeature(true);

    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            FEATURES_PROFILE_PREFERENCES);
    currentTestResponse.set(updatePreferencesFeaturesResponse);

    updatePreferencesFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update profile preferences feature enabled status")
  public void clientCanUpdateProfilePreferencesEnabledStatus(boolean enabled) throws HttpException {

    ProfilePreferencesFeature preferencesFeature = createDefaultProfilePreferencesFeature(enabled);

    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);

    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    response
        .then()
        .assertThat()
        .body("data", notNullValue())
        .body("data.profile_preferences", notNullValue());

    response.then().assertThat().body("data.profile_preferences.enabled", equalTo(enabled));
  }
}
