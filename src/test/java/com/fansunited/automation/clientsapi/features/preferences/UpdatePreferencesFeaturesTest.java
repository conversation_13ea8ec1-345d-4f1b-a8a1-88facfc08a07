package com.fansunited.automation.clientsapi.features.preferences;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_PROFILE_PREFERENCES;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.ProfilePreferencesHelper.*;
import static org.hamcrest.Matchers.*;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.ProfilePreferencesFeature;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName(
    "Client Api - PATCH /v1/clients/{clientId}/feature/profile_preferences endpoint happy path tests")
public class UpdatePreferencesFeaturesTest extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update profile preferences feature")
  public void clientCanUpdateProfilePreferencesFeatures() throws HttpException {

    // Create a profile preferences feature with default values
    ProfilePreferencesFeature preferencesFeature = createDefaultProfilePreferencesFeature(true);

    // Update the profile preferences feature
    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);

    // Verify the update was successful
    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Get the updated features
    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS);

    // Verify the response status code
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.profile_preferences.enabled", equalTo(true))
        .body("data.profile_preferences.categories.size()", greaterThanOrEqualTo(2))
        .body("data.profile_preferences.categories[0].id", equalTo(FOOD_CATEGORY_ID))
        .body("data.profile_preferences.categories[1].id", equalTo(BRAND_CATEGORY_ID))
        .body("data.profile_preferences.preferences.size()", greaterThanOrEqualTo(2))
        .body("data.profile_preferences.preferences[0].id", equalTo(PIZZA_PREFERENCE_ID))
        .body("data.profile_preferences.preferences[1].id", equalTo(NIKE_PREFERENCE_ID))
        .body("data.profile_preferences.categories[0].display_name", notNullValue())
        .body("data.profile_preferences.categories[1].display_name", notNullValue())
        .body("data.profile_preferences.preferences[0].display_name", notNullValue())
        .body("data.profile_preferences.preferences[1].display_name", notNullValue());
  }
}
