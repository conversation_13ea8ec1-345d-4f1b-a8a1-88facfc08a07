package com.fansunited.automation.clientsapi.features.preferences;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_PROFILE_PREFERENCES;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.ProfilePreferencesHelper.*;
import static org.hamcrest.Matchers.*;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.ProfilePreferencesFeature;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName(
    "Client Api - PATCH /v1/clients/{clientId}/feature/profile_preferences endpoint happy path tests")
public class UpdatePreferencesFeaturesTest extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update profile preferences feature")
  public void clientCanUpdateProfilePreferencesFeatures() throws HttpException {

    // Create a profile preferences feature with default values
    ProfilePreferencesFeature preferencesFeature = createDefaultProfilePreferencesFeature(true);

    // Update the profile preferences feature
    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);

    // Verify the update was successful
    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Get the updated features
    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS);

    // Verify the response status code
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Verify the preferences feature exists
    response
        .then()
        .assertThat()
        .body("data", notNullValue())
        .body("data.preferences", notNullValue());

    // Verify the preferences feature is enabled
    response
        .then()
        .assertThat()
        .body("data.preferences.enabled", equalTo(true));

    // Verify the preferences categories exist
    response
        .then()
        .assertThat()
        .body("data.preferences.categories", notNullValue())
        .body("data.preferences.categories.size()", greaterThanOrEqualTo(2));

    // Verify the food category exists
    response
        .then()
        .assertThat()
        .body("data.preferences.categories.find { it.id == '" + FOOD_CATEGORY_ID + "' }", notNullValue())
        .body("data.preferences.categories.find { it.id == '" + FOOD_CATEGORY_ID + "' }.displayName", notNullValue());

    // Verify the brand category exists
    response
        .then()
        .assertThat()
        .body("data.preferences.categories.find { it.id == '" + BRAND_CATEGORY_ID + "' }", notNullValue())
        .body("data.preferences.categories.find { it.id == '" + BRAND_CATEGORY_ID + "' }.displayName", notNullValue());

    // Verify the preferences list exists
    response
        .then()
        .assertThat()
        .body("data.preferences.preferences", notNullValue())
        .body("data.preferences.preferences.size()", greaterThanOrEqualTo(2));

    // Verify the pizza preference exists
    response
        .then()
        .assertThat()
        .body("data.preferences.preferences.find { it.id == '" + PIZZA_PREFERENCE_ID + "' }", notNullValue())
        .body("data.preferences.preferences.find { it.id == '" + PIZZA_PREFERENCE_ID + "' }.displayName", notNullValue())
        .body("data.preferences.preferences.find { it.id == '" + PIZZA_PREFERENCE_ID + "' }.categories", hasItems(FOOD_CATEGORY_ID, BRAND_CATEGORY_ID));

    // Verify the nike preference exists
    response
        .then()
        .assertThat()
        .body("data.preferences.preferences.find { it.id == '" + NIKE_PREFERENCE_ID + "' }", notNullValue())
        .body("data.preferences.preferences.find { it.id == '" + NIKE_PREFERENCE_ID + "' }.displayName", notNullValue())
        .body("data.preferences.preferences.find { it.id == '" + NIKE_PREFERENCE_ID + "' }.categories", hasItems(FOOD_CATEGORY_ID, BRAND_CATEGORY_ID));
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update profile preferences feature enabled status")
  public void clientCanUpdateProfilePreferencesEnabledStatus(boolean enabled) throws HttpException {

    // Create a profile preferences feature with specified enabled status
    ProfilePreferencesFeature preferencesFeature = createDefaultProfilePreferencesFeature(enabled);

    // Update the profile preferences feature
    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);

    // Verify the update was successful
    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Get the updated features
    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS);

    // Verify the response status code
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Verify the preferences feature exists
    response
        .then()
        .assertThat()
        .body("data", notNullValue())
        .body("data.profile_preferences", notNullValue());

    // Verify the preferences feature has the correct enabled status
    response
        .then()
        .assertThat()
        .body("data.profile_preferences.enabled", equalTo(enabled));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update profile preferences feature with custom values")
  public void clientCanUpdateProfilePreferencesWithCustomValues() throws HttpException {
    // Create custom category and preference IDs
    String customCategoryId1 = "sports";
    String customCategoryId2 = "teams";
    String customPreferenceId1 = "football";
    String customPreferenceId2 = "basketball";

    // Create a profile preferences feature with custom values
    ProfilePreferencesFeature preferencesFeature = createProfilePreferencesFeature(
        true, customPreferenceId1, customPreferenceId2, customCategoryId1, customCategoryId2);

    // Update the profile preferences feature
    var updatePreferencesFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            preferencesFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_PROFILE_PREFERENCES);

    // Verify the update was successful
    updatePreferencesFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Get the updated features
    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS);

    // Verify the response status code
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Verify the preferences feature exists
    response
        .then()
        .assertThat()
        .body("data", notNullValue())
        .body("data.preferences", notNullValue());

    // Verify the custom categories exist
    response
        .then()
        .assertThat()
        .body("data.preferences.categories.find { it.id == '" + customCategoryId1 + "' }", notNullValue())
        .body("data.preferences.categories.find { it.id == '" + customCategoryId2 + "' }", notNullValue());

    // Verify the custom preferences exist
    response
        .then()
        .assertThat()
        .body("data.preferences.preferences.find { it.id == '" + customPreferenceId1 + "' }", notNullValue())
        .body("data.preferences.preferences.find { it.id == '" + customPreferenceId2 + "' }", notNullValue());

    // Verify the custom preferences have the correct categories
    response
        .then()
        .assertThat()
        .body("data.preferences.preferences.find { it.id == '" + customPreferenceId1 + "' }.categories", hasItems(customCategoryId1, customCategoryId2))
        .body("data.preferences.preferences.find { it.id == '" + customPreferenceId2 + "' }.categories", hasItems(customCategoryId1, customCategoryId2));
  }
}
