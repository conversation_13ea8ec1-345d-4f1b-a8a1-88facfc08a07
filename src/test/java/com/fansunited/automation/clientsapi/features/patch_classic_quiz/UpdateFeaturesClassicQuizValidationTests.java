package com.fansunited.automation.clientsapi.features.patch_classic_quiz;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_CLASSIC_QUIZ;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static java.util.List.of;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.BasicObject;
import com.fansunited.automation.model.clientapi.features.response.ClassicQuizFeature;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName(
    "Client Api - GET /v1/clients/{clientId}/feature/classic_quiz endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFeaturesClassicQuizValidationTests extends ClientApiBaseTest {

  public static String label = new Faker().animal().toString();

  public static String id = new Faker().idNumber().toString();

  @Test
  @DisplayName("Verify that billing manager can not update quiz features")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void billingManagerCantUpdateQuizFeatures() throws HttpException {

    ClassicQuizFeature quizFeature =
        ClassicQuizFeature.builder()
            .types(of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            BILLING_MANAGER_EMAIL,
            quizFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_CLASSIC_QUIZ);

    currentTestResponse.set(updateQuizFeaturesResponse);

    updateQuizFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify client can not {arguments} action")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  public void clientByIdUpdatesIllegallyActionWeightValue(String invalidData) throws HttpException {

    ClassicQuizFeature quizFeature =
        ClassicQuizFeature.builder()
            .types(of(BasicObject.builder().label(invalidData).id(invalidData).build()))
            .build();

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            quizFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_CLASSIC_QUIZ);

    currentTestResponse.set(updateQuizFeaturesResponse);

    updateQuizFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update quiz features with no body ")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void cantUpdateQuizFeaturesWithNoBody() throws HttpException {

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            "null",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_CLASSIC_QUIZ);

    currentTestResponse.set(updateQuizFeaturesResponse);

    updateQuizFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update quiz features with not specified content type")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void cantUpdateQuizFeaturesWithNotSpecifiedContentType() throws HttpException {

    ClassicQuizFeature quizFeature =
        ClassicQuizFeature.builder()
            .types(of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            quizFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            FEATURES_CLASSIC_QUIZ);

    currentTestResponse.set(updateQuizFeaturesResponse);

    updateQuizFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @ParameterizedTest(
      name = "Verify API returns BAD_REQUEST when update quiz features. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  public void updateQuizFeaturesInvalidClientId(String clientId) throws HttpException {

    ClassicQuizFeature quizFeature =
        ClassicQuizFeature.builder()
            .types(of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            quizFeature,
            clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_CLASSIC_QUIZ);

    currentTestResponse.set(updateQuizFeaturesResponse);

    ErrorValidator.validateErrorResponse(updateQuizFeaturesResponse, List.of(HttpStatus.SC_BAD_REQUEST));
  }
}
