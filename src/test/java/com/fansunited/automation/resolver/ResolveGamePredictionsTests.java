package com.fansunited.automation.resolver;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.OTHER_VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.PREDICTIONS_MADE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.POINTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.TestGroups.STAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGameRequestForTopXGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createMatchQuizGameForMarkets;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.updateGameStatusInFirebase;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createMatchQuizPredictionForGameWithSingleMarketOwnGoal;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionFixtureForAdvancedCorrectScore;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.setInvalidPredictionsForAllMarkets;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.setPartiallyCorrectPredictionsForAllMarkets;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.setValidPredictionsForAllMarkets;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;
import static com.fansunited.automation.core.resolver.enums.TimelineEventType.OWN_GOAL;
import static com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues.GenerateMatchTimeLine.Goal_ValidPlayerId_10_TRUE_1;
import static com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues.GenerateMatchTimeLine.Goal_ValidPlayerId_15_FALSE_2;
import static com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues.GenerateMatchTimeLine.Goal_ValidPlayerId_20_TRUE_3;
import static com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues.GenerateMatchTimeLine.Goal_ValidPlayerId_51_TRUE_4;
import static com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues.GenerateMatchTimeLine.Goal_ValidPlayerId_55_TRUE_5;
import static com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues.GenerateMatchTimeLine.Goal_ValidPlayerId_80_TRUE_6;
import static com.fansunited.automation.helpers.BigQueryHelper.waitDataToBeSavedInMySQL;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGamePredictionFixtureOutcomeAndStatusToUpdate;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGameStatusToUpdate;
import static com.fansunited.automation.helpers.WaitHelper.waitGameStatusToBeUpdated;
import static com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper.USER_RANKING_GAME_MV;
import static com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.CLOSED;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.OPEN;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.SETTLED;
import static com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket.FantasyMarkets.PLAYER_PERFORMANCE;
import static com.fansunited.automation.validators.RedisValidator.validateGameDoesNotExistInActiveGames;
import static com.fansunited.automation.validators.RedisValidator.validateMatchesDoesNotExistInActiveMatches;
import static java.time.ZoneOffset.UTC;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.PredictionStatusResolution;
import com.fansunited.automation.core.apis.loyaltyapi.GameLeaderboardsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.MatchStatsGenerator;
import com.fansunited.automation.core.resolver.MatchTimelineGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.enums.TimelineEventType;
import com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.predictionapi.predictions.AdvancedPredictions;
import com.fansunited.automation.validators.RedisValidator;
import com.fansunited.automation.validators.ResolverValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Resolver - game predictions tests")
public class ResolveGamePredictionsTests extends ResolverBase {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Verify prediction for MATCH_QUIZ is resolved to WON when outcome is guessed")
  public void resolvePredictionMatchQuiz() throws HttpException, InterruptedException {

    var markets =
        PredictionMarket.getValidMarkets().stream().toList();

    var match = MatchGenerator.generateMatchesInFuture(1, 2).get(0);

    setupMatchTimelineForMatchQuizGame(
        match,
        TimelineMatchInputValues.CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010,
        List.of(
            Goal_ValidPlayerId_10_TRUE_1,
            Goal_ValidPlayerId_15_FALSE_2,
            Goal_ValidPlayerId_20_TRUE_3,
            Goal_ValidPlayerId_51_TRUE_4,
            Goal_ValidPlayerId_55_TRUE_5,
            Goal_ValidPlayerId_80_TRUE_6));

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(),
                OPEN,
                markets,
                match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setPredictionToStatus(
        PredictionStatusResolution.RESOLVE_TO_WON, markets, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var prediction = PredictionsEndpoint.createPrediction(createPredictionRequest);

    prediction.then().assertThat().statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Resolver.resolve();
    Thread.sleep(120000);
    Resolver.resolve();
    ResolverValidator.validateMatchQuizGamePredictionsAreResolvedToWon(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);

    validateMatchesDoesNotExistInActiveMatches(List.of(match));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Verify prediction for MATCH_QUIZ is resolved to LOST when outcome is NOT guessed")
  public void resolvePredictionMatchQuizNotGuessed() throws HttpException, InterruptedException {

    var markets =
        PredictionMarket.getValidMarkets().stream()
            .filter(market -> !market.equals(PLAYER_PERFORMANCE))
            .toList();
    var match = MatchGenerator.generateMatchesInFuture(1, 2).get(0);

    setupMatchTimelineForMatchQuizGame(
        match,
        TimelineMatchInputValues.CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010,
        List.of(
            Goal_ValidPlayerId_10_TRUE_1,
            Goal_ValidPlayerId_15_FALSE_2,
            Goal_ValidPlayerId_20_TRUE_3,
            Goal_ValidPlayerId_51_TRUE_4,
            Goal_ValidPlayerId_55_TRUE_5,
            Goal_ValidPlayerId_80_TRUE_6));

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(),
                OPEN,
                markets,
                match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setPredictionToStatus(
        PredictionStatusResolution.RESOLVE_TO_LOST, markets, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Resolver.resolve();

    ResolverValidator.validateMatchQuizGamePredictionsAreResolvedToLost(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);

    validateMatchesDoesNotExistInActiveMatches(List.of(match));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Verify all predictions for MATCH_QUIZ are resolved when match is finished")
  public void resolveAllPredictionsMatchQuiz()
      throws HttpException,
          InterruptedException,
          IOException,
          ExecutionException,
          FirebaseAuthException {

    var markets =
        PredictionMarket.getValidMarkets().stream()
            .filter(market -> !market.equals(PLAYER_PERFORMANCE))
            .toList();
    var match = MatchGenerator.generateMatchesInFuture(1, 2).get(0);

    setupMatchTimelineForMatchQuizGame(
        match,
        TimelineMatchInputValues.CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010,
        List.of(
            Goal_ValidPlayerId_10_TRUE_1,
            Goal_ValidPlayerId_15_FALSE_2,
            Goal_ValidPlayerId_20_TRUE_3,
            Goal_ValidPlayerId_51_TRUE_4,
            Goal_ValidPlayerId_55_TRUE_5,
            Goal_ValidPlayerId_80_TRUE_6));

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(),
                OPEN,
                markets,
                match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setPredictionToStatus(
        PredictionStatusResolution.RESOLVE_TO_WON, markets, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var users = createUsers(10);

    var predictionIdList = new ArrayList<String>();

    for (var user : users) {
      predictionIdList.add(
          PredictionsEndpoint.createPredictionForUser(createPredictionRequest, user.getEmail())
              .as(PredictionInstance.class)
              .getId());
    }

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Thread.sleep(3000);
    Resolver.resolve();
    Thread.sleep(3000);
    Resolver.resolve();

    for (var user : users) {
      ResolverValidator.validateMatchQuizGamePredictionsAreResolvedToWon(
          PredictionsEndpoint.getOwnPredictionsForUser(user.getEmail()), predictionFixtures);
    }

    validateMatchesDoesNotExistInActiveMatches(List.of(match));
    RedisValidator.validatePredictionsDoesNotExistInProcessingPredictionsSet(predictionIdList);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Verify prediction for TOP_X is resolved to WON when outcome is guessed")
  public void resolvePredictionTopX() throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsExtraTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsExtraTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(match.getGoalsFullTimeHome())
                    .goalsAway(match.getGoalsFullTimeAway())
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Resolver.resolve(matchList.size());

    ResolverValidator.validateTopXGamePredictionsAreResolvedToWon(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);
    validateGameDoesNotExistInActiveGames(gameInstance.getId());
    validateMatchesDoesNotExistInActiveMatches(matchList);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "Verify prediction for TOP_X is resolved correctly when matches finish one after the other")
  public void resolvePredictionTopXMatchesFinishedDifferentTime()
      throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(match.getGoalsFullTimeHome())
                    .goalsAway(match.getGoalsFullTimeAway())
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var resolvedMatches = new ArrayList<Match>();

    for (var match : matchList) {
      Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

      Thread.sleep(3000);
      Resolver.resolve();
      resolvedMatches.add(match);

      ResolverValidator.validateOnlyPredictionFixturesForFinishedMatchesInTopXGameAreResolved(
          PredictionsEndpoint.getOwnPredictions(), resolvedMatches,
          ResultOutcome.CORRECT);
    }

    ResolverValidator.validateTopXGamePredictionsAreResolvedToWon(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);
    validateGameDoesNotExistInActiveGames(gameInstance.getId());
    validateMatchesDoesNotExistInActiveMatches(matchList);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Verify prediction for TOP_X is resolved to LOST when outcome is NOT guessed")
  public void resolvePredictionTopXNotGuessed() throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) 4);
          match.setGoalsFullTimeAway((byte) 1);
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(3)
                    .goalsAway(3)
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Thread.sleep(3000);
    Resolver.resolve(matchList.size());

    ResolverValidator.validateTopXGamePredictionsAreResolvedToLost(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);
    validateGameDoesNotExistInActiveGames(gameInstance.getId());
    validateMatchesDoesNotExistInActiveMatches(matchList);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "Verify prediction for TOP_X is resolved to PARTIALLY CORRECT when outcome is partially guessed")
  public void resolvePredictionTopXPartiallyCorrect() throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) 4);
          match.setGoalsFullTimeAway((byte) 1);
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(3)
                    .goalsAway(1)
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Thread.sleep(3000);
    Resolver.resolve(matchList.size());

    ResolverValidator.validateGamePredictionsAreResolvedToPartiallyCorrect(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);

    validateMatchesDoesNotExistInActiveMatches(matchList);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(REMOTE_ONLY)})
  @DisplayName("Verify game statuses are properly updated - OPEN -> SETTLED")
  public void verifyGameStatusIsProperlyUpdated() throws HttpException, InterruptedException {

    var markets =
        PredictionMarket.getValidMarkets().stream()
            .filter(market -> !market.equals(PLAYER_PERFORMANCE))
            .toList();
    var match = MatchGenerator.generateMatch();

    setupMatchTimelineForMatchQuizGame(
        match,
        TimelineMatchInputValues.CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010,
        List.of(
            Goal_ValidPlayerId_10_TRUE_1,
            Goal_ValidPlayerId_15_FALSE_2,
            Goal_ValidPlayerId_20_TRUE_3,
            Goal_ValidPlayerId_51_TRUE_4,
            Goal_ValidPlayerId_55_TRUE_5,
            Goal_ValidPlayerId_80_TRUE_6));

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(), OPEN, markets, ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setValidPredictionsForAllMarkets(markets, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .log()
        .body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    waitGameStatusToBeUpdated(
        gameInstance, LIVE, 5, 240); // Wait for game status to be updated to live

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    // In order the test to pass locally update the wait to 240 sec
    waitGameStatusToBeUpdated(
        gameInstance, CLOSED, 5, 240); // Wait for game status to be updated to closed

    Resolver.resolve();
    waitGameStatusToBeUpdated(
        gameInstance, SETTLED, 5, 240); // Wait for game status to be updated to settled

    ResolverValidator.validateGamePredictionsAreResolvedToWon(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);

    validateMatchesDoesNotExistInActiveMatches(List.of(match));
    validateGameDoesNotExistInActiveGames(gameInstance.getId());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "Verify prediction for MATCH_QUIZ is resolved to PARTIALLY CORRECT when outcome is partially guessed")
  public void resolvePredictionMatchQuizPartiallyCorrect()
      throws HttpException, InterruptedException {

    var markets =
        PredictionMarket.getValidMarkets().stream()
            .filter(market -> !market.equals(PLAYER_PERFORMANCE))
            .toList();
    var match = MatchGenerator.generateMatchesInFuture(1, 2).get(0);

    setupMatchTimelineForMatchQuizGame(
        match,
        TimelineMatchInputValues.CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010,
        List.of(
            Goal_ValidPlayerId_10_TRUE_1,
            Goal_ValidPlayerId_15_FALSE_2,
            Goal_ValidPlayerId_20_TRUE_3,
            Goal_ValidPlayerId_51_TRUE_4,
            Goal_ValidPlayerId_55_TRUE_5,
            Goal_ValidPlayerId_80_TRUE_6));

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(),
                OPEN,
                markets,
                match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setPartiallyCorrectPredictionsForAllMarkets(markets, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Thread.sleep(3000);
    Resolver.resolve();
    Thread.sleep(3000);
    Resolver.resolve();

    PredictionsEndpoint.getOwnPredictions();

    ResolverValidator.validateMatchQuizGamePredictionsAreResolvedToPartiallyCorrect(
        PredictionsEndpoint.getOwnPredictions(), predictionFixtures);

    validateMatchesDoesNotExistInActiveMatches(List.of(match));
  }

  @ParameterizedTest(
      name =
          "Verify Advanced Correct Market prediction for MATCH_QUIZ is resolved correctly when : {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @EnumSource(value = AdvancedPredictions.class)
  public void resolvePredictionMatchQuizAdvancedCorrectScorePrediction(
      AdvancedPredictions advancedPrediction) throws HttpException, InterruptedException {

    var markets = List.of(PredictionMarket.CORRECT_SCORE_ADVANCED);
    var match = MatchGenerator.generateMatchesInFuture(1, 2).get(0);

    match.setTimeline(new ArrayList<>());
    match.setStats(
        List.of(
            MatchStatsGenerator.generateEmptyMatchStats(match.getId(), true),
            MatchStatsGenerator.generateEmptyMatchStats(match.getId(), false)));
    match.setGoalsFullTimeHome((byte) 4);
    match.setGoalsFullTimeAway((byte) 2);

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(),
                OPEN,
                markets,
                match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    createPredictionFixtureForAdvancedCorrectScore(advancedPrediction, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Resolver.resolve();
    Thread.sleep(3000);
    Resolver.resolve();

    ResolverValidator.validateMatchQuizGameForAdvancedCorrectScore(
        advancedPrediction, PredictionsEndpoint.getOwnPredictions(), predictionFixtures);

    validateMatchesDoesNotExistInActiveMatches(List.of(match));
  }

  @ParameterizedTest(
      name =
          "Verify Advanced Correct Market prediction for TOP_X is resolved correctly when : {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @EnumSource(value = AdvancedPredictions.class)
  public void resolvePredictionTopXMatchesAdvancedCorrectScorePrediction(
      AdvancedPredictions advancedPrediction) throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) 4);
          match.setGoalsFullTimeAway((byte) 2);
          match.setKickoffAt(LocalDateTime.now().plusHours(7));
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var matchKickOffAt = matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC"));
    var createGameRequest =
        createGameRequestForTopXGame(
            matchKickOffAt.minusMinutes(20),
            PredictionMarket.CORRECT_SCORE_ADVANCED,
            matchList.stream().map(Match::getId).toList());
    createGameRequest.setScheduleOpenAt(
        Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(3L)));

    var gameInstance =
        GamesEndpoint.createTopXGameForSpecificMarket(createGameRequest).as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            createPredictionFixtureForAdvancedCorrectScore(
                advancedPrediction, match, predictionFixtures));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    updateGameStatusInFirebase(OPEN, gameInstance);
    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Thread.sleep(300);

    Resolver.resolve(matchList.size());

    ResolverValidator.validateTopXGameForAdvancedCorrectScore(
        advancedPrediction, PredictionsEndpoint.getOwnPredictions(), predictionFixtures);
    validateGameDoesNotExistInActiveGames(gameInstance.getId());
    validateMatchesDoesNotExistInActiveMatches(matchList);
  }

  @ParameterizedTest(
      name =
          "Points not given for prediction on {arguments} fixture when status VOID due to postponed match - Top X")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @ValueSource(strings = {"first", "last"})
  public void noPointsForVoidedPredictionFixtureOnCorrectTopX(String matchPosition)
      throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(match.getGoalsFullTimeHome())
                    .goalsAway(match.getGoalsFullTimeAway())
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var createPredictionResponse = PredictionsEndpoint.createPrediction(createPredictionRequest);

    createPredictionResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var predictionInstance = createPredictionResponse.as(PredictionInstance.class);

    int expectedUpdatedFixtureStatusIndex;
    if ("first".equals(matchPosition)) {
      expectedUpdatedFixtureStatusIndex = 0;
    } else {
      expectedUpdatedFixtureStatusIndex = 5;
    }

    var postponedMatch = matchList.get(expectedUpdatedFixtureStatusIndex);

    // Postpone the match
    updateMatchToAnotherStatus(postponedMatch.getId(), MatchGenerator.STATUS_POSTPONED);

    //    Wait for the Game and Prediction Fixture Statuses to updated
    waitForGamePredictionFixtureOutcomeAndStatusToUpdate(
        getCurrentTestUser().getEmail(),
        ResultOutcome.VOID.getValue(),
        expectedUpdatedFixtureStatusIndex,
        10000,
        240);

    // Remove the postponed match from the list of matches which will be set to 'finished' status
    matchList.remove(expectedUpdatedFixtureStatusIndex);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Thread.sleep(3000);
    Resolver.resolve(matchList.size());
    Resolver.resolve();

    // Assert Game Status
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(OPEN.getValue()));

    ResolverValidator.validateTopXGamePredictionsWithOnePostponedMatch(
        PredictionsEndpoint.getOwnPredictions(),
        predictionFixtures,
        expectedUpdatedFixtureStatusIndex);

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(expectedUpdatedFixtureStatusIndex).getMatchId(),
        MatchGenerator.STATUS_NOT_STARTED);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateMatchesDoesNotExistInActiveMatches(List.of(postponedMatch));
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(
        predictionInstance.getId(), List.of(postponedMatch.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "Points not given for prediction on any fixture when status VOID due to postponed match - Top X")
  public void noPointsForVoidedPredictionFixturesOnCorrectTopX()
      throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(match.getGoalsFullTimeHome())
                    .goalsAway(match.getGoalsFullTimeAway())
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var createPredictionResponse = PredictionsEndpoint.createPrediction(createPredictionRequest);

    createPredictionResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var predictionInstance = createPredictionResponse.as(PredictionInstance.class);

    // Postpone the matches
    IntStream.range(0, matchList.size())
        .forEach(
            index ->
                updateMatchToAnotherStatus(
                    gameInstance.getFixtures().get(index).getMatchId(),
                    MatchGenerator.STATUS_POSTPONED));

    // Wait for the Game and Prediction Fixture Statuses to updated
    waitForGamePredictionFixtureOutcomeAndStatusToUpdate(
        getCurrentTestUser().getEmail(), ResultOutcome.VOID.getValue(), 0, 10000, 340);

    Resolver.resolve(matchList.size());

    // Assert Game Status
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    ResolverValidator.validateTopXGamePredictionsWithAllMatchesPostponed(
        PredictionsEndpoint.getOwnPredictions());

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateMatchesDoesNotExistInActiveMatches(matchList);
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(
        predictionInstance.getId(), matchList.stream().map(Match::getId).toList());

    IntStream.range(0, matchList.size())
        .forEach(
            index ->
                updateMatchToAnotherStatus(
                    gameInstance.getFixtures().get(index).getMatchId(),
                    MatchGenerator.STATUS_NOT_STARTED));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "Points not given for prediction on any fixture when status VOID due to postponed match - Match Quiz")
  public void noPointsForVoidedPredictionFixturesOnCorrectMatchQuiz()
      throws HttpException, InterruptedException {

    var markets =
        PredictionMarket.getValidMarkets().stream()
            .filter(market -> !market.equals(PLAYER_PERFORMANCE))
            .toList();
    var match = MatchGenerator.generateMatchesInFuture(1, 2).get(0);

    setupMatchTimelineForMatchQuizGame(
        match,
        TimelineMatchInputValues.CORNERS_6020_RCARD_1000_Timeline_6_Goals_HT_31_FT_4010,
        List.of(
            Goal_ValidPlayerId_10_TRUE_1,
            Goal_ValidPlayerId_15_FALSE_2,
            Goal_ValidPlayerId_20_TRUE_3,
            Goal_ValidPlayerId_51_TRUE_4,
            Goal_ValidPlayerId_55_TRUE_5,
            Goal_ValidPlayerId_80_TRUE_6));

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(),
                OPEN,
                markets,
                match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setValidPredictionsForAllMarkets(markets, match, predictionFixtures);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var createPredictionResponse = PredictionsEndpoint.createPrediction(createPredictionRequest);

    createPredictionResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var predictionInstance = createPredictionResponse.as(PredictionInstance.class);

    // Postpone the match
    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(0).getMatchId(), MatchGenerator.STATUS_POSTPONED);

    // Wait for the Game and Prediction Fixture Statuses to updated
    waitForGameStatusToUpdate(gameInstance.getId(), GameStatus.CANCELED.getValue(), 10000, 340);

    Resolver.resolve();

    // Assert Game Status
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    ResolverValidator.validateMatchQuizGamePredictionsWithMatchPostponed(
        PredictionsEndpoint.getOwnPredictions());

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateMatchesDoesNotExistInActiveMatches(List.of(match));
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(
        predictionInstance.getId(), List.of(match.getId()));

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(0).getMatchId(), MatchGenerator.STATUS_NOT_STARTED);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "Points not given for prediction on fixture when status VOID due to postponed match - Top X Partially Correct")
  public void noPointsForVoidedPredictionFixtureOnPartiallyCorrectTopX()
      throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 2);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) 4);
          match.setGoalsFullTimeAway((byte) 2);
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    // Set correct predictions for first five matches
    for (int i = 0; i < matchList.size() - 1; i++) {
      predictionFixtures.add(
          CorrectScorePredictionFixture.builder()
              .matchId(matchList.get(i).getId())
              .matchType(MatchType.FOOTBALL.getValue())
              .goalsHome(matchList.get(i).getGoalsFullTimeHome())
              .goalsAway(matchList.get(i).getGoalsFullTimeAway())
              .build());
    }

    // Set incorrect predictions for the sixth match
    predictionFixtures.add(
        CorrectScorePredictionFixture.builder()
            .matchId(matchList.get(5).getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(matchList.get(5).getGoalsFullTimeHome() + 1)
            .goalsAway(matchList.get(5).getGoalsFullTimeAway() - 1)
            .build());

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var createPredictionResponse = PredictionsEndpoint.createPrediction(createPredictionRequest);

    createPredictionResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var predictionInstance = createPredictionResponse.as(PredictionInstance.class);

    int expectedUpdatedFixtureStatusIndex = 0;

    // Postpone the match
    var postponedMatch = matchList.get(expectedUpdatedFixtureStatusIndex);

    updateMatchToAnotherStatus(postponedMatch.getId(), MatchGenerator.STATUS_POSTPONED);

    // Wait for the Game and Prediction Fixture Statuses to updated
    waitForGamePredictionFixtureOutcomeAndStatusToUpdate(
        getCurrentTestUser().getEmail(),
        ResultOutcome.VOID.getValue(),
        expectedUpdatedFixtureStatusIndex,
        10000,
        340);

    // Remove the postponed match from the list of matches which will be set to 'finished' status
    matchList.remove(expectedUpdatedFixtureStatusIndex);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);
    Resolver.resolve(matchList.size());

    // Assert Game Status
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(OPEN.getValue()));

    ResolverValidator.validateTopXGamePredictionsWithOnePostponedMatchPartiallyCorrect(
        PredictionsEndpoint.getOwnPredictions(),
        predictionFixtures,
        expectedUpdatedFixtureStatusIndex);

    var redisGamesResponse = RedisEndpoint.getGames();
    RedisValidator.validateMatchesDoesNotExistInActiveMatches(List.of(postponedMatch));
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(
        predictionInstance.getId(), List.of(postponedMatch.getId()));

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(expectedUpdatedFixtureStatusIndex).getMatchId(),
        MatchGenerator.STATUS_NOT_STARTED);
  }

  private void setupMatchTimelineForMatchQuizGame(
      Match match,
      TimelineMatchInputValues timelineMatchValues,
      List<TimelineMatchInputValues.GenerateMatchTimeLine> matchTimeLines) {

    match.setTimeline(new ArrayList<>());
    match.setStats(
        List.of(
            MatchStatsGenerator.generateEmptyMatchStats(match.getId(), true),
            MatchStatsGenerator.generateEmptyMatchStats(match.getId(), false)));
    match.getStats().get(0).getStatistics().setCorners((byte) timelineMatchValues.getCornersHome());
    match
        .getStats()
        .get(0)
        .getStatistics()
        .setCorners_et((byte) timelineMatchValues.getCornersHomeEt());
    match.getStats().get(1).getStatistics().setCorners((byte) timelineMatchValues.getCornersAway());
    match
        .getStats()
        .get(1)
        .getStatistics()
        .setCorners_et((byte) timelineMatchValues.getCornersAwayEt());
    match
        .getStats()
        .get(0)
        .getStatistics()
        .setRed_cards((byte) timelineMatchValues.getRedCardsHome());
    match
        .getStats()
        .get(0)
        .getStatistics()
        .setRed_cards_et((byte) timelineMatchValues.getRedCardsHomeEt());
    match
        .getStats()
        .get(1)
        .getStatistics()
        .setRed_cards((byte) timelineMatchValues.getRedCardsAway());
    match
        .getStats()
        .get(1)
        .getStatistics()
        .setRed_cards_et((byte) timelineMatchValues.getRedCardsAwayEt());
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                matchTimeLines.get(0).getTimelineEventType(),
                matchTimeLines.get(0).getPlayerId(),
                (short) matchTimeLines.get(0).getMinute(),
                matchTimeLines.get(0).isHomeTeam(),
                (byte) matchTimeLines.get(0).getSortOrder()));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                matchTimeLines.get(1).getTimelineEventType(),
                matchTimeLines.get(1).getPlayerId(),
                (short) matchTimeLines.get(1).getMinute(),
                matchTimeLines.get(1).isHomeTeam(),
                (byte) matchTimeLines.get(1).getSortOrder()));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                matchTimeLines.get(2).getTimelineEventType(),
                matchTimeLines.get(2).getPlayerId(),
                (short) matchTimeLines.get(2).getMinute(),
                matchTimeLines.get(2).isHomeTeam(),
                (byte) matchTimeLines.get(2).getSortOrder()));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                matchTimeLines.get(3).getTimelineEventType(),
                matchTimeLines.get(3).getPlayerId(),
                (short) matchTimeLines.get(3).getMinute(),
                matchTimeLines.get(3).isHomeTeam(),
                (byte) matchTimeLines.get(3).getSortOrder()));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                matchTimeLines.get(4).getTimelineEventType(),
                matchTimeLines.get(4).getPlayerId(),
                (short) matchTimeLines.get(4).getMinute(),
                matchTimeLines.get(4).isHomeTeam(),
                (byte) matchTimeLines.get(4).getSortOrder()));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                matchTimeLines.get(5).getTimelineEventType(),
                matchTimeLines.get(5).getPlayerId(),
                (short) matchTimeLines.get(5).getMinute(),
                matchTimeLines.get(5).isHomeTeam(),
                (byte) matchTimeLines.get(5).getSortOrder()));
    match.setGoalsHalfTimeHome((byte) timelineMatchValues.getGoalHTHomeTeam());
    match.setGoalsHalfTimeAway((byte) timelineMatchValues.getGoalHTAwayTeam());
    match.setGoalsFullTimeHome((byte) timelineMatchValues.getGoalFTHomeTeam());
    match.setGoalsFullTimeAway((byte) timelineMatchValues.getGoalFTAwayTeam());
    match.setGoalsExtraTimeHome((byte) timelineMatchValues.getGoalETHomeTeam());
    match.setGoalsExtraTimeAway((byte) timelineMatchValues.getGoalETAwayTeam());
  }

  private static void setPredictionToStatus(
      String resolutionStatus,
      List<PredictionMarket> markets,
      Match match,
      ArrayList<PredictionFixture> predictionFixtures) {
    switch (resolutionStatus) {
      case PredictionStatusResolution.RESOLVE_TO_WON ->
          setValidPredictionsForAllMarkets(markets, match, predictionFixtures);
      case PredictionStatusResolution.RESOLVE_TO_LOST ->
          setInvalidPredictionsForAllMarkets(markets, match, predictionFixtures);
      default ->
          throw new IllegalArgumentException("Unknown resolution status: " + resolutionStatus);
    }
  }

  @Disabled("TODO: Need to update the test. Must add wait to synchronize the MySQL MV")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Verify won prediction points are reverted to 0 when TOP_X game is canceled")
  public void revertWonPointsOnCanceledTopXGame() throws HttpException, InterruptedException {

    // Generate and set up matches for the game
    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    // Create a Leaderboard Template
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .markets(List.of(PredictionMarket.CORRECT_SCORE.getValue()))
            .matchIds(matchList.stream().map(Match::getId).toList())
            .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    var template = createTemplateResponse.as(TemplateResponse.class);

    // Create TOP X Game
    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                OPEN,
                matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    // Create Prediction
    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(
        match ->
            predictionFixtures.add(
                CorrectScorePredictionFixture.builder()
                    .matchId(match.getId())
                    .matchType(MatchType.FOOTBALL.getValue())
                    .goalsHome(match.getGoalsFullTimeHome())
                    .goalsAway(match.getGoalsFullTimeAway())
                    .build()));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId())
            .fixtures(predictionFixtures)
            .build();

    var response = PredictionsEndpoint.createPrediction(createPredictionRequest);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var predictionInstance = response.as(PredictionInstance.class);


    // Resolve the first match from the game to WON and assign points
    Resolver.updateMatchToBeFinishedInThePast(matchList.get(0).getId(), 31);
    TimeUnit.SECONDS.sleep(5L);
    Resolver.resolve();

    // Assert points are given in Firestore for the resolved match
    PredictionsEndpoint.getOwnPredictions()
        .then()
        .assertThat()
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + POINTS_PROP, equalTo(50));

    // Assert points are displayed when GET Leaderboard by game id
    GameLeaderboardsEndpoint gameLeaderboardsEndpoint =
        GameLeaderboardsEndpoint.builder().gameId(gameInstance.getId()).build();

    // Sync MySQL with BigQuery
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        USER_RANKING_GAME_MV, gameInstance.getId());
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        USER_RANKING_TEMPLATE_MV, template.getId());


    waitDataToBeSavedInMySQL("GAME", gameInstance.getId(), 5, 880);

    gameLeaderboardsEndpoint
        .getLeaderboardForGameId()
        .then()
        .assertThat()
        .body("data[0]." + POINTS_PROP, equalTo(50))
        .body("data[0]." + PREDICTIONS_MADE_PROP, equalTo(1));

    waitDataToBeSavedInMySQL("TEMPLATE", template.getId(), 5, 240);
    // Assert points are displayed when GET Leaderboard by template id
    LeaderboardByIdEndpoint.getLeaderboardForTemplateWithId(template.getId(), null, null)
        .then()
        .assertThat()
        .body("data[0]." + POINTS_PROP, equalTo(50))
        .body("data[0]." + PREDICTIONS_MADE_PROP, equalTo(1));

    /*
       Assert points are displayed when GET own stats
      NOTE: Assertion may fail if total_points_MV view hasn't refreshed yet (up to 90min delay).
      Points are sourced from materialized view instead of rank table.
    */

    StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail())
        .then()
        .assertThat()
        .body("data.predictions.top_x." + POINTS_PROP, equalTo(50));

    // Update game to canceled
    UpdateGameRequest.builder().status(GameStatus.CANCELED.getValue()).build();
    var body = new JSONObject();
    body.put(STATUS_PROP, GameStatus.CANCELED.getValue());

    updateGame(gameInstance.getId(), body);

    // Assert Game Status in Firebase
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    // Assert Prediction Status in Firebase
    ResolverValidator.validateCanceledTopXGameWithOnePredictionWon(
        PredictionsEndpoint.getOwnPredictions());

    var redisGamesResponse = RedisEndpoint.getGames();

    // Assert Game and Prediction in Redis
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(
        predictionInstance.getId(), matchList.stream().map(Match::getId).toList());

    // Assert points are reverted in BQ when GET Leaderboard by game id
    // Bypassing the cache with setting dummy value 'version'
    // TODO add wait  for particular time
    gameLeaderboardsEndpoint.setVersion(2);
    gameLeaderboardsEndpoint
        .getLeaderboardForGameId()
        .then()
        .assertThat()
        .body("data[0]." + POINTS_PROP, equalTo(0))
        .body("data[0]." + PREDICTIONS_MADE_PROP, equalTo(0));

    // Assert points are reverted in BQ when GET own stats
    StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail())
        .then()
        .assertThat()
        .body("data." + ApiConstants.LoyaltyApi.POINTS_PROP, equalTo(0));

    // Assert points are reverted in BQ when GET Leaderboard by template id
    LeaderboardByIdEndpoint.getLeaderboardForTemplateWithId(template.getId(), null, null)
        .then()
        .assertThat()
        .body("data[0]." + POINTS_PROP, equalTo(0))
        .body("data[0]." + PREDICTIONS_MADE_PROP, equalTo(0));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL), @Tag("FZ-1796")})
  @DisplayName("Resolve the MATCH_QUIZ game to WON when the playerId is OWN_GOAL")
  public void resolveOwnGoals() throws HttpException, InterruptedException {

    var user = getCurrentTestUser();
    var match = MatchGenerator.generateMatch();
    LocalDateTime localDateTime = match.getKickoffAt();

    match.setTimeline(
        List.of(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(), OWN_GOAL, VALID_PLAYER_ID, (short) 5, true, (byte) 1),
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.GOAL,
                OTHER_VALID_PLAYER_ID,
                (short) 9,
                true,
                (byte) 1)));
    match.setGoalsFullTimeHome((byte) 2);

    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    // Create game
    var gameInstance =
        createMatchQuizGameForMarkets(
                match.getId(),
                GameStatus.OPEN,
                List.of(PredictionMarket.PLAYER_SCORE_FIRST_GOAL),
                localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    createMatchQuizPredictionForGameWithSingleMarketOwnGoal(
        gameInstance.getId(),
        PredictionMarket.PLAYER_SCORE_FIRST_GOAL,
        user.getEmail(),
        String.valueOf(OWN_GOAL));

    // Resolve the first match from the game to WON and assign points
    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    Thread.sleep(3000);
    Resolver.resolve();

    Thread.sleep(13000);
    ResolverValidator.validateSinglePredictionIsResolvedToWon(
        PredictionsEndpoint.getOwnPredictions());

    RedisValidator.validateMatchesDoesNotExistInActiveMatches(List.of(match));
  }
}
