package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.DEFAULT_LIMIT_PARAM_GAME_RESULTS;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.GOAL_TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.GOLDEN_GOALS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MATCH_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MINUTE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.PLAYER_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_OUTCOME;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.TIE_BREAKERS;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.USER_ID_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Games.GET_GAME_CORRECT_RESULT_SCHEMA;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Games.GET_GAME_RESULTS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.TestGroups.STAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionsForGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.setValidPredictionsForAllMarkets;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGameStatusToUpdate;
import static com.fansunited.automation.helpers.WaitHelper.waitGameStatusToBeUpdated;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.CLOSED;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.SETTLED;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGetGameResultsResponse;
import static com.fansunited.automation.validators.PredictionApiValidator.validateMatchQuizGameCorrectMarketResultsResponse;
import static com.fansunited.automation.validators.PredictionApiValidator.validateTopXGameCorrectMarketResultsResponse;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameResultsEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.MatchStatsGenerator;
import com.fansunited.automation.core.resolver.MatchTimelineGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.enums.TimelineEventType;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.GameResultsData;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.RedisValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/games/{gameId}/results endpoint happy path tests")
public class GetGameResultsTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify game results are successfully fetched and valid. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getResultsForGameType(GameType gameType)
      throws HttpException, IllegalArgumentException, IOException, FirebaseAuthException,
      InterruptedException, ExecutionException {

    var gameId = createGames(gameType, 2).get(0);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    final var predictionsCount = Helper.generateRandomNumber(5, 10);

    var userPredictionsIdList = new ArrayList<String>();

    createPredictionsForGame(gameId, gameType, predictionsCount)
        .forEach(predictionId -> userPredictionsIdList.add(predictionId.split("_")[0]));

    GameResultsEndpoint gameResultsEndpoint =
        GameResultsEndpoint.builder()
            .gameId(gameId)
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(-1)
            .build();

    var response = gameResultsEndpoint.getGameResults();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_GAME_RESULTS_SCHEMA))
        .body("data." + USER_ID_PROP, containsInAnyOrder(userPredictionsIdList.toArray()))
        .body("data[0]." + RESULTS_PROP + "." + MATCH_ID_PROP,
            containsInAnyOrder(gameInstance.getFixtures().stream().map(
                GameFixture::getMatchId).toArray()))
        .body("data[0]." + RESULTS_PROP + "." + RESULT_OUTCOME,
            Every.everyItem(is(ResultOutcome.NOT_VERIFIED.getValue())));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify /GET/v1/games/{gameId}/results response returned by the server is cached for 1h")
  @EnabledIf("isUseStageEnvironment")
  public void verifyGetResultsForGameResponseIsCached()
      throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    GameResultsEndpoint gameResultsEndpoint =
        GameResultsEndpoint.builder()
            .gameId(gameId)
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(-1)
            .build();

    var response = gameResultsEndpoint.getGameResults();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting game results")
  public void getGameResultsWithDefaultPaginationLimitParam()
      throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    GameResultsEndpoint gameResultsEndpoint =
        GameResultsEndpoint.builder()
            .gameId(gameId)
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(-1)
            .build();

    var response = gameResultsEndpoint.getGameResults();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(DEFAULT_LIMIT_PARAM_GAME_RESULTS));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting game results")
  public void getGameResultsPaginationParams()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
        .matchesIdList(matchList.stream().map(Match::getId).collect(Collectors.toList()))
        .build()
        .createGame()
        .as(GameInstance.class);

    final var predictionsCount = Helper.generateRandomNumber(10, 15);

    createPredictionsForGame(gameInstance.getId(), GameType.MATCH_QUIZ, predictionsCount);

    final var resultsLimit = Helper.generateRandomNumber(1, 5);

    GameResultsEndpoint gameResultsEndpoint =
        GameResultsEndpoint.builder()
            .gameId(gameInstance.getId())
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(resultsLimit)
            .build();

    var response = gameResultsEndpoint.getGameResults();

    currentTestResponse.set(response);

    var gameResultsResponse = response.as(GameResultsData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(gameResultsResponse.getData()
                .get(gameResultsResponse.getData().size() - 1)
                .getUserId() + "_g_" + gameInstance.getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting game results works")
  public void getGameResultsPaginationMoveToNextPage()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
        .matchesIdList(matchList.stream().map(Match::getId).collect(Collectors.toList()))
        .build()
        .createGame()
        .as(GameInstance.class);

    final var predictionsCount = Helper.generateRandomNumber(15, 30);

    var userPredictionsIdList = new ArrayList<String>();

    createPredictionsForGame(gameInstance.getId(), GameType.MATCH_QUIZ, predictionsCount)
        .forEach(predictionId -> userPredictionsIdList.add(predictionId.split("_")[0]));

    final int resultsLimit = Helper.generateRandomNumber(1, 5);

    GameResultsEndpoint gameResultsEndpoint =
        GameResultsEndpoint.builder()
            .gameId(gameInstance.getId())
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(resultsLimit)
            .build();

    var response = gameResultsEndpoint.getGameResults();

    currentTestResponse.set(response);

    validateGetGameResultsResponse(gameInstance.getId(), response,
        userPredictionsIdList, resultsLimit);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL), @Tag(DISABLED), @Tag("FZ-1697")})
  @DisplayName("Verify correct results for MATCH_QUIZ game")
  public void verifyCorrectMarketResultForGameTypeMatchQuiz()
      throws HttpException, InterruptedException {

    var markets = PredictionMarket.getValidMarkets();

    var match = MatchGenerator.generateMatch();
    match.setTimeline(new ArrayList<>());

    match.setStats(List.of(
        MatchStatsGenerator.generateEmptyMatchStats(match.getId(), true),
        MatchStatsGenerator.generateEmptyMatchStats(match.getId(), false)
    ));

    match.getStats().get(0).getStatistics().setCorners((byte) 6);
    match.getStats().get(1).getStatistics().setCorners((byte) 2);
    match.getStats().get(0).getStatistics().setRed_cards((byte) 1);
    match.getStats().get(1).getStatistics().setRed_cards((byte) 0);

    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 10, true, (byte) 1));

    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 15, false, (byte) 2));

    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 20, true, (byte) 3));

    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.GOAL,
            VALID_PLAYER_ID, (short) 51, true, (byte) 4));

    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(),
            TimelineEventType.YELLOW_CARD,
            VALID_PLAYER_ID, (short) 55, true, (byte) 5));

    match.getTimeline()
        .add(MatchTimelineGenerator.generateMatchTimeline(match.getId(), TimelineEventType.RED_CARD,
            VALID_PLAYER_ID, (short) 80, true, (byte) 6));

    match.setGoalsHalfTimeHome((byte) 3);
    match.setGoalsHalfTimeAway((byte) 1);
    match.setGoalsFullTimeHome((byte) 4);
    match.setGoalsFullTimeAway((byte) 1);

    Resolver.openMatchForPredictions(match);
    init();
    cleanUpMatchIdList.add(match.getId());

    var gameInstance = GamesEndpoint.createMatchQuizGameForMarkets(match.getId(), GameStatus.OPEN,
            markets, ZonedDateTime.now().plusMinutes(2))
        .as(GameInstance.class);

    Thread.sleep(10000);
    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    setValidPredictionsForAllMarkets(markets, match, predictionFixtures);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    waitForGameStatusToUpdate(gameInstance.getId(), LIVE.getValue(), 10000, 450); // Wait for game status to be updated to live

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(LIVE.getValue()));

    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    waitGameStatusToBeUpdated(gameInstance.getId(), List.of(CLOSED, SETTLED), 5, 360);

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        anyOf(
            equalTo(GameStatus.CLOSED.getValue()),
            equalTo(GameStatus.SETTLED.getValue())
        )
    );

    Resolver.resolve();

    waitForGameStatusToUpdate(gameInstance.getId(), GameStatus.SETTLED.getValue(), 10000, 400);
    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.SETTLED.getValue()));

    /**
     * The following assertion is related to FZ-1627.
     * It asserts that after the game is settled and the prediction no longer exists in Redis,
     * the number of game participants is still correctly returned.
     */

    var response = GameEndpoint.getGameById(gameInstance.getId());
    currentTestResponse.set(response);
    var gameResponse = response.as(GameInstance.class);

    Assertions.assertEquals(1, gameResponse.getParticipantsCount(),
        String.format("""
                The participants_count does not match the actual!
                Expected %s
                Actual   %s""", 1, gameResponse.getParticipantsCount()));

    /**
     * ------------- This is the end of the above assertion
     */

    GameResultsEndpoint gameResultsEndpoint = GameResultsEndpoint.builder()
            .gameId(gameInstance.getId())
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(-1)
            .build();

    response = gameResultsEndpoint.getGameCorrectScores();

    currentTestResponse.set(response);

    validateMatchQuizGameCorrectMarketResultsResponse(response, match);
    ResolverBase.cleanUp();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL), @Tag("FZ-1697")})
  @DisplayName("Verify correct results for TOP_X game")
  public void verifyCorrectMarketResultForGameTypeTopX()
      throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
      match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    });

    Resolver.openMatchesForPredictions(matchList);

    init();

    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(matchList.stream().map(Match::getId).toList(), GameType.TOP_X,
                GameStatus.OPEN, ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
        .matchId(match.getId())
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(match.getGoalsFullTimeHome())
        .goalsAway(match.getGoalsFullTimeAway())
        .build()));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    // Wait for game status to be updated to live
    waitGameStatusToBeUpdated(gameInstance, LIVE , 5, 360);

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(LIVE.getValue()));

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);

    // Wait for game status to be updated to closed or settled
    waitGameStatusToBeUpdated(gameInstance.getId(), List.of(CLOSED, SETTLED), 5, 360);

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        anyOf(
            equalTo(GameStatus.CLOSED.getValue()),
            equalTo(GameStatus.SETTLED.getValue())
        )
    );

    Resolver.resolve(matchList.size());

    // Wait for game status to be updated to settled
    waitGameStatusToBeUpdated(gameInstance, SETTLED, 5, 360);
    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.SETTLED.getValue()));

    /**
     * The following assertion is related to FZ-1627.
     * It asserts that after the game is settled and the prediction no longer exists in Redis,
     * the number of game participants is still correctly returned.
     */

    var response = GameEndpoint.getGameById(gameInstance.getId());

    currentTestResponse.set(response);
     gameInstance = response.as(GameInstance.class);
    Assertions.assertEquals(1, gameInstance.getParticipantsCount(),
        String.format("""
                The participants_count does not match the actual!
                Expected %s
                Actual   %s""", 1, gameInstance.getParticipantsCount()));

    /*
      ------------- This is the end of the above assertion
     */

    GameResultsEndpoint gameResultsEndpoint = GameResultsEndpoint.builder()
            .gameId(gameInstance.getId())
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(-1)
            .build();

    response = gameResultsEndpoint.getGameCorrectScores();

    currentTestResponse.set(response);

    validateTopXGameCorrectMarketResultsResponse(response, matchList);

    ResolverBase.cleanUp();
  }

  //@Disabled("This test is skipped REASON: currently it is DISABLED for stage environment!")
  @ParameterizedTest(name = "Verify golden goal tiebreaker object for TOP_X is correctly displayed when having golden goal = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @ValueSource(booleans = {true, false})
  public void goldenGoalForTopxIsDisplayed(boolean isGoldenGoalAvailable)
      throws HttpException, InterruptedException {

    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) 4);
      match.setGoalsFullTimeAway((byte) 2);
      match.setKickoffAt(LocalDateTime.now().plusDays(1L));
    });

    if (isGoldenGoalAvailable) {
      List<Integer> goldenGoalsList = List.of(1, 10, 90, 95);

      IntStream.range(0, goldenGoalsList.size() - 1).forEach(index -> {
        matchList.get(index).setTimeline(new ArrayList<>());

        matchList.get(index).getTimeline()
            .add(MatchTimelineGenerator.generateMatchTimeline(matchList.get(index).getId(),
                TimelineEventType.GOAL,
                VALID_PLAYER_ID, (short) ((int) goldenGoalsList.get(index)), true, (byte) 1));
      });

      matchList.get(4).setTimeline(new ArrayList<>());

      matchList.get(4).getTimeline()
          .add(MatchTimelineGenerator.generateMatchTimeline(matchList.get(4).getId(),
              TimelineEventType.PENALTY_GOAL,
              VALID_PLAYER_ID, (short) ((int) goldenGoalsList.get(0)), true, (byte) 1));

      matchList.get(5).setTimeline(new ArrayList<>());

      matchList.get(5).getTimeline()
          .add(MatchTimelineGenerator.generateMatchTimeline(matchList.get(5).getId(),
              TimelineEventType.OWN_GOAL,
              VALID_PLAYER_ID, (short) ((int) goldenGoalsList.get(0)), true, (byte) 1));
    }

    Resolver.openMatchesForPredictions(matchList);

    init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsFullTimeHome())
            .goalsAway(match.getGoalsFullTimeAway())
            .build()));

    var predictionsCutoff = ZonedDateTime.now().plusMinutes(2);

    CreateGameEndpoint createGameEndpoint =
        CreateGameEndpoint.builder()
            .matchesIdList(matchList.stream().map(Match::getId).collect(Collectors.toList()))
            .gameStatus(GameStatus.OPEN)
            .predictionsCutoff(predictionsCutoff)
            .gameType(GameType.TOP_X)
            .build();

    var gameInstance = createGameEndpoint.createGame().as(GameInstance.class);
    Thread.sleep(10000);
    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var createPredictionRequest = CreatePredictionRequest.builder()
            .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPrediction(createPredictionRequest)
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_OK);

    matchList.forEach(match -> updateMatchToAnotherStatus(match.getId(), MatchGenerator.STATUS_TO_LIVE));

    waitGameStatusToBeUpdated(gameInstance, LIVE, 5, 360);// Wait for game status to be updated to live

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(LIVE.getValue()));

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 31);

    // Wait for game status to be updated to closed or settled
    waitGameStatusToBeUpdated(gameInstance.getId(), List.of(CLOSED, SETTLED), 5, 360);

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        anyOf(
            equalTo(GameStatus.CLOSED.getValue()),
            equalTo(GameStatus.SETTLED.getValue())
        )
    );

    Resolver.resolve(matchList.size());

    waitGameStatusToBeUpdated(gameInstance, SETTLED, 5, 360); // Wait for game status to be updated to settled

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.SETTLED.getValue()));

    GameResultsEndpoint gameResultsEndpoint =
        GameResultsEndpoint.builder()
            .gameId(gameInstance.getId())
            .clientId(CLIENT_AUTOMATION_ID)
            .apiKey(AuthConstants.ENDPOINTS_API_KEY)
            .contentType(ContentType.JSON)
            .limit(-1)
            .build();

    var response = gameResultsEndpoint.getGameCorrectScores();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_GAME_CORRECT_RESULT_SCHEMA))
        .body("data." + TIE_BREAKERS, is(notNullValue()));

    if (isGoldenGoalAvailable) {
      response
          .then()
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[0]." + MINUTE_PROP, equalTo(1))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[0]." + PLAYER_ID_PROP, equalTo(VALID_PLAYER_ID))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[0]." + MATCH_ID_PROP, equalTo(matchList.get(0).getId()))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[0]." + GOAL_TYPE_PROP, equalTo(TimelineEventType.GOAL.getValue()))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[1]." + MINUTE_PROP, equalTo(1))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[1]." + PLAYER_ID_PROP, equalTo(VALID_PLAYER_ID))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[1]." + MATCH_ID_PROP, equalTo(matchList.get(4).getId()))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[1]." + GOAL_TYPE_PROP, equalTo(TimelineEventType.PENALTY_GOAL.getValue()))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[2]." + MINUTE_PROP, equalTo(1))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[2]." + PLAYER_ID_PROP, equalTo(VALID_PLAYER_ID))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[2]." + MATCH_ID_PROP, equalTo(matchList.get(5).getId()))
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP + "[2]." + GOAL_TYPE_PROP, equalTo(TimelineEventType.OWN_GOAL.getValue()));
    } else {
      response
          .then()
          .body("data." + TIE_BREAKERS + "." + GOLDEN_GOALS_PROP, is(List.of()));
    }

    ResolverBase.cleanUp();
  }
}
