package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.INVALID_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.getGamesWithIds;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.getListOfGames;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.Arrays;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/games endpoint validation tests")
public class GetListOfGamesValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify API cannot get list of games with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void getListOfGamesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON,
            GameStatus.OPEN.getValue(), GameType.TOP_X.getValue(), -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting list of games with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getListOfGamesWithInvalidClientId(String clientId) throws HttpException {

    var response =
        getListOfGames(clientId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
            GameStatus.OPEN.getValue(), GameType.MATCH_QUIZ.getValue(), -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting list of games with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getListOfGamesWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            GameStatus.OPEN.getValue(), GameType.MATCH_QUIZ.getValue(), -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify list of games data is still fetched if content type is NOT specified")
  public void getListOfGamesWithoutSpecifyingContentType() throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            null,
            GameStatus.OPEN.getValue(), GameType.MATCH_QUIZ.getValue(), -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST if game type is NOT specified")
  public void getListOfGamesWithoutSpecifyingGameType()
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, GameStatus.OPEN.getValue(),
            null,
            -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST if game type and status are NOT specified")
  public void getListOfGamesWithoutSpecifyingGameTypeAndGameStatus()
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, null,
            null,
            -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting list of games with invalid game type. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void getListOfGamesWithInvalidGameType(GameType gameType)
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            GameStatus.OPEN.getValue(), gameType.getValue(), -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting list of games for game type TOP_X with invalid game status. Game status: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void getListOfGamesForGameTypeTopXWithInvalidGameStatus(GameStatus gameStatus)
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            gameStatus.getValue(), GameType.TOP_X.getValue(), -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting list of games with valid and invalid game statuses. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getListOfGamesWithValidAndInvalidGameStatuses(GameType gameType)
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            gameType.getValue(),
            GameStatus.listToCommaSeparated(Arrays.stream(GameStatus.values()).toList()), -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting list of games for game type MATCH_QUIZ with invalid game status. Game status: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void getListOfGamesForGameTypeMatchQuizWithInvalidGameStatus(GameStatus gameStatus)
      throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            gameStatus.getValue(), GameType.MATCH_QUIZ.getValue(), -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns BAD_REQUEST when getting list of games with negative or 0(integers) for pagination \"limit\" param. "
          + "\"limit\" param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-2, 0})
  public void getListOfGamesWithInvalidLimitParam(int limit) throws HttpException {

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            GameStatus.OPEN.getValue(), GameType.TOP_X.getValue(), limit, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns empty list of games, if game_ids query filter has only invalid games. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getListOfGamesWithGameIdsFilterForGameType(GameType gameType)
      throws HttpException, IllegalArgumentException {

    createGames(gameType, 5);

    var gameIdsCommaSeparated = String.join(",", List.of(INVALID_GAME_ID));

    var response =
        getGamesWithIds(gameIdsCommaSeparated, gameType.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(0));
  }

  @ParameterizedTest(name = "Verify game ids filter is ignored if is empty string. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesWithEmptyGameIdsFilterForGameType(GameType gameType)
      throws HttpException, IllegalArgumentException {

    createGames(gameType, 5);

    var response =
        getGamesWithIds("", gameType.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(greaterThan(0)));
  }
}
