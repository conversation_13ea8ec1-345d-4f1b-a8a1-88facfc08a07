package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_MAN_UTD;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.LIMIT_PARAM_MAX_VALUE_GAMES;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiConstants.SortOrder.ASC;
import static com.fansunited.automation.constants.ApiConstants.SortOrder.DESC;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Games.GET_GAME_LIST_MATCH_QUIZ_SCHEMA;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Games.GET_GAME_LIST_TOP_X_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.getGamesWithIds;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.getGamesWithIdsAndLimit;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.getListOfGames;
import static com.fansunited.automation.helpers.AssertionHelper.assertSortOrderBasedOnMatchKickoffAt;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.GAME_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.getFirestoreCollection;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGetListOfGamesResponse;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.ListOfGamesData;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.RedisValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@DisplayName("Prediction Api - GET /v1/games endpoint happy path tests")
public class GetListOfGamesTests extends PredictionApiBaseTest {

  private static final Logger LOG = LoggerFactory.getLogger(GetListOfGamesTests.class);

  @ParameterizedTest(name = "Verify list of games is successfully fetched and valid. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("one")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesForGameType(GameType gameType)
      throws HttpException, IllegalArgumentException {

    final var gamesCount = Helper.generateRandomNumber(30, 90);

    var gamesIdList = createGames(gameType, gamesCount);

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, GameStatus.OPEN.getValue(),
            gameType.getValue(),
            -1, null);

    currentTestResponse.set(response);

    validateGetListOfGamesResponse(gameType, response, gamesIdList, -1);

  }

  @ParameterizedTest(name = "Verify list of games is successfully fetched and valid for each game status. Game status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getListOfGamesForStatus(GameStatus gameStatus)
      throws HttpException, IllegalArgumentException, ExecutionException, InterruptedException,
      IOException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    FirebaseHelper.updateCollectionField(
        getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameId,
        STATUS_PROP, gameStatus.getValue());

    var response =
        GamesEndpoint.getListOfGame(GameType.MATCH_QUIZ.getValue(), gameStatus.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + STATUS_PROP, everyItem(is(gameStatus.getValue())));
  }

  @ParameterizedTest(name = "Verify list of games with all game statuses is successfully fetched and valid. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesWithAllStatuses(GameType gameType)
      throws HttpException, IllegalArgumentException, ExecutionException, InterruptedException,
      IOException {

    var gamesIdList = createGames(gameType, GameStatus.getValidStatuses().size());

    for (int i = 0; i < gamesIdList.size(); i++) {
      FirebaseHelper.updateCollectionField(
          getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
          gamesIdList.get(i),
          STATUS_PROP, GameStatus.getValidStatuses().get(i).getValue());
    }

    var response = getListOfGames(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
        GameStatus.listToCommaSeparated(GameStatus.getValidStatuses()), gameType.getValue(),
        LIMIT_PARAM_MAX_VALUE_GAMES,
        null);

    List<String> allStatuses;
    var allGames = getAllGames(gameType.getValue());
    allStatuses = allGames.stream().map(g -> g.getStatus()).collect(
        Collectors.toList());

    currentTestResponse.set(response);

    Assertions.assertTrue(allStatuses.containsAll(
            GameStatus.getValidStatuses().stream().map(GameStatus::getValue).toList()),
        "There are missing games for some of the statuses");
  }

  private List<String> getAllGameStatuses(String gameType) throws HttpException {
    var response = getListOfGames(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
        GameStatus.listToCommaSeparated(GameStatus.getValidStatuses()), gameType,
        LIMIT_PARAM_MAX_VALUE_GAMES,
        null);

    var currentPageOfGames = response.as(ListOfGamesData.class);

    var gameStatusList = new ArrayList<>(currentPageOfGames.getData().stream().map(
        GameInstance::getStatus).toList());

    var startAfter = currentPageOfGames.getMeta().getPagination().getNextPageStartsAfter();

    var counter = 0;

    while (
        startAfter != null
            && !gameStatusList.containsAll(GameStatus.getValidStatuses().stream().map(GameStatus::getValue).toList())
            && counter < 1000) {

      currentPageOfGames = getListOfGames(CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, GameStatus.OPEN.getValue(),
          gameType,
          LIMIT_PARAM_MAX_VALUE_GAMES, startAfter).as(ListOfGamesData.class);

      startAfter = currentPageOfGames.getMeta().getPagination().getNextPageStartsAfter();

      gameStatusList.addAll(currentPageOfGames.getData().stream().map(
          GameInstance::getStatus).toList());

      counter++;
    }
    return gameStatusList;
  }

  private List<GameInstance> getAllGames(String gameType) throws HttpException {
    List<GameInstance> allGames = new ArrayList<>();
    String startAfter = null;
    int maxRetries = 1000; // Configurable maximum retries
    int counter = 0;

    do {
      // Fetch the current page of games
      var response = getListOfGames(
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          GameStatus.listToCommaSeparated(GameStatus.getValidStatuses()),
          gameType,
          LIMIT_PARAM_MAX_VALUE_GAMES,
          startAfter
      );

      var currentPageOfGames = response.as(ListOfGamesData.class);

      // Add games from the current page
      allGames.addAll(currentPageOfGames.getData());

      // Update the pagination token
      startAfter = currentPageOfGames.getMeta().getPagination().getNextPageStartsAfter();

      // Increment the counter
      counter++;

      // Log progress
      LOG.debug("Fetched page {}. Total games fetched: {}", counter, allGames.size());

    } while (
        startAfter != null && counter < maxRetries
    );

    // Log final result
    LOG.info("Fetched a total of {} games of type {} after {} pages.", allGames.size(), gameType, counter);

    return allGames;
  }

  @ParameterizedTest(name = "Verify list of games is fetched without specifying status param. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesWithoutStatusParam(GameType gameType)
      throws HttpException, IllegalArgumentException {

    createGames(gameType, 1);

    Response response = GamesEndpoint.getListOfGame(gameType.getValue(), null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            gameType == GameType.TOP_X ? GET_GAME_LIST_TOP_X_SCHEMA
                : GET_GAME_LIST_MATCH_QUIZ_SCHEMA));
  }

  @ParameterizedTest(name = "Verify list of games response returned by the server is cached for 1h. Requesting game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  @EnabledIf("isUseStageEnvironment")
  public void verifyGetListOfGamesResponseIsCached(GameType gameType)
      throws HttpException {

    var response =
        GamesEndpoint.getListOfGame(null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, GameStatus.OPEN.getValue(), gameType.getValue(), -1, null, "DESC",
            true);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @ParameterizedTest(name = "Verify default limit pagination param for getting list of games. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getListOfGamesWithDefaultPaginationLimitParam(GameType gameType)
      throws HttpException {

    var response = GamesEndpoint.getListOfGame(gameType.getValue(), GameStatus.OPEN.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @ParameterizedTest(name = "Verify pagination params for getting list of games. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesPaginationParams(GameType gameType)
      throws HttpException, IllegalArgumentException {

    final var gamesCount = Helper.generateRandomNumber(10, 15);

    final var resultsLimit = 5;

    createGames(gameType, gamesCount);

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, GameStatus.OPEN.getValue(),
            gameType.getValue(),
            resultsLimit, null);

    currentTestResponse.set(response);

    var listOfGamesData = response.as(ListOfGamesData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(listOfGamesData.getData().get(listOfGamesData.getData().size() - 1).getId()));
  }
  @ParameterizedTest(name = "Verify move to next page for getting list of games works. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesPaginationMoveToNextPage(GameType gameType)
      throws HttpException, IllegalArgumentException {

    final var gamesCount = Helper.generateRandomNumber(20, 30);

    final var resultsLimit = Helper.generateRandomNumber(1, 15);

    var gamesIdList = createGames(gameType, gamesCount);

    var response =
        getListOfGames(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, GameStatus.OPEN.getValue(),
            gameType.getValue(),
            resultsLimit, null);

    currentTestResponse.set(response);

    validateGetListOfGamesResponse(gameType, response, gamesIdList, resultsLimit);
  }

  @ParameterizedTest(name = "Verify game_ids query filter is working for game type {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getListOfGamesWithGameIdsFilterForGameType(GameType gameType)
      throws HttpException, IllegalArgumentException {

    final var gamesCount = Helper.generateRandomNumber(20, 30);

    var gameIds = createGames(gameType, gamesCount);

    var gameIdsCommaSeparated = String.join(",", gameIds);

    var response =
        getGamesWithIdsAndLimit(gameIdsCommaSeparated, gameType.getValue(), 40);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            gameType == GameType.TOP_X ? GET_GAME_LIST_TOP_X_SCHEMA
                : GET_GAME_LIST_MATCH_QUIZ_SCHEMA))
        .body("meta.pagination.items_per_page", equalTo(gameIds.size()))
        .body("meta.pagination.next_page_starts_after", is(nullValue()))
        .body("data." + ID_PROP, containsInAnyOrder(gameIds.toArray()));
  }

  /**
   * The order of the games is based on the match kickoff time. In the case of MATCH_QUIZ, for each
   * game, it is considered the kickoff time of the earliest match
   */
  @Execution(ExecutionMode.SAME_THREAD)
  @ParameterizedTest(
      name =
          "Verify sort order is working correctly for all types of games. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("one")})
  @EnumSource(
      value = GameType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void sortOrderTheListOfGames(GameType gameType)
      throws HttpException, IllegalArgumentException {

    final var numOfMatchesPerGame = gameType == GameType.TOP_X ? 6 : 1;
    final var gamesCount = Helper.generateRandomNumber(6, 9);

    List<String> gameTitleList = new ArrayList<>();
    Map<LocalDateTime, GameInstance> gameInstanceMap = new HashMap<>();

    for (int i = 0; i < gamesCount; i++) {
      String gameTitle = String.format("%s - Match with kickoffAdjustment SORTED", i + 1);
      gameTitleList.add(gameTitle);
    }

    List<Match> matchList;

    for (int i = 0; i < gamesCount; i++) {
       matchList = new ArrayList<>();

      for (int k = 0; k < numOfMatchesPerGame; k++) {
        matchList.add(
            MatchGenerator.generateMatchWithKickoffMinutesAs(
                TEAM_ID_LIVERPOOL, TEAM_ID_MAN_UTD, PREMIER_LEAGUE_COMP_ID, i + 60));
      }

      for (Match match : matchList) {
        match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
        match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
      }

      Resolver.openMatchesForPredictions(matchList);

        List<String> list = new ArrayList<>();
        for (Match match : matchList) {
            String id = match.getId();
            list.add(id);
        }
        var gameInstance =
          CreateGameEndpoint.builder()
              .gameType(gameType)
              .matchesIdList(list)
              .predictionsCutoff(
                  matchList.stream()
                      .map(Match::getKickoffAt)
                      .sorted()
                      .toList()
                      .get(0)
                      .atZone(ZoneId.of("UTC"))
                      .minusMinutes(15))
              .title(gameTitleList.get(i))
              .build()
              .createGame()
              .as(GameInstance.class);

      var maxKickoffAt = matchList.get(0).getKickoffAt();

      // For TOP_X  get the kickoff_at with max value
      if (gameType.equals(GameType.TOP_X)) {
        maxKickoffAt =
            matchList.stream().max(Comparator.comparing(Match::getKickoffAt)).get().getKickoffAt();
      }

      gameInstanceMap.put(maxKickoffAt, gameInstance);

      RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());
    }
    assertSortOrderBasedOnMatchKickoffAt(gameType, gameInstanceMap, DESC);
    assertSortOrderBasedOnMatchKickoffAt(gameType, gameInstanceMap, ASC);
  }

  /**
   * The order of the games is based on the match kickoff time.
   * In the case of Top X, for each game, it is considered the kickoff time of the earliest match

   * Because it is difficult to evaluate all matches kickoff time, the test evaluates the title of the game.
   * There are between 6 and 9 games generated.
   * The first game has title generated in format '1 - .....'
   * The next game has title generated in format '2 - .....' etc
   * The first game matches kickoff time is always the earliest.
   * Then every next game matches kickoff time is later than the previous game.
   * This way the game with title starting smaller digit have always matches with earlier kickoff time.
   * This now allows the assertion to be based on Game Title which is easier than Game Matches Kickoff time
   *
   */

  @ParameterizedTest(name = "Verify that the games are sorted DESC by default. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("one")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void verifyThatTheGamesAreSortedDESCByDefault(GameType gameType)
      throws HttpException, IllegalArgumentException {

    final var numOfMatchesPerGame = gameType == GameType.TOP_X ? 6 : 1;
    final var gamesCount = Helper.generateRandomNumber(6, 9);

    List<String> gameTitleList = new ArrayList<>();
    Map<LocalDateTime, GameInstance> gameInstanceMap = new HashMap<>();

    for (int i = 0; i < gamesCount; i++) {
      String gameTitle = String.format("%s - Match with kickoffAdjustment Default Sort", i + 1);
      gameTitleList.add(gameTitle);
    }

    for (int i = 0; i < gamesCount; i++) {
      var matchList = new ArrayList<Match>();

      for (int k = 0; k < numOfMatchesPerGame; k++) {
        matchList.add(
                MatchGenerator.generateMatchWithKickoffMinutesAs(
                        TEAM_ID_LIVERPOOL, TEAM_ID_MAN_UTD, PREMIER_LEAGUE_COMP_ID, i + 60));
      }

      for (Match match : matchList) {
        match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
        match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
      }

      Resolver.openMatchesForPredictions(matchList);

        List<String> list = new ArrayList<>();
        for (Match match : matchList) {
            String id = match.getId();
            list.add(id);
        }
        var gameInstance =
              CreateGameEndpoint.builder()
                      .gameType(gameType)
                      .matchesIdList(list)
                      .predictionsCutoff(
                              matchList.stream()
                                      .map(Match::getKickoffAt)
                                      .sorted()
                                      .toList()
                                      .get(0)
                                      .atZone(ZoneId.of("UTC"))
                                      .minusMinutes(15))
                      .title(gameTitleList.get(i))
                      .build()
                      .createGame()
                      .as(GameInstance.class);

      var maxKickoffAt = matchList.get(0).getKickoffAt();

      // For TOP_X  get the kickoff_at with max value
      if (gameType.equals(GameType.TOP_X)) {
        maxKickoffAt =
                matchList.stream().max(Comparator.comparing(Match::getKickoffAt)).get().getKickoffAt();
      }

      gameInstanceMap.put(maxKickoffAt, gameInstance);

      RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());
    }
    assertSortOrderBasedOnMatchKickoffAt(gameType, gameInstanceMap, DESC);
  }
}