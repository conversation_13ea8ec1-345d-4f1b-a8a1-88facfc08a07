package com.fansunited.automation.predictionapi.games.post;

import static com.fansunited.automation.constants.ApiConstants.LIMIT_QUERY_IDS;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_MATCH_QUIZ_SCHEMA;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_TOP_X_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.TestGroups.STAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getNonWhitelistedCompetitions;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getTopXMaxFixtures;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getFinishedMatchesIdList;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getMatchesIdListAfterDate;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGameStatusToUpdate;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.Helper.subtractMinutesFromZonedDateTime;
import static com.fansunited.automation.helpers.WaitHelper.waitMatchStatusToBeUpdatedFromNotStartedToPostponed;
import static java.time.ZoneOffset.UTC;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.validators.ErrorValidator;
import com.fansunited.automation.validators.RedisValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - POST /v1/games endpoint validation tests")
public class CreateGameValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify game cannot be created with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void createGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var createGameRequest = CreateGameRequest.builder().build();

    var response =
        createGame(createGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER,
            CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify game cannot be created with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void createGamesWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        createGame(CreateGameRequest.builder().build(), argumentsHolder.getJwtToken());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(name = "Verify games cannot be created with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void createGameWithInvalidClientId(String clientId) throws HttpException {

    var createGameRequest = CreateGameRequest.builder().build();

    var response =
        createGame(createGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, clientId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if(clientId == null) {
      response
          .then()
          .assertThat()
          .statusCode(equalTo(HttpStatus.SC_FORBIDDEN));
    } else {
      ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify games cannot be created by clients from different project")
  @EnabledIf("isUseStageEnvironment")
  public void createGameForAnotherClient() throws HttpException {

    var gameFixtureList = new ArrayList<GameFixture>();

    var predictionsCutoff = generateFutureDate(12);

    var matchId = getSingleMatchIdAfterDate(
        getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(predictionsCutoff));

    PredictionMarket.getValidMarkets().forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(matchId)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description("Saturday derbies")
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .type(GameType.MATCH_QUIZ.getValue())
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response =
        createGame(createGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_PRODUCTION_TESTING_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify games cannot be created by billing user")
  @EnabledIf("isUseStageEnvironment")
  public void billingUserCanNotCreateGame() throws HttpException {

    var gameFixtureList = new ArrayList<GameFixture>();

    var predictionsCutoff = generateFutureDate(12);

    var matchId = getSingleMatchIdAfterDate(
        getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(predictionsCutoff));

    PredictionMarket.getValidMarkets().forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(matchId)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description("Saturday derbies")
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .type(GameType.MATCH_QUIZ.getValue())
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response =
        createGame(createGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            BILLING_USER, CLIENT_PRODUCTION_TESTING_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create a game with non supported content type")
  public void createGameWithNotSupportedContentType()
      throws HttpException {

    var response =
        createGame("", FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.TEXT);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users cannot create games")
  public void createGameByEndUser() throws HttpException {

    var createGameRequest = CreateGameRequest.builder().build();

    var response =
        createGame(createGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions cannot create games")
  public void createGameByUserWithInsufficientPermissions() throws HttpException {

    var createGameRequest = CreateGameRequest.builder().build();

    var response =
        createGame(createGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            BILLING_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify game cannot be created without auth")
  public void createGameWithoutAuth() throws HttpException {

    var createGameRequest = CreateGameRequest.builder().build();

    var response =
        createGame(createGameRequest, null,
            null, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
            .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with invalid type. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createGameWithInvalidType(GameType gameType) throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList =
        generateValidFixturesForGameType(GameType.TOP_X, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with non supported date format for predictions_cutoff prop. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithNonSupportedDateFormatForPredictionsCutoff(GameType gameType)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(predictionsCutoff.toString())
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify creation of a game when predictions cutoff is empty. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void createGameWithEmptyDateForPredictionsCutoff(GameType gameType) throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff("")
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            gameType == GameType.TOP_X ? CREATE_GAME_TOP_X_SCHEMA : CREATE_GAME_MATCH_QUIZ_SCHEMA));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with empty fixtures list . Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithEmptyFixtureList(GameType gameType) throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(List.of())
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Disabled("Need to be fixed in FZ-2988")
  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when trying to create a game with fixtures list set to NULL . Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = GameType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithFixtureListSetToNull(GameType gameType) throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(null)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create a TOP_X game with more games than the max allowed")
  public void createTopXGameWithMoreGamesThanMaxAllowed() throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.TOP_X),
        generateDateTimeInIsoFormat(predictionsCutoff), getTopXMaxFixtures() + 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    matchesIdList.forEach(match ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(PredictionMarket.CORRECT_SCORE.getValue())
            .matchId(match)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.TOP_X + " " + UUID.randomUUID())
        .description(GameType.TOP_X + " " + UUID.randomUUID())
        .type(GameType.TOP_X.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create a MATCH_QUIZ game with different match IDs")
  public void createMatchQuizGameWithDifferentMatchIds()
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(predictionsCutoff), 3);

    var gameFixtureList = new ArrayList<GameFixture>();

    PredictionMarket.getValidMarkets().forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(matchesIdList.get(generateRandomNumber(0, matchesIdList.size() - 1)))
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .type(GameType.MATCH_QUIZ.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a TOP_X game with invalid match type. Match type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = MatchType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createTopXGameWithInvalidMatchType(MatchType matchType)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.TOP_X),
        generateDateTimeInIsoFormat(predictionsCutoff), 6);

    var gameFixtureList = new ArrayList<GameFixture>();

    matchesIdList.forEach(match ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(matchType.getValue())
            .market(PredictionMarket.CORRECT_SCORE.getValue())
            .matchId(match)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.TOP_X + " " + UUID.randomUUID())
        .description(GameType.TOP_X + " " + UUID.randomUUID())
        .type(GameType.TOP_X.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a MATCH_QUIZ game with invalid match type. Match type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = MatchType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createMatchQuizGameWithInvalidMatchType(MatchType matchType)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(predictionsCutoff), 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    PredictionMarket.getValidMarkets().forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(matchType.getValue())
            .market(market.getValue())
            .matchId(matchesIdList.get(0))
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .type(GameType.MATCH_QUIZ.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a TOP_X game with invalid market. Market: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createTopXGameWithInvalidMarket(PredictionMarket market)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.TOP_X),
        generateDateTimeInIsoFormat(predictionsCutoff), 6);

    var gameFixtureList = new ArrayList<GameFixture>();

    matchesIdList.forEach(match ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(match)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.TOP_X + " " + UUID.randomUUID())
        .description(GameType.TOP_X + " " + UUID.randomUUID())
        .type(GameType.TOP_X.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create a MATCH_QUIZ game with duplicate markets")
  public void createMatchQuizGameWithDuplicateMarkets()
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(predictionsCutoff), 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    PredictionMarket.getValidMarkets().forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(matchesIdList.get(0))
            .build()));

    var duplicateMarket = PredictionMarket.getValidMarkets().get(0);

    gameFixtureList.add(GameFixture
        .builder()
        .matchType(MatchType.FOOTBALL.getValue())
        .market(duplicateMarket.getValue())
        .matchId(matchesIdList.get(0))
        .build());

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .type(GameType.MATCH_QUIZ.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a MATCH_QUIZ game with invalid market. Market: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createMatchQuizGameWithInvalidMarket(PredictionMarket market)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.TOP_X),
        generateDateTimeInIsoFormat(predictionsCutoff), 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    gameFixtureList.add(GameFixture
        .builder()
        .matchType(MatchType.FOOTBALL.getValue())
        .market(market.getValue())
        .matchId(matchesIdList.get(0))
        .build());

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .type(GameType.MATCH_QUIZ.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a TOP_X game with invalid/empty match id. Match id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = ApiConstants.FootballApi.INVALID_MATCH_ID)
  @NullAndEmptySource
  public void createTopXGameWithInvalidMatchId(String matchId)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList = new ArrayList<GameFixture>();

    gameFixtureList.add(GameFixture
        .builder()
        .matchType(MatchType.FOOTBALL.getValue())
        .market(PredictionMarket.CORRECT_SCORE.getValue())
        .matchId(matchId)
        .build());

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.TOP_X + " " + UUID.randomUUID())
        .description(GameType.TOP_X + " " + UUID.randomUUID())
        .type(GameType.TOP_X.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a MATCH_QUIZ game with invalid/empty match id. Match id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = ApiConstants.FootballApi.INVALID_MATCH_ID)
  @NullAndEmptySource
  public void createMatchQuizGameWithInvalidMatchId(String matchId)
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList = new ArrayList<GameFixture>();

    PredictionMarket.getValidMarkets().forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(matchId)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .type(GameType.MATCH_QUIZ.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create a TOP_X game with different market than the allowed one(CORRECT_SCORE)")
  public void createTopXGameWithDifferentMarket()
      throws HttpException {

    var predictionsCutoff = generateFutureDate(12);

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.TOP_X),
        generateDateTimeInIsoFormat(predictionsCutoff), 6);

    var gameFixtureList = new ArrayList<GameFixture>();

    matchesIdList.forEach(match ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(PredictionMarket.FT_1X2.getValue())
            .matchId(match)
            .build()));

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.TOP_X + " " + UUID.randomUUID())
        .description(GameType.TOP_X + " " + UUID.randomUUID())
        .type(GameType.TOP_X.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with matches that starts after predictions cutoff time. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithPredictionsCutoffAfterMatchesStart(GameType gameType)
      throws HttpException {

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, generateFutureDate(12));

    CreateGameRequest createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(generateFutureDate(1440)))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with finished matches. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithFinishedMatches(GameType gameType)
      throws HttpException {

    var matchesIdList = getFinishedMatchesIdList(getCompetitionsWhitelist(gameType),
        gameType == GameType.TOP_X ? 6 : 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    switch (gameType) {
      case TOP_X -> matchesIdList.forEach(match ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(PredictionMarket.CORRECT_SCORE.getValue())
              .matchId(match)
              .build()));

      case MATCH_QUIZ -> PredictionMarket.getValidMarkets().forEach(market ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(market.getValue())
              .matchId(matchesIdList.get(0))
              .build()));
    }

    CreateGameRequest createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(generateFutureDate(12)))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with past predictions_cutoff date. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithInvalidPredictionsCutoff(GameType gameType)
      throws HttpException {

    var matchesIdList = getMatchesIdListAfterDate(getCompetitionsWhitelist(gameType),
        generateDateTimeInIsoFormat(generateFutureDate(12)),
        gameType == GameType.TOP_X ? 6 : 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    switch (gameType) {
      case TOP_X -> matchesIdList.forEach(match ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(PredictionMarket.CORRECT_SCORE.getValue())
              .matchId(match)
              .build()));

      case MATCH_QUIZ -> PredictionMarket.getValidMarkets().forEach(market ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(market.getValue())
              .matchId(matchesIdList.get(0))
              .build()));
    }

    CreateGameRequest createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(subtractMinutesFromZonedDateTime(
            ZonedDateTime.now(), 60)))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create a game with matches from NON WHITELISTED competitions. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void createGameWithMatchesFromNonWhitelistedCompetition(GameType gameType)
      throws HttpException {
    var matchList = MatchGenerator.generateMatchesInFutureInvalidCompetition(6, 16);
    Resolver.openMatchesForPredictions(matchList);

    var matchesIdList = getMatchesIdListAfterDate(
        getNonWhitelistedCompetitions(gameType).subList(0, LIMIT_QUERY_IDS),
        Helper.generateDateTimeInIsoFormat(generateFutureDate(12)),
        gameType == GameType.TOP_X ? 6 : 1);

    var gameFixtureList = new ArrayList<GameFixture>();

    switch (gameType) {
      case TOP_X -> matchesIdList.forEach(match ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(PredictionMarket.CORRECT_SCORE.getValue())
              .matchId(match)
              .build()));

      case MATCH_QUIZ -> PredictionMarket.getValidMarkets().forEach(market ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(market.getValue())
              .matchId(matchesIdList.get(0))
              .build()));
    }

    CreateGameRequest createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(generateFutureDate(12)))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify game title cannot be longer than 255 chars. Creating game: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameForTypeWithLongTitle(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var title = RandomStringUtils.randomAlphabetic(256);

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(title)
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(name = "Verify game description cannot be longer than 50001 chars. Creating game: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameForTypeWithLongDesc(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var desc = RandomStringUtils.randomAlphabetic(50001);

    var predictionsCutoff = generateFutureDate(120);

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(desc)
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var response = createGame(createGameRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest
  @DisplayName("Verify creation of a game with invalid schedule time. Creating game: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createGameWithInvalidScheduleTime(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    System.out.println(ZonedDateTime.now(UTC).minusHours(5));

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .scheduleOpenAt(ZonedDateTime.now(UTC).minusHours(5).toString())
        .fixtures(gameFixtureList)
        .status(GameStatus.PENDING.getValue())
        .build();

    var createGameResponse = createGame(createGameRequest);

    createGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName("Points not given for prediction on fixture when status VOID due to postponed match - Top X Partially Correct")
  public void topXgameIsSetIncorrectly()
      throws HttpException,
      InterruptedException {

    var match = MatchGenerator.generateMatchesInFuture(1, 6).get(0);
    match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
    match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    Resolver.openMatchForPredictions(match);
    ResolverBase.init();

    var gameInstance =
        GamesEndpoint.createGame(Collections.singletonList(match.getId()), GameType.TOP_X,
                        GameStatus.OPEN, match.getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    predictionFixtures.add(
        CorrectScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsFullTimeHome())
            .goalsAway(match.getGoalsFullTimeAway())
            .build());

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    var createPredictionResponse = PredictionsEndpoint.createPrediction(createPredictionRequest);

    createPredictionResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    //Postpone the match
    updateMatchToAnotherStatus(match.getId(), MatchGenerator.STATUS_POSTPONED);

    waitMatchStatusToBeUpdatedFromNotStartedToPostponed(match.getId(), 3, 10);

    Resolver.resolve();
    //Wait for the Game and Prediction Fixture Statuses to updated
    waitForGameStatusToUpdate(gameInstance.getId(), GameStatus.CANCELED.getValue(), 10000, 550);
    //Assert Game Status
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    ResolverBase.cleanUp(cleanUpMatchIdList);
  }
}
