package com.fansunited.automation.predictionapi.games.post;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.DESC_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_COVER;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_MAIN;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_MOBILE;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.PREDICTIONS_CUTOFF_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.PREDICTIONS_CUTOFF_SUBTRACTION_MINUTES;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_OUTCOME;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_STATUS;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.SCHEDULER;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.TITLE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.UPDATED_AT_PROP;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.TestGroups.STAGE;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.extractEarliestMatchKickOffDate;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getMatchKickOffDate;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGameRequestForTopXGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createTopXGameForSpecificMarket;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.setUpImages;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getAllFixtures;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGamePredictionFixtureOutcomeAndStatusToUpdate;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGameStatusToUpdate;
import static com.fansunited.automation.helpers.Helper.convertStringDateTimeToZonedTime;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.helpers.Helper.subtractMinutesFromZonedDateTime;
import static com.fansunited.automation.helpers.RelatedFieldGenerator.getAllEntityTypeRelationships;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.MATCH_QUIZ;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGameCreationResponse;
import static java.time.ZoneOffset.UTC;
import static org.apache.http.HttpStatus.SC_OK;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.RequestGenerator;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.fansunited.automation.validators.RedisValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

@DisplayName("Prediction Api - POST /v1/games endpoint happy path tests")
public class CreateGameTests extends PredictionApiBaseTest {
  // Image
  @ParameterizedTest(name = "Verify creation of a game. Creating game: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void createGameForType(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(2);

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var mainImageUrl = "https://example/imageurl";
    var coverImageUrl = "https://example/folder/imageurl";
    var mobileImageUrl = "http://example/imageurl";

    var images = setUpImages(mainImageUrl, coverImageUrl, mobileImageUrl);
    var generateScheduleTime =
        Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusMinutes(5));

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(generateDateTimeInIsoFormat(predictionsCutoff))
            .scheduleOpenAt(generateScheduleTime)
            .fixtures(gameFixtureList)
            .status(GameStatus.PENDING.getValue())
            .images(images)
            .branding(
                BrandingDTO.builder()
                    .colors(
                        BrandingColorsDTO.builder()
                            .additionalColor("test")
                            .backgroundColor("")
                            .primaryColor("")
                            .contentColor("")
                            .borderColor("")
                            .secondaryColor("")
                            .build())
                    .urls(
                        BrandingUrlsDTO.builder()
                            .primaryUrl("тест")
                            .privacyPolicyUrl("тест")
                            .additionalUrl("тест")
                            .build())
                    .images(
                        BrandingImagesDTO.builder()
                            .additionalImage("тест")
                            .backgroundImage("тест")
                            .mainLogo("тест")
                            .mobileBackgroundImage("тест")
                            .build())
                    .build())
            .adContent("test")
            .labels(Fields.builder().label1("test_////.!567").label2("test2").build())
            .customFields(Fields.builder().label2("test").label1("test_1").build())
            .build();

    var createGameResponse = createGame(createGameRequest);

    validateGameCreationResponse(gameType, createGameResponse, createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    var response = GameEndpoint.getGameById(gameInstance.getId());

    currentTestResponse.set(response);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(gameInstance, redisGamesResponse);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                gameType == GameType.TOP_X
                    ? JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_TOP_X_SCHEMA
                    : JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_MATCH_QUIZ_SCHEMA))
        .body(ID_PROP, equalTo(gameInstance.getId()))
        .body(TITLE_PROP, equalTo(gameInstance.getTitle()))
        .body(DESC_PROP, equalTo(gameInstance.getDescription()))
        .body(TYPE_PROP, equalTo(gameInstance.getType()))
        .body(STATUS_PROP, equalTo(gameInstance.getStatus()))
        .body(FIXTURES_PROP, hasSize(gameInstance.getFixtures().size()))
        .body(PREDICTIONS_CUTOFF_PROP, equalTo(gameInstance.getPredictionsCutoff()))
        .body(UPDATED_AT_PROP, is(notNullValue()))
        .body(IMAGES_PROP + "." + IMAGES_MAIN, equalTo(mainImageUrl))
        .body(IMAGES_PROP + "." + IMAGES_COVER, equalTo(coverImageUrl))
        .body(IMAGES_PROP + "." + IMAGES_MOBILE, equalTo(mobileImageUrl))
        .body(SCHEDULER, equalTo(generateScheduleTime));
  }

  @ParameterizedTest(
      name = "Verify creation of a game without title and description. Creating game: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void createGameWithoutNameAndDescription(GameType gameType)
      throws HttpException, IllegalArgumentException {

    List<Match> matchList =
        MatchGenerator.generateMatches(gameType == GameType.TOP_X ? 6 : 1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var predictionsCutoff = localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16);

    CreateGameEndpoint createGameEndpoint =
        CreateGameEndpoint.builder()
            .gameType(gameType)
            .ignoreDescription(true)
            .predictionsCutoff(predictionsCutoff)
            .matchesIdList(matchList.stream().map(Match::getId).toList())
            .build();

    var gameResponse = createGameEndpoint.createGame();

    var gameInstance = gameResponse.as(GameInstance.class);

    var response = GameEndpoint.getGameById(gameInstance.getId());

    currentTestResponse.set(response);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(gameInstance, redisGamesResponse);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                gameType == GameType.TOP_X
                    ? JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_TOP_X_SCHEMA
                    : JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_MATCH_QUIZ_SCHEMA))
        .body(ID_PROP, equalTo(gameInstance.getId()))
        .body(TITLE_PROP, nullValue())
        .body(DESC_PROP, equalTo(gameInstance.getDescription()))
        .body(TYPE_PROP, equalTo(gameInstance.getType()))
        .body(STATUS_PROP, equalTo(gameInstance.getStatus()))
        .body(FIXTURES_PROP, hasSize(gameInstance.getFixtures().size()))
        .body(PREDICTIONS_CUTOFF_PROP, equalTo(gameInstance.getPredictionsCutoff()))
        .body(UPDATED_AT_PROP, notNullValue()); // Check that updated_at is not null
  }

  @ParameterizedTest(
      name = "Verify creation of a game without setting predictions cutoff. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void createGameWithoutPredictionsCutoff(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .ignorePredictionsCutoff(true)
            .fixtures(gameFixtureList)
            .status(GameStatus.OPEN.getValue())
            .build();

    var createGameResponse = createGame(createGameRequest);

    if (gameType == GameType.TOP_X) {
      var matchesIdList = new ArrayList<String>();

      gameFixtureList.forEach(gameFixture -> matchesIdList.add(gameFixture.getMatchId()));

      createGameRequest.setPredictionsCutoff(
          generateDateTimeInIsoFormat(
              subtractMinutesFromZonedDateTime(
                  extractEarliestMatchKickOffDate(matchesIdList),
                  PREDICTIONS_CUTOFF_SUBTRACTION_MINUTES)));
    } else if (gameType == MATCH_QUIZ) {

      createGameRequest.setPredictionsCutoff(
          generateDateTimeInIsoFormat(
              subtractMinutesFromZonedDateTime(
                  convertStringDateTimeToZonedTime(
                      getMatchKickOffDate(gameFixtureList.get(0).getMatchId())),
                  PREDICTIONS_CUTOFF_SUBTRACTION_MINUTES)));
    }

    validateGameCreationResponse(gameType, createGameResponse, createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    var response = GameEndpoint.getGameById(gameInstance.getId());

    currentTestResponse.set(response);

    var redisGamesResponse = RedisEndpoint.getGameById(gameInstance.getId());

    RedisValidator.validateGameExtractedByIdInRedis(gameInstance, redisGamesResponse);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                gameType == GameType.TOP_X
                    ? JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_TOP_X_SCHEMA
                    : JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_MATCH_QUIZ_SCHEMA))
        .body(ID_PROP, equalTo(gameInstance.getId()))
        .body(TITLE_PROP, equalTo(gameInstance.getTitle()))
        .body(DESC_PROP, equalTo(gameInstance.getDescription()))
        .body(TYPE_PROP, equalTo(gameInstance.getType()))
        .body(STATUS_PROP, equalTo(gameInstance.getStatus()))
        .body(FIXTURES_PROP, hasSize(gameInstance.getFixtures().size()))
        .body(PREDICTIONS_CUTOFF_PROP, equalTo(gameInstance.getPredictionsCutoff()))
        .body(UPDATED_AT_PROP, equalTo(gameInstance.getUpdated_at()));
  }

  @ParameterizedTest(name = "Verify creation of Top X game with market: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @MethodSource(
      "com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket#getTopXGameMarkets")
  public void createTopXGame(PredictionMarket market)
      throws HttpException, IllegalArgumentException {
    var matchList = MatchGenerator.generateMatches(1, false);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
          match.setKickoffAt((LocalDateTime.now().plusMinutes(130)));
        });

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var createGameRequest =
        createGameRequestForTopXGame(
            generateFutureDate(1), market, matchList.stream().map(Match::getId).toList());

    var createGameResponse = createTopXGameForSpecificMarket(createGameRequest);

    validateGameCreationResponse(GameType.TOP_X, createGameResponse, createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    var response = GameEndpoint.getGameById(gameInstance.getId());

    currentTestResponse.set(response);

    var redisGamesResponse = RedisEndpoint.getGameById(gameInstance.getId());

    RedisValidator.validateGameExtractedByIdInRedis(gameInstance, redisGamesResponse);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.PredictionApi.Endpoints.Games.CREATE_GAME_TOP_X_SCHEMA))
        .body(ID_PROP, equalTo(gameInstance.getId()))
        .body(TITLE_PROP, equalTo(gameInstance.getTitle()))
        .body(DESC_PROP, equalTo(gameInstance.getDescription()))
        .body(TYPE_PROP, equalTo(gameInstance.getType()))
        .body(STATUS_PROP, equalTo(gameInstance.getStatus()))
        .body(FIXTURES_PROP, hasSize(gameInstance.getFixtures().size()))
        .body(PREDICTIONS_CUTOFF_PROP, equalTo(gameInstance.getPredictionsCutoff()))
        .body(UPDATED_AT_PROP, equalTo(gameInstance.getUpdated_at()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "OPENED Match Quiz status is set to CANCELED and Fixture Outcome and Status to VOID when the match is postponed")
  public void setGameStatusCanceledWhenMatchPostponedMatchQuiz()
      throws HttpException, InterruptedException {

    // Create a game
    var predictionsCutoff = generateFutureDate(12);

    var gameType = MATCH_QUIZ;

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.OPEN.getValue())
            .build();

    var createGameResponse = createGame(createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    currentTestResponse.set(createGameResponse);

    // Create a prediction
    var matchId = gameInstance.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameInstance.getId())
            .build();

    createPrediction(createPredictionRequest);

    // Postpone the match
    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(0).getMatchId(), MatchGenerator.STATUS_POSTPONED);

    // Wait for the Game and Prediction Fixture Statuses to updated
    waitForGameStatusToUpdate(gameInstance.getId(), GameStatus.CANCELED.getValue(), 10000, 480);

    // Assert Game Status
    Response updatedGameResponse = GameEndpoint.getGameById(gameInstance.getId());

    updatedGameResponse
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    // Assert Fixture Outcome and Status
    Response ownPredictions = PredictionsEndpoint.getOwnPredictions();

    ownPredictions
        .then()
        .assertThat()
        .body(
            "data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.VOID.getValue())))
        .body(
            "data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_STATUS,
            everyItem(equalTo(ResultOutcome.VOID.getValue())));

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(0).getMatchId(), MatchGenerator.STATUS_NOT_STARTED);
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "OPENED Top X when last match is postponed, its Fixture Outcome and Status are set to VOID but game status remains OPENED")
  public void setGameFixtureStatusVoidWhenOneMatchPostponedTopX()
      throws HttpException, InterruptedException {

    // Create a game
    int expectedUpdatedFixtureStatusIndex = 5;

    var predictionsCutoff = generateFutureDate(336);

    var gameType = GameType.TOP_X;

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.OPEN.getValue())
            .build();

    var createGameResponse = createGame(createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    currentTestResponse.set(createGameResponse);

    // Create a prediction
    var matchesIdList = gameInstance.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId ->
            predictions.addAll(
                generateValidSinglePredictionFixture(
                    PredictionMarket.CORRECT_SCORE, matchId, null)));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameInstance.getId())
            .build();

    createPrediction(createPredictionRequest);

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(expectedUpdatedFixtureStatusIndex).getMatchId(),
        MatchGenerator.STATUS_POSTPONED);

    waitForGamePredictionFixtureOutcomeAndStatusToUpdate(getCurrentTestUser().getEmail(), ResultOutcome.VOID.getValue(),
        expectedUpdatedFixtureStatusIndex, 10000, 360);

    // Assert Game Status
    Response updatedGameResponse = GameEndpoint.getGameById(gameInstance.getId());

    updatedGameResponse.then().assertThat().body(STATUS_PROP, equalTo(GameStatus.OPEN.getValue()));

    // Assert Fixture Outcome
    Response ownPredictions = PredictionsEndpoint.getOwnPredictions();

    ownPredictions
        .then()
        .assertThat()
        .body(
            "data[0]."
                + FIXTURES_PROP
                + "["
                + expectedUpdatedFixtureStatusIndex
                + "]."
                + RESULT_PROP
                + "."
                + RESULT_OUTCOME,
            equalTo(ResultOutcome.VOID.getValue()))
        .body(
            "data[0]." + FIXTURES_PROP + "[2]." + RESULT_PROP + "." + RESULT_OUTCOME,
            not(equalTo(ResultOutcome.VOID.getValue())))
        .body(
            "data[0]."
                + FIXTURES_PROP
                + "["
                + expectedUpdatedFixtureStatusIndex
                + "]."
                + RESULT_PROP
                + "."
                + RESULT_STATUS,
            equalTo(ResultOutcome.VOID.getValue()))
        .body(
            "data[0]." + FIXTURES_PROP + "[3]." + RESULT_PROP + "." + RESULT_STATUS,
            not(equalTo(ResultOutcome.VOID.getValue())));

    String predictionOutcome =
        ownPredictions
            .as(PredictionsData.class)
            .getData()
            .get(0)
            .getFixtures()
            .get(expectedUpdatedFixtureStatusIndex)
            .getResult()
            .getOutcome()
            .getValue();

    Assertions.assertEquals(predictionOutcome, ResultOutcome.VOID.getValue());

    // Assert Fixture Status
    String predictionStatus =
        ownPredictions
            .as(PredictionsData.class)
            .getData()
            .get(0)
            .getFixtures()
            .get(expectedUpdatedFixtureStatusIndex)
            .getResult()
            .getStatus()
            .name();

    Assertions.assertEquals(predictionStatus, ResultOutcome.VOID.getValue());

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(expectedUpdatedFixtureStatusIndex).getMatchId(),
        MatchGenerator.STATUS_NOT_STARTED);
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL), @Tag("FZ-1794")})
  @DisplayName("Status for Top X game remains PENDING when last match is postponed")
  public void setGameStatusStayInPendingWhenOneMatchPostponedTopX()
      throws HttpException, InterruptedException {

    // Create a game
    int expectedUpdatedFixtureStatusIndex = 5;

    var predictionsCutoff = generateFutureDate(336);

    var gameType = GameType.TOP_X;

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.PENDING.getValue())
            .build();

    var createGameResponse = createGame(createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    currentTestResponse.set(createGameResponse);

    // Create a prediction
    var matchesIdList = gameInstance.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId ->
            predictions.addAll(
                generateValidSinglePredictionFixture(
                    PredictionMarket.CORRECT_SCORE, matchId, null)));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameInstance.getId())
            .build();

    createPrediction(createPredictionRequest);

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(expectedUpdatedFixtureStatusIndex).getMatchId(),
        MatchGenerator.STATUS_POSTPONED);

    // Wait for scheduler
    Thread.sleep(60100);

    // Assert Game Status
    Response updatedGameResponse = GameEndpoint.getGameById(gameInstance.getId());

    updatedGameResponse
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.PENDING.getValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE), @Tag(LOCAL)})
  @DisplayName(
      "OPENED Top X status is set to 'CANCELED' and Fixture Outcome and Status to 'VOID' when all matches are postponed")
  public void setGameStatusCanceledWhenAllMatchesPostponedTopX()
      throws HttpException, InterruptedException {

    //Create a game
    var predictionsCutoff = ZonedDateTime.now().plusMinutes(2);
    var gameType = GameType.TOP_X;
    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);
    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.OPEN.getValue())
            .build();
    var createGameResponse = createGame(createGameRequest);
    var gameInstance = createGameResponse.as(GameInstance.class);

    currentTestResponse.set(createGameResponse);

    // Create a prediction
    var matchesIdList = gameInstance.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId ->
            predictions.addAll(
                generateValidSinglePredictionFixture(
                    PredictionMarket.CORRECT_SCORE, matchId, null)));

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameInstance.getId())
            .build();

    createPrediction(createPredictionRequest);

    waitForGameStatusToUpdate(gameInstance.getId(), LIVE.getValue(), 10000, 360); // Wait for game status to be updated to live

    IntStream.range(0, matchesIdList.size()).forEach(index ->
        updateMatchToAnotherStatus(
            gameInstance.getFixtures().get(index).getMatchId(),
            MatchGenerator.STATUS_POSTPONED));

    Resolver.resolve();
    waitForGameStatusToUpdate(gameInstance.getId(), GameStatus.CANCELED.getValue(), 10000, 350);

    // Assert Game Status
    Response updatedGameResponse = GameEndpoint.getGameById(gameInstance.getId());

    updatedGameResponse
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    // Assert Fixture Outcome and Status
    Response ownPredictions = PredictionsEndpoint.getOwnPredictions();

    ownPredictions
        .then()
        .assertThat()
        .body(
            "data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.VOID.getValue())))
        .body(
            "data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_STATUS,
            everyItem(equalTo(ResultOutcome.VOID.getValue())));

    IntStream.range(0, gameInstance.getFixtures().size())
        .forEach(
            index -> {
              updateMatchToAnotherStatus(
                  gameInstance.getFixtures().get(index).getMatchId(),
                  MatchGenerator.STATUS_NOT_STARTED);
            });
  }

  @ParameterizedTest(name = "Create game for type {arguments} with configured related field")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(
      value = GameType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void createGameWithConfiguredRelatedField(GameType gameType) throws HttpException {

    var request = RequestGenerator.getCreateGameRequest(gameType);

    List<RelatedDto> relatedDtoList = getAllEntityTypeRelationships();
    // Set related field and send the request
    request.setRelated(relatedDtoList);

    var response = createGame(request);
    response.then().assertThat().statusCode(SC_OK);

    var actualRelatedList = response.as(GameInstance.class).getRelated();
    assertThat(actualRelatedList)
        .hasSize(relatedDtoList.size())
        .containsExactlyInAnyOrderElementsOf(relatedDtoList);
  }
}
