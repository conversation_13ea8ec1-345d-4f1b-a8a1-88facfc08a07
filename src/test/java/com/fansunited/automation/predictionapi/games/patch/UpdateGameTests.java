package com.fansunited.automation.predictionapi.games.patch;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_MAN_UTD;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_COVER;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_MAIN;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_MOBILE;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.IMAGES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.UPDATED_AT_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.TYPE_TEMPLATE;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getTopXMaxFixtures;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getMatchesIdListAfterDate;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.UserRankingTypes.GAME;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.UserRankingTypes.TEMPLATE;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.GamePredictionsEndpoint.getPredictionsForGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createMatchQuizGameForMarkets;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.setUpImages;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.updateGameStatusInFirebase;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionsForGame;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.GAME_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionField;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGameUpdateResponse;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGameUpdateResponseForAllStatuses;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.loyaltyapi.TemplateByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.MatchStatsGenerator;
import com.fansunited.automation.core.resolver.MatchTimelineGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.enums.TimelineEventType;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.ValidRelatedEntity;
import com.fansunited.automation.validators.PredictionApiValidator;
import com.fansunited.automation.validators.RedisValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.RestAssured;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - PATCH /v1/games endpoint happy path tests")
public class UpdateGameTests extends PredictionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating fixture markets for MATCH_QUIZ game")
  public void updateMatchQuizGameFixturesMarkets() throws HttpException, IllegalArgumentException {

    var markets = PredictionMarket.getValidMarkets();

    var gameInstance =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets.subList(0, markets.size() - 3))
            .as(GameInstance.class);

    var gameInstance2 =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets.subList(0, markets.size() - 3))
            .as(GameInstance.class);
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().funnyName().name())
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .rules(new Faker().regexify("[A-Za-z0-9$&+,:;=?@#|'<>.^*()%!-]{10,20}"))
            .flags(new Faker().lorem().words(Helper.generateRandomNumber(1, 3)))
            .description(new Faker().lorem().characters(10, 1000))
            .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    var matchId = gameInstance.getFixtures().get(0).getMatchId();

    var updatedFixturesList =
        generateValidFixturesForGameType(GameType.MATCH_QUIZ, List.of(matchId));

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .fixtures(updatedFixturesList)
            .adContent("test")
            .labels(Fields.builder().label1("test_////.!567").label2("test2").build())
            .customFields(Fields.builder().label2("test").label1("test_1").build())
            .related(
                List.of(
                    ValidRelatedEntity.builder()
                        .entityType(TYPE_TEMPLATE)
                        .entityId(
                            createTemplateResponse
                                .then()
                                .extract()
                                .body()
                                .jsonPath()
                                .getString(ID_PROP))
                        .entityRelationship("relatedTo")
                        .build()))
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse.then().statusCode(HttpStatus.SC_OK);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGameById(gameInstance.getId());

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(STATUS_PROP, equalTo(GameStatus.PENDING.getValue()));

    var redisGameInstance = updateGameResponse.as(GameInstance.class);
    // We set the `updated_at` field to `null` because there is always a difference of about 4
    // seconds between Firestore and Redis for this field, which is expected behavior.
    updatedGameInstance.setUpdated_at(null);
    redisGameInstance.setUpdated_at(null);
    assertThat(redisGameInstance)
        .usingRecursiveComparison()
        .ignoringFields("updated_at") // Ignore the timestamp field
        .isEqualTo(updatedGameInstance);
    validateGameUpdateResponse(GameType.MATCH_QUIZ, updateGameResponse, gameInstance,
        updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating fixtures match id for MATCH_QUIZ game")
  public void updateMatchQuizGameFixturesMatch() throws HttpException, IllegalArgumentException {

    var gameInstance =
        createMatchQuizGameForMarkets(GameStatus.PENDING, PredictionMarket.getValidMarkets())
            .as(GameInstance.class);

    var gameFixtureMatchId = gameInstance.getFixtures().get(0).getMatchId();

    var updatedMatchId =
        getMatchesIdListAfterDate(
                getCompetitionsWhitelist(GameType.MATCH_QUIZ),
                generateDateTimeInIsoFormat(generateFutureDate(12)),
                6)
            .stream()
            .filter(matchId -> !matchId.equals(gameFixtureMatchId))
            .toList()
            .get(0);

    var updatedFixturesList =
        generateValidFixturesForGameType(GameType.MATCH_QUIZ, List.of(updatedMatchId));

    var updatedGameRequest = UpdateGameRequest.builder().fixtures(updatedFixturesList).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, hasItem(gameInstance.getId()));

    var redisGameByIdInstance =
        RedisEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class);
    // There is always a slight difference in updated_at. So, we need to check if this difference is
    // within 10 seconds.
    Assertions.assertTrue(
        PredictionApiValidator.isDateWithinRange(
            updatedGameInstance.getUpdated_at(), redisGameByIdInstance.getUpdated_at(), 10L));

    updatedGameInstance.setUpdated_at(null);
    redisGameByIdInstance.setUpdated_at(null);
    assertThat(redisGameByIdInstance)
        .usingRecursiveComparison()
        .ignoringFields("updated_at") // Ignore the timestamp field
        .isEqualTo(updatedGameInstance);
    validateGameUpdateResponse(
        GameType.MATCH_QUIZ, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating predictions cutoff for MATCH_QUIZ game")
  public void updateMatchQuizGamePredictionsCutoff()
      throws HttpException, IllegalArgumentException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance =
        CreateGameEndpoint.builder()
            .gameType(GameType.MATCH_QUIZ)
            .gameStatus(GameStatus.PENDING)
            .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
            .matchesIdList(matchList.stream().map(Match::getId).toList())
            .build()
            .createGame()
            .as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .predictionsCutoff(
                localDateTime
                    .atZone(ZoneId.of("UTC"))
                    .minusMinutes(15)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")))
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, hasItem(gameInstance.getId()));

    var redisGameByIdResponse = RedisEndpoint.getGameById(gameInstance.getId());

    assertThat(redisGameByIdResponse.as(GameInstance.class))
        .usingRecursiveComparison()
        .ignoringFields("updated_at") // Ignore the timestamp field
        .isEqualTo(updatedGameInstance);
    validateGameUpdateResponse(
        GameType.MATCH_QUIZ, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating title for MATCH_QUIZ game")
  public void updateMatchQuizGameTitle() throws HttpException, IllegalArgumentException {

    var markets = PredictionMarket.getValidMarkets();

    var gameInstance =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets).as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .title(
                "Updated title for MATCH_QUIZ game at time: "
                    + ZonedDateTime.now().toLocalDateTime())
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, hasItem(gameInstance.getId()));

    var redisGameByIdResponse = RedisEndpoint.getGameById(gameInstance.getId());
    var redisGameInstance = redisGameByIdResponse.as(GameInstance.class);

    // There is always a slight difference in updated_at. So, we need to check if this difference is
    // within 10 seconds.
    Assertions.assertTrue(
        PredictionApiValidator.isDateWithinRange(
            updatedGameInstance.getUpdated_at(), redisGameInstance.getUpdated_at(), 10L));

    updatedGameInstance.setUpdated_at(null);
    redisGameInstance.setUpdated_at(null);
    assertThat(redisGameInstance)
        .usingRecursiveComparison()
        .ignoringFields("updated_at") // Ignore the timestamp field
        .isEqualTo(updatedGameInstance);
    validateGameUpdateResponse(
        GameType.MATCH_QUIZ, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating description for MATCH_QUIZ game")
  public void updateMatchQuizGameDescription() throws HttpException, IllegalArgumentException {

    var markets = PredictionMarket.getValidMarkets();

    var gameInstance =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets).as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .description(
                "Updated description for MATCH_QUIZ game at time: "
                    + ZonedDateTime.now().toLocalDateTime())
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, hasItem(gameInstance.getId()));

    var redisGameByIdResponse = RedisEndpoint.getGameById(gameInstance.getId());

    assertThat(redisGameByIdResponse.as(GameInstance.class))
        .usingRecursiveComparison()
        .ignoringFields("updated_at") // Ignore the timestamp field
        .isEqualTo(updatedGameInstance);
    validateGameUpdateResponse(
        GameType.MATCH_QUIZ, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating status to OPEN for MATCH_QUIZ game")
  public void updateMatchQuizGameStatusToOpen() throws HttpException, IllegalArgumentException {

    var markets = PredictionMarket.getValidMarkets();

    var gameInstance =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets).as(GameInstance.class);

    var updatedGameRequest = UpdateGameRequest.builder().status(GameStatus.OPEN.getValue()).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, hasItem(gameInstance.getId()));

    var redisGameByIdResponse = RedisEndpoint.getGameById(gameInstance.getId());

    assertThat(redisGameByIdResponse.as(GameInstance.class))
        .usingRecursiveComparison()
        .ignoringFields("updated_at") // Ignore the timestamp field
        .isEqualTo(updatedGameInstance);
    validateGameUpdateResponse(
        GameType.MATCH_QUIZ, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @ParameterizedTest
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating status to CANCELED for MATCH_QUIZ game with status={arguments}")
  @EnumSource(
      value = GameStatus.class,
      names = {"PENDING", "OPEN"})
  public void updateMatchQuizGameStatusFromOpenOrPendingToCanceled(GameStatus status)
      throws HttpException, IllegalArgumentException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance =
        CreateGameEndpoint.builder()
            .gameType(GameType.MATCH_QUIZ)
            .gameStatus(status)
            .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
            .matchesIdList(matchList.stream().map(Match::getId).toList())
            .build()
            .createGame()
            .as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder().status(GameStatus.CANCELED.getValue()).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);

    validateGameUpdateResponse(
        GameType.MATCH_QUIZ, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @ParameterizedTest
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating status to CANCELED for TOP_X game with {arguments} status")
  @EnumSource(
      value = GameStatus.class,
      names = {"PENDING", "OPEN"})
  public void updateTopXGameStatusFromOpenOrPendingToCanceled(GameStatus status)
      throws HttpException, IllegalArgumentException {

    CreateGameEndpoint createGameEndpoint =
        CreateGameEndpoint.builder().gameType(GameType.TOP_X).gameStatus(status).build();

    var gameInstance = createGameEndpoint.createGame().as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder().status(GameStatus.CANCELED.getValue()).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating number of fixtures for TOP_X game")
  public void updateTopXGameFixtures()
      throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList = generateValidFixturesForGameType(GameType.TOP_X, predictionsCutoff);

    var createGameRequest =
        CreateGameRequest.builder()
            .title(GameType.TOP_X + " " + UUID.randomUUID())
            .description(GameType.TOP_X + " " + UUID.randomUUID())
            .type(GameType.TOP_X.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.PENDING.getValue())
            .build();

    var gameInstance = createGame(createGameRequest).as(GameInstance.class);

    var matchIdList = new ArrayList<String>();

    gameInstance.getFixtures().forEach(fixture -> matchIdList.add(fixture.getMatchId()));

    var updatedFixturesList =
        generateValidFixturesForGameType(
            GameType.TOP_X, matchIdList.subList(0, matchIdList.size() - 2));

    var updatedGameRequest = UpdateGameRequest.builder().fixtures(updatedFixturesList).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    currentTestResponse.set(redisGamesResponse);

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating matches for TOP_X game")
  public void updateTopXGameFixturesMatches() throws HttpException, IllegalArgumentException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0))
            .as(GameInstance.class);

    var matchIdList =
        getMatchesIdListAfterDate(
                getCompetitionsWhitelist(GameType.TOP_X),
                generateDateTimeInIsoFormat(generateFutureDate(12)),
                20)
            .stream()
            .filter(
                matchId ->
                    !gameInstance.getFixtures().stream()
                        .map(GameFixture::getMatchId)
                        .toList()
                        .contains(matchId))
            .toList()
            .subList(0, getTopXMaxFixtures());

    var updatedFixturesList = generateValidFixturesForGameType(GameType.TOP_X, matchIdList);

    var updatedGameRequest = UpdateGameRequest.builder().fixtures(updatedFixturesList).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating predictions cutoff for TOP_X game")
  public void updateTopXGamePredictionsCutoff() throws HttpException, IllegalArgumentException {

    var matchList = MatchGenerator.generateMatches(6, true);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    ResolverBase.cleanUpMatchIdList.addAll(
        matchList.stream()
            .map(com.fansunited.automation.core.resolver.hibernate.Match::getId)
            .toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream()
                    .map(com.fansunited.automation.core.resolver.hibernate.Match::getId)
                    .toList(),
                GameType.TOP_X,
                GameStatus.PENDING,
                localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
            .as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .predictionsCutoff(
                generateDateTimeInIsoFormat(
                    localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(26)))
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating title for TOP_X game")
  public void updateTopXGameTitle() throws HttpException, IllegalArgumentException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0))
            .as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .title("Updated title for TOP_X game at time: " + ZonedDateTime.now().toLocalDateTime())
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating description for TOP_X game")
  public void updateTopXGameDescription() throws HttpException, IllegalArgumentException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0))
            .as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .description(
                "Updated description for TOP_X game at time: "
                    + ZonedDateTime.now().toLocalDateTime())
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating status to OPEN for TOP_X game")
  public void updateTopXGameStatusToOpen() throws HttpException, IllegalArgumentException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0))
            .as(GameInstance.class);

    var updatedGameRequest = UpdateGameRequest.builder().status(GameStatus.OPEN.getValue()).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating status to CANCELED for TOP_X game with OPEN status")
  public void updateTopXGameStatusToCanceled() throws HttpException, IllegalArgumentException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.OPEN, GameType.TOP_X, 1).get(0))
            .as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder().status(GameStatus.CANCELED.getValue()).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify 'updated_at' date is updated, upon updating a game")
  public void verifyGameUpdatedAtDateIsUpdated()
      throws HttpException, IllegalArgumentException, InterruptedException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0))
            .as(GameInstance.class);

    // To avoid game create and update date equality due to game update within the same second as
    // creation
    TimeUnit.SECONDS.sleep(2);

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .title("Updated title for TOP_X game at time: " + ZonedDateTime.now().toLocalDateTime())
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    var updatedGameInstance = updateGameResponse.as(GameInstance.class);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(updatedGameInstance, redisGamesResponse);

    validateGameUpdateResponse(
        GameType.TOP_X, updateGameResponse, gameInstance, updatedGameRequest);

    updateGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(UPDATED_AT_PROP, not(equalTo(gameInstance.getUpdated_at())));
  }

  @ParameterizedTest(
      name =
          "Verify updating status to CANCELED for {arguments} game with OPEN status also cancels predictions for that game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void updateGameStatusToCanceled(GameType gameType)
      throws HttpException,
          IllegalArgumentException,
          IOException,
          FirebaseAuthException,
          InterruptedException,
          ExecutionException {

    var gameId = createGames(gameType, 1).get(0);

    var predictionsIdList = createPredictionsForGame(gameId, gameType, 10);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var updatedGameRequest =
        UpdateGameRequest.builder().status(GameStatus.CANCELED.getValue()).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    validateGameUpdateResponse(gameType, updateGameResponse, gameInstance, updatedGameRequest);

    var getPredictionsForGameResponse = getPredictionsForGame(gameId);

    currentTestResponse.set(getPredictionsForGameResponse);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);

    getPredictionsForGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(predictionsIdList.size()))
        .body("data." + STATUS_PROP, Every.everyItem(is(PredictionStatus.CANCELED.getValue())));
  }

  @ParameterizedTest(name = "Verify game image type can be updated as: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @CsvSource({
    "http://example/testurl.jpg, https://example/updatedurl.png",
    "https://example/testurl.png, http://example/updatedurl.gif",
    "http://example/testurl.jpg, ",
    " , http://example/notnullurl.png"
  })
  public void updateMatchQuizGameImageObject(String originalUrl, String updatedUrl)
      throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(12);

    var gameType = GameType.MATCH_QUIZ;

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var images = setUpImages(originalUrl, originalUrl, originalUrl);

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.PENDING.getValue())
            .images(images)
            .build();

    var gameInstance = createGame(createGameRequest).as(GameInstance.class);

    images = setUpImages(updatedUrl, updatedUrl, updatedUrl);

    var updatedGameRequest = UpdateGameRequest.builder().images(images).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, equalTo(gameInstance.getId()))
        .body(IMAGES_PROP + "." + IMAGES_MAIN, equalTo(updatedUrl))
        .body(IMAGES_PROP + "." + IMAGES_COVER, equalTo(updatedUrl))
        .body(IMAGES_PROP + "." + IMAGES_MOBILE, equalTo(updatedUrl));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify game image type is be updated to null when type not passed")
  public void updateMatchQuizGameImageObject() throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(12);

    var gameType = GameType.MATCH_QUIZ;

    var gameFixtureList = generateValidFixturesForGameType(gameType, predictionsCutoff);

    var images =
        setUpImages(
            "http://example/originalurl.jpg",
            "http://example/originalurl.jpg",
            "http://example/folder/originalurl.jpg");

    var createGameRequest =
        CreateGameRequest.builder()
            .title(gameType + " " + UUID.randomUUID())
            .description(gameType + " " + UUID.randomUUID())
            .type(gameType.getValue())
            .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
            .fixtures(gameFixtureList)
            .status(GameStatus.PENDING.getValue())
            .images(images)
            .build();

    var gameInstance = createGame(createGameRequest).as(GameInstance.class);

    images = Images.builder().build();

    var updatedGameRequest = UpdateGameRequest.builder().images(images).build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(ID_PROP, equalTo(gameInstance.getId()))
        .body(IMAGES_PROP + "." + IMAGES_MAIN, is(nullValue()))
        .body(IMAGES_PROP + "." + IMAGES_COVER, is(nullValue()))
        .body(IMAGES_PROP + "." + IMAGES_MOBILE, is(nullValue()));
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify updating allowed fields for MATCH_QUIZ game successful for all game statuses")
  public void updateMatchQuizGameAllowedFieldsForAllStatuses()
      throws HttpException,
          IllegalArgumentException,
          InterruptedException,
          IOException,
          ExecutionException {

    var markets = PredictionMarket.getValidMarkets();
    var match = MatchGenerator.generateMatch();
    match.setTimeline(new ArrayList<>());
    match.setStats(
        List.of(
            MatchStatsGenerator.generateEmptyMatchStats(match.getId(), true),
            MatchStatsGenerator.generateEmptyMatchStats(match.getId(), false)));
    match.getStats().get(0).getStatistics().setCorners((byte) 6);
    match.getStats().get(1).getStatistics().setCorners((byte) 2);
    match.getStats().get(0).getStatistics().setRed_cards((byte) 1);
    match.getStats().get(1).getStatistics().setRed_cards((byte) 0);
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.GOAL,
                VALID_PLAYER_ID,
                (short) 10,
                true,
                (byte) 1));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.GOAL,
                VALID_PLAYER_ID,
                (short) 15,
                false,
                (byte) 2));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.GOAL,
                VALID_PLAYER_ID,
                (short) 20,
                true,
                (byte) 3));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.GOAL,
                VALID_PLAYER_ID,
                (short) 51,
                true,
                (byte) 4));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.YELLOW_CARD,
                VALID_PLAYER_ID,
                (short) 55,
                true,
                (byte) 5));
    match
        .getTimeline()
        .add(
            MatchTimelineGenerator.generateMatchTimeline(
                match.getId(),
                TimelineEventType.RED_CARD,
                VALID_PLAYER_ID,
                (short) 80,
                true,
                (byte) 6));
    match.setGoalsHalfTimeHome((byte) 3);
    match.setGoalsHalfTimeAway((byte) 1);
    match.setGoalsFullTimeHome((byte) 4);
    match.setGoalsFullTimeAway((byte) 1);

    Resolver.openMatchForPredictions(match);
    ResolverBase.init();
    cleanUpMatchIdList.add(match.getId());

    var gameInstance =
        GamesEndpoint.createMatchQuizGameForMarkets(
                match.getId(), GameStatus.PENDING, markets, ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    validateUpdatedGameWithStatus(GameStatus.PENDING, gameInstance);

    updateGameStatusInFirebase(GameStatus.OPEN, gameInstance);

    validateUpdatedGameWithStatus(GameStatus.OPEN, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.LIVE.getValue());

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.LIVE.getValue()));

    validateUpdatedGameWithStatus(GameStatus.LIVE, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.CLOSED.getValue());

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.CLOSED.getValue()));

    validateUpdatedGameWithStatus(GameStatus.CLOSED, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.SETTLED.getValue());

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.SETTLED.getValue()));

    validateUpdatedGameWithStatus(GameStatus.SETTLED, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.CANCELED.getValue());

    validateUpdatedGameWithStatus(GameStatus.CANCELED, gameInstance);
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify updating allowed fields for TOP_X game successful for all game statuses")
  public void updateTopXGameAllowedFieldsForAllStatuses()
      throws HttpException,
          IllegalArgumentException,
          InterruptedException,
          IOException,
          ExecutionException {
    RestAssured.useRelaxedHTTPSValidation();

    var matchList = MatchGenerator.generateMatches(6, true);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                GameStatus.PENDING,
                ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    validateUpdatedGameWithStatus(GameStatus.PENDING, gameInstance);

    updateGameStatusInFirebase(GameStatus.OPEN, gameInstance);

    validateUpdatedGameWithStatus(GameStatus.OPEN, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.LIVE.getValue());

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.LIVE.getValue()));

    validateUpdatedGameWithStatus(GameStatus.LIVE, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.CLOSED.getValue());

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.CLOSED.getValue()));

    validateUpdatedGameWithStatus(GameStatus.CLOSED, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.SETTLED.getValue());

    MatcherAssert.assertThat(
        GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class).getStatus(),
        equalTo(GameStatus.SETTLED.getValue()));

    validateUpdatedGameWithStatus(GameStatus.SETTLED, gameInstance);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        STATUS_PROP,
        GameStatus.CANCELED.getValue());

    validateUpdatedGameWithStatus(GameStatus.CANCELED, gameInstance);
  }

  public void validateUpdatedGameWithStatus(GameStatus status, GameInstance gameInstance)
      throws HttpException {
    var updatedGameRequest =
        UpdateGameRequest.builder()
            .title(
                "Updated title for MATCH_QUIZ game at time: "
                    + ZonedDateTime.now().toLocalDateTime())
            .description("Updated Description for game with status: " + status)
            .rules("Updated Rules for game with status: " + status)
            .flags(
                List.of(
                    "Updated Flag 1 for game with status: " + status,
                    "Updated Flag 2 for game with status: " + status))
            .images(
                setUpImages(
                    String.format("http://someimageurl/%s/updatedimage.jpg", status),
                    String.format("http://someimageurl/%s/updatedimage.png", status),
                    String.format("http://someimageurl/%s/updatedimage.gif", status)))
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    validateGameUpdateResponseForAllStatuses(
        gameInstance.getType(), updateGameResponse, gameInstance, updatedGameRequest, status);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that MATCH_QUIZ game can be related to another game or template")
  // this can be enabled for local env when FZ-3639 is fixed
  @EnabledIf("isUseStageEnvironment")
  public void updateMatchQuizGameRelatedFunctionality()
      throws HttpException,
          IllegalArgumentException,
          IOException,
          ExecutionException,
          InterruptedException {

    var markets = PredictionMarket.getValidMarkets();

    var gameInstance =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets.subList(0, markets.size() - 3))
            .as(GameInstance.class);

    var gameInstance2 =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets.subList(0, markets.size() - 3))
            .as(GameInstance.class);

    var gameInstance3 =
        createMatchQuizGameForMarkets(GameStatus.PENDING, markets.subList(0, markets.size() - 3))
            .as(GameInstance.class);

    var matchId = gameInstance.getFixtures().get(0).getMatchId();

    var market =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());
    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .related(
                List.of(
                    ValidRelatedEntity.builder()
                        .entityType((GAME.getValue()))
                        .entityId(gameInstance3.getId())
                        .entityRelationship("relatedTo")
                        .build()))
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .markets(market)
            .gameIds(List.of(gameInstance2.getId()))
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("related[0].entity_id", equalTo(gameInstance3.getId()));
    var templateId = response.then().extract().body().jsonPath().getString("id");

    var templateRequest1 =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .related(
                List.of(
                    ValidRelatedEntity.builder()
                        .entityType((TEMPLATE.getValue()))
                        .entityId(templateId)
                        .entityRelationship("relatedTo")
                        .build()))
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .markets(market)
            .gameIds(List.of(gameInstance2.getId()))
            .build();

    var responseTemplate1 = TemplatesEndpoint.createLeaderboardTemplate(templateRequest1);

    var templateId2 = responseTemplate1.then().extract().body().jsonPath().getString("id");

    var updateTemplateRequest =
        TemplateRequest.builder()
            .name(new Faker().funnyName().name())
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .related(
                List.of(
                    ValidRelatedEntity.builder()
                        .entityType((TEMPLATE.getValue()))
                        .entityId(templateId2)
                        .entityRelationship("relatedTo")
                        .build(),
                    ValidRelatedEntity.builder()
                        .entityId(gameInstance2.getId())
                        .entityType(GAME.getValue())
                        .entityRelationship("relatedTo")
                        .build()))
            .build();

    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateId, updateTemplateRequest);

    updateTemplateResponse
        .then()
        .statusCode(HttpStatus.SC_OK)
        .body("related[0].entity_id", equalTo(templateId2))
        .body("related[1].entity_id", equalTo(gameInstance2.getId()));

    var updatedFixturesList =
        generateValidFixturesForGameType(GameType.MATCH_QUIZ, List.of(matchId));

    var updatedGameRequest =
        UpdateGameRequest.builder()
            .fixtures(updatedFixturesList)
            .related(
                List.of(
                    ValidRelatedEntity.builder()
                        .entityType(GAME.getValue())
                        .entityId(gameInstance2.getId())
                        .entityRelationship("relatedTo")
                        .build(),
                    ValidRelatedEntity.builder()
                        .entityType(TEMPLATE.getValue())
                        .entityId(templateId)
                        .entityRelationship("relatedTo")
                        .build()))
            .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then()
        .statusCode(HttpStatus.SC_OK)
        .body("related[0].entity_id", equalTo(gameInstance2.getId()))
        .body("related[1].entity_id", equalTo(templateId));

    var redisGamesResponse = RedisEndpoint.getGameById(gameInstance.getId());

    redisGamesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("related[0].entity_id", equalTo(gameInstance2.getId()))
        .body("related[1].entity_id", equalTo(templateId));

    // Update the same game again
    var updatedGameRequest1 =
        UpdateGameRequest.builder()
            .fixtures(updatedFixturesList)
            .related(
                List.of(
                    ValidRelatedEntity.builder()
                        .entityType(String.valueOf(GAME))
                        .entityId(gameInstance3.getId())
                        .entityRelationship("relatedTo")
                        .build()))
            .build();

    var updateGameResponse1 = updateGame(gameInstance.getId(), updatedGameRequest1);

    currentTestResponse.set(updateGameResponse1);

    updateGameResponse1.then().statusCode(HttpStatus.SC_OK);

    var redisGamesResponse1 = RedisEndpoint.getGameById(gameInstance.getId());

    redisGamesResponse1
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("related[0].entity_id", equalTo(gameInstance3.getId()));

    var gameDocument =
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION)
            .document(gameInstance.getId())
            .get()
            .get();
    Assertions.assertAll(
        () -> contains(gameDocument.getId(), contains(gameInstance.getId())),
        () ->
            contains(
                gameDocument.getData().get("related.[0].entity_id"),
                equalTo(gameDocument.get(gameInstance2.getId()))));
  }
}
