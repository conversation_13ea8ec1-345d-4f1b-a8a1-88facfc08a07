package com.fansunited.automation.predictionapi;

import static com.fansunited.automation.constants.Endpoints.PredictionApi.GAMES;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.optionsPredictionApi;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import io.restassured.response.Response;
import java.util.Arrays;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;

@DisplayName("Prediction API - OPTIONS method Checks the allowed methods for Prediction API.")
public class PredictionsOptionsMethodTest extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify prediction API using the OPTIONS method. Endpoint: OPTIONS /v1/games")
  @Tag(SMOKE)
  public void optionsMethodPredictionTest() throws HttpException {
    Response response = optionsPredictionApi(GAMES);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var actualMethods =
        Arrays.stream(response.getHeaders().getValues("Allow").get(0).split(", ")).toList();
    var expectedMethods = ApiConstants.HttpMethods.getValuesAsList();

    assertThat(actualMethods).as(EMPTY_LIST_MESSAGE).isNotEmpty().isNotNull();
    Assertions.assertTrue(expectedMethods.containsAll(actualMethods), METHODS_MISMATCH_MESSAGE);
  }
}
