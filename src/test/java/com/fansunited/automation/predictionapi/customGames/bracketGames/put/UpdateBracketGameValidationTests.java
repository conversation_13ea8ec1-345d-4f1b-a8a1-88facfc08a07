package com.fansunited.automation.predictionapi.customGames.bracketGames.put;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.updateBracketGame;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.BracketGameHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.bracket.request.UpdateBracketRequest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.ValueSource;

public class UpdateBracketGameValidationTests extends PredictionApiBaseTest {

  private String bracketId;
  private UpdateBracketRequest updateRequest;
  private final Faker faker = new Faker();

  @BeforeEach
  public void setUp() throws HttpException {
    var createBracketRequest =
        BracketGameHelper.createBracketGameRequest(
            faker.toString(), null, null, 1, GameStatus.PENDING);
    bracketId = createBracketRequest.getId();
    updateRequest =
        UpdateBracketRequest.builder().title("Updated Title " + ZonedDateTime.now()).build();
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be updated with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void updateBracketGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        updateBracketGame(
            bracketId,
            updateRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_FORBIDDEN)));
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be updated with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void updateBracketGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        updateBracketGame(
            bracketId,
            updateRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be updated with null content type")
  public void updateBracketGameWithNullContentType() throws HttpException {
    var response =
        updateBracketGame(
            bracketId,
            updateRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be updated with invalid JWT token: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void updateBracketGameWithInvalidJwtToken() throws HttpException {

    var response =
        updateBracketGame(
            bracketId,
            updateRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            BILLING_MANAGER_EMAIL,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_FORBIDDEN), is(SC_UNAUTHORIZED)));
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be updated with invalid bracket ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"invalid-id", "123456", ""})
  public void updateBracketGameWithInvalidBracketId(String invalidBracketId) throws HttpException {
    var response =
        updateBracketGame(
            invalidBracketId,
            updateRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be updated with empty request body")
  public void updateBracketGameWithEmptyRequestBody() throws HttpException {
    var response =
        updateBracketGame(
            bracketId,
            "{}",
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-4011")})
  @DisplayName("Verify bracket game cannot be updated with invalid request body")
  public void updateBracketGameWithInvalidRequestBody() throws HttpException {
    var response =
        updateBracketGame(
            bracketId,
            "{ invalid json }",
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }
}
