package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Security tests for the GET /v1/custom/event/{game_id}/ranking endpoint.
 */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking security tests")
public class GetCustomEventRankingSecurityTests extends PredictionApiBaseTest {

  private static String gameId;
  private final Faker faker = new Faker();

  @BeforeAll
  static void setUp() throws HttpException {
    // Create a custom event game for security tests
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    gameId = createResponse.then().extract().path("id").toString();
  }

  @ParameterizedTest(
      name = "Verify ranking endpoint rejects SQL injection attempts in game ID: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = {
      "'; DROP TABLE games; --",
      "' OR '1'='1",
      "' UNION SELECT * FROM users --",
      "'; DELETE FROM rankings WHERE 1=1; --",
      "' OR 1=1 --",
      "admin'--",
      "' OR 'x'='x",
      "'; INSERT INTO games VALUES ('malicious'); --"
  })
  public void verifyRankingRejectsSqlInjectionInGameId(String maliciousGameId) throws HttpException {
    var response =
        getCustomEventRanking(
            maliciousGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should reject malicious input with appropriate error
    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @ParameterizedTest(
      name = "Verify ranking endpoint rejects XSS attempts in game ID: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = {
      "<script>alert('xss')</script>",
      "<img src=x onerror=alert(1)>",
      "javascript:alert('xss')",
      "<svg onload=alert(1)>",
      "';alert(String.fromCharCode(88,83,83))//';alert(String.fromCharCode(88,83,83))//",
      "\"><script>alert('xss')</script>",
      "'><script>alert(document.cookie)</script>",
      "<iframe src=javascript:alert('xss')></iframe>"
  })
  public void verifyRankingRejectsXssInGameId(String maliciousGameId) throws HttpException {
    var response =
        getCustomEventRanking(
            maliciousGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should reject malicious input with appropriate error
    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @ParameterizedTest(
      name = "Verify ranking endpoint handles path traversal attempts: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = {
      "../../../etc/passwd",
      "..\\..\\..\\windows\\system32\\config\\sam",
      "....//....//....//etc//passwd",
      "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
      "..%252f..%252f..%252fetc%252fpasswd",
      "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
      "../../../../../../etc/passwd%00",
      "....\\\\....\\\\....\\\\windows\\\\system32\\\\drivers\\\\etc\\\\hosts"
  })
  public void verifyRankingRejectsPathTraversal(String maliciousPath) throws HttpException {
    var response =
        getCustomEventRanking(
            maliciousPath,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should reject path traversal attempts
    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @DisplayName("Verify ranking endpoint requires authentication")
  @Tags({@Tag(REGRESSION)})
  public void verifyRankingRequiresAuthentication() throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            CLIENT_AUTOMATION_ID,
            null, // No API key
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_UNAUTHORIZED);
  }

  @Test
  @DisplayName("Verify ranking endpoint validates client authorization")
  @Tags({@Tag(REGRESSION)})
  public void verifyRankingValidatesClientAuthorization() throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            "unauthorized-client-id",
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_UNAUTHORIZED);
  }

  @ParameterizedTest(
      name = "Verify ranking endpoint handles malformed client IDs: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = {
      "<script>alert('xss')</script>",
      "'; DROP TABLE clients; --",
      "../../../etc/passwd",
      "null",
      "undefined",
      "admin'--",
      "<img src=x onerror=alert(1)>",
      "javascript:alert(1)"
  })
  public void verifyRankingHandlesMalformedClientIds(String maliciousClientId) throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            maliciousClientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should reject malicious client IDs
    response.then().assertThat().statusCode(SC_UNAUTHORIZED);
  }

  @Test
  @DisplayName("Verify ranking endpoint prevents information disclosure")
  @Tags({@Tag(REGRESSION)})
  public void verifyRankingPreventsInformationDisclosure() throws HttpException {
    // Try to access ranking for non-existent game
    var nonExistentGameId = faker.internet().uuid();
    
    var response =
        getCustomEventRanking(
            nonExistentGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should return 404 without revealing internal information
    response.then().assertThat().statusCode(SC_NOT_FOUND);
    
    // Verify response doesn't contain sensitive information
    String responseBody = response.getBody().asString();
    assert !responseBody.toLowerCase().contains("database") : 
        "Response should not contain database information";
    assert !responseBody.toLowerCase().contains("internal") : 
        "Response should not contain internal system information";
    assert !responseBody.toLowerCase().contains("stack") : 
        "Response should not contain stack trace information";
  }

  @ParameterizedTest(
      name = "Verify ranking endpoint handles oversized requests: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(ints = {1000, 5000, 10000})
  public void verifyRankingHandlesOversizedGameId(int size) throws HttpException {
    // Create extremely long game ID
    String oversizedGameId = "a".repeat(size);
    
    var response =
        getCustomEventRanking(
            oversizedGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should reject oversized input
    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @DisplayName("Verify ranking endpoint handles concurrent malicious requests")
  @Tags({@Tag(REGRESSION)})
  public void verifyRankingHandlesConcurrentMaliciousRequests() throws HttpException, InterruptedException {
    // Simulate multiple rapid malicious requests
    String[] maliciousGameIds = {
        "'; DROP TABLE games; --",
        "<script>alert('xss')</script>",
        "../../../etc/passwd",
        "' OR '1'='1",
        "<img src=x onerror=alert(1)>"
    };

    for (String maliciousId : maliciousGameIds) {
      var response =
          getCustomEventRanking(
              maliciousId,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON);

      // All malicious requests should be rejected
      ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
      
      Thread.sleep(50); // Small delay between requests
    }
  }

  @ParameterizedTest(
      name = "Verify ranking endpoint handles Unicode and special characters: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = {
      "测试游戏ID",
      "🎮🎯🏆",
      "ñáéíóú",
      "αβγδε",
      "🚀💻🔥",
      "Ω≈ç√∫˜µ",
      "‰€£¥",
      "♠♣♥♦"
  })
  public void verifyRankingHandlesUnicodeCharacters(String unicodeGameId) throws HttpException {
    var response =
        getCustomEventRanking(
            unicodeGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    // Should handle Unicode gracefully without errors
    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @DisplayName("Verify ranking endpoint rate limiting")
  @Tags({@Tag(REGRESSION)})
  public void verifyRankingRateLimiting() throws HttpException, InterruptedException {
    // Make rapid successive requests to test rate limiting
    int requestCount = 50;
    int rateLimitedRequests = 0;

    for (int i = 0; i < requestCount; i++) {
      var response =
          getCustomEventRanking(
              gameId,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON);

      if (response.getStatusCode() == 429) { // Too Many Requests
        rateLimitedRequests++;
      }
      
      // No delay to test rate limiting
    }

    // If rate limiting is implemented, some requests should be limited
    // This is a soft assertion as rate limiting implementation may vary
    if (rateLimitedRequests > 0) {
      System.out.println("Rate limiting detected: " + rateLimitedRequests + 
                        " out of " + requestCount + " requests were rate limited");
    } else {
      System.out.println("No rate limiting detected for " + requestCount + " rapid requests");
    }
  }
}
