package com.fansunited.automation.predictionapi.customGames.customEventGame.participation.get;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.getOwnPredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static org.hamcrest.Matchers.equalTo;

public class GetOwnCustomEventPredictionsTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify the user can see own participation in custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getCustomEventGameHappyPath() throws HttpException {
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);
    createResponse.then().assertThat().statusCode(200);
    var gameId = createResponse.then().extract().path("id").toString();
    var id = createResponse.then().extract().path("fixtures[0].id").toString();
    var id1 = createResponse.then().extract().path("fixtures[1].id").toString();

    // Use helper to create a default custom event game request
    var participateInCustomEventGameRequest = participateInDefaultCustomEventGameRequest(id, id1);

    var response =
        participateInCustomEventGame(
            CLIENT_AUTOMATION_ID,
            gameId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            getCurrentTestUser().getEmail(),
            ContentType.JSON,
            participateInCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var getOwnPredictionsResponse = getOwnPredictions(CLIENT_AUTOMATION_ID,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        AuthConstants.ENDPOINTS_API_KEY,
        getCurrentTestUser().getEmail(),
        ContentType.JSON);

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(200)
        .body("data[0].fixtures[0].id", equalTo(id))
        .body("data[0].fixtures[1].id", equalTo(id1));
}
}
