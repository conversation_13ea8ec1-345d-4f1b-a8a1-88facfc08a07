package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequestWithOutcome;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

/**
 * Functional tests for the GET /v1/custom/event/{game_id}/ranking endpoint.
 */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking functional tests")
public class GetCustomEventRankingFunctionalTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify ranking calculation for resolved game with multiple participants")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingForResolvedGameWithMultipleParticipants() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs for participation
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add multiple participants
    for (int i = 0; i < 3; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          gameId,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100); // Small delay between participants
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify ranking response
    rankingResponse.then().assertThat().log().all().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThan(0));
    
    // Verify ranking structure
    rankingResponse.then().assertThat().body("data[0].user_id", notNullValue());
    rankingResponse.then().assertThat().body("data[0].score", notNullValue());
    rankingResponse.then().assertThat().body("data[0].rank", notNullValue());
  }

  @Test
  @DisplayName("Verify ranking order is correct based on scores")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingOrderBasedOnScores() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants with different prediction accuracy
    for (int i = 0; i < 5; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          gameId,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify ranking is ordered correctly (highest score first)
    rankingResponse.then().assertThat().statusCode(200);
    
    List<Integer> scores = rankingResponse.then().extract().path("data.score");
    List<Integer> ranks = rankingResponse.then().extract().path("data.rank");
    
    // Verify scores are in descending order
    for (int i = 0; i < scores.size() - 1; i++) {
      assert scores.get(i) >= scores.get(i + 1) : 
          "Scores should be in descending order. Score at position " + i + 
          " (" + scores.get(i) + ") should be >= score at position " + (i + 1) + 
          " (" + scores.get(i + 1) + ")";
    }
    
    // Verify ranks are in ascending order
    for (int i = 0; i < ranks.size() - 1; i++) {
      assert ranks.get(i) <= ranks.get(i + 1) : 
          "Ranks should be in ascending order. Rank at position " + i + 
          " (" + ranks.get(i) + ") should be <= rank at position " + (i + 1) + 
          " (" + ranks.get(i + 1) + ")";
    }
  }

  @Test
  @DisplayName("Verify ranking for game with tied scores")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForGameWithTiedScores() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants with same predictions (likely to result in tied scores)
    for (int i = 0; i < 3; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          gameId,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify ranking handles tied scores correctly
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data.size()", greaterThanOrEqualTo(3));
    
    // Verify that tied participants have appropriate ranking
    List<Integer> scores = rankingResponse.then().extract().path("data.score");
    List<Integer> ranks = rankingResponse.then().extract().path("data.rank");
    
    // Check if there are tied scores and verify ranking logic
    for (int i = 0; i < scores.size() - 1; i++) {
      if (scores.get(i).equals(scores.get(i + 1))) {
        // For tied scores, ranks should be handled appropriately
        // (either same rank or consecutive ranks depending on business logic)
        assert ranks.get(i) <= ranks.get(i + 1) : 
            "Tied scores should have appropriate ranking";
      }
    }
  }

  @Test
  @DisplayName("Verify ranking pagination and limits")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingPaginationAndLimits() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add many participants to test pagination
    for (int i = 0; i < 10; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          gameId,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(50);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify ranking response structure and limits
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThan(0));
    
    // Verify pagination metadata if present
    if (rankingResponse.then().extract().path("pagination") != null) {
      rankingResponse.then().assertThat().body("pagination.total", greaterThanOrEqualTo(10));
      rankingResponse.then().assertThat().body("pagination.page", notNullValue());
      rankingResponse.then().assertThat().body("pagination.limit", notNullValue());
    }
  }
}
