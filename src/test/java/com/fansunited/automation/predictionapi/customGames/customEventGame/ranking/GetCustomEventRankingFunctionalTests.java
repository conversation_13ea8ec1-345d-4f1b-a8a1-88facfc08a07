package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.CustomResolverApi.CUSTOM_EVENT_RESOLVER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.core.resolver.CustomResolver.customGameResolve;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.FirebaseHelper.CUSTOM_EVENT;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.getFirestoreCollection;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionTimestampField;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequestWithOutcome;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.google.cloud.Timestamp;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

/** Functional tests for the GET /v1/custom/event/{game_id}/ranking endpoint. */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking functional tests")
public class GetCustomEventRankingFunctionalTests extends PredictionApiBaseTest {

  public List<UserRecord> users = createUsers(5);
  public String gameId;

  public GetCustomEventRankingFunctionalTests()
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException {}

  @BeforeEach
  public void setUp()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    // Create a custom event game for functional tests
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createRequest);

    createResponse.then().assertThat().statusCode(200);
    gameId = createResponse.then().extract().path("id").toString();
  }

  @Test
  @DisplayName("Verify ranking calculation for resolved game with multiple participants")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingForResolvedGameWithMultipleParticipants()
      throws HttpException, InterruptedException, IOException, ExecutionException {

    for (UserRecord user : users) {
      var participationRequest = participateInDefaultCustomEventGameRequest("1", "2");
      var participationResponse =
          participateInCustomEventGame(
              CLIENT_AUTOMATION_ID,
              gameId,
              FANS_UNITED_PROFILE,
              AuthConstants.ENDPOINTS_API_KEY,
              user.getEmail(),
              ContentType.JSON,
              participationRequest);

      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100); // Small delay between participants
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    updateCollectionTimestampField(
        getFirestoreCollection(FANS_UNITED_PROFILE, CUSTOM_EVENT),
        gameId,
        "predictions_cutoff",
        Timestamp.of(pastCutoff));

    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    customGameResolve(CUSTOM_EVENT_RESOLVER, gameId);

    // Get ranking
    var rankingResponse =
        getCustomEventRanking(
            gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    // Verify ranking response
    rankingResponse.then().assertThat().log().all().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThan(0));

    // Verify ranking structure
    rankingResponse.then().assertThat().body("data[0].profile_id", notNullValue());
    rankingResponse.then().assertThat().body("data[0].status", notNullValue());
    rankingResponse.then().assertThat().body("data[0].fixtures[0].prediction", notNullValue());
  }

  @Test
  @DisplayName("Verify ranking order is correct based on scores")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingOrderBasedOnScores()
      throws HttpException,
          InterruptedException,
          IOException,
          ExecutionException,
          FirebaseAuthException {
    // Create a custom event game
    var users = createUsers(5);
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants with different prediction accuracy
    for (UserRecord user : users) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse =
          participateInCustomEventGame(
              CLIENT_AUTOMATION_ID,
              gameId,
              FANS_UNITED_PROFILE,
              AuthConstants.ENDPOINTS_API_KEY,
              user.getEmail(),
              ContentType.JSON,
              participationRequest);

      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    updateCollectionTimestampField(
        getFirestoreCollection(FANS_UNITED_PROFILE, CUSTOM_EVENT),
        gameId,
        "predictions_cutoff",
        Timestamp.of(pastCutoff));

    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    customGameResolve(CUSTOM_EVENT_RESOLVER, gameId);
    Thread.sleep(10000);

    // Get ranking
    var rankingResponse =
        getCustomEventRanking(
            gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    // Verify ranking is ordered correctly (highest score first)
    rankingResponse.then().assertThat().statusCode(200);

    List<Integer> scores = rankingResponse.then().extract().path("data.score");
    List<Integer> ranks = rankingResponse.then().extract().path("data.rank");

    // Verify scores are in descending order
    for (int i = 0; i < scores.size(); i++) {
      assert scores.get(i) >= scores.get(i + 1)
          : "Scores should be in descending order. Score at position "
              + i
              + " ("
              + scores.get(i)
              + ") should be >= score at position "
              + (i + 1)
              + " ("
              + scores.get(i + 1)
              + ")";
    }

    // Verify ranks are in ascending order
    for (int i = 0; i < ranks.size() - 1; i++) {
      assert ranks.get(i) <= ranks.get(i + 1)
          : "Ranks should be in ascending order. Rank at position "
              + i
              + " ("
              + ranks.get(i)
              + ") should be <= rank at position "
              + (i + 1)
              + " ("
              + ranks.get(i + 1)
              + ")";
    }
  }

  @Test
  @DisplayName("Verify ranking for game with tied scores")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForGameWithTiedScores()
      throws HttpException,
          InterruptedException,
          IOException,
          ExecutionException,
          FirebaseAuthException {
    // Create a custom event game
    var users = createUsers(3);
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants with same predictions (likely to result in tied scores)
    for (UserRecord user : users) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse =
          participateInCustomEventGame(
              CLIENT_AUTOMATION_ID,
              gameId,
              FANS_UNITED_PROFILE,
              AuthConstants.ENDPOINTS_API_KEY,
              user.getEmail(),
              ContentType.JSON,
              participationRequest);

      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    updateCollectionTimestampField(
        getFirestoreCollection(FANS_UNITED_PROFILE, CUSTOM_EVENT),
        gameId,
        "predictions_cutoff",
        Timestamp.of(pastCutoff));

    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    customGameResolve(CUSTOM_EVENT_RESOLVER, gameId);

    // Get ranking
    var rankingResponse =
        getCustomEventRanking(
            gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    // Verify ranking handles tied scores correctly
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data.size()", greaterThanOrEqualTo(3));

    // Verify that tied participants have appropriate ranking
    List<Integer> scores = rankingResponse.then().extract().path("data.score");
    List<Integer> ranks = rankingResponse.then().extract().path("data.rank");

    // Check if there are tied scores and verify ranking logic
    for (int i = 0; i < scores.size() - 1; i++) {
      if (scores.get(i).equals(scores.get(i + 1))) {
        // For tied scores, ranks should be handled appropriately
        // (either same rank or consecutive ranks depending on business logic)
        assert ranks.get(i) <= ranks.get(i + 1) : "Tied scores should have appropriate ranking";
      }
    }
  }

  @Test
  @DisplayName("Verify ranking pagination and limits")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingPaginationAndLimits()
      throws HttpException,
          InterruptedException,
          IOException,
          ExecutionException,
          FirebaseAuthException {
    // Create a custom event game
    var users = createUsers(10);
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add many participants to test pagination
    for (UserRecord user : users) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse =
          participateInCustomEventGame(
              CLIENT_AUTOMATION_ID,
              gameId,
              FANS_UNITED_PROFILE,
              AuthConstants.ENDPOINTS_API_KEY,
              user.getEmail(),
              ContentType.JSON,
              participationRequest);

      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(50);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse =
        getCustomEventRanking(
            gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    // Verify ranking response structure and limits
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThan(0));

    // Verify pagination metadata if present
    if (rankingResponse.then().extract().path("pagination") != null) {
      rankingResponse.then().assertThat().body("pagination.total", greaterThanOrEqualTo(10));
      rankingResponse.then().assertThat().body("pagination.page", notNullValue());
      rankingResponse.then().assertThat().body("pagination.limit", notNullValue());
    }
  }

  @Test
  @DisplayName("Verify ranking with 5 users in different positions")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingWithFiveUsersInDifferentPositions() throws HttpException, InterruptedException,
      IOException, ExecutionException, FirebaseAuthException {
    // Create a custom event game
    var users = createUsers(5);
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants with different prediction strategies to ensure different scores
    for (int i = 0; i < users.size(); i++) {
      UserRecord user = users.get(i);

      // Create different prediction requests to ensure varied scores
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);

      // Modify predictions to create score variation
      // User 0: Most accurate predictions
      // User 1: Partially accurate predictions
      // User 2: Mixed predictions
      // User 3: Less accurate predictions
      // User 4: Least accurate predictions
      if (i == 0) {
        // Best predictions - should get highest score
        participationRequest.getFixtures().get(0).setPrediction("true");
        participationRequest.getFixtures().get(1).setPrediction("fine");
      } else if (i == 1) {
        // Good predictions - should get second highest score
        participationRequest.getFixtures().get(0).setPrediction("false");
        participationRequest.getFixtures().get(1).setPrediction("okay");
      } else if (i == 2) {
        // Average predictions - should get middle score
        participationRequest.getFixtures().get(0).setPrediction("true");
        participationRequest.getFixtures().get(1).setPrediction("fine");
      } else if (i == 3) {
        // Below average predictions - should get lower score
        participationRequest.getFixtures().get(0).setPrediction("false");
        participationRequest.getFixtures().get(1).setPrediction("okay");
      } else {
        // Worst predictions - should get lowest score
        participationRequest.getFixtures().get(0).setPrediction("true");
        participationRequest.getFixtures().get(1).setPrediction("awful");
      }

      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          user.getEmail(),
          ContentType.JSON,
          participationRequest);

      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    updateCollectionTimestampField(
        getFirestoreCollection(FANS_UNITED_PROFILE, CUSTOM_EVENT),
        gameId,
        "predictions_cutoff",
        Timestamp.of(pastCutoff));

    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    customGameResolve(CUSTOM_EVENT_RESOLVER, gameId);
    Thread.sleep(10000);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify ranking response
    rankingResponse.then().assertThat().log().all().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data[].", equalTo(5));

    // Extract ranking data
    List<String> userIds = rankingResponse.then().extract().path("data.user_id");
    List<Integer> scores = rankingResponse.then().extract().path("data.score");
    List<Integer> ranks = rankingResponse.then().extract().path("data.rank");

    // Verify all 5 users are present
    assert userIds.size() == 5 : "Expected 5 users in ranking, but got: " + userIds.size();

    // Verify all users have different ranks (positions 1, 2, 3, 4, 5)
    Set<Integer> uniqueRanks = new HashSet<>(ranks);
    assert uniqueRanks.size() == 5 :
        "Expected 5 different ranks, but got " + uniqueRanks.size() + " unique ranks: " + uniqueRanks;

    // Verify ranks are consecutive (1, 2, 3, 4, 5)
    Collections.sort(ranks);
    for (int i = 0; i < ranks.size(); i++) {
      assert ranks.get(i) == (i + 1) :
          "Expected rank " + (i + 1) + " at position " + i + ", but got: " + ranks.get(i);
    }

    // Verify scores are in descending order (highest score = rank 1)
    List<Integer> originalOrderScores = rankingResponse.then().extract().path("data.score");
    for (int i = 0; i < originalOrderScores.size() - 1; i++) {
      assert originalOrderScores.get(i) >= originalOrderScores.get(i + 1) :
          "Scores should be in descending order. Score at rank " + (i + 1) +
          " (" + originalOrderScores.get(i) + ") should be >= score at rank " + (i + 2) +
          " (" + originalOrderScores.get(i + 1) + ")";
    }

    // Verify all scores are different (no ties)
    Set<Integer> uniqueScores = new HashSet<>(scores);
    assert uniqueScores.size() == 5 :
        "Expected 5 different scores for different positions, but got " + uniqueScores.size() +
        " unique scores: " + uniqueScores;

    System.out.println("✅ Successfully verified 5 users in different ranking positions:");
    for (int i = 0; i < userIds.size(); i++) {
      System.out.println("Rank " + ranks.get(i) + ": User " + userIds.get(i) +
                        " with score " + scores.get(i));
    }
  }
}
