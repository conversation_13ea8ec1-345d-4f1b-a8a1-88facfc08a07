package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.CustomResolverApi.CUSTOM_EVENT_RESOLVER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.updatePredictionInCustomEventGame;
import static com.fansunited.automation.core.resolver.CustomResolver.customGameResolve;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.FirebaseHelper.CUSTOM_EVENT;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionField;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequest;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequestWithOutcome;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class GetCustomEventRankingTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify ranking working correctly for custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getCustomEventRankingHappyPath()
      throws HttpException, InterruptedException, IOException, ExecutionException {
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);
    createResponse.then().assertThat().statusCode(200);
    var gameId = createResponse.then().extract().path("id").toString();
    var predictionsCutoffString =
        createResponse.then().extract().path("predictions_cutoff").toString();
    Date predictionsCutoff = Date.from(ZonedDateTime.parse(predictionsCutoffString).toInstant());
    var id = createResponse.then().extract().path("fixtures[0].id").toString();
    var id1 = createResponse.then().extract().path("fixtures[1].id").toString();

    // Use helper to create a default custom event game request
    var participateInCustomEventGameRequest = participateInDefaultCustomEventGameRequest(id, id1);

    var response =
        participateInCustomEventGame(
            CLIENT_AUTOMATION_ID,
            gameId,
            FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            getCurrentTestUser().getEmail(),
            ContentType.JSON,
            participateInCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var predictionCutoffInPast = predictionsCutoff.toInstant().minusNanos(1);
    var predictionId = response.then().extract().path("id").toString();
    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, CUSTOM_EVENT),
        gameId,
        "predictions_cutoff",
        predictionCutoffInPast.toString());

    // Wait for Firebase sync
    Thread.sleep(2000);

    // Get predictions_cutoff from Firebase and use it directly
    try {
      var firebasePredictionsCutoff = Date.from(ZonedDateTime.parse(
          FirebaseHelper.getEntityFromFirebaseCollection(
                  FANS_UNITED_PROFILE,
                  CUSTOM_EVENT,
                  gameId,
                  "predictions_cutoff").get("predictions_cutoff").toString()
      ).toInstant());

      // Update your request
      var updateRequest = updateCustomEventGameRequest();
      updateRequest.setPredictionsCutoff(firebasePredictionsCutoff);
    } catch (Exception e) {
      // Fallback to using the original predictions cutoff if Firebase fails
      System.out.println("Firebase lookup failed, using original predictions cutoff: " + e.getMessage());
    }

    var updateCustomEventPredictionRequest = updateCustomEventGameRequest();

    var updateResponse =
        updatePredictionInCustomEventGame(
            CLIENT_AUTOMATION_ID,
            predictionId,
            gameId,
            FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            getCurrentTestUser().getEmail(),
            ContentType.JSON,
            updateCustomEventPredictionRequest);

    updateResponse.then().assertThat().log().all().statusCode(200);

    var updateCustomEventGameRequest = updateCustomEventGameRequestWithOutcome(predictionsCutoff);

    var updateEventGameResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateCustomEventGameRequest);

    updateEventGameResponse.then().assertThat().log().all().statusCode(200);

    customGameResolve(CUSTOM_EVENT_RESOLVER, gameId);

    var rankingResponse = getCustomEventRanking(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    // Add assertions to verify the ranking response
    rankingResponse.then().assertThat().log().all().statusCode(200);
    rankingResponse.then().assertThat().body("data", org.hamcrest.Matchers.notNullValue());
    rankingResponse.then().assertThat().body("data.size()", org.hamcrest.Matchers.greaterThanOrEqualTo(0));
  }
}
