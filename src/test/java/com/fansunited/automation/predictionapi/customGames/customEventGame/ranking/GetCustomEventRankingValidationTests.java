package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Validation tests for the GET /v1/custom/event/{game_id}/ranking endpoint.
 */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking endpoint validation tests")
public class GetCustomEventRankingValidationTests extends PredictionApiBaseTest {

  private static String gameId;
  private final Faker faker = new Faker();

  @BeforeAll
  static void setUp() throws HttpException {
    // Create a custom event game for ranking tests
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    gameId = createResponse.then().extract().path("id").toString();
  }

  @ParameterizedTest(
      name = "Verify custom event ranking cannot be fetched with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getCustomEventRankingWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_UNAUTHORIZED)));
  }

  @ParameterizedTest(
      name = "Verify custom event ranking cannot be fetched with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getCustomEventRankingWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify API returns UNSUPPORTED_MEDIA_TYPE when getting ranking with non-supported content type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getCustomEventRankingWithUnsupportedContentType(ContentType contentType) throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event ranking cannot be fetched with null content type")
  public void getCustomEventRankingWithNullContentType() throws HttpException {
    var response =
        getCustomEventRanking(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name = "Verify custom event ranking cannot be fetched with invalid game ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"invalid-id", "123456", "", " ", "null"})
  public void getCustomEventRankingWithInvalidGameId(String invalidGameId) throws HttpException {
    var response =
        getCustomEventRanking(
            invalidGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event ranking cannot be fetched with non-existent game ID")
  public void getCustomEventRankingWithNonExistentGameId() throws HttpException {
    var nonExistentId = faker.internet().uuid();
    
    var response =
        getCustomEventRanking(
            nonExistentId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_NOT_FOUND);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event ranking cannot be fetched with null game ID")
  public void getCustomEventRankingWithNullGameId() throws HttpException {
    var response =
        getCustomEventRanking(
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @ParameterizedTest(
      name = "Verify custom event ranking cannot be fetched with malformed game ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {
      "game-id-with-special-chars!@#",
      "game_id_with_underscores",
      "game id with spaces",
      "very-long-game-id-that-exceeds-normal-length-limits-and-should-be-rejected",
      "123",
      "abc",
      "game-id-with-unicode-字符"
  })
  public void getCustomEventRankingWithMalformedGameId(String malformedGameId) throws HttpException {
    var response =
        getCustomEventRanking(
            malformedGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event ranking returns empty for game with no participants")
  public void getCustomEventRankingForGameWithNoParticipants() throws HttpException {
    // Create a new game with no participants
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String newGameId = createResponse.then().extract().path("id").toString();

    var response =
        getCustomEventRanking(
            newGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200);
    response.then().assertThat().body("data.size()", equalTo(0));
  }
}
