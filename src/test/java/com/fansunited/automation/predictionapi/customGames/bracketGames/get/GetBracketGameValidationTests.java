package com.fansunited.automation.predictionapi.customGames.bracketGames.get;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.getPredictionBracketGame;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.BracketGameHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

/** Validation tests for the GET /v1/custom/bracket/{bracket_id} endpoint. */
@DisplayName("Prediction Api - GET /v1/custom/bracket/{bracket_id} endpoint validation tests")
public class GetBracketGameValidationTests extends PredictionApiBaseTest {

  private String bracketId;
  private final Faker faker = new Faker();

  @BeforeEach
  public void setUp() throws HttpException {
    var createBracketRequest =
        BracketGameHelper.createBracketGameRequest(
            "Test Bracket Game " + faker.number().randomNumber(),
            "Test Description",
            "Test Rules",
            1,
            GameStatus.PENDING);
    bracketId = createBracketRequest.getId();
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be fetched with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getBracketGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getPredictionBracketGame(
            bracketId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be fetched with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getBracketGameWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {
    var response =
        getPredictionBracketGame(
            bracketId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify API returns UNSUPPORTED_MEDIA_TYPE when getting bracket game with non-supported content type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getBracketGameWithUnsupportedContentType(ContentType contentType)
      throws HttpException {
    var response =
        getPredictionBracketGame(
            bracketId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be fetched with null content type")
  public void getBracketGameWithNullContentType() throws HttpException {
    var response =
        getPredictionBracketGame(
            bracketId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be fetched with invalid bracket ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"invalid-id", "123456"})
  public void getBracketGameWithInvalidBracketId(String invalidBracketId) throws HttpException {
    var response =
        getPredictionBracketGame(
            invalidBracketId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be fetched with non-existent bracket ID")
  public void getBracketGameWithNonExistentBracketId() throws HttpException {
    var nonExistentId = faker.internet().uuid();

    var response =
        getPredictionBracketGame(
            nonExistentId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_NOT_FOUND);
  }
}
