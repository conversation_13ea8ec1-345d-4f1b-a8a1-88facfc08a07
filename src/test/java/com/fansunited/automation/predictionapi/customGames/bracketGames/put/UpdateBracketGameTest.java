package com.fansunited.automation.predictionapi.customGames.bracketGames.put;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.getPredictionBracketGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.updateBracketGame;
import static java.time.ZoneOffset.UTC;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.BracketGameHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.bracket.Fixture;
import com.fansunited.automation.model.predictionapi.bracket.Meta;
import com.fansunited.automation.model.predictionapi.bracket.Participant;
import com.fansunited.automation.model.predictionapi.bracket.request.UpdateBracketRequest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

/**
 * Test class for updating bracket games with a single comprehensive test.
 * This test focuses on updating cutoff time, title, participants, fixtures, status, and points in a single request.
 */
public class UpdateBracketGameTest extends PredictionApiBaseTest {
  private final Faker faker=new Faker();
  @Test
  @DisplayName("Verify updating cutoff time, title, participants, fixtures, status, and points")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void updateBracketGameEssentialProperties() throws HttpException {
    // Create a bracket game
    var createBracketRequest = BracketGameHelper.createBracketGameRequest(faker.toString(), null, null, 1, GameStatus.PENDING);
    var bracketId = createBracketRequest.getId();
    var images = faker.internet().avatar();

    // Create new fixtures
    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(3));
    var newFixtures = List.of(
        Fixture.builder()
            .matchId(faker.internet().uuid())
            .start_date(date)
            .participantOne("X")
            .participantTwo("Y")
            .home_participant("X")
            .build(),
        Fixture.builder()
            .matchId(faker.internet().uuid())
            .start_date(date)
            .participantOne("Z")
            .participantTwo("W")
            .home_participant("Z")
            .build());

    // Create new participants
    var newParticipants = List.of(
        Participant.builder()
            .id("X")
            .name(faker.name().firstName())
            .image(images)
            .undecided(false)
            .build(),
        Participant.builder()
            .id("Y")
            .name(faker.name().firstName())
            .image(images)
            .undecided(false)
            .build(),
        Participant.builder()
            .id("Z")
            .name(faker.name().firstName())
            .image(images)
            .undecided(false)
            .build(),
        Participant.builder()
            .id("W")
            .name(faker.name().firstName())
            .image(images)
            .undecided(false)
            .build());

    // Update cutoff time, title, participants, fixtures, status, and points
    var updatedTitle = "Essential Properties Update " + ZonedDateTime.now();
    var updatedCutoff = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(5));

    var updateRequest = UpdateBracketRequest.builder()
        .title(updatedTitle)
        .predictionsCutoff(updatedCutoff)
        .points(1)
        .status(GameStatus.OPEN)
        .fixtures(newFixtures)
        .meta(Meta.builder().participants(newParticipants).build())
        .build();

    var updateResponse = updateBracketGame(
        bracketId,
        updateRequest,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        null,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify the update was successful
    updateResponse.then()
        .assertThat()
        .log().body()
        .statusCode(SC_OK)
        .body(ID_PROP, equalTo(bracketId))
        .body("title", equalTo(updatedTitle))
        .body("status", equalTo(GameStatus.OPEN.toString()))
        .body("points", equalTo(1))
        .body("predictions_cutoff", equalTo(updatedCutoff))
        .body("fixtures.size()", equalTo(newFixtures.size()))
        .body("meta.participants.size()", equalTo(newParticipants.size()));

    var getBracketGameResponse = getPredictionBracketGame(
        bracketId,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    getBracketGameResponse.then()
        .assertThat()
        .statusCode(SC_OK)
        .body(ID_PROP, equalTo(bracketId))
        .body("title", equalTo(updatedTitle))
        .body("predictions_cutoff", equalTo(updatedCutoff))
        .body("fixtures.size()", equalTo(newFixtures.size()))
        .body("meta.participants.size()", equalTo(newParticipants.size()))
        .body("fixtures[0].participant_one", equalTo("X"))
        .body("fixtures[0].participant_two", equalTo("Y"))
        .body("fixtures[1].participant_one", equalTo("Z"))
        .body("fixtures[1].participant_two", equalTo("W"))
        .body("status",equalTo(GameStatus.OPEN.toString()))
        .body("points", equalTo(1))
        .body("meta.participants.find { it.id == 'X' }.id", equalTo("X"))
        .body("meta.participants.find { it.id == 'Y' }.id", equalTo("Y"))
        .body("meta.participants.find { it.id == 'Z' }.id", equalTo("Z"))
        .body("meta.participants.find { it.id == 'W' }.id", equalTo("W"));
  }
}
