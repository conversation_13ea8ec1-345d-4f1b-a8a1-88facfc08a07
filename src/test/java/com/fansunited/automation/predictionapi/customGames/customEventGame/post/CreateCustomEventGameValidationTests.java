package com.fansunited.automation.predictionapi.customGames.customEventGame.post;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Validation tests for the POST /v1/event/games endpoint.
 */
@DisplayName("Prediction Api - POST /v1/event/games endpoint validation tests")
public class CreateCustomEventGameValidationTests extends PredictionApiBaseTest {

  private Faker faker;
  private CreateCustomEventGameRequest createRequest;

  @BeforeEach
  public void setUp() {
    faker = new Faker();
    createRequest = createDefaultCustomEventGameRequest();
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be created with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void createCustomEventGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        createCustomEventGame(
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_UNAUTHORIZED)));
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be created with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void createCustomEventGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @Test()
  @DisplayName("Verify custom event game cannot be created with non-supported content type")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createCustomEventGameWithUnsupportedContentType() throws HttpException {
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            null,
            createRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with null content type")
  public void createCustomEventGameWithNullContentType() throws HttpException {
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,null,
            createRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }


  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with empty request body")
  public void createCustomEventGameWithEmptyRequestBody() throws HttpException {
    var emptyRequest = CreateCustomEventGameRequest.builder().build();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            "{emptyRequest}");

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created without required type field")
  public void createCustomEventGameWithoutType() throws HttpException {
    createRequest.setType(null);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created without required title field")
  public void createCustomEventGameWithoutTitle() throws HttpException {
    createRequest.setTitle(null);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created without required fixtures field")
  public void createCustomEventGameWithoutFixtures() throws HttpException {
    createRequest.setFixtures(null);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with empty fixtures")
  public void createCustomEventGameWithEmptyFixtures() throws HttpException {
    createRequest.setFixtures(List.of());
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created without required status field")
  public void createCustomEventGameWithoutStatus() throws HttpException {
    createRequest.setStatus(null);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created without required predictions cutoff field")
  public void createCustomEventGameWithoutPredictionsCutoff() throws HttpException {
    createRequest.setPredictionsCutoff(null);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be created with invalid type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"INVALID", "", " ", "123", "null"})
  public void createCustomEventGameWithInvalidType(String invalidType) throws HttpException {
    createRequest.setType(invalidType);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be created with invalid title: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"", " "})
  public void createCustomEventGameWithInvalidTitle(String invalidTitle) throws HttpException {
    createRequest.setTitle(invalidTitle);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be created with invalid status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"INVALID", "UNKNOWN", "", " ", "123", "null"})
  public void createCustomEventGameWithInvalidStatus(String invalidStatus) throws HttpException {
    try {
      createRequest.setStatus(GameStatus.valueOf(invalidStatus));
    } catch (IllegalArgumentException e) {
      // For invalid enum values, we'll skip the enum conversion and test with string
      // This would require a different approach in actual implementation
      //response.then().assertThat().statusCode(SC_BAD_REQUEST);
      return;
    }
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with extremely long title")
  public void createCustomEventGameWithExtremelyLongTitle() throws HttpException {
    var longTitle = "a".repeat(1000); // 1000 character title
    createRequest.setTitle(longTitle);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with extremely long description")
  public void createCustomEventGameWithExtremelyLongDescription() throws HttpException {
    var longDescription = "a".repeat(5000); // 5000 character description
    createRequest.setDescription(longDescription);
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with past predictions cutoff")
  public void createCustomEventGameWithPastPredictionsCutoff() throws HttpException {
    // Set predictions cutoff to 1 day ago
    createRequest.setPredictionsCutoff(
        Date.from(ZonedDateTime.now(java.time.ZoneOffset.UTC).minusDays(1).toInstant()));
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with fixture without question")
  public void createCustomEventGameWithFixtureWithoutQuestion() throws HttpException {
    var fixtureWithoutQuestion = CustomEventFixture.builder()
        .id(faker.internet().uuid())
        .question(null) // Missing question
        .voidReason(null)
        .points(1)
        .outcomeType("FREE_INPUT")
        .build();
    
    createRequest.setFixtures(List.of(fixtureWithoutQuestion));
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with fixture without outcome type")
  public void createCustomEventGameWithFixtureWithoutOutcomeType() throws HttpException {
    var fixtureWithoutOutcomeType = CustomEventFixture.builder()
        .id(faker.internet().uuid())
        .question("Test question")
        .voidReason(null)
        .points(1)
        .outcomeType(null) // Missing outcome type
        .build();
    
    createRequest.setFixtures(List.of(fixtureWithoutOutcomeType));
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with fixture with invalid points")
  public void createCustomEventGameWithFixtureWithInvalidPoints() throws HttpException {
    var fixtureWithInvalidPoints = CustomEventFixture.builder()
        .id(faker.internet().uuid())
        .question("Test question")
        .voidReason(null)
        .points(-1) // Invalid negative points
        .outcomeType("FREE_INPUT")
        .build();
    
    createRequest.setFixtures(List.of(fixtureWithInvalidPoints));
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be created with invalid outcome type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"INVALID", "", " ", "123", "null"})
  public void createCustomEventGameWithInvalidOutcomeType(String invalidOutcomeType) throws HttpException {
    var fixtureWithInvalidOutcomeType = CustomEventFixture.builder()
        .id(faker.internet().uuid())
        .question("Test question")
        .voidReason(null)
        .points(1)
        .outcomeType(invalidOutcomeType)
        .build();
    
    createRequest.setFixtures(List.of(fixtureWithInvalidOutcomeType));
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be created with duplicate fixture IDs")
  public void createCustomEventGameWithDuplicateFixtureIds() throws HttpException {
    var duplicateId = faker.internet().uuid();
    var fixture1 = CustomEventFixture.builder()
        .id(duplicateId)
        .question("Question 1")
        .voidReason(null)
        .points(1)
        .outcomeType("FREE_INPUT")
        .build();
    
    var fixture2 = CustomEventFixture.builder()
        .id(duplicateId) // Same ID as fixture1
        .question("Question 2")
        .voidReason(null)
        .points(1)
        .outcomeType("FREE_INPUT")
        .build();
    
    createRequest.setFixtures(List.of(fixture1, fixture2));
    
    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }
}
