package com.fansunited.automation.predictionapi.customGames.bracketGames.post;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.getPredictionBracketGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.predictionBracket;
import static java.time.ZoneOffset.UTC;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.bracket.Fixture;
import com.fansunited.automation.model.predictionapi.bracket.Meta;
import com.fansunited.automation.model.predictionapi.bracket.Participant;
import com.fansunited.automation.model.predictionapi.bracket.request.CreateBracketGameRequest;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

public class CreateBracketGameTest extends PredictionApiBaseTest {
  @Test
  @DisplayName("Verify specific game is successfully created. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createBracketGame() throws HttpException, IllegalArgumentException {

    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));

    var images = new Faker().internet().avatar();
    var createBracketRequest =
        CreateBracketGameRequest.builder()
            .id(new Faker().internet().uuid())
            .title("Only for test")
            .type(GameType.CustomGameType.BRACKET)
            .fixtures(
                List.of(
                    Fixture.builder()
                        .matchId("81e98bc6-11f3-400b-aa73-9682a1cdb5a8")
                        .start_date(date)
                        .participantOne("A")
                        .participantTwo("B")
                        .home_participant("A")
                        .build(),
                    Fixture.builder()
                        .matchId("ce97689e-af84-46ac-9d8c-5be93254e485")
                        .start_date(date)
                        .participantOne("C")
                        .participantTwo("D")
                        .home_participant("C")
                        .build()))
            .description(new Faker().internet().domainWord())
            .meta(
                Meta.builder()
                    .participants(
                        List.of(
                            Participant.builder()
                                .id("B")
                                .name(new Faker().name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("A")
                                .name(new Faker().name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("C")
                                .name(new Faker().name().firstName())
                                .image(images)
                                .undecided(false)
                                .build(),
                            Participant.builder()
                                .id("D")
                                .name(new Faker().name().firstName())
                                .image(images)
                                .undecided(false)
                                .build()))
                    .build())
            .rules(new Faker().internet().domainWord())
            .images(Images.builder().cover(images).main(images).mobile(images).build())
            .points(1)
            .predictionsCutoff(
                Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(1)))
            .status(GameStatus.OPEN)
            .build();

    var response =
        predictionBracket(
            createBracketRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var getBracketGameResponse =
        getPredictionBracketGame(
            createBracketRequest.getId(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    getBracketGameResponse
        .then()
        .assertThat()
        .body(ID_PROP, equalTo(createBracketRequest.getId()))
        .body("title", equalTo(createBracketRequest.getTitle()))
        .body("predictions_cutoff", equalTo(createBracketRequest.getPredictionsCutoff()));
  }
}
