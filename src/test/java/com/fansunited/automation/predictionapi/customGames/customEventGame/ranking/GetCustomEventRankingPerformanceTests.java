package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequestWithOutcome;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

/**
 * Performance tests for the GET /v1/custom/event/{game_id}/ranking endpoint.
 */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking performance tests")
public class GetCustomEventRankingPerformanceTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify ranking response time for game with large number of participants")
  @Tags({@Tag(REGRESSION)})
  @Timeout(value = 30, unit = TimeUnit.SECONDS)
  public void verifyRankingResponseTimeForLargeParticipantCount() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add large number of participants (simulate high load)
    int participantCount = 100;
    for (int i = 0; i < participantCount; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      
      // Small delay to avoid overwhelming the system
      if (i % 10 == 0) {
        Thread.sleep(100);
      }
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Measure ranking response time
    long startTime = System.currentTimeMillis();
    
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    long endTime = System.currentTimeMillis();
    long responseTime = endTime - startTime;

    // Verify response
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThan(0));

    // Verify response time is acceptable (less than 5 seconds for 100 participants)
    assert responseTime < 5000 : 
        "Ranking response time (" + responseTime + "ms) should be less than 5000ms for " + 
        participantCount + " participants";

    System.out.println("Ranking response time for " + participantCount + 
                      " participants: " + responseTime + "ms");
  }

  @Test
  @DisplayName("Verify ranking endpoint handles concurrent requests")
  @Tags({@Tag(REGRESSION)})
  @Timeout(value = 60, unit = TimeUnit.SECONDS)
  public void verifyRankingConcurrentRequests() throws HttpException, InterruptedException,
      ExecutionException, TimeoutException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add some participants
    for (int i = 0; i < 20; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(50);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Execute concurrent ranking requests
    int concurrentRequests = 10;
    ExecutorService executor = Executors.newFixedThreadPool(concurrentRequests);
    List<CompletableFuture<Response>> futures = new ArrayList<>();

    long startTime = System.currentTimeMillis();

    for (int i = 0; i < concurrentRequests; i++) {
      CompletableFuture<Response> future = CompletableFuture.supplyAsync(() -> {
        try {
          return getCustomEventRanking(
              gameId,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON);
        } catch (HttpException e) {
          throw new RuntimeException(e);
        }
      }, executor);
      
      futures.add(future);
    }

    // Wait for all requests to complete
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(
        futures.toArray(new CompletableFuture[0]));
    
    allFutures.get(30, TimeUnit.SECONDS);
    
    long endTime = System.currentTimeMillis();
    long totalTime = endTime - startTime;

    // Verify all responses are successful
    int successfulRequests = 0;
    for (CompletableFuture<Response> future : futures) {
      Response response = future.get();
      if (response.getStatusCode() == 200) {
        successfulRequests++;
        response.then().assertThat().body("data", notNullValue());
      }
    }

    executor.shutdown();

    // Verify all requests were successful
    assert successfulRequests == concurrentRequests : 
        "All " + concurrentRequests + " concurrent requests should be successful. " +
        "Actual successful: " + successfulRequests;

    // Verify reasonable total time for concurrent requests
    assert totalTime < 15000 : 
        "Total time for " + concurrentRequests + " concurrent requests (" + totalTime + 
        "ms) should be less than 15000ms";

    System.out.println("Concurrent ranking requests: " + concurrentRequests + 
                      ", Total time: " + totalTime + "ms, " +
                      "Successful: " + successfulRequests);
  }

  @Test
  @DisplayName("Verify ranking caching behavior")
  @Tags({@Tag(REGRESSION)})
  public void verifyRankingCachingBehavior() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants
    for (int i = 0; i < 10; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(50);
    }

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // First request (cache miss)
    long firstRequestStart = System.currentTimeMillis();
    var firstResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
    long firstRequestTime = System.currentTimeMillis() - firstRequestStart;

    firstResponse.then().assertThat().statusCode(200);

    // Second request (potential cache hit)
    long secondRequestStart = System.currentTimeMillis();
    var secondResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
    long secondRequestTime = System.currentTimeMillis() - secondRequestStart;

    secondResponse.then().assertThat().statusCode(200);

    // Verify responses are identical
    String firstResponseBody = firstResponse.getBody().asString();
    String secondResponseBody = secondResponse.getBody().asString();
    
    assert firstResponseBody.equals(secondResponseBody) : 
        "Cached response should be identical to original response";

    // If caching is implemented, second request should be faster
    // This is a soft assertion as caching behavior may vary
    if (secondRequestTime < firstRequestTime * 0.8) {
      System.out.println("Caching appears to be working. First request: " + 
                        firstRequestTime + "ms, Second request: " + secondRequestTime + "ms");
    } else {
      System.out.println("Caching behavior unclear. First request: " + 
                        firstRequestTime + "ms, Second request: " + secondRequestTime + "ms");
    }
  }
}
