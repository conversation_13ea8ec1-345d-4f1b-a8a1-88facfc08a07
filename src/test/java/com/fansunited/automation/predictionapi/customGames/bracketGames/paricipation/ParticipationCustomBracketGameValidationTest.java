package com.fansunited.automation.predictionapi.customGames.bracketGames.paricipation;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.participateBracketGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.predictionBracket;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.CreateBracketGameHelper;
import com.fansunited.automation.model.predictionapi.bracket.BracketTieBreaker;
import com.fansunited.automation.model.predictionapi.bracket.CustomBracketFixture;
import com.fansunited.automation.model.predictionapi.bracket.request.CreateBracketGameRequest;
import com.fansunited.automation.model.predictionapi.bracket.request.CustomBracketPrediction;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class ParticipationCustomBracketGameValidationTest extends PredictionApiBaseTest {
  private CreateBracketGameRequest createRequest;
  private Faker faker = new Faker();

  @BeforeEach
  public void setUp() {
    createRequest =
        CreateBracketGameHelper.createCustomBracketGameRequest(
            faker.internet().domainWord(),
            faker.lorem().sentence(),
            faker.lorem().paragraph(),
            10,
            GameStatus.PENDING,
            true,
            100);
  }

  @Test
  @DisplayName("Create participation when the game status is PENDING")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void participateBracketGameWithPendingStatus() throws HttpException {

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_OK);

    var predictionRequest =
        CustomBracketPrediction.builder()
            .fixtures(
                List.of(
                    CustomBracketFixture.builder()
                        .matchId(response.body().jsonPath().get("fixtures[0].match_id").toString())
                        .participantTwo(
                            response
                                .body()
                                .jsonPath()
                                .get("fixtures[0].participant_two")
                                .toString())
                        .participantOne(
                            response
                                .body()
                                .jsonPath()
                                .get("fixtures[0].participant_one")
                                .toString())
                        .winner("participant_one")
                        .build()))
            .tiebreaker(BracketTieBreaker.builder().statTotal(10).build())
            .build();

    var participateResponse =
        participateBracketGame(
            response.body().jsonPath().get(ID_PROP).toString(),
            predictionRequest,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(participateResponse);

    participateResponse
        .then()
        .assertThat()
        .statusCode(SC_BAD_REQUEST)
        .body("error.message", equalTo("Game is not open for predictions"));
  }

}
