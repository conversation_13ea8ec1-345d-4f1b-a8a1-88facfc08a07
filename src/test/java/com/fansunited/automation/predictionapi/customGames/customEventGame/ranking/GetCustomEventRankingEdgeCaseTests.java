package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequest;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequestWithOutcome;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

/**
 * Edge case tests for the GET /v1/custom/event/{game_id}/ranking endpoint.
 */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking edge case tests")
public class GetCustomEventRankingEdgeCaseTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify ranking for unresolved game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForUnresolvedGame() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants but don't resolve the game
    for (int i = 0; i < 3; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Get ranking for unresolved game
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response - should either return empty ranking or preliminary ranking
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    
    // For unresolved games, ranking might be empty or show preliminary results
    List<Object> rankingData = rankingResponse.then().extract().path("data");
    if (!rankingData.isEmpty()) {
      // If preliminary ranking is shown, verify structure
      rankingResponse.then().assertThat().body("data[0].user_id", notNullValue());
      rankingResponse.then().assertThat().body("data[0].rank", notNullValue());
    }
  }

  @Test
  @DisplayName("Verify ranking for game with cancelled status")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForCancelledGame() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants
    for (int i = 0; i < 2; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Cancel the game
    var updateRequest = updateCustomEventGameRequest();
    updateRequest.setStatus(GameStatus.CANCELED);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking for cancelled game
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response - cancelled games should return empty ranking or specific message
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", equalTo(0));
  }

  @Test
  @DisplayName("Verify ranking for game with expired predictions cutoff")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForGameWithExpiredPredictionsCutoff() throws HttpException, InterruptedException {
    // Create a custom event game with past cutoff
    var createRequest = createDefaultCustomEventGameRequest();
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    createRequest.setPredictionsCutoff(pastCutoff);
    
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Try to get ranking for game with expired cutoff
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    // Should return empty ranking since no predictions could be made
    rankingResponse.then().assertThat().body("data.size()", equalTo(0));
  }

  @Test
  @DisplayName("Verify ranking for game with single participant")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForGameWithSingleParticipant() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add only one participant
    var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
    var participationResponse = participateInCustomEventGame(
        CLIENT_AUTOMATION_ID,
        gameId,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        AuthConstants.ENDPOINTS_API_KEY,
        getCurrentTestUser().getEmail(),
        ContentType.JSON,
        participationRequest);
    
    participationResponse.then().assertThat().statusCode(200);

    // Resolve the game
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify single participant ranking
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", equalTo(1));
    rankingResponse.then().assertThat().body("data[0].rank", equalTo(1));
    rankingResponse.then().assertThat().body("data[0].user_id", notNullValue());
    rankingResponse.then().assertThat().body("data[0].score", notNullValue());
  }

  @Test
  @DisplayName("Verify ranking for game with partial fixture outcomes")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForGameWithPartialFixtureOutcomes() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants
    for (int i = 0; i < 3; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Partially resolve the game (only some fixtures have outcomes)
    var updateRequest = updateCustomEventGameRequest();
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    updateRequest.setPredictionsCutoff(pastCutoff);
    
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking for partially resolved game
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response - should handle partial resolution gracefully
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThanOrEqualTo(0));
    
    // If ranking is shown for partial resolution, verify structure
    List<Object> rankingData = rankingResponse.then().extract().path("data");
    if (!rankingData.isEmpty()) {
      rankingResponse.then().assertThat().body("data[0].user_id", notNullValue());
      rankingResponse.then().assertThat().body("data[0].rank", notNullValue());
    }
  }

  @Test
  @DisplayName("Verify ranking for game with all zero scores")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyRankingForGameWithAllZeroScores() throws HttpException, InterruptedException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();
    
    // Extract fixture IDs
    List<String> fixtureIds = createResponse.then().extract().path("fixtures.id");
    String fixtureId1 = fixtureIds.get(0);
    String fixtureId2 = fixtureIds.get(1);

    // Add participants with predictions that will likely result in zero scores
    for (int i = 0; i < 3; i++) {
      var participationRequest = participateInDefaultCustomEventGameRequest(fixtureId1, fixtureId2);
      var participationResponse = participateInCustomEventGame(
          CLIENT_AUTOMATION_ID,
          gameId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          AuthConstants.ENDPOINTS_API_KEY,
          getCurrentTestUser().getEmail(),
          ContentType.JSON,
          participationRequest);
      
      participationResponse.then().assertThat().statusCode(200);
      Thread.sleep(100);
    }

    // Resolve the game with outcomes that don't match any predictions
    Date pastCutoff = Date.from(ZonedDateTime.now().minusDays(1).toInstant());
    var updateRequest = updateCustomEventGameRequestWithOutcome(pastCutoff);
    var updateResponse = updateCustomEventGame(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        updateRequest);

    updateResponse.then().assertThat().statusCode(200);

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify ranking handles all zero scores correctly
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
    rankingResponse.then().assertThat().body("data.size()", greaterThanOrEqualTo(3));
    
    // All participants should have the same rank if all scores are zero
    List<Integer> scores = rankingResponse.then().extract().path("data.score");
    List<Integer> ranks = rankingResponse.then().extract().path("data.rank");
    
    // Verify ranking logic for tied zero scores
    for (int i = 0; i < scores.size(); i++) {
      assert scores.get(i) >= 0 : "Scores should not be negative";
    }
  }
}
