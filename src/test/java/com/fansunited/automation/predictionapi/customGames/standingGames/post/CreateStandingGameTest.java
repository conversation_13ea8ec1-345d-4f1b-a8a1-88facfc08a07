package com.fansunited.automation.predictionapi.customGames.standingGames.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.OPEN;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

public class CreateStandingGameTest extends PredictionStandingBaseSetup {

  @Test
  @DisplayName("Verify standing game is successfully created.")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createStandingGame() throws HttpException, IllegalArgumentException {

    var response =
        PredictionStandingBaseSetup.createCustomStandingGame(
            createStandingGameRequest(10, OPEN, 1),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);
  }
}
