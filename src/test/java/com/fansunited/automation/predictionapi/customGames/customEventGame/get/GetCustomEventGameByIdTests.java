package com.fansunited.automation.predictionapi.customGames.customEventGame.get;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventGameById;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.hamcrest.Matchers.equalTo;

@DisplayName("Prediction Api - GET /v1/event/games/{gameId} endpoint happy path tests")
public class GetCustomEventGameByIdTests {

  @Test
  @DisplayName("Verify GET /v1/event/games/{gameId} happy path")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getCustomEventGameHappyPath() throws HttpException {

    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var gameId = response.then().extract().path("id").toString();

    var getResponse =
        getCustomEventGameById(
            gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getResponse
        .then()
        .assertThat()
        .log()
        .all()
        .statusCode(200)
        .body("id", equalTo(gameId))
        .body("title", equalTo(createCustomEventGameRequest.getTitle()))
        .body("description", equalTo(createCustomEventGameRequest.getDescription()))
        .body("type", equalTo(createCustomEventGameRequest.getType()))
        .body("status", equalTo(createCustomEventGameRequest.getStatus().getValue()))
        .body("fixtures[0].id", equalTo(createCustomEventGameRequest.getFixtures().get(0).getId()))
        .body("fixtures[1].id", equalTo(createCustomEventGameRequest.getFixtures().get(1).getId()));
  }

  @Test
  @DisplayName(
      "Verify GET /v1/event/games/{gameId} response returned by the server is cached for 10m")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getCustomEventGame() throws HttpException {

    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var gameId = response.then().extract().path("id").toString();

    var getResponse =
        getCustomEventGameById(
            gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getResponse.then().assertThat().log().all().statusCode(200);
    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TEN_MINUTES);
  }
}
