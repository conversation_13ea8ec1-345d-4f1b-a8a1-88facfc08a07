package com.fansunited.automation.predictionapi.customGames.bracketGames.post;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.predictionBracket;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static java.time.ZoneOffset.UTC;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.CreateBracketGameHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.bracket.Fixture;
import com.fansunited.automation.model.predictionapi.bracket.Meta;
import com.fansunited.automation.model.predictionapi.bracket.Participant;
import com.fansunited.automation.model.predictionapi.bracket.request.CreateBracketGameRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

public class CreateBracketGameValidationTests extends PredictionApiBaseTest {

  private CreateBracketGameRequest createRequest;
  private Faker faker;

  @BeforeEach
  public void setUp() {
    faker = new Faker();
    createRequest = CreateBracketGameHelper.createDefaultBracketGameRequest();
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be created with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void createBracketGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_FORBIDDEN)));
  }

  @ParameterizedTest(
      name = "Verify bracket game cannot be created with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void createBracketGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @Test()
  @DisplayName("Verify bracket game cannot be created with unsupported content type")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createBracketGameWithUnsupportedContentType() throws HttpException {
    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null);
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created with null content type")
  public void createBracketGameWithNullContentType() throws HttpException {
    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @DisplayName("Verify bracket game cannot be created from billing manager")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createBracketGameWithInvalidJwtToken() throws HttpException {

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_PROFILE,
            BILLING_MANAGER_EMAIL,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_FORBIDDEN), is(SC_UNAUTHORIZED)));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created with empty request body")
  public void createBracketGameWithEmptyRequestBody() throws HttpException {
    var response =
        predictionBracket(
            "{}",
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-4011")})
  @DisplayName("Verify bracket game cannot be created with invalid request body")
  public void createBracketGameWithInvalidRequestBody() throws HttpException {
    var response =
        predictionBracket(
            "{ invalid json }",
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created without ID")
  public void createBracketGameWithoutId() throws HttpException {
    // Use helper to create a request with null ID
    createRequest = CreateBracketGameHelper.createBracketGameRequestWithId(null);

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created without title")
  public void createBracketGameWithoutTitle() throws HttpException {
    // Use helper to create a request with null title
    createRequest = CreateBracketGameHelper.createBracketGameRequestWithTitle(null);

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-4019")})
  @DisplayName("Verify bracket game cannot be created without fixtures")
  public void createBracketGameWithoutFixtures() throws HttpException {
    // Use helper to create a request without fixtures
    createRequest = CreateBracketGameHelper.createBracketGameRequestWithoutFixtures();

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created without participants")
  public void createBracketGameWithoutParticipants() throws HttpException {
    // Use helper to create a request without participants
    createRequest = CreateBracketGameHelper.createBracketGameRequestWithoutParticipants();

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created without predictions cutoff")
  public void createBracketGameWithoutPredictionsCutoff() throws HttpException {
    // Use helper to create a request with null predictions cutoff
    createRequest = CreateBracketGameHelper.createBracketGameRequestWithCutoff(null);

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created with past predictions cutoff")
  public void createBracketGameWithPastPredictionsCutoff() throws HttpException {
    // Use helper to create a request with past predictions cutoff
    String pastCutoff = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).minusHours(1));
    createRequest = CreateBracketGameHelper.createBracketGameRequestWithCutoff(pastCutoff);

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify bracket game cannot be created with invalid ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  public void createBracketGameWithInvalidId(String invalidId) throws HttpException {

    createRequest.setId(invalidId);

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created with mismatched participant IDs")
  public void createBracketGameWithMismatchedParticipantIds() throws HttpException {
    // Create fixtures with participant IDs that don't match the participants list
    var date = Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusHours(2));
    var images = faker.internet().avatar();

    createRequest.setFixtures(
        List.of(
            Fixture.builder()
                .matchId(faker.internet().uuid())
                .start_date(date)
                .participantOne("X") // This ID doesn't exist in participants
                .participantTwo("Y") // This ID doesn't exist in participants
                .home_participant("Z")
                .build()));

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify bracket game cannot be created with duplicate participant IDs")
  public void createBracketGameWithDuplicateParticipantIds() throws HttpException {
    var images = faker.internet().avatar();

    // Create participants with duplicate IDs
    createRequest.setMeta(
        Meta.builder()
            .participants(
                List.of(
                    Participant.builder()
                        .id("A")
                        .name(faker.name().firstName())
                        .image(images)
                        .undecided(false)
                        .build(),
                    Participant.builder()
                        .id("A") // Duplicate ID
                        .name(faker.name().firstName())
                        .image(images)
                        .undecided(false)
                        .build()))
            .build());

    var response =
        predictionBracket(
            createRequest,
            FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }
}
