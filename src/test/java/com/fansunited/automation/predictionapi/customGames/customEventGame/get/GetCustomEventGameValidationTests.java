package com.fansunited.automation.predictionapi.customGames.customEventGame.get;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventGames;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/event/games endpoint validation tests")
public class GetCustomEventGameValidationTests extends PredictionApiBaseTest {

  private String gameId;

 @BeforeEach
  public void setUp() throws HttpException {

    // Create a custom event game to use for GET tests
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createRequest);

    createResponse.then().assertThat().statusCode(200);
  }

  @ParameterizedTest(
      name =
          "Verify custom event game cannot be fetched with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getCustomEventGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getCustomEventGames(
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_UNAUTHORIZED)));
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be fetched with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getCustomEventGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getCustomEventGames(
             CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON,
            null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event games are returned in ascending order by created_at")
  public void getCustomEventGamesInAscendingOrderByCreatedAt() throws HttpException {
    var response =
        getCustomEventGames(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            "?sort=created_at&direction=asc");

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200);

    // Extract created_at dates from response
    List<String> createdAtDates = response.then().extract().path("data.created_at");

    // Verify that dates are in ascending order
    for (int i = 0; i < createdAtDates.size() - 1; i++) {
      LocalDateTime current = LocalDateTime.parse(createdAtDates.get(i), DateTimeFormatter.ISO_DATE_TIME);
      LocalDateTime next = LocalDateTime.parse(createdAtDates.get(i + 1), DateTimeFormatter.ISO_DATE_TIME);

      // Current date should be less than or equal to next date (ascending order)
      assert current.isBefore(next) || current.isEqual(next) :
          "Games are not in ascending order by created_at. Current: " + current + ", Next: " + next;
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event games are returned in descending order by created_at")
  public void getCustomEventGamesInDescendingOrderByCreatedAt() throws HttpException {
    var response =
        getCustomEventGames(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            "?sort=created_at&direction=desc");

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200);

    // Extract created_at dates from response
    List<String> createdAtDates = response.then().extract().path("data.created_at");

    // Verify that dates are in descending order
    for (int i = 0; i < createdAtDates.size() - 1; i++) {
      LocalDateTime current = LocalDateTime.parse(createdAtDates.get(i), DateTimeFormatter.ISO_DATE_TIME);
      LocalDateTime next = LocalDateTime.parse(createdAtDates.get(i + 1), DateTimeFormatter.ISO_DATE_TIME);

      // Current date should be greater than or equal to next date (descending order)
      assert current.isAfter(next) || current.isEqual(next) :
          "Games are not in descending order by created_at. Current: " + current + ", Next: " + next;
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event games are returned in ascending order by title")
  public void getCustomEventGamesInAscendingOrderByTitle() throws HttpException {
    var response =
        getCustomEventGames(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            "?sort=title&direction=asc");

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200);

    // Extract titles from response
    List<String> titles = response.then().extract().path("data.title");

    // Create a sorted copy for comparison
    List<String> sortedTitles = new ArrayList<>(titles);
    Collections.sort(sortedTitles);

    // Verify that titles are in ascending order
    assert titles.equals(sortedTitles) :
        "Games are not in ascending order by title. Actual: " + titles + ", Expected: " + sortedTitles;
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event games are returned in descending order by title")
  public void getCustomEventGamesInDescendingOrderByTitle() throws HttpException {
    var response =
        getCustomEventGames(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            "?sort=title&direction=desc");

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200);

    // Extract titles from response
    List<String> titles = response.then().extract().path("data.title");

    // Create a sorted copy for comparison (reverse order)
    List<String> sortedTitles = new ArrayList<>(titles);
    Collections.sort(sortedTitles, Collections.reverseOrder());

    // Verify that titles are in descending order
    assert titles.equals(sortedTitles) :
        "Games are not in descending order by title. Actual: " + titles + ", Expected: " + sortedTitles;
  }

  @ParameterizedTest(
      name = "Verify custom event games cannot be fetched with invalid sort parameter: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"invalid_field", "unknown_column", "123", "@#$%", "null", "undefined"})
  public void getCustomEventGamesWithInvalidSortParameter(String invalidSort) throws HttpException {
    var response =
        getCustomEventGames(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,

            ContentType.JSON,
            "?sort=" + invalidSort);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name = "Verify custom event games cannot be fetched with invalid sort direction: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"invalid", "up", "down", "123", "true", "false", "random"})
  public void getCustomEventGamesWithInvalidSortDirection(String invalidDirection) throws HttpException {
    var response =
        getCustomEventGames(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,

            ContentType.JSON,
            "?sort=created_at&direction=" + invalidDirection);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  
}
