package com.fansunited.automation.predictionapi.customGames.customEventGame.post;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Prediction Api - POST /v1/event/games endpoint happy path tests")
public class CreateCustomEventGameTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify creation of a custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createCustomEventGameHappyPath() throws HttpException {

    // Use helper to create a default custom event game request
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);

    response
        .then()
        .assertThat()
        .log()
        .all()
        .statusCode(200)
        .body("title", equalTo(createCustomEventGameRequest.getTitle()))
        .body("description", equalTo(createCustomEventGameRequest.getDescription()))
        .body("type", equalTo(createCustomEventGameRequest.getType()))
        .body("status", equalTo(createCustomEventGameRequest.getStatus().getValue()))
        .body("fixtures[0].id", equalTo(createCustomEventGameRequest.getFixtures().get(0).getId()))
        .body("fixtures[1].id", equalTo(createCustomEventGameRequest.getFixtures().get(1).getId()));
  }

  @Test
  @DisplayName("Verify that user can not create a custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void userCanNotCreateCustomEventGame() throws HttpException {

    // Use helper to create a default custom event game request
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            getCurrentTestUser().getEmail(),
            ContentType.JSON,
            createCustomEventGameRequest);

    response
        .then()
        .assertThat()
        .log()
        .all()
        .statusCode(SC_FORBIDDEN);
  }
}
