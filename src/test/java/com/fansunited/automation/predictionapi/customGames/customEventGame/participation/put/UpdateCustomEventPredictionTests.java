package com.fansunited.automation.predictionapi.customGames.customEventGame.participation.put;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.Endpoints.CustomResolverApi.CUSTOM_EVENT_RESOLVER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.participateInCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionCustomEventsEndpoints.updatePredictionInCustomEventGame;
import static com.fansunited.automation.core.resolver.CustomResolver.customGameResolve;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.participateInDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.updateCustomEventPredictionRequest;
import static com.fansunited.automation.helpers.UpdateRequest.updateCustomEventGameRequestWithOutcome;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Prediction Api - PUT /v1/event/games/{gameId}/participation endpoint happy path tests")
public class UpdateCustomEventPredictionTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify creation of a custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createCustomEventGameHappyPath() throws HttpException, InterruptedException {
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();
    var createResponse =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);
    createResponse.then().assertThat().statusCode(200);
    var gameId = createResponse.then().extract().path("id").toString();
    var cutOff = createResponse.then().extract().path("predictions_cutoff").toString();
    var id = createResponse.then().extract().path("fixtures[0].id").toString();
    var id1= createResponse.then().extract().path("fixtures[1].id").toString();

    // Use helper to create a default custom event game request
    var participateInCustomEventGameRequest = participateInDefaultCustomEventGameRequest(id,id1);

    var response =
        participateInCustomEventGame(
            CLIENT_AUTOMATION_ID,
            gameId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            getCurrentTestUser().getEmail(),
            ContentType.JSON,
            participateInCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var predictionId = response.then().extract().path("id").toString();

    var updateCustomEventPredictionRequest = updateCustomEventPredictionRequest();

    var updateResponse =
        updatePredictionInCustomEventGame(
            CLIENT_AUTOMATION_ID,
            predictionId,gameId,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            getCurrentTestUser().getEmail(),
            ContentType.JSON,
            updateCustomEventPredictionRequest);

    updateResponse.then().assertThat().log().all().statusCode(200);



    var updateCustomEventGameRequest= updateCustomEventGameRequestWithOutcome(cutOff);

    var updateEventGameResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateCustomEventGameRequest);

    updateEventGameResponse.then().assertThat().log().all().statusCode(200);
    
    customGameResolve(CUSTOM_EVENT_RESOLVER, gameId);
    
  }

  
}
