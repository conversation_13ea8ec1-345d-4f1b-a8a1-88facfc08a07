package com.fansunited.automation.predictionapi.customGames.customEventGame.ranking;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventRanking;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

/**
 * Simple working tests for the GET /v1/custom/event/{game_id}/ranking endpoint.
 */
@DisplayName("Prediction Api - GET /v1/custom/event/{game_id}/ranking simple tests")
public class GetCustomEventRankingSimpleTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify ranking endpoint returns 200 for valid game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingForValidGame() throws HttpException {
    // Create a custom event game
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        AuthConstants.FansUnitedClientsProject.ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Get ranking
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response
    rankingResponse.then().assertThat().statusCode(200);
    rankingResponse.then().assertThat().body("data", notNullValue());
  }

  @Test
  @DisplayName("Verify ranking endpoint returns 404 for non-existent game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingForNonExistentGame() throws HttpException {
    String nonExistentGameId = "non-existent-game-id";

    // Get ranking for non-existent game
    var rankingResponse = getCustomEventRanking(
        nonExistentGameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response
    rankingResponse.then().assertThat().statusCode(404);
  }

  @Test
  @DisplayName("Verify ranking endpoint returns 401 for invalid API key")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingWithInvalidApiKey() throws HttpException {
    // Create a custom event game first
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        AuthConstants.FansUnitedClientsProject.ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    String gameId = createResponse.then().extract().path("id").toString();

    // Get ranking with invalid API key
    var rankingResponse = getCustomEventRanking(
        gameId,
        CLIENT_AUTOMATION_ID,
        "invalid-api-key",
        ContentType.JSON);

    // Verify response
    rankingResponse.then().assertThat().statusCode(401);
  }

  @Test
  @DisplayName("Verify ranking endpoint returns 400 for invalid game ID format")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingWithInvalidGameIdFormat() throws HttpException {
    String invalidGameId = "invalid-game-id-format!@#$%";

    // Get ranking with invalid game ID
    var rankingResponse = getCustomEventRanking(
        invalidGameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response - should be 400 or 404
    int statusCode = rankingResponse.getStatusCode();
    assert statusCode == 400 || statusCode == 404 : 
        "Expected status code 400 or 404, but got: " + statusCode;
  }

  @Test
  @DisplayName("Verify ranking endpoint handles empty game ID")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getRankingWithEmptyGameId() throws HttpException {
    String emptyGameId = "";

    // Get ranking with empty game ID
    var rankingResponse = getCustomEventRanking(
        emptyGameId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    // Verify response - should be 400 or 404
    int statusCode = rankingResponse.getStatusCode();
    assert statusCode == 400 || statusCode == 404 : 
        "Expected status code 400 or 404, but got: " + statusCode;
  }
}
