package com.fansunited.automation.predictionapi.contest.post;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.contest.CreateWinnersRequest;
import com.fansunited.automation.model.predictionapi.contest.UserListToSetWinners;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - POST /v1/winners endpoint happy path tests")
public class SetWinnersTests extends TemplateBaseTest {

  @ParameterizedTest(name = "Verify creation of winners by contest type. Contest Type {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"GAME", "TEMPLATE"})
  public void verifySetWinners(String contestType)
      throws Exception {

    var faker = new Faker();
    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                GameStatus.OPEN,
                ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);
    var gameId = gameInstance.getId();
    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileId = createUser().getUid();

    String contestId = gameId;

    if (contestType.equals("TEMPLATE")) {

      var templateRequest =
          TemplateRequest.builder()
              .name(new Faker().name().title())
              .markets(markets)
              .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
              .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(10)))
              .matchIds(matchList.stream().map(Match::getId).toList())
              .gameIds(Collections.singletonList(gameId))
              .gameTypes(Collections.singletonList(gameInstance.getType()))
              .build();

      contestId =
          TemplatesEndpoint.createLeaderboardTemplate(templateRequest)
              .as(TemplateResponse.class)
              .getId();
    }

    InsertBigQData.insertSingleRankEventWithFinishDate(
        LocalDateTime.now(),
        profileId,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X,
        30,
        gameId,
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate,
        LocalDateTime.now()
            .plusDays(1)
            .atOffset(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss 'UTC'")));

    if (contestType.equals("TEMPLATE")) {
      TestSynchronizationHelper.getInstance()
          .completePreconditionAndAddMidCondition(
              TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
              contestId);
    } else {
      TestSynchronizationHelper.getInstance()
          .completePreconditionAndAddMidCondition(
              TestSynchronizationHelper.USER_RANKING_GAME_MV,
              gameId);
    }

    UserListToSetWinners userWinnersRequest =
            UserListToSetWinners.builder()
                    .profile_id(profileId)
                    .position("1")
                    .note(faker.lorem().word())
                    .tags(List.of(faker.lorem().word(), faker.lorem().word(), faker.lorem().word()))
                    .build();

    var request =
            CreateWinnersRequest.builder()
                    .contest_type(contestType)
                    .contest_id(contestId)
                    .description(faker.lorem().sentence(3))
                    .user_list(List.of(userWinnersRequest))
                    .build();

    var responseWinner =
        WinnersEndpoint.setWinnersByContestType(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER,
            ContentType.JSON);

    responseWinner
        .then()
        .log()
        .all()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.description", equalTo(request.getDescription()))
        .body("data.contest_id", equalTo(contestId))
        .body("data.user_list.profile_id", hasItem(profileId))
        .body("data.contest_type", equalTo(contestType));
  }
}
