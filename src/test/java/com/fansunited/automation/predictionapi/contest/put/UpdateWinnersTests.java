package com.fansunited.automation.predictionapi.contest.put;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint.updateWinnersByContestId;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.contest.CreateWinnersRequest;
import com.fansunited.automation.model.predictionapi.contest.UserListToSetWinners;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Prediction Api - PUT /v1/winners endpoint happy path tests")
public class UpdateWinnersTests extends TemplateBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify update winners.")
  public void verifySetWinners()
      throws Exception {

    var profileIdFirstPlace = createUser().getUid();
    var matchList = MatchGenerator.generateMatches(1, false);

    matchList.forEach(
        match -> {
          match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
          match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
        });

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var gameInstance =
        GamesEndpoint.createGame(
                matchList.stream().map(Match::getId).toList(),
                GameType.TOP_X,
                GameStatus.OPEN,
                ZonedDateTime.now().plusMinutes(2))
            .as(GameInstance.class);

    var templateRequest =
        TemplateRequest.builder()
            .name(new Faker().name().title())
            .markets(markets)
            .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
            .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
            .matchIds(matchList.stream().map(Match::getId).toList())
            .gameIds(Collections.singletonList(gameInstance.getId()))
            .gameTypes(Collections.singletonList(gameInstance.getType()))
            .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);
    var template = response.as(TemplateResponse.class);
    currentTestResponse.set(response);
    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    InsertBigQData.insertSingleRankEventWithFinishDate(
        LocalDateTime.now(),
        profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X,
        30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate,
        LocalDateTime.now()
            .plusDays(1)
            .atOffset(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss 'UTC'")));

    InsertBigQData.insertSingleRankEventWithFinishDate(
        LocalDateTime.now(),
        profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X,
        30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate,
        LocalDateTime.now()
            .plusDays(1)
            .atOffset(ZoneOffset.UTC)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss 'UTC'")));

    var winnerRequest =
        CreateWinnersRequest.builder()
            .contest_id(template.getId())
            .description("Description")
            .contest_type("TEMPLATE")
            .user_list(
                List.of(
                    UserListToSetWinners.builder()
                        .profile_id(profileIdFirstPlace)
                        .note("test")
                        .position("2")
                        .tags(List.of("test", "test"))
                        .build()))
            .build();

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var updateResponse =
        updateWinnersByContestId(
            winnerRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER,
            ContentType.JSON);
    updateResponse
        .then()
        .statusCode(SC_OK)
        .body("data.description", equalTo(winnerRequest.getDescription()))
        .body("data.contest_id", equalTo(template.getId()))
        .body("data.user_list.profile_id", hasItem(profileIdFirstPlace))
        .body("data.contest_type", equalTo("TEMPLATE"));
  }
}
