package com.fansunited.automation.predictionapi.contest.get;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint.getWinnersByContestId;
import static com.fansunited.automation.helpers.BigQueryHelper.waitForEventsToBeSaved;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.predictionapi.contest.CreateWinnersRequest;
import com.fansunited.automation.model.predictionapi.contest.UserListToSetWinners;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Prediction Api - GET /v1/winners endpoint happy path tests")
public class GetWinnersTests extends PredictionApiBaseTest {

  @Test
  @DisplayName("Verify winners are successfully fetched")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getWinnersForContestTypeForGame()
      throws Exception {

    var gameId = createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileId = createUser().getUid();

    InsertBigQData.insertSingleRankEvent(
        LocalDateTime.now(),
        profileId,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X,
        30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate);
    // Have to wait for the ranking event to be saved.
    waitForEventsToBeSaved(14);

    String templateId = LeagueBaseTest.createTemplateForLeague().getId();

    Faker faker = new Faker(new Locale("en"));
    UserListToSetWinners userWinnersRequest =
        UserListToSetWinners.builder()
            .profile_id(profileId)
            .position("1")
            .note(faker.lorem().word())
            .tags(List.of(faker.lorem().word(), faker.lorem().word(), faker.lorem().word()))
            .build();

    var request =
        CreateWinnersRequest.builder()
            .contest_type("GAME")
            .contest_id(gameId)
            .description(faker.lorem().sentence(3))
            .user_list(List.of(userWinnersRequest))
            .build();

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameId);

    WinnersEndpoint.setWinnersByContestType(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER,
            ContentType.JSON)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var response =
        getWinnersByContestId(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .log()
        .body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("description", equalTo(request.getDescription()))
        .body("contest_id", equalTo(gameId))
        .body("user_list.profile_id", hasItem(profileId))
        .body("contest_type", equalTo("GAME"));
  }
}
