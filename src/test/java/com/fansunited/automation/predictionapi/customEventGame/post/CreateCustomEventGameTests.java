package com.fansunited.automation.predictionapi.customEventGame.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static java.time.ZoneOffset.UTC;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CustomEventFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import io.restassured.http.ContentType;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Prediction Api - POST /v1/event/games endpoint happy path tests")
public class CreateCustomEventGameTests {

  @Test
  @DisplayName("Verify creation of a custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createCustomEventGameHappyPath() throws HttpException {
    var createCustomEventGameRequest =
        CreateCustomEventGameRequest.builder()
            .type("EVENT")
            .fixtures(
                List.of(
                    CustomEventFixture.builder()
                        .question("how are you")
                        .status(GameStatus.OPEN)
                        .voidReason(null)
                        .points(1)
                        .outcomeType("FREE_INPUT")
                        .build(),
                    CustomEventFixture.builder()
                        .question("what is going on")
                        .status(GameStatus.OPEN)
                        .voidReason(null)
                        .points(1)
                        .outcomeType("FREE_INPUT")
                        .build()))
            .title("test")
            .description("test")
            .rules("test")
            .images(null)
            .related(null)
            .customFields(null)
            .status(GameStatus.OPEN)
            .predictionsCutoff(generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusMinutes(10)))
            .outcome(false)
            .build();

  var response =createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createCustomEventGameRequest);

  response.then().assertThat().statusCode(200);
  }
}
