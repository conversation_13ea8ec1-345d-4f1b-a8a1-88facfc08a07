package com.fansunited.automation.predictionapi.customEventGame.put;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.updateCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Validation tests for the PUT /v1/event/games/{gameId} endpoint.
 */
@DisplayName("Prediction Api - PUT /v1/event/games/{gameId} endpoint validation tests")
public class UpdateCustomEventGameValidationTests extends PredictionApiBaseTest {

  private String gameId;
  private Faker faker;
  private CreateCustomEventGameRequest updateRequest;

  @BeforeEach
  public void setUp() throws HttpException {
    faker = new Faker();
    
    // Create a custom event game to update
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        createRequest);
    
    createResponse.then().assertThat().statusCode(200);
    gameId = createResponse.then().extract().path("id").toString();
    
    // Prepare update request
    updateRequest = updateCustomEventGameRequest();
  }



  @ParameterizedTest(
      name = "Verify custom event game cannot be updated with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void updateCustomEventGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        updateCustomEventGame(
            gameId,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_UNAUTHORIZED)));
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be updated with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void updateCustomEventGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            updateRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify API returns UNSUPPORTED_MEDIA_TYPE when updating custom event game with non-supported content type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void updateCustomEventGameWithUnsupportedContentType(ContentType contentType) throws HttpException {
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            updateRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with null content type")
  public void updateCustomEventGameWithNullContentType() throws HttpException {
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            updateRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be updated with invalid game ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"invalid-id", "123456", "", " "})
  public void updateCustomEventGameWithInvalidGameId(String invalidGameId) throws HttpException {
    var response =
        updateCustomEventGame(
            invalidGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with non-existent game ID")
  public void updateCustomEventGameWithNonExistentGameId() throws HttpException {
    var nonExistentId = faker.internet().uuid();
    
    var response =
        updateCustomEventGame(
            nonExistentId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_NOT_FOUND);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with null game ID")
  public void updateCustomEventGameWithNullGameId() throws HttpException {
    var response =
        updateCustomEventGame(
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with null request body")
  public void updateCustomEventGameWithNullRequestBody() throws HttpException {
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with empty request body")
  public void updateCustomEventGameWithEmptyRequestBody() throws HttpException {
    var emptyRequest = CreateCustomEventGameRequest.builder().build();
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            emptyRequest);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated without required type field")
  public void updateCustomEventGameWithoutType() throws HttpException {
    var requestWithoutType = updateCustomEventGameRequest();
    requestWithoutType.setType(null);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithoutType);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated without required title field")
  public void updateCustomEventGameWithoutTitle() throws HttpException {
    var requestWithoutTitle = updateCustomEventGameRequest();
    requestWithoutTitle.setTitle(null);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithoutTitle);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated without required fixtures field")
  public void updateCustomEventGameWithoutFixtures() throws HttpException {
    var requestWithoutFixtures = updateCustomEventGameRequest();
    requestWithoutFixtures.setFixtures(null);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithoutFixtures);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with empty fixtures")
  public void updateCustomEventGameWithEmptyFixtures() throws HttpException {
    var requestWithEmptyFixtures = updateCustomEventGameRequest();
    requestWithEmptyFixtures.setFixtures(List.of());
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithEmptyFixtures);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be updated with invalid type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"INVALID", "BRACKET", "STANDING", "", " ", "123", "null"})
  public void updateCustomEventGameWithInvalidType(String invalidType) throws HttpException {
    var requestWithInvalidType = updateCustomEventGameRequest();
    requestWithInvalidType.setType(invalidType);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithInvalidType);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be updated with invalid title: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"", " "})
  public void updateCustomEventGameWithInvalidTitle(String invalidTitle) throws HttpException {
    var requestWithInvalidTitle = updateCustomEventGameRequest();
    requestWithInvalidTitle.setTitle(invalidTitle);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithInvalidTitle);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with extremely long title")
  public void updateCustomEventGameWithExtremelyLongTitle() throws HttpException {
    var longTitle = "a".repeat(1000); // 1000 character title
    var requestWithLongTitle = updateCustomEventGameRequest();
    requestWithLongTitle.setTitle(longTitle);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithLongTitle);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated with past predictions cutoff")
  public void updateCustomEventGameWithPastPredictionsCutoff() throws HttpException {
    var requestWithPastCutoff = updateCustomEventGameRequest();
    // Set predictions cutoff to 1 day ago
    requestWithPastCutoff.setPredictionsCutoff(
        java.util.Date.from(java.time.ZonedDateTime.now(java.time.ZoneOffset.UTC).minusDays(1).toInstant()));
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithPastCutoff);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify custom event game cannot be updated without predictions cutoff")
  public void updateCustomEventGameWithoutPredictionsCutoff() throws HttpException {
    var requestWithoutCutoff = updateCustomEventGameRequest();
    requestWithoutCutoff.setPredictionsCutoff(null);
    
    var response =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            requestWithoutCutoff);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_BAD_REQUEST);
  }
}
