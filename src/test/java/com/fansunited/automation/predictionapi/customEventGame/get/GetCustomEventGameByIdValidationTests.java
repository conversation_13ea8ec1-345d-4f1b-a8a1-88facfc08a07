package com.fansunited.automation.predictionapi.customEventGame.get;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.getCustomEventGameById;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/event/games/{gameId} endpoint validation tests")
public class GetCustomEventGameByIdValidationTests extends PredictionApiBaseTest {

  private String gameId;

  @BeforeEach
  public void setUp() throws HttpException {
    

    // Create a custom event game to use for GET tests
    var createRequest = createDefaultCustomEventGameRequest();
    var createResponse = createCustomEventGame(
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
        ContentType.JSON,
        createRequest);

    createResponse.then().assertThat().statusCode(200);
    gameId = createResponse.then().extract().path("id").toString();
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be fetched with invalid/missing client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getCustomEventGameWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getCustomEventGameById(
            gameId,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(anyOf(is(SC_BAD_REQUEST), is(SC_UNAUTHORIZED)));
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be fetched with invalid/missing API key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getCustomEventGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getCustomEventGameById(
            gameId,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify custom event game cannot be fetched with invalid game ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  @ValueSource(strings = {"invalid-id", "123456"})
  public void getCustomEventGameWithInvalidGameId(String invalidGameId) throws HttpException {
    var response =
        getCustomEventGameById(
            invalidGameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_NOT_FOUND));
  }
  
}
