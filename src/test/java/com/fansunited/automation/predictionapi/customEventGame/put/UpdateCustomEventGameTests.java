package com.fansunited.automation.predictionapi.customEventGame.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createCustomEventGameRequest;
import static com.fansunited.automation.helpers.CustomEventGameHelper.CustomEventGameConfig;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createFixtureWithOutcome;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.model.predictionapi.games.request.CreateCustomEventGameRequest;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class UpdateCustomEventGameTests {
  @Test
  @DisplayName("Verify update of a custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void updateCustomEventGameHappyPath() throws HttpException {

    // Use helper to create a default custom event game request
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            createCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var gameId = response.then().extract().path("id").toString();

    // Use helper to create an updated custom event game request with different fixtures
    var updateCustomEventGameRequest = createCustomEventGameRequest(
        CustomEventGameConfig.builder()
            .fixtures(List.of(
                createFixtureWithOutcome("how are you today", null, "FREE_INPUT"),
                createFixtureWithOutcome("what is going on", "fine", "FREE_INPUT")
            ))
            .build());

    var updateResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateCustomEventGameRequest);

    updateResponse.then().assertThat().log().all().statusCode(200);
    updateResponse.then().assertThat().body("fixtures[0].question", equalTo("how are you today"));
    updateResponse.then().assertThat().body("fixtures[1].question", equalTo("what is going on"));
    updateResponse.then().assertThat().body("updated_at", notNullValue());





  }

}
