package com.fansunited.automation.predictionapi.customEventGame.put;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.createCustomEventGame;
import static com.fansunited.automation.core.apis.predictionapi.CustomEventsGameEndpoints.updateCustomEventGame;
import static com.fansunited.automation.helpers.CustomEventGameHelper.createDefaultCustomEventGameRequest;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Prediction Api - PUT /v1/event/games/{gameId} endpoint happy path tests")
public class UpdateCustomEventGameTests {
  @Test
  @DisplayName("Verify update of a custom event game")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void updateCustomEventGameHappyPath() throws HttpException {

    // Use helper to create a default custom event game request
    var createCustomEventGameRequest = createDefaultCustomEventGameRequest();

    var response =
        createCustomEventGame(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,ADMIN_USER,
            ContentType.JSON,
            createCustomEventGameRequest);

    response.then().assertThat().log().all().statusCode(200);

    var gameId = response.then().extract().path("id").toString();

    // Use helper to create an updated custom event game request with different fixtures

    var updateCustomEventGameRequest= createDefaultCustomEventGameRequest();

    var updateResponse =
        updateCustomEventGame(
            gameId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            updateCustomEventGameRequest);

    updateResponse.then().assertThat().log().all().statusCode(200);
    updateResponse.then().assertThat().body("fixtures[0].question", equalTo(updateCustomEventGameRequest.getFixtures().get(0).getQuestion()));
    updateResponse.then().assertThat().body("fixtures[1].question", equalTo(updateCustomEventGameRequest.getFixtures().get(1).getQuestion()));
    updateResponse.then().assertThat().body("updated_at", notNullValue());

  }

}
