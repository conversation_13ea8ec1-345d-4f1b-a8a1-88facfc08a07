package com.fansunited.automation.predictionapi.customEventGame.get;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;

public class GetCustomEventGameTests {


  @Test
  @DisplayName("Verify GET /v1/event/games/{gameId} response returned by the server is cached for 10m")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getCustomEventGame() {




  }

}
