package com.fansunited.automation.predictionapi.predictions.get;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.QUERY_ERROR_MESSAGE;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_PROFILE_ID;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getMatchesIdListAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.UserPredictionsEndpoint.getPredictionsForSpecificUser;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.predictionapi.GetPredictionsEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/predictions/users/{userId} endpoint validation tests")
public class GetPredictionsForSpecificUserValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify API cannot fetch specific user predictions with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void getSpecificUserPredictionsWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON,
            -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting specific user predictions with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getSpecificUserPredictionsWithInvalidClientId(String clientId) throws HttpException {

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, clientId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
            -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Disabled("This validation is deprecated")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-734")})
  @DisplayName("Verify API returns 404 when getting predictions for specific user with invalid user id")
  public void getSpecificUserPredictionsWithInvalidUserId() throws HttpException {

    var response =
        getPredictionsForSpecificUser(INVALID_PROFILE_ID, null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting specific user predictions with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getSpecificUserPredictionsWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType,
            -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify specific user predictions are still fetched if content type is NOT specified")
  public void getSpecificUserPredictionsWithoutSpecifyingContentType() throws HttpException {

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting specific user predictions with invalid prediction status. Prediction status: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = PredictionStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void getSpecificUserPredictionsWithInvalidStatus(PredictionStatus status)
      throws HttpException {

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), status.getValue(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns BAD_REQUEST when getting specific user predictions with negative or 0(integers) for pagination \"limit\" param. "
          + "\"limit\" param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-2, 0})
  public void getSpecificUserPredictionsWithInvalidLimitParam(int limit) throws HttpException {

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, limit, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API cannot fetch user by id predictions with game_types and status are used together")
  public void getUserByIdPredictionsWithGameTypesAndStatusInUse() throws HttpException {

    var status =
        PredictionStatus
            .getValidStatuses()
            .get(
                Helper.generateRandomNumber(0, PredictionStatus.getValidStatuses().size() - 1)
            );

    var gameType =
        GameType
            .getValidGameTypes()
            .get(
                Helper.generateRandomNumber(0, GameType.getValidGameTypes().size() - 1)
            );

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), gameType, status);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(QUERY_ERROR_MESSAGE));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API cannot fetch user's predictions when match_id and game_types are used together")
  public void getUserByIdPredictionsWithMatchIdAndGameTypesInUse() throws HttpException {

    var gameType =
        GameType
            .getValidGameTypes()
            .get(
                Helper.generateRandomNumber(0, GameType.getValidGameTypes().size() - 1)
            );

    var predictionsCutoff = generateFutureDate(12);
    var matchesIdList = getMatchesIdListAfterDate(
        getCompetitionsWhitelist(gameType),
        generateDateTimeInIsoFormat(predictionsCutoff),
        gameType == GameType.MATCH_QUIZ ? 1 : 6);

    GetPredictionsEndpoint getPredictionsEndpoint =
        GetPredictionsEndpoint.builder()
            .gameType(gameType)
            .limit(-1)
            .matchIds(matchesIdList)
            .build();

    var response = getPredictionsEndpoint.getPredictionsForSpecificUser();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(QUERY_ERROR_MESSAGE));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API cannot fetch user's predictions when match_id and status are used together")
  public void getUserByIdPredictionsWithMatchIdAndStatusInUse() throws HttpException {

    var gameType =
        GameType
            .getValidGameTypes()
            .get(
                Helper.generateRandomNumber(0, GameType.getValidGameTypes().size() - 1)
            );

    var status =
        PredictionStatus
            .getValidStatuses()
            .get(
                Helper.generateRandomNumber(0, PredictionStatus.getValidStatuses().size() - 1)
            );

    var predictionsCutoff = generateFutureDate(12);
    var matchesIdList = getMatchesIdListAfterDate(
        getCompetitionsWhitelist(gameType),
        generateDateTimeInIsoFormat(predictionsCutoff),
        gameType == GameType.MATCH_QUIZ ? 1 : 6);

    GetPredictionsEndpoint getPredictionsEndpoint =
        GetPredictionsEndpoint.builder()
            .status(status)
            .limit(-1)
            .matchIds(matchesIdList)
            .build();

    var response = getPredictionsEndpoint.getPredictionsForSpecificUser();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(QUERY_ERROR_MESSAGE));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API cannot fetch user's predictions when match_ids > 30")
  public void getUserByIdPredictionsWithMoreThanTenMatchIds() throws HttpException {

    var predictionsCutoff = generateFutureDate(12);
    var matchesIdList = getMatchesIdListAfterDate(
        getCompetitionsWhitelist(GameType.TOP_X),
        generateDateTimeInIsoFormat(predictionsCutoff),
        31);

    GetPredictionsEndpoint getPredictionsEndpoint =
        GetPredictionsEndpoint.builder()
            .limit(-1)
            .matchIds(matchesIdList)
            .build();

    var response = getPredictionsEndpoint.getPredictionsForSpecificUser();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo("Maximum match IDs in query param should be 30 or less."));
  }
}
