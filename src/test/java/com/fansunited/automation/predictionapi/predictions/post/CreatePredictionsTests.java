package com.fansunited.automation.predictionapi.predictions.post;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Predictions.GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getFullCoverageCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getAllFixtures;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getPredictionFixturesForOverCorners;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getPredictionFixturesForOverGoalMarkets;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.STATUS_POSTPONED;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGamePredictionFixtureOutcomeAndStatusToUpdate;
import static com.fansunited.automation.validators.PredictionApiValidator.validateCreatePredictionResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.Tiebreaker;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OverCornersValue;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OverGoalsValue;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.fansunited.automation.validators.RedisValidator;
import com.fansunited.automation.validators.ResolverValidator;
import io.restassured.RestAssured;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - POST /v1/predictions endpoint happy path tests")
public class CreatePredictionsTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify creation of single prediction for market: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createSinglePrediction(PredictionMarket market)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        Helper.generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = generateValidSinglePredictionFixture(market, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createSinglePredictionRequest, true, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify creation of prediction for game type: MATCH_QUIZ")
  public void createPredictionsForMatchQuiz()
      throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createPredictionRequest, false, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    var getOwnPredictionsResponse = getOwnPredictions();

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @ParameterizedTest(name = "Verify creation of single prediction for every allowed target value in OVER_GOALS market. Target: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = OverGoalsValue.class)
  public void createSinglePredictionOverGoals(OverGoalsValue overGoalsValue)
      throws Exception {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        Helper.generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    List<? extends PredictionFixture> prediction = getPredictionFixturesForOverGoalMarkets(overGoalsValue, matchId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures((List<? extends PredictionFixture>) prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createSinglePredictionRequest, true, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @ParameterizedTest(name = "Verify creation of single prediction for every allowed target value in OVER_CORNERS market. Target: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = OverCornersValue.class)
  public void createSinglePredictionOverCorners(OverCornersValue overCornersValue)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        Helper.generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    List<? extends PredictionFixture> prediction = getPredictionFixturesForOverCorners(overCornersValue, matchId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createSinglePredictionRequest, true, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify creation of prediction for game type: MATCH_QUIZ with random prediction fixture order")
  public void createPredictionsForMatchQuizWithDifferentMarketOrder()
      throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    Collections.shuffle(predictions);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createPredictionRequest, false, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify creation of prediction for game type: TOP_X")
  public void createPredictionsForTopX()
      throws HttpException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, matchId,
                null)));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createPredictionRequest, false, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validateGamePredictionForMatches(predictionInstance.getId(), matchesIdList);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify creation of prediction for game type: TOP_X with tiebreaker")
  public void createPredictionsForTopXWithTiebreaker()
      throws HttpException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, matchId,
                null)));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .tiebreaker(new Tiebreaker(15))
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createPredictionRequest, false, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validateGamePredictionForMatches(predictionInstance.getId(), matchesIdList);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify creation of prediction for game type: TOP_X with random prediction fixture order")
  public void createPredictionsForTopXWithDifferentMatchOrder()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, matchId,
                null)));

    Collections.shuffle(predictions);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createPredictionRequest, false, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validateGamePredictionForMatches(predictionInstance.getId(), matchesIdList);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1));

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }

  @ParameterizedTest(name = "Verify single prediction status for market: {arguments} when match is postponed")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createSinglePredictionForPostponedMatch(PredictionMarket market)
      throws HttpException, InterruptedException {
    RestAssured.useRelaxedHTTPSValidation();
    var match = MatchGenerator.generateMatch();
    match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
    match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    Resolver.openMatchForPredictions(match);
    ResolverBase.init();

    var matchId = match.getId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = generateValidSinglePredictionFixture(market, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    updateMatchToAnotherStatus(matchId, STATUS_POSTPONED);
// TODO Need an additional check to determine why this test timed out.
    waitForGamePredictionFixtureOutcomeAndStatusToUpdate(getCurrentTestUser().getEmail(), ResultOutcome.VOID.getValue(),
        0, 10000, 550);

    ResolverBase.cleanUp(cleanUpMatchIdList);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify updating MATCH_QUIZ game to Cancel, will cancel all game predictions")
  public void updateMatchQuizToCancelVoidsItsPredictions()
      throws HttpException {

    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .build();

    var gameInstance = createGameEndpoint.createGame().as(GameInstance.class);

    var matchId = gameInstance.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameInstance.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    var predictionInstance = response.as(PredictionInstance.class);

    var updatedGameRequest = UpdateGameRequest.builder()
        .status(GameStatus.CANCELED.getValue())
        .build();

    updateGame(gameInstance.getId(), updatedGameRequest);

    //Assert Game Status in Firebase
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    //Assert Prediction Status in Firebase
    ResolverValidator.validateMatchQuizGamePredictionsWithMatchPostponed(
        PredictionsEndpoint.getOwnPredictions());

    var redisGamesResponse = RedisEndpoint.getGames();

    //Assert Game and Prediction in Redis
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(predictionInstance.getId(), List.of(matchId));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify updating TOP_X game to Cancel, will cancel all game predictions")
  public void updateTopXToCancelVoidsItsPredictions()
      throws HttpException {

    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
      match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    });

    Resolver.openMatchesForPredictions(matchList);
    init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(matchList.stream().map(Match::getId).toList(), GameType.TOP_X,
                GameStatus.OPEN, matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusMinutes(15))
            .as(GameInstance.class);

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
        .matchId(match.getId())
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(match.getGoalsFullTimeHome())
        .goalsAway(match.getGoalsFullTimeAway())
        .build()));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    var response = PredictionsEndpoint.createPrediction(createPredictionRequest);

    var predictionInstance = response.as(PredictionInstance.class);

    var updatedGameRequest = UpdateGameRequest.builder()
        .status(GameStatus.CANCELED.getValue())
        .build();

    updateGame(gameInstance.getId(), updatedGameRequest);

    //Assert Game Status in Firebase
    GameEndpoint.getGameById(gameInstance.getId())
        .then()
        .assertThat()
        .body(STATUS_PROP, equalTo(GameStatus.CANCELED.getValue()));

    //Assert Prediction Status in Firebase
    ResolverValidator.validateMatchQuizGamePredictionsWithMatchPostponed(
        PredictionsEndpoint.getOwnPredictions());

    var redisGamesResponse = RedisEndpoint.getGames();

    //Assert Game and Prediction in Redis
    RedisValidator.validateGameDoesNotExistInRedis(gameInstance.getId(), redisGamesResponse);
    RedisValidator.validateGamePredictionForMatchesNotExist(predictionInstance.getId(), matchList.stream().map(Match::getId).collect(
        Collectors.toList()));
  }
}
