package com.fansunited.automation.predictionapi.predictions.get;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MATCH_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Predictions.GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createGamePredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionForGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionsForGameWithSpecificUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createSinglePredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictions;
import static com.fansunited.automation.core.apis.predictionapi.UserPredictionsEndpoint.getPredictionsForSpecificUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.PREDICTION_COLLECTION;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGetSpecificUserPredictionsResponse;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GetPredictionsEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.fansunited.automation.validators.CacheValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - GET /v1/predictions/users/{userId} endpoint happy path tests")
public class GetPredictionsForSpecificUserTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify getting predictions(single and game) for all prediction statuses for specific user. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getPredictionsForUser(GameType gameType)
      throws HttpException, IllegalArgumentException, ExecutionException, InterruptedException,
      IOException {

    var predictionStatuses = PredictionStatus.getValidStatuses();

    var predictionsIdList = createSinglePredictions(predictionStatuses.size());
    predictionsIdList.addAll(createGamePredictions(gameType, predictionStatuses.size()));

    for (int i = 0; i < predictionsIdList.subList(0, predictionStatuses.size()).size(); i++) {
      FirebaseHelper.updateCollectionField(
          FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, PREDICTION_COLLECTION),
          predictionsIdList.get(i), STATUS_PROP,
          PredictionStatus.getValidStatuses().get(i).getValue());
    }

    var response = getPredictionsForSpecificUser(getCurrentTestUser().getUid(),
        PredictionStatus.listToCommaSeparated(predictionStatuses));

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()))
        .body("data." + STATUS_PROP,
            hasItems(predictionStatuses.stream().map(PredictionStatus::getValue).toArray()));

    Assertions.assertEquals(response.as(PredictionsData.class).getData(),
        getOwnPredictions(PredictionStatus.listToCommaSeparated(predictionStatuses)).as(
            PredictionsData.class).getData(),
        "Some of the predictions created by user {userId} are missing in GET /v1/predictions/user/{userId}");
  }

  @ParameterizedTest(name = "Verify getting predictions for specific user filtered by status. Prediction status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = PredictionStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getPredictionsForUserWithStatus(PredictionStatus predictionStatus)
      throws HttpException, ExecutionException, InterruptedException, IOException {

    var predictionStatuses = PredictionStatus.getValidStatuses();

    var predictionsIdList = createSinglePredictions(predictionStatuses.size());

    for (int i = 0; i < predictionsIdList.size(); i++) {
      FirebaseHelper.updateCollectionField(
          FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, PREDICTION_COLLECTION),
          predictionsIdList.get(i), STATUS_PROP,
          PredictionStatus.getValidStatuses().get(i).getValue());
    }

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), predictionStatus.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1))
        .body("data." + STATUS_PROP, contains(predictionStatus.getValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify all predictions for specific user are returned by the API, if 'status' query parameter is missing")
  public void getSpecificUserPredictionsWithoutStatus()
      throws HttpException, ExecutionException, InterruptedException, IOException {

    var predictionStatuses = PredictionStatus.getValidStatuses();

    var predictionsIdList = createSinglePredictions(predictionStatuses.size());

    for (int i = 0; i < predictionsIdList.size(); i++) {
      FirebaseHelper.updateCollectionField(
          FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, PREDICTION_COLLECTION),
          predictionsIdList.get(i), STATUS_PROP,
          PredictionStatus.getValidStatuses().get(i).getValue());
    }

    var response = getPredictionsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()))
        .body("data." + STATUS_PROP,
            hasItems(predictionStatuses.stream().map(PredictionStatus::getValue).toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify /GET/v1/predictions/user/{userId} response returned by the server is cached for 30min")
  @EnabledIf("isUseStageEnvironment")
  public void verifyGetSpecificUserPredictionsResponseIsCached()
      throws HttpException {

    var response = getPredictionsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting specific user predictions")
  public void getSpecificUserPredictionsWithDefaultPaginationLimitParam()
      throws HttpException {

    var response = getPredictionsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting specific user predictions")
  public void getSpecificUserPredictionsPaginationParams()
      throws HttpException {

    final var predictionsCount = Helper.generateRandomNumber(10, 15);

    final var resultsLimit = 5;

    createSinglePredictions(predictionsCount);

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    var getSpecificUserPredictions = response.as(PredictionsData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(getSpecificUserPredictions.getData()
                .get(getSpecificUserPredictions.getData().size() - 1)
                .getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting specific user predictions works")
  public void getSpecificUserPredictionsPaginationMoveToNextPage() throws HttpException {

    final var predictionsCount = Helper.generateRandomNumber(20, 40);

    final var resultsLimit = Helper.generateRandomNumber(1, 5);

    var predictionsIdList = createSinglePredictions(predictionsCount);

    var response =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid(), null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    validateGetSpecificUserPredictionsResponse(getCurrentTestUser().getUid(), response,
        predictionsIdList, resultsLimit);
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns only user predictions matching match id")
  @EnabledIf("isUseStageEnvironment")
  public void getPredictionsForUserByMatchId()
      throws HttpException, IllegalArgumentException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
        .matchesIdList(matchList.stream().map(Match::getId).collect(Collectors.toList()))
        .build()
        .createGame()
        .as(GameInstance.class);

    var predictionsCount = 1;

    var predictionsIdList = createPredictionsForGameWithSpecificUser(gameInstance.getId(), GameType.MATCH_QUIZ, getCurrentTestUser().getEmail(), predictionsCount);

    GetPredictionsEndpoint getPredictionsEndpoint = GetPredictionsEndpoint.builder()
        .matchIds(matchList.stream().map(Match::getId).toList())
        .build();

    var response = getPredictionsEndpoint.getPredictionsForSpecificUser();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()))
        .body("data[0].fixtures[0]." + MATCH_ID_PROP, equalTo(matchList.get(0).getId()));

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting game predictions for deleted user returns empty response")
  public void getPredictionsForDeletedUser()
      throws HttpException, IllegalArgumentException, IOException, FirebaseAuthException,
      InterruptedException, ExecutionException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var predictionsId = createPredictionForGame(getCurrentTestUser().getEmail(), gameId, GameType.MATCH_QUIZ);

    deleteUser(getCurrentTestUser().getUid());

    var response = getPredictionsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, equalTo(List.of(predictionsId)))
        .body("data." + STATUS_PROP,
            equalTo(List.of(PredictionStatus.ACTIVE.getValue())));
  }
}
