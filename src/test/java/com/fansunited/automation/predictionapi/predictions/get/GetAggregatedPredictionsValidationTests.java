package com.fansunited.automation.predictionapi.predictions.get;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_MATCH_ID;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsSummaryEndpoint.getAggregatedPredictionsResultsForMatch;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/predictions/summary/{matchId} endpoint validation tests")
public class GetAggregatedPredictionsValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify API cannot fetch aggregated predictions results for a match with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void getAggregatedPredictionsResultsForMatchWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        getAggregatedPredictionsResultsForMatch("fb:m:12345", CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON, false);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting aggregated predictions results for a match with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getAggregatedPredictionsResultsForMatchWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        getAggregatedPredictionsResultsForMatch("fb:m:1234", clientId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, false);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting aggregated predictions results for a match with invalid match id")
  public void getAggregatedPredictionsResultsForMatchWithInvalidMatchId()
      throws HttpException {

    var response =
        getAggregatedPredictionsResultsForMatch(INVALID_MATCH_ID, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, false);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting aggregated predictions results for a match with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getAggregatedPredictionsResultsForMatchWithNotSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        getAggregatedPredictionsResultsForMatch("fb:m:1234", CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType, false);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }
}
