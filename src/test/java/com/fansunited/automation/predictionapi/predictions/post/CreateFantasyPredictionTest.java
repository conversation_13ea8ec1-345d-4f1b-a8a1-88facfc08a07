package com.fansunited.automation.predictionapi.predictions.post;

import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.UserPredictionsEndpoint.getPredictionsForSpecificUser;
import static com.fansunited.automation.core.base.predictionapi.FantasyCleanUpData.cleanUpDataFromEventPlayer;
import static com.fansunited.automation.core.base.predictionapi.FantasyCleanUpData.cleanUpDataFromEventTimeline;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.STATUS_POSTPONED;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.core.resolver.MatchTimelineGenerator.generateMatchPlayers;
import static com.fansunited.automation.core.resolver.Resolver.updateEventPlayer;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchStats;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.GenerateMatchTimeLineSubstitution.generateTimelineInputValues;
import static com.fansunited.automation.helpers.GenerateMatchTimeLineSubstitution.setupMatchTimelineForFantasy;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.PlayerMatchStatsGenerator.generatePlayerIds;
import static com.fansunited.automation.helpers.TemplateRequestGenerator.generateTemplateRequest;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.DEFENDER;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.FORWARD;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.KEEPER;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.MIDFIELDER;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.predictionapi.FantasyCleanUpData;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.core.resolver.hibernate.PlayerMatchStats;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.FootballFantasyCalculatePoints;
import com.fansunited.automation.helpers.PlayerMatchStatsGenerator;
import com.fansunited.automation.helpers.PredictionFantasyRequestGenerator;
import com.fansunited.automation.model.clientapi.features.response.Coefficients;
import com.fansunited.automation.model.clientapi.features.response.FootballFantasyFeature;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@Execution(ExecutionMode.SAME_THREAD)
public class CreateFantasyPredictionTest extends PredictionApiBaseTest {
  private static final int MINUTE_PLAYED = generateRandomNumber(0, 90);

  @Test
  @DisplayName(value = "Fantasy prediction")
  // We need to investigate what exactly happens with the test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED)})
  public void verifyCustomTemplateCreationWithMarkets() throws HttpException, InterruptedException {
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var match = generateMatch();
    match.setKickoffAt(dateTriplet.getMidLocalDateTime());
    var playerIds = generatePlayerIds(match);
    var timelineInputValues = generateTimelineInputValues(playerIds);

    setupMatchTimelineForFantasy(match, timelineInputValues);

    Resolver.openMatchForPredictions(match);

    var matchPlayersMap = generateMatchPlayers(match, playerIds);
    match.setPlayers(new ArrayList<>(matchPlayersMap.values()));

    var templateRequest = generateTemplateRequest(dateTriplet);
    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);
    var request =
        PredictionFantasyRequestGenerator.getFantasyPredictionRequest(
            templateResponse,
            playerIds.get(0),
            playerIds.get(1),
            playerIds.get(2),
            playerIds.get(3),
            playerIds.get(3),
            playerIds.get(2));

    var predictionResponse = createPrediction(request);
    predictionResponse.then().assertThat().statusCode(200);
    currentTestResponse.set(predictionResponse);

    List<PlayerMatchStats> playerMatchStatsList = new ArrayList<>();

    PlayerMatchStats playerMatchStats;
    for (String id : playerIds) {
      playerMatchStats =
          PlayerMatchStatsGenerator.generateRandomPlayerMatchStats(id, match.getId());

      playerMatchStatsList.add(playerMatchStats);
      updateMatchStats(playerMatchStats);
    }

    updateEventPlayer(matchPlayersMap);
    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    // Wait for resolver to resolve.
    // Sometimes it needs 2 minutes to resolve this prediction (probably some cache).
    for (int i = 0; i < 30; i++) {
      Resolver.resolve();
      var pts =
          getPredictionsForSpecificUser(getCurrentTestUser().getUid())
              .then()
              .extract()
              .body()
              .jsonPath()
              .getInt("data[0].points");
      if (pts > 0) {
        break;
      }
      Thread.sleep(10000);
    }

    int actualTotalPoints =
        getPredictionsForSpecificUser(getCurrentTestUser().getUid())
            .then()
            .extract()
            .body()
            .jsonPath()
            .getInt("data[0].points");

    // We get playermatchstats from football api. Otherwise, we should calculate them here, but it
    // is a lot of code.
    var matchFromFootballApi = MatchByIdEndpoint.getMatchDtoById(match.getId());
    var playerStatsFromFootballApi = matchFromFootballApi.getPlayer_stats();

    int expectedTotalPoints = getExpectedTotalPoints(playerStatsFromFootballApi, getCoefficients());

    Assertions.assertEquals(expectedTotalPoints, actualTotalPoints);
    // DELETE rows from the 'player_match_stats', 'event_timeline', 'event_player' and 'event'
    // tables.
    cleanupAfterTest(match, playerMatchStatsList);
  }

  @Test
  @DisplayName(value = "Fantasy prediction for postponed matches")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyCanPredictForPostponedMatch() throws HttpException, InterruptedException {
    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var match = generateMatch();
    match.setKickoffAt(dateTriplet.getMidLocalDateTime());
    var playerIds = generatePlayerIds(match);
    var timelineInputValues = generateTimelineInputValues(playerIds);

    setupMatchTimelineForFantasy(match, timelineInputValues);

    init();
    cleanUpMatchIdList.add(match.getId());
    Resolver.openMatchForPredictions(match);

    var matchPlayersMap = generateMatchPlayers(match, playerIds);
    match.setPlayers(new ArrayList<>(matchPlayersMap.values()));

    var templateRequest = generateTemplateRequest(dateTriplet);
    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);
    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);
    var request =
        PredictionFantasyRequestGenerator.getFantasyPredictionRequest(
            templateResponse,
            playerIds.get(0),
            playerIds.get(1),
            playerIds.get(2),
            playerIds.get(3),
            playerIds.get(3),
            playerIds.get(2));

    var predictionResponse = createPrediction(request);
    predictionResponse.then().assertThat().statusCode(200);
    currentTestResponse.set(predictionResponse);

    List<PlayerMatchStats> playerMatchStatsList = new ArrayList<>();

    PlayerMatchStats playerMatchStats;
    for (String id : playerIds) {
      playerMatchStats =
          PlayerMatchStatsGenerator.generateRandomPlayerMatchStats(id, match.getId());
      playerMatchStats.setMinutesPlayed(MINUTE_PLAYED);
      playerMatchStatsList.add(playerMatchStats);
      updateMatchStats(playerMatchStats);
    }

    updateEventPlayer(matchPlayersMap);
    Resolver.updateMatchToAnotherStatus(match.getId(), STATUS_POSTPONED);
    Resolver.resolve();

    getPredictionsForSpecificUser(getCurrentTestUser().getUid())
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].points", equalTo(0));
    cleanupAfterTest(match, playerMatchStatsList);
  }

  private static int getExpectedTotalPoints(
      List<PlayerMatchStats> playerMatchStatsList, Coefficients coefficients) {
    int keeperPoints =
        new FootballFantasyCalculatePoints()
            .calculatePoints(playerMatchStatsList.get(0), coefficients, KEEPER);
    int defenderPoints =
        new FootballFantasyCalculatePoints()
            .calculatePoints(playerMatchStatsList.get(1), coefficients, DEFENDER);
    int forwardPoints =
        new FootballFantasyCalculatePoints()
            .calculatePoints(playerMatchStatsList.get(2), coefficients, FORWARD);
    int midfielderPoints =
        new FootballFantasyCalculatePoints()
            .calculatePoints(playerMatchStatsList.get(3), coefficients, MIDFIELDER);
    int captainPoints =
        new FootballFantasyCalculatePoints()
            .calculatePoints(playerMatchStatsList.get(2), coefficients, FORWARD);
    int viceCaptainPoints =
        new FootballFantasyCalculatePoints()
            .calculatePoints(playerMatchStatsList.get(3), coefficients, MIDFIELDER);
    return keeperPoints
        + defenderPoints
        + midfielderPoints
        + forwardPoints
        + (captainPoints*3)
        + (viceCaptainPoints*2);
  }

  private void cleanupAfterTest(Match match, List<PlayerMatchStats> playerMatchStatsList) {
    playerMatchStatsList.forEach(FantasyCleanUpData::cleanUpDataFromPlayerMatchStats);
    cleanUpDataFromEventPlayer(match.getId());
    cleanUpDataFromEventTimeline(match.getId());
    ResolverBase.cleanUp();
  }

  private Coefficients getCoefficients() throws HttpException {
    return ClientFeaturesEndpoint.getFootballFantasyFeaturesByClientId(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS)
        .then()
        .extract()
        .body()
        .jsonPath()
        .getObject("data", FootballFantasyFeature.class)
        .getCoefficients();
  }
}
