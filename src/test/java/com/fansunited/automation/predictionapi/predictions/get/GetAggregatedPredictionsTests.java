package com.fansunited.automation.predictionapi.predictions.get;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MATCH_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MATCH_TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.PREDICTIONS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Predictions.GET_AGGREGATED_PREDICTIONS_SUMMARY_RESULTS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getFullCoverageCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createSinglePrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsSummaryEndpoint.getAggregatedPredictionsResultsForMatch;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasKey;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.validators.CacheValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;

@DisplayName("Prediction Api - GET /v1/predictions/summary/{matchId} endpoint happy path tests")
public class GetAggregatedPredictionsTests extends PredictionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting aggregated predictions results for a match")
  public void getAggregatedPredictionsResults()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      createSinglePrediction(matchId, playerId, market);
    }

    var response = getAggregatedPredictionsResultsForMatch(matchId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_AGGREGATED_PREDICTIONS_SUMMARY_RESULTS_SCHEMA))
        .body(MATCH_ID_PROP, equalTo(matchId))
        .body(MATCH_TYPE_PROP, equalTo(MatchType.FOOTBALL.getValue()));

    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      response
          .then()
          .body(PREDICTIONS_PROP + "." + market.getValue(), not(empty()));
    }

    for (PredictionMarket market : PredictionMarket.getPlayerMarkets()) {
      response
          .then()
          .body(PREDICTIONS_PROP + "." + market.getValue(), hasKey(playerId));

      var playerMap = (Map) response.jsonPath()
          .getMap(PREDICTIONS_PROP + "." + market.getValue())
          .get(playerId);

      Assertions.assertTrue((Integer) playerMap.get("yes") >= 0,
          market + " market for player: " + playerId + " has no predictions!");
      Assertions.assertTrue((Integer) playerMap.get("no") >= 0,
          market + " market for player: " + playerId + " has no predictions!");
    }
  }
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify /GET/v1/predictions/summary/{matchId} response returned by the server is cached for 120 min")
  @EnabledIf("isUseStageEnvironment")
  public void verifyGetAggregatedPredictionsResultsForMatchIsCached()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var response = getAggregatedPredictionsResultsForMatch(matchId, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, true);

    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.THIRTY_MINUTES);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting aggregated predictions results for a match incl Deleted User's predictions")
  public void getAggregatedPredictionsResultsIncludingDeletedUser()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var user = createUser();

    var match = generateMatch();

    var matchId = match.getId();

    init();
    Resolver.openMatchForPredictions(match);
    cleanUpMatchIdList.add(match.getId());

    var playerId = VALID_PLAYER_ID;

    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      createSinglePrediction(matchId, playerId, market);
    }

    var numOfPredictions = Integer.parseInt(getAggregatedPredictionsResultsForMatch(matchId).body()
        .jsonPath()
        .get("predictions.BOTH_TEAMS_SCORE.yes")
        .toString());

    Assertions.assertEquals(1, numOfPredictions);

    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      createPrediction(
          CreatePredictionRequest.builder()
              .fixtures(generateValidSinglePredictionFixture(market, matchId, playerId))
              .build(),
          null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, user.getEmail(),
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
    }

    deleteUser(user.getUid());

    var response = getAggregatedPredictionsResultsForMatch(matchId);

    numOfPredictions = Integer.parseInt(
        getAggregatedPredictionsResultsForMatch(matchId).body()
            .jsonPath()
            .get("predictions.BOTH_TEAMS_SCORE.yes").toString());

    Assertions.assertEquals(2, numOfPredictions);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_AGGREGATED_PREDICTIONS_SUMMARY_RESULTS_SCHEMA))
        .body(MATCH_ID_PROP, equalTo(matchId))
        .body(MATCH_TYPE_PROP, equalTo(MatchType.FOOTBALL.getValue()));

    ResolverBase.cleanUp();
  }
}
