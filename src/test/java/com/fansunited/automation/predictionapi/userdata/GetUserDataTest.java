package com.fansunited.automation.predictionapi.userdata;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getFullCoverageCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.validators.PredictionApiValidator.validateCreatePredictionResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.UserData;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.RedisValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - GET /v1/games/{gameId}/results endpoint happy path tests")
public class GetUserDataTest extends PredictionApiBaseTest {

  @ParameterizedTest
  @DisplayName("Verify game results are successfully fetched and valid. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getResultsForGameType(PredictionMarket market)
      throws HttpException, IllegalArgumentException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        Helper.generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = generateValidSinglePredictionFixture(market, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest, null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createSinglePredictionRequest, true, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    var UserDataResponse =
        UserData.getUserData(CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, 10, predictionInstance.getId());

    currentTestResponse.set(UserDataResponse);

    UserDataResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].prediction_id", equalTo(predictionInstance.getId()))
        .body("data[0].device", is(nullValue()));

    CacheValidator.validateCacheExpirationDate(UserDataResponse, CacheValidator.CachePeriod.ONE_DAY);
  }
}
