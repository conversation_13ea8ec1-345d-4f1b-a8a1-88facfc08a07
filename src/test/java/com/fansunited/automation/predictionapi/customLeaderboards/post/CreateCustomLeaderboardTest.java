package com.fansunited.automation.predictionapi.customLeaderboards.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.createCustomLeaderboard;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.createCustomLeaderboardRequest;
import static com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup.createStandingGame;
import static com.fansunited.automation.helpers.BracketGameHelper.createBracketGame;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.OPEN;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.BRACKET;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.STANDING;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;

public class CreateCustomLeaderboardTest extends BaseTest {



  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify standing game is successfully created.")
  public void creatfffffffffeLeaderboard() throws HttpException, IllegalArgumentException {


    var game = createStandingGame(OPEN, 1, 10);
    var game1= createBracketGame();
    var gameId = game.getId();
    var gameId1=game1.getId();
    
    
    var response =createCustomLeaderboard(createCustomLeaderboardRequest(gameId,STANDING,gameId1,BRACKET,LIVE),
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        null,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);
  }

}
