package com.fansunited.automation.predictionapi.customLeaderboards.get;

import static com.fansunited.automation.constants.Endpoints.CustomResolverApi.RESOLVER_BRACKET;
import static com.fansunited.automation.constants.Endpoints.CustomResolverApi.STANDING_RESOLVER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.createLeaderboard;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.getCustomLeaderboardRanking;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.participateBracketGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionBracketsEndpoint.updateBracketGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup.createStandingGame;
import static com.fansunited.automation.core.resolver.CustomResolver.customGameResolve;
import static com.fansunited.automation.helpers.BracketGameHelper.createBracketGame;
import static com.fansunited.automation.helpers.BracketGameHelper.updateRequest;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.OPEN;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.BRACKET;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.STANDING;
import static org.apache.http.HttpStatus.SC_OK;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.bracket.CustomBracketFixture;
import com.fansunited.automation.model.predictionapi.bracket.request.CustomBracketPrediction;
import com.fansunited.automation.model.predictionapi.standing.request.CustomStandingPredictionRequest;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class GetCustomLeaderboardRankTest extends AuthBase {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create custom leaderboard happy path")
  public void createLeaderboardHappyPath()
      throws HttpException, IllegalArgumentException, InterruptedException, IOException,
      ExecutionException, FirebaseAuthException {

    var game = createStandingGame(OPEN, 1, 20);
    var game1 = createBracketGame();
    var gameId = game.getId();
    var gameId1 = game1.getId();
    var leaderboard = createLeaderboard(gameId, STANDING, gameId1, BRACKET, LIVE);
    var leaderboardId = leaderboard.getId();

    var participant_id = game.getMeta().getParticipants().get(1).getId();
    var participate =
        CustomStandingPredictionRequest.builder().standing(List.of(participant_id)).build();

    var participateStandingResponse =
        PredictionStandingBaseSetup.participateStandingGame(
            participate,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            gameId,
            createUsers(1).get(0).getEmail(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    participateStandingResponse.then().assertThat().log().all().statusCode(SC_OK);

    PredictionStandingBaseSetup.updateStandingGameStandingGame(OPEN, 1, 20, participant_id, gameId);
    // Resolve to won
    customGameResolve(STANDING_RESOLVER, gameId);

    var predictionRequest =
        CustomBracketPrediction.builder()
            .fixtures(
                List.of(
                    CustomBracketFixture.builder()
                        .matchId(game1.getFixtures().get(0).getMatchId())
                        .participantTwo(game1.getFixtures().get(0).getParticipantTwo())
                        .participantOne(game1.getFixtures().get(0).getParticipantOne())
                        .winner("participant_two")
                        .build()))
            .build();

    var participateBracketResponse =
        participateBracketGame(
            gameId1,
            predictionRequest,
            FANS_UNITED_PROFILE,
            createUsers(1).get(0).getEmail(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    participateBracketResponse.then().assertThat().statusCode(SC_OK);

    var updateResponse =
        updateBracketGame(
            gameId1,
            updateRequest(OPEN, 20, "participant_two"),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    updateResponse.then().assertThat().log().all().statusCode(SC_OK);

    customGameResolve(RESOLVER_BRACKET, gameId1);

    var customLeaderboard =
        getCustomLeaderboardRanking(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            leaderboardId);

    customLeaderboard.then().assertThat().log().all().statusCode(SC_OK);
  }
}
