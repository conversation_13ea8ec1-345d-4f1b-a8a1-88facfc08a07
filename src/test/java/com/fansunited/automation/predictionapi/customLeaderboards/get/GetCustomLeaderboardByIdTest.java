package com.fansunited.automation.predictionapi.customLeaderboards.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.createSimpleLeaderboard;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.getCustomLeaderboard;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class GetCustomLeaderboardByIdTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get custom leaderboard by ID happy path")
  public void createLeaderboardHappyPath()
      throws HttpException, IllegalArgumentException {

    var leaderboard = createSimpleLeaderboard(LIVE);
    var leaderboardId = leaderboard.getId();
    var customLeaderboard =
        getCustomLeaderboard(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            leaderboardId);

    customLeaderboard.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);
  }
}
