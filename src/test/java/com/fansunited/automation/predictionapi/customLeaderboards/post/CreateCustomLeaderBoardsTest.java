package com.fansunited.automation.predictionapi.customLeaderboards.post;

import static com.fansunited.automation.constants.Endpoints.CustomResolverApi.STANDING_RESOLVER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.createLeaderboard;
import static com.fansunited.automation.core.apis.predictionapi.CustomLeaderboardBaseSetup.getCustomLeaderboard;
import static com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup.createStandingGame;
import static com.fansunited.automation.core.resolver.CustomResolver.customGameResolve;
import static com.fansunited.automation.helpers.BracketGameHelper.createBracketGame;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.LIVE;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.OPEN;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.BRACKET;
import static com.fansunited.automation.model.predictionapi.games.enums.GameType.CustomGameType.STANDING;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.predictionapi.PredictionStandingBaseSetup;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.standing.request.CustomStandingPredictionRequest;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class CreateCustomLeaderBoardsTest extends AuthBase {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create custom leaderboard happy path")
  public void createLeaderboardHappyPath()
      throws HttpException, IllegalArgumentException, InterruptedException {
    var game = createStandingGame(OPEN, 1, 10);
    var game1 = createBracketGame();
    var gameId = game.getId();
    var gameId1 = game1.getId();
    var participant_id = game.getMeta().getParticipants().get(0).getId();
    var leaderboard = createLeaderboard(gameId, STANDING, gameId1, BRACKET, LIVE);
    var leaderboardId = leaderboard.getId();

    var participate =
        CustomStandingPredictionRequest.builder().standing(List.of(participant_id)).build();

    var response =
        PredictionStandingBaseSetup.participateStandingGame(
            participate,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            gameId,
            getCurrentTestUser().getEmail(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);

    PredictionStandingBaseSetup.updateStandingGameStandingGame(OPEN, 1, 10, participant_id, gameId);

    customGameResolve(STANDING_RESOLVER, gameId);

    var customLeaderboard =
        getCustomLeaderboard(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            leaderboardId);

    customLeaderboard.then().assertThat().log().all().statusCode(HttpStatus.SC_OK);
  }
}
