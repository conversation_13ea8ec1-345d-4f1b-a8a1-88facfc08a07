package com.fansunited.automation.discussionapi.posts.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.ReactPostEndpoint.reactUsersPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.model.discussionapi.ReactionType.getRandomReactionType;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.MadeReactionRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Create reaction happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class ReactUsersPostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Create reaction for users post")
  public void createReactionTest() throws IllegalArgumentException, HttpException {

    var reactPost = MadeReactionRequest.builder().reaction(getRandomReactionType(false)).build();

    var response =
        reactUsersPost(
            reactPost,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    response.then().assertThat().statusCode(org.apache.http.HttpStatus.SC_OK);
  }
}
