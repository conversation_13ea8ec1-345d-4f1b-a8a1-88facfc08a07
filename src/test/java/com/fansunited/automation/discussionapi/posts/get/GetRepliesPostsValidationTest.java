package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetPostsRepliesEndpoint.getRepliesForDiscussionPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Discussion Api - GET /v1/posts/{postId}/replies endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetRepliesPostsValidationTest extends DiscussionApiBaseTest {

  private String postId;

  @BeforeEach
  public void createTestData() throws HttpException {
    postId = createPostForDiscussionsTests().getData().getId();
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to get post replies with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getRepliesWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        getRepliesForDiscussionPost(
            postId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            0, null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to get post replies with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getRepliesWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getRepliesForDiscussionPost(
            postId,
            invalidClientIdHolder.clintId(),
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            0, null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting replies with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getRepliesNonSupportedContentType(ContentType contentType) throws HttpException {
    var response =
        getRepliesForDiscussionPost(
            postId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            0, null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns a BAD_REQUEST error when replies data with an out-of-range limit parameter. Limit: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-1, 0, 26})
  public void getRepliesWithOutOfRangeLimit(int limit) throws HttpException {

    var response =
        getRepliesForDiscussionPost(
            postId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            limit, null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that API returns NOT_FOUND when send request with non existing post ID")
  public void getRepliesWithInvalidPostIdTest() throws HttpException {

    var response =
        getRepliesForDiscussionPost(
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            5, null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_NOT_FOUND));
  }
}
