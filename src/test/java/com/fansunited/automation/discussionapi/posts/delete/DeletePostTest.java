package com.fansunited.automation.discussionapi.posts.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.DeletePostEndpoint.deleteDiscussionsPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - DELETE /v1/posts/{postId} endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class DeletePostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify - User can delete own post")
  public void deleteOwnPost() throws HttpException {
    String email = getCurrentTestUser().getEmail();
    String postId = PostsGenerator.createPublicPosts(2, email).get(0);
    var response =
        deleteDiscussionsPost(
            postId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            email,
            null);

    currentTestResponse.set(response);
    response.then().statusCode(HttpStatus.SC_OK);
  }
}
