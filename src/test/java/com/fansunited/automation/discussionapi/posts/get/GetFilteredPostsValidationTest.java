package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.RegexConstants.CONTAINS_WORD_INVALID;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.FilterPostsEndpoint.filterDiscussionsPosts;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.discussionapi.enums.PostSortType;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Discussion Api - GET /v1/posts/staff endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetFilteredPostsValidationTest extends DiscussionApiBaseTest {

  @ParameterizedTest(
      name =
          "Verify that staff is unable to sort posts with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void sortPostsWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        filterDiscussionsPosts(
            null,
            null,
            false,
            false,
            null,
            null,
            10,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that it is not possible to sort posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void sortPostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        filterDiscussionsPosts(
            null,
            null,
            false,
            false,
            null,
            null,
            10,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when sorting posts data with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void sortPostNonSupportedContentType(ContentType contentType) throws HttpException {

    var response =
        filterDiscussionsPosts(
            null,
            null,
            false,
            false,
            null,
            null,
            10,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify  non staff users cannot sort posts")
  public void nonStaffSortPostsTest() throws HttpException {
    var response =
        filterDiscussionsPosts(
            null,
            null,
            false,
            false,
            null,
            null,
            10,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail());

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns a BAD_REQUEST error when sorting post data with an out-of-range limit parameter. Limit: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-1, 0, 51})
  public void sortPostWithOutOfRangeLimit(int limit) throws HttpException {

    var response =
        filterDiscussionsPosts(
            null,
            null,
            false,
            false,
            null,
            null,
            limit,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns BAD_REQUEST when an invalid sort field is provided. Field: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(
      value = PostSortType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = CONTAINS_WORD_INVALID)
  public void sortPostsWithInvalidSortField(PostSortType field) throws HttpException {
    var response =
        filterDiscussionsPosts(
            null,
            null,
            false,
            false,
            field,
            null,
            10,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns BAD_REQUEST when an invalid type is provided. Field: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"  ", "InvalidType"})
  @EmptySource
  public void sortPostsWithInvalidTypeField(String field) throws HttpException {
    var response =
        filterDiscussionsPosts(
            null,
            field,
            false,
            false,
            null,
            null,
            10,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }
}
