package com.fansunited.automation.discussionapi.posts.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.UpdateUsersPostsEndpoint.updateUserPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.UpdatePostRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Update users post for discussion happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateUserPostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Update users post")
  public void updateUsersPostTest() throws IllegalArgumentException, HttpException {

    var updatePost = UpdatePostRequest.builder().content(new Faker().internet().uuid()).build();

    var response =
        updateUserPost(
            updatePost,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    response.then().assertThat().statusCode(org.apache.http.HttpStatus.SC_OK);
  }
}
