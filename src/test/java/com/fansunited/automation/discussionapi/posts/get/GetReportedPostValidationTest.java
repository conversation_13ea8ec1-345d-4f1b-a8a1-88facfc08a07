package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetReportedPostsEndpoint.getReportedPosts;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.discussionapi.InvalidStartAfterParamArguments;
import com.fansunited.automation.arguments.discussionapi.InvalidStartAfterParamHolder;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Discussion Api - GET /v1/posts/staff/reported endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetReportedPostValidationTest extends DiscussionApiBaseTest {
  @ParameterizedTest(
      name =
          "Verify staff is unable to get reported posts with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getReportedPostsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getReportedPosts(
            1,
            null,
            1,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot get reported posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void getReportedPostsWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        getReportedPosts(
            1,
            null,
            1,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            argumentsHolder.jwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to get reported posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getReportedPostsWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        getReportedPosts(
            1,
            null,
            1,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST, SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting reported posts with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getReportedPostsNonSupportedContentType(ContentType contentType)
      throws HttpException {
    var response =
        getReportedPosts(
            1,
            null,
            1,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_UNSUPPORTED_MEDIA_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns a BAD_REQUEST error when getting reported post data with an out-of-range limit parameter. Limit: {arguments}")
  @Tag(SMOKE)
  @ValueSource(ints = {-1, 0, 51})
  public void getReportedPostWithOutOfRangeLimit(int limit) throws HttpException {

    var response =
        getReportedPosts(
            1,
            null,
            limit,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns a BAD_REQUEST error when getting reported post data with an out-of-range reports_count parameter. Reported Count: {arguments}")
  @Tag(SMOKE)
  @ValueSource(ints = {-1, 26})
  public void getReportedPostWithOutOfRangeReportsCount(int reportsCount) throws HttpException {

    var response =
        getReportedPosts(
            reportsCount,
            null,
            1,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot get reported posts with an invalid start_after param. start_after: {arguments}")
  @Tag(SMOKE)
  @ArgumentsSource(InvalidStartAfterParamArguments.class)
  public void getReportedPostsWithInvalidStartAfterTest(
      InvalidStartAfterParamHolder argumentsHolder) throws HttpException {
    var response =
        getReportedPosts(
            0,
            argumentsHolder.startAfter(),
            1,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }
}
