package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsEndpoint.getDiscussionsPosts;
import static com.fansunited.automation.core.apis.discussionapi.GetPostByPostIdEndpoint.getSinglePost;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.PostsGenerator.createPublicPosts;
import static com.fansunited.automation.helpers.PostsGenerator.moderatePosts;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - GET /v1/posts/{postId} endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetDiscussionPostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get single post by id")
  public void getDiscussionsPostByIdTest() throws IllegalArgumentException, HttpException {
    String postId = createPostForDiscussionsTests().getData().getId();

    var response = getSinglePost(postId);
    currentTestResponse.set(response);

    response
        .then()
        .statusCode(SC_OK)
        .assertThat()
        .body("data.user_id", equalTo(getCurrentTestUser().getUid()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Successfully retrieves all non-moderated posts from discussion")
  public void getPostsListWithSkipModeratedTest() throws IllegalArgumentException, HttpException {
    int postCount = generateRandomNumber(4, 8);
    var postsIds = createPublicPosts(postCount, getCurrentTestUser().getEmail());
    var moderatedPosts = postsIds.subList(0, 2);

    postsIds = postsIds.subList(2, postsIds.size());
    moderatePosts(moderatedPosts);
    var discussionID = getSinglePost(postsIds.get(2)).jsonPath().getString("data.discussion_id");
    var response = getDiscussionsPosts(discussionID);

    response
        .then()
        .statusCode(SC_OK)
        .assertThat()
        .body("data.user_id", everyItem(equalTo(getCurrentTestUser().getUid())))
        .body("data.discussion_id", everyItem(equalTo(discussionID)))
        .body("data.id", containsInAnyOrder(postsIds.toArray()));
  }
}
