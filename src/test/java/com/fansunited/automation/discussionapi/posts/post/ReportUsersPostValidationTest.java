package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.ReportPostEndpoint.reportPosResponse;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.model.discussionapi.PostReportReason.INVALID_REPORT_TYPE;
import static com.fansunited.automation.model.discussionapi.PostReportReason.OTHER;
import static com.fansunited.automation.model.discussionapi.PostReportReason.SPAM;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.ReportPostRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Discussion Api - POST /v1/posts/{post_id}/report endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class ReportUsersPostValidationTest extends DiscussionApiBaseTest {

  private final ReportPostRequest request =
      ReportPostRequest.builder()
          .reason(SPAM)
          .reasonDetails(new Faker().lorem().sentence(2))
          .build();

  @ParameterizedTest(
      name =
          "Verify user is unable to report posts with an invalid/missing API key. API key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void reportPostWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        reportPosResponse(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot report posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void reportPostWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        reportPosResponse(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            null,
            argumentsHolder.jwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to report posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(value = InvalidClientIdArguments.class)
  public void reportPostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        reportPosResponse(
            request,
            createPostForDiscussionsTests().getData().getId(),
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify user is unable to report posts with an invalid reason.")
  public void reportPostWithInvalidReason() throws HttpException {
    request.setReason(INVALID_REPORT_TYPE);

    var response =
        reportPosResponse(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName(
      "Verify that the user is unable to report posts when the reason is set to 'OTHER' and the reason details are missing.")
  public void reportPostWithNullReasonDetails() throws HttpException {
    request.setReason(OTHER);
    request.setReasonDetails(null);

    var response =
        reportPosResponse(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that user cannot репорт post with invalid/missing post id")
  public void creatPostWithoutContentTest() throws HttpException {
    var response =
        reportPosResponse(
            request,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_NOT_FOUND));
  }
}
