package com.fansunited.automation.discussionapi.posts.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.pinnedposts.DeletePinnedPostEndpoint.deletePinnedPostsForDiscussion;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.api.client.http.HttpMethods;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName(
    "Discussion Api - DELETE /v1/discussions/{discussion_id}/pins  endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class DeletePinsPostsFromDiscussionValidationTest extends DiscussionApiBaseTest {

  private List<String> postIds;

  private String discussionId;

  @BeforeEach
  public void setTestData() throws HttpException {
    discussionId = createDiscussionForTests(false).getData().getId();
    postIds = PostsGenerator.createPublicPosts(3, getCurrentTestUser().getEmail());
  }

  @ParameterizedTest(
      name =
          "Verify that staff is unable to delete pinned post with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void deletePinsPostsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var response =
        deletePinnedPostsForDiscussion(
            postIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null,
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify that pinned post cannot be deleted with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void deletePinsPostsWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder) {
    var response =
        deletePinnedPostsForDiscussion(
            postIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            argumentsHolder.jwtToken(),
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.statusCode(), HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify that it is not possible to delete pinned posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void deletePinsPostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder) {
    var response =
        deletePinnedPostsForDiscussion(
            postIds,
            discussionId,
            invalidClientIdHolder.clintId(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting discussion data with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void deletePinsPostsNonSupportedContentType(ContentType contentType) {

    var response =
        deletePinnedPostsForDiscussion(
            postIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            null,
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that users cannot delete pinned posts")
  public void userCannotDeletePinnedPostsTest()
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException {

    var response =
        deletePinnedPostsForDiscussion(
            postIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            createUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that staff cannot delete pinned posts with non-existing discussion ID")
  public void deletePinnedPostsWithInvalidDiscussionIdTest() {

    var staffMemberEmail = createStaffUser().jsonPath().get("data.email").toString();

    var response =
        deletePinnedPostsForDiscussion(
            postIds,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that staff cannot delete pinned posts with empty list of posts ID")
  public void deleteEmptyListOfPinnedPosts() {

    var response =
        deletePinnedPostsForDiscussion(
            null,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.DELETE);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }
}
