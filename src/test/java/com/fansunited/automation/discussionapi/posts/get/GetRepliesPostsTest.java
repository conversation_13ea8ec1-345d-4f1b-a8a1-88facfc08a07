package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.TOTAL_ITEMS_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.CreatePostEndpoint.createPostForDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetPostsRepliesEndpoint.getRepliesForDiscussionPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.PostsGenerator.moderatePosts;
import static com.fansunited.automation.helpers.PostsGenerator.multipleUsersReactPosts;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import com.fansunited.automation.model.discussionapi.request.CreatePostRequest;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get replies GET /v1/posts/{postId}/replies happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class GetRepliesPostsTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify replies data for users post")
  public void getRepliesTest()
      throws IllegalArgumentException,
          HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    var email = getCurrentTestUser().getEmail();
    var discussionId = createDiscussionForTests(false).getData().getId();
    var createPost = CreatePostRequest.builder().content(new Faker().internet().uuid()).build();

    var response =
        createPostForDiscussion(
            createPost,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    response.then().assertThat().statusCode(SC_OK);
    var postId = response.getBody().jsonPath().get("data.id").toString();

    var replyRequest =
        CreatePostRequest.builder().content(new Faker().internet().uuid()).replyId(postId).build();

    var replyId =
        createPostForDiscussion(
                replyRequest,
                discussionId,
                CLIENT_AUTOMATION_ID,
                FANS_UNITED_PROFILE,
                email,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON,
                null)
            .jsonPath()
            .getString("data.id");

    int countOfReactions = generateRandomNumber(3, 8);
    multipleUsersReactPosts(replyId, null, countOfReactions, false);

    var getRepliesResponse =
        getRepliesForDiscussionPost(
            postId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            10,
            null);

    getRepliesResponse
        .then()
        .assertThat()
        .statusCode(SC_OK)
        .rootPath("data[0]")
        .body("reactions.reaction_count.sum()", equalTo(countOfReactions))
        .body("reactions_count", is(equalTo(countOfReactions)))
        .body("discussion_id", is(equalTo(discussionId)))
        .body("user_id", is(equalTo(getCurrentTestUser().getUid())))
        .rootPath("meta")
        .body("pagination." + TOTAL_ITEMS_PROP, is(equalTo(1)));
  }

  @Disabled("WIP FZ-4007")
  @Test
  @Tags({@Tag(REGRESSION), @Tag("FZ-4007")})
  @DisplayName("Get replies with skipModerated parameter")
  public void getRepliesWithSkipModeratedTest() throws HttpException {
    // Create a discussion and add a post
    var discussionId =
        createDiscussion(new CreateDiscussionRequest()).jsonPath().get("data.id").toString();
    var email = getCurrentTestUser().getEmail();

    // Create a main post
    var createPost = CreatePostRequest.builder().content("Main post content").build();
    var postResponse =
        createPostForDiscussion(
            createPost,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    var postId = postResponse.jsonPath().get("data.id").toString();
    List<String> replayIds = new ArrayList<>();

    // Create 4 reply posts
    for (int i = 0; i < 4; i++) {
      var replyRequest =
          CreatePostRequest.builder().content("Reply content " + i).replyId(postId).build();

      replayIds.add(
          createPostForDiscussion(
                  replyRequest,
                  discussionId,
                  CLIENT_AUTOMATION_ID,
                  FANS_UNITED_PROFILE,
                  email,
                  AuthConstants.ENDPOINTS_API_KEY,
                  ContentType.JSON,
                  null)
              .jsonPath()
              .getString("data.id"));
    }

    // Moderate 2 of the replies
    moderatePosts(replayIds.subList(0, 2));

    // First, get replies without skipModerated parameter
    var responseWithoutSkip =
        getRepliesForDiscussionPost(
            postId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            10,
            null);

    responseWithoutSkip
        .then()
        .assertThat()
        .statusCode(SC_OK)
        .body("data.size()", equalTo(2))
        .body("data.findAll { it.moderated == true }.size()", equalTo(0))
        .body("meta.pagination.total_items", equalTo(replayIds.size() - 2));

    // Then get replies with skipModerated=false
    var responseWithSkip =
        getRepliesForDiscussionPost(
            postId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            10,
            false);

    responseWithSkip
        .then()
        .assertThat()
        .statusCode(SC_OK)
        .body("data.size()", equalTo(4))
        .body("data.findAll { it.moderated == true }.size()", equalTo(2))
        .body("meta.pagination.total_items", equalTo(replayIds.size()));
  }
}
