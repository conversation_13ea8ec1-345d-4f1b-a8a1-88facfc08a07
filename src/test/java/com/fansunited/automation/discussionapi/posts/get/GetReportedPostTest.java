package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetReportedPostsEndpoint.getReportedPosts;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.PostsGenerator.multipleUsersReportPosts;
import static com.fansunited.automation.validators.PostValidator.validateAllPostsAreReported;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.PostsGenerator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Get reported POSTS - GET /v1/posts/staff/reported endpoint Get reported posts happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetReportedPostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all reported posts")
  public void getAllPostsAfterPostIdTest()
      throws IllegalArgumentException,
          HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    var email = getCurrentTestUser().getEmail();
    var postIds = PostsGenerator.createPublicPosts(generateRandomNumber(2, 4), email);

    for (String postId : postIds) {
      multipleUsersReportPosts(postId, null, generateRandomNumber(1, 4));
    }

    var getAllReportedPostsStartAfterResponse =
        getReportedPosts(
            0,
            null,
            50,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null,
            null);

    currentTestResponse.set(getAllReportedPostsStartAfterResponse);
    validateAllPostsAreReported(getAllReportedPostsStartAfterResponse);
  }
}
