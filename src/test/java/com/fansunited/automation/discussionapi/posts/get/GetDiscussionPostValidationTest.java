package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetPostByPostIdEndpoint.getSinglePost;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Discussion Api - GET /v1/posts/{postId} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetDiscussionPostValidationTest extends DiscussionApiBaseTest {

  private String postId;

  @BeforeEach
  public void setTestData() throws HttpException {
    postId = createPostForDiscussionsTests().getData().getId();
  }

  @ParameterizedTest(
      name =
          "Verify that user is unable to get post with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getPostsWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        getSinglePost(
            postId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that it is not possible to get post with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getPostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        getSinglePost(
            postId,
            invalidClientIdHolder.clintId(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting post data with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getPostNonSupportedContentType(ContentType contentType) throws HttpException {

    var response =
        getSinglePost(
            postId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that the API returns NOT_FOUND when attempting to retrieve a non-existing post.")
  public void getNonExistingPostTest() throws HttpException {
    var response =
        getSinglePost(
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_NOT_FOUND));
  }
}
