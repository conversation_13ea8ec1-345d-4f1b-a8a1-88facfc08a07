package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.ModeratePostEndpoint.moderatePost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.PostsGenerator.addPostToPrivateDiscussion;
import static com.fansunited.automation.helpers.PostsGenerator.createPrivateLeague;
import static com.fansunited.automation.helpers.PostsGenerator.getDiscussionPostsIds;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.ModeratePostRequest;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - POST /v1/posts/{post_id}/admin/moderate endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class ModeratePostByAdminTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Moderate a post in private league by league admin")
  public void moderatePostAdminTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    var moderatePostRequest =
        ModeratePostRequest.builder().moderationReason(new Faker().lorem().sentence(2)).build();
    String email = getCurrentTestUser().getEmail();
    String discussionId = createPrivateLeague(2, email);
    addPostToPrivateDiscussion(discussionId, createUser().getEmail(), 2);
    var postId = getDiscussionPostsIds(discussionId);

    var response =
        moderatePost(
            moderatePostRequest,
            postId.get(0),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);
    response.then().statusCode(HttpStatus.SC_OK).assertThat().body("data.moderated", equalTo(true));
  }
}
