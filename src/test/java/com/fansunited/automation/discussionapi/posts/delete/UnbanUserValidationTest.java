package com.fansunited.automation.discussionapi.posts.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.BanUserEndpoint.banUser;
import static com.fansunited.automation.core.apis.discussionapi.GetPostsByUserIdEndpoint.getPostByUserIdPost;
import static com.fansunited.automation.core.apis.discussionapi.UnbanUsersEndpoint.unbanUser;
import static com.fansunited.automation.core.apis.discussionapi.UnbanUsersEndpoint.usersUnban;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.core.Is.is;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.model.discussionapi.request.BanUserRequest;
import com.fansunited.automation.model.discussionapi.request.UnbanUserRequest;
import com.fansunited.automation.validators.DiscussionValidator;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Discussion Api - /v1/discussions/users/{user_id}/unban endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UnbanUserValidationTest extends DiscussionApiBaseTest {
  private Object object = UnbanUserRequest.builder().unban_reason("Only for test").build();

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify -Unban user can add a posts")
  public void unbanUserCanAddPost() throws HttpException {
    var request = new BanUserRequest(3, "Only for test");

    var response = banUser(request, getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    DiscussionValidator.validateBanUserResponse(response, request, CLIENT_AUTOMATION_USER_ID);

    PostsGenerator.createPublicPosts(1, getCurrentTestUser().getEmail());

    var getUserPostResponse =
        getPostByUserIdPost(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, null);

    getUserPostResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.items_per_page",nullValue());

    var unbanResponse = usersUnban(object, getCurrentTestUser().getUid());
    currentTestResponse.set(unbanResponse);

    unbanResponse.then().statusCode(HttpStatus.SC_NO_CONTENT);

    PostsGenerator.createPublicPosts(1, getCurrentTestUser().getEmail());

    var getUserPostAfterUnbanResponse =
        getPostByUserIdPost(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, null);

    getUserPostAfterUnbanResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id", is(notNullValue()));

  }

  @ParameterizedTest(
      name = "Verify User cannot be unban with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void unbanUserWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        unbanUser(
            object,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);
    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_UNAUTHORIZED));
  }

  @ParameterizedTest(
      name = "Verify activity cannot be unban with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void unbanUserWithInvalidJwtToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {
    var response =
        unbanUser(
            object,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            argumentsHolder.getJwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify that it is not possible to unban a user with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void unbanUserWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        unbanUser(
            object,
            INVALID_ID,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponse(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @DisplayName("Verify that non-staff users can not unable other users.")
  public void nonStaffUsersCannotUnban() throws HttpException {

    var request = new BanUserRequest(3, "Only for test");
    var createStaffResponse = createStaffUser();

    String staffEmail = createStaffResponse.jsonPath().getString("data.email");
    var banUserResponse = banUser(request, getCurrentTestUser().getUid());
    banUserResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var unBanUserResponse =
        unbanUser(
            object,
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            staffEmail,
            null);

    ErrorValidator.validateErrorResponse(unBanUserResponse, List.of(HttpStatus.SC_FORBIDDEN));
  }
}
