package com.fansunited.automation.discussionapi.posts.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.PostsGenerator.createPublicPosts;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.validators.DiscussionValidator;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Discussion Api - DELETE /v1/discussions/{discussion_id}/pins endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class DeletePinsPostsFromDiscussionTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Delete pinned posts from discussion")
  public void deletePinnedPosts() throws HttpException {
    List<String> postIds = createPublicPosts(3, getCurrentTestUser().getEmail());
    pinPostForDiscussion(postIds);

    var response = deletePostsForDiscussion(postIds);

    currentTestResponse.set(response);
    DiscussionValidator.validateDeletePinnedPosts(response, postIds);
  }
}
