package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.ApiConstants.DiscussionsApi.DISCUSSION_TYPE_PRIVATE;
import static com.fansunited.automation.constants.ApiConstants.DiscussionsApi.DISCUSSION_TYPE_PUBLIC;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.FilterPostsEndpoint.filterDiscussionsPosts;
import static com.fansunited.automation.core.apis.discussionapi.GetPostByPostIdEndpoint.getSinglePost;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.PostsGenerator.addPostToPrivateDiscussion;
import static com.fansunited.automation.helpers.PostsGenerator.createPublicPosts;
import static com.fansunited.automation.helpers.PostsGenerator.deletePosts;
import static com.fansunited.automation.helpers.PostsGenerator.moderatePosts;
import static com.fansunited.automation.helpers.PostsGenerator.multipleUsersReactPosts;
import static com.fansunited.automation.helpers.PostsGenerator.multipleUsersReportPosts;
import static com.fansunited.automation.model.discussionapi.enums.PostSortType.INTERACTED;
import static com.fansunited.automation.model.discussionapi.enums.PostSortType.LATEST;
import static com.fansunited.automation.model.discussionapi.enums.PostSortType.OLDEST;
import static com.fansunited.automation.model.discussionapi.enums.PostSortType.POPULAR;
import static com.fansunited.automation.model.discussionapi.enums.PostSortType.REPORTED;
import static com.fansunited.automation.validators.PostValidator.validatePostFilteredBySortInteracted;
import static com.fansunited.automation.validators.PostValidator.validatePostFilteredBySortPopular;
import static com.fansunited.automation.validators.PostValidator.validatePostFilteredBySortReported;
import static com.fansunited.automation.validators.PostValidator.validatePostsFilteredByDeleted;
import static com.fansunited.automation.validators.PostValidator.validatePostsFilteredByModerated;
import static com.fansunited.automation.validators.PostValidator.validatePostsFilteredBySortLatest;
import static com.fansunited.automation.validators.PostValidator.validatePostsFilteredByTypePrivate;
import static com.fansunited.automation.validators.PostValidator.validatePostsFilteredByTypePublic;
import static com.fansunited.automation.validators.PostValidator.validatePostsStartAfter;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.PostsGenerator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Get filtered POSTS - GET /v1/posts/staff endpoint Get all Posts and  filter them happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetFilteredPostsTest extends DiscussionApiBaseTest {
  private List<String> postIds;
  private String discussionId;
  private String email;
  private int postCount;

  @BeforeEach
  public void createTestData() throws HttpException {

    postCount = generateRandomNumber(5, 10);
    email = getCurrentTestUser().getEmail();
    postIds = createPublicPosts(postCount, email);
    discussionId = getSinglePost(postIds.get(0)).getBody().jsonPath().get("data.discussion_id");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all DELETED posts from discussion")
  public void getDeletedPostsTest() throws IllegalArgumentException, HttpException {
    deletePosts(postIds, email);
    var response = filterDiscussionsPosts(List.of(discussionId), true);

    currentTestResponse.set(response);
    validatePostsFilteredByDeleted(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts sorted by LATEST")
  public void getSortedPostsByLatestTest() throws HttpException {
    var response = filterDiscussionsPosts(List.of(discussionId), LATEST);
    currentTestResponse.set(response);
    validatePostsFilteredBySortLatest(response, LATEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts sorted by OLDEST")
  public void getSortedPostsByOldestTest() throws HttpException {
    var response = filterDiscussionsPosts(List.of(discussionId), OLDEST);
    currentTestResponse.set(response);
    validatePostsFilteredBySortLatest(response, OLDEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts sorted by INTERACTED")
  public void getSortedPostsByInteractedTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    for (String postId : postIds) {
      multipleUsersReactPosts(postId, null, generateRandomNumber(2, 5), true);
    }

    var response = filterDiscussionsPosts(List.of(discussionId), INTERACTED);
    currentTestResponse.set(response);
    validatePostFilteredBySortInteracted(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts sorted by REPORTED")
  public void getSortedPostsByReportedTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    for (String postId : postIds) {
      multipleUsersReportPosts(postId, null, generateRandomNumber(2, 5));
    }
    var response = filterDiscussionsPosts(List.of(discussionId), REPORTED);
    currentTestResponse.set(response);
    validatePostFilteredBySortReported(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts sorted by Popular")
  public void getSortedPostsByPopularTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    for (String postId : postIds) {
      multipleUsersReactPosts(postId, null, generateRandomNumber(2, 5), true);
    }
    var response = filterDiscussionsPosts(List.of(discussionId), POPULAR);
    currentTestResponse.set(response);
    validatePostFilteredBySortPopular(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all MODERATED posts")
  public void getModeratedPostsTest() throws IllegalArgumentException, HttpException {
    // Create and add 3 additional posts to the discussion which will not be moderated;
    addPostToPrivateDiscussion(discussionId, email, 3);
    moderatePosts(postIds);

    var getAllModeratedPostsResponse = filterDiscussionsPosts(List.of(discussionId), true, 50);

    currentTestResponse.set(getAllModeratedPostsResponse);
    validatePostsFilteredByModerated(getAllModeratedPostsResponse, postIds.size());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts from PRIVATE discussion")
  public void getAllPostsFromPrivateDiscussionsTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    String privateDiscussionId = PostsGenerator.createPrivateLeague(1, email);
    addPostToPrivateDiscussion(privateDiscussionId, email, postCount);

    var response = filterDiscussionsPosts(List.of(privateDiscussionId), DISCUSSION_TYPE_PRIVATE);
    currentTestResponse.set(response);
    validatePostsFilteredByTypePrivate(response, postCount);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts from PUBLIC discussion")
  public void getAllPostsFromPublicDiscussionsTest() throws HttpException {

    var response = filterDiscussionsPosts(List.of(discussionId), DISCUSSION_TYPE_PUBLIC);
    currentTestResponse.set(response);
    validatePostsFilteredByTypePublic(response, postCount);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all posts from discussion filtered by start after parameter")
  public void getAllPostsFilteredByStartAfter() throws HttpException {

    var startAfter = postIds.get(generateRandomNumber(2, 4));
    var response =
        filterDiscussionsPosts(
            List.of(discussionId),
            DISCUSSION_TYPE_PUBLIC,
            false,
            false,
            null,
            startAfter,
            50,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null);

    currentTestResponse.set(response);
    validatePostsStartAfter(response, startAfter);
  }
}
