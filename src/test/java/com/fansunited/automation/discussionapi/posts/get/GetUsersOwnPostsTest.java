package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.PostsGenerator.getDiscussionPostsIds;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.apis.discussionapi.GetUsersPostEndpoint;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import com.fansunited.automation.validators.PostValidator;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - GET /v1/posts endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetUsersOwnPostsTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get user's own posts.")
  public void getUsersOwnPostsTest() throws HttpException {
    int postsCount = generateRandomNumber(3, 8);
    String email = getCurrentTestUser().getEmail();
    PostsGenerator.createPublicPosts(postsCount, email);

    var response = GetUsersPostEndpoint.getUsersOwnPost(email);
    response.then().statusCode(HttpStatus.SC_OK);

    currentTestResponse.set(response);
    PostValidator.validatePostsCount(response, postsCount);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Get user's own posts with skipModerated parameter")
  public void getUsersOwnPostsWithSkipModeratedTest() throws HttpException {
    // Create a discussion and add posts
    var discussionId =
        createDiscussion(new CreateDiscussionRequest()).jsonPath().get("data.id").toString();
    var email = getCurrentTestUser().getEmail();
    PostsGenerator.addPostToPrivateDiscussion(discussionId, email, 4);

    var postIds = getDiscussionPostsIds(discussionId);

    // Moderate some posts
    PostsGenerator.moderatePosts(postIds.subList(0, 2));

    // First, get posts without skipModerated parameter
    var responseWithoutSkip = GetUsersPostEndpoint.getUsersOwnPost(email);

    responseWithoutSkip
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(2))
        .body("data.findAll { it.moderated == true }.size()", equalTo(0));

    // Then get posts with skipModerated=false
    var responseWithSkip = GetUsersPostEndpoint.getUsersOwnPost(email, false);

    // Verify that the response
    responseWithSkip
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(4))
        .body("data.findAll { it.moderated == true }.size()", equalTo(2));
  }
}
