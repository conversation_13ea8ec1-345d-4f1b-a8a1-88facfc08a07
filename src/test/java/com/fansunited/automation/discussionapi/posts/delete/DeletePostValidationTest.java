package com.fansunited.automation.discussionapi.posts.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.DeletePostEndpoint.deleteDiscussionsPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Discussion Api - DELETE /v1/posts/{postId} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class DeletePostValidationTest extends DiscussionApiBaseTest {
  private String email;
  private String postId;

  @BeforeEach
  public void setTestData() throws HttpException {
    email = getCurrentTestUser().getEmail();
    postId = PostsGenerator.createPublicPosts(1, getCurrentTestUser().getEmail()).get(0);
  }

  @ParameterizedTest(
      name =
          "Verify that user is unable to delete post with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void deletePostWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        deleteDiscussionsPost(
            postId,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            email,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_UNAUTHORIZED));
  }

  @ParameterizedTest(
      name =
          "Verify that a post cannot be deleted with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void deletePostWithInvalidJwtToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {
    var response =
        deleteDiscussionsPost(
            postId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            null,
            argumentsHolder.getJwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify that it is not possible to delete post with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void deletePostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        deleteDiscussionsPost(
            postId,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            email,
            null);

    ErrorValidator.validateErrorResponse(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @Tag(SMOKE)
  @DisplayName(
      "Verify that the API returns a NOT_FOUND when attempting to delete a non-existing post.")
  public void deletePostWithInvalidId() throws HttpException {
    var response =
        deleteDiscussionsPost(
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            email,
            null);

    ErrorValidator.validateErrorResponse(response, List.of(HttpStatus.SC_NOT_FOUND));
  }

  @Test
  @DisplayName("Verify that user cannot delete a post that belongs to another user.")
  public void userCannotDeleteExternalPost()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    String postId = PostsGenerator.createPublicPosts(1, getCurrentTestUser().getEmail()).get(0);
    var response =
        deleteDiscussionsPost(
            postId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            createUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponse(response, List.of(HttpStatus.SC_FORBIDDEN));
  }
}
