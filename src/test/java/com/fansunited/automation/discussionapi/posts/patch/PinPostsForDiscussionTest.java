package com.fansunited.automation.discussionapi.posts.patch;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.PostsGenerator.createPublicPosts;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.validators.DiscussionValidator;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Discussion Api - PATCH /v1/discussions/{discussion_id}/pins endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class PinPostsForDiscussionTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Set pinned posts for discussion")
  public void SetPostsAsPinned() throws HttpException {
    List<String> postIds = createPublicPosts(3, getCurrentTestUser().getEmail());

    var response = pinPostForDiscussion(postIds);

    currentTestResponse.set(response);
    DiscussionValidator.validatePinnedPost(response, postIds);
  }
}
