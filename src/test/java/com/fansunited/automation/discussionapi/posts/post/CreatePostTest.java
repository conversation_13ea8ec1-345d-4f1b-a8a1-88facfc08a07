package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreatePostEndpoint.createPostForDiscussion;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.CreatePostRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Create discussions post happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class CreatePostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create post for discussion works")
  public void createDiscussionsPostTest() throws IllegalArgumentException, HttpException {

    var createPost = CreatePostRequest.builder().content(new Faker().internet().uuid()).build();

    var response =
        createPostForDiscussion(
            createPost,
            createDiscussionForTests(false).getData().getId(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    response.then().assertThat().statusCode(org.apache.http.HttpStatus.SC_OK);
  }
}
