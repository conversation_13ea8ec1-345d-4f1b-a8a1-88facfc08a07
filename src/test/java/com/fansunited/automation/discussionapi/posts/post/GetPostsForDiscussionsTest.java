package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsCountViaPostOrGetRequest.getDiscussionsPostsCountViaPostRequest;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.assertj.core.api.Assertions.assertThat;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import com.fansunited.automation.model.discussionapi.request.GetDiscussionsPostsViaPostMethodRequest;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Discussion Api - POST /v1/posts/count endpoint happy path tests")
public class GetPostsForDiscussionsTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get posts by list of discussions IDs")
  public void getPostCountByDiscussionIdTest() throws HttpException {

    List<String> discussionIds = new ArrayList<>();
    for (int i = 0; i < 4; i++) {
      discussionIds.add(createDiscussion(new CreateDiscussionRequest()).jsonPath().get("data.id"));
    }

    Map<String, Integer> postsCount = new HashMap<>();
    discussionIds.forEach(
        id -> {
          try {
            var discussionsPostCount = generateRandomNumber(1, 10);
            postsCount.put(id, discussionsPostCount);
            PostsGenerator.addPostToPrivateDiscussion(
                id, getCurrentTestUser().getEmail(), discussionsPostCount);
          } catch (HttpException e) {
            throw new RuntimeException(e);
          }
        });

    GetDiscussionsPostsViaPostMethodRequest request =
        new GetDiscussionsPostsViaPostMethodRequest(discussionIds);
    var response =
        getDiscussionsPostsCountViaPostRequest(
            request,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
                getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);
    List<Map<String, Object>> discussions = response.jsonPath().getList("data");

    assertThat(discussions)
        .hasSize(postsCount.size())
        .allSatisfy(
            discussion -> {
              String id = discussion.get("id").toString();
              int actualCount = ((Integer) discussion.get("posts_count"));

              assertThat(actualCount)
                  .as("Posts count for discussion %s", id)
                  .isEqualTo(postsCount.get(id));
            });
  }
}
