package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetUsersPostEndpoint.getUsersOwnPost;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Discussion Api - GET /v1/posts endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetUsersOwnPostsValidationTest extends DiscussionApiBaseTest {

  @ParameterizedTest(
      name =
          "Verify user is unable to get own posts with an invalid/missing API key. API key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getOwnPostsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getUsersOwnPost(
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null, null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot get own posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void getOwnPostsWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        getUsersOwnPost(
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            argumentsHolder.jwtToken(), null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.statusCode(), HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to get own posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getOwnPostsWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        getUsersOwnPost(
            invalidClientIdHolder.clintId(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null, null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting own posts with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getOwnPostsNonSupportedContentType(ContentType contentType) throws HttpException {
    var response =
        getUsersOwnPost(
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            null, null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_UNSUPPORTED_MEDIA_TYPE));
  }
}
