package com.fansunited.automation.discussionapi.posts.patch;

import static com.fansunited.automation.constants.ApiErrorCodes.DiscussionErrorCodes.INVALID_CONTENT_TYPE_STATUS;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.BasePinnedPostsEndpoint.modifyPinnedPostsForDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsEndpoint.getDiscussionsPosts;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.discussionapi.InvalidDiscussionClientIdArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.api.client.http.HttpMethods;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import lombok.SneakyThrows;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName(
    "Discussion Api - PATCH /v1/discussions/{discussion_id}/pins endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class PinPostsForDiscussionValidationTest extends DiscussionApiBaseTest {
  private final List<String> pinPostIds = new ArrayList<>();
  private final String discussionId = getDiscussionId();
  private final String staffEmail = getStaffEmail();

  @ParameterizedTest(
      name =
          "Verify user is unable to pin posts with an invalid/missing API key. API key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void setPinPostIdsPostsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        modifyPinnedPostsForDiscussion(
            pinPostIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            staffEmail,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot set pinned posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void setPinPostsWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        modifyPinnedPostsForDiscussion(
            pinPostIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            argumentsHolder.jwtToken(),
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to set pinned posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidDiscussionClientIdArguments.class)
  public void setPinPostsWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        modifyPinnedPostsForDiscussion(
            pinPostIds,
            discussionId,
            invalidClientIdHolder.clintId(),
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponse(response, List.of(SC_BAD_REQUEST, SC_FORBIDDEN), invalidClientIdHolder.errorStatus());
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when setting pinned posts with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void setPinPostsNonSupportedContentType(ContentType contentType) throws HttpException {
    var response =
        modifyPinnedPostsForDiscussion(
            pinPostIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponse(response, List.of(SC_UNSUPPORTED_MEDIA_TYPE), INVALID_CONTENT_TYPE_STATUS);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that only admin can set pinned posts.")
  public void onlyAdminCanPinnedPostsTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    var response =
        modifyPinnedPostsForDiscussion(
            pinPostIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that API returns BAR REQUEST when send request with non existing discussion ID.")
  public void invalidDiscussionIdTest() throws HttpException {
    PostsGenerator.addPostToPrivateDiscussion(discussionId, staffEmail, 3);
    List<String> postIds = getDiscussionsPosts(discussionId).jsonPath().getList("data.id");

    var response =
        modifyPinnedPostsForDiscussion(
            postIds,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that API returns BAR REQUEST when send request with empty pin_post_ids parameter.")
  public void emptyPinPostIdsParamTest() throws HttpException {
    var response =
        modifyPinnedPostsForDiscussion(
            pinPostIds,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that API returns NOT FOUND when send request with wrong pin_post_ids parameter.")
  public void wrongPinPostIdsParamTest() throws HttpException {
    var response =
        modifyPinnedPostsForDiscussion(
            List.of(INVALID_ID),
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null,
            HttpMethods.PATCH);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_NOT_FOUND));
  }

  private String getDiscussionId() {
    try {
      return createDiscussionForTests(false).getData().getId();
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  @SneakyThrows
  private String getStaffEmail() {
    return createStaffUser().jsonPath().get("data.email");
  }
}
