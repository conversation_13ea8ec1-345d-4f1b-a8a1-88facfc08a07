package com.fansunited.automation.discussionapi.discussions.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.BanUserEndpoint.banUser;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.BanUserRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Discussion Api - POST /v1/discussions/users/{user_id}/ban endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class BanUserValidationTest extends DiscussionApiBaseTest {
  private final BanUserRequest request = new BanUserRequest(1, "Inappropriate behavior");

  @ParameterizedTest(
      name = "Verify User cannot be ban with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void banUserWithInvalidApyKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        banUser(
            request,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            null);
    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_UNAUTHORIZED));
  }

  @ParameterizedTest(
      name = "Verify activity cannot be created with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void banUserWithInvalidJwtToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {
    var response =
        banUser(
            request,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            argumentsHolder.getJwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify that it is not possible to ban a user with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void banUserWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        banUser(
            request,
            INVALID_ID,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponse(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @DisplayName("Verify that non-staff users are unable to ban.")
  public void nonStaffUsersCannotBan()
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {
    var response =
        banUser(
            request,
            createUser().getUid(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
           FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponse(response, List.of(HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @DisplayName("Verify that Banned reason is required")
  public void bannedIsRequired()
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {

    request.setBannedReason(null);
    var response =
        banUser(
            request,
            createUser().getUid(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            null);

    ErrorValidator.validateErrorResponse(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }
}
