package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetPublicDiscussionsEndpoint.getPublicDiscussion;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Discussion Api - GET GET /v1/discussions endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetAllPublicDiscussionsValidationTest extends DiscussionApiBaseTest {

  @ParameterizedTest(
      name =
          "Verify discussions cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getDiscussionsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getPublicDiscussion(
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);
    response.then().statusCode(argumentsHolder.getStatusCode());
  }
  
  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting discussion data with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getDiscussionsWithNonSupportedContentType(ContentType contentType)
      throws HttpException {
    var response =
        getPublicDiscussion(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);
    response
        .then()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .assertThat()
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify discussion data cannot be fetched with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getDiscussionsWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getPublicDiscussion(
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            null);
    response.then().assertThat().body("error.status", equalTo(invalidClientIdHolder.errorStatus()));
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }
}
