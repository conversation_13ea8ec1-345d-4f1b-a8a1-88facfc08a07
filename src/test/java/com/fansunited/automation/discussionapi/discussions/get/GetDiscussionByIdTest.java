package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionsEndpoint.getDiscussion;
import static com.fansunited.automation.validators.DiscussionValidator.validateDiscussionCreationResponse;
import static com.fansunited.automation.validators.DiscussionValidator.validateDiscussionResponseScCode;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get discussion by ID happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class GetDiscussionByIdTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get discussion by ID")
  public void getDiscussionTest() throws IllegalArgumentException {

    var createDiscussionRequest = new CreateDiscussionRequest();
    var createDiscussionResponse = createDiscussion(createDiscussionRequest);

    validateDiscussionCreationResponse(createDiscussionResponse, createDiscussionRequest);

    var discussionId = createDiscussionResponse.getBody().jsonPath().get("data.id").toString();
    var getDiscussionResponse = getDiscussion(discussionId);

    currentTestResponse.set(getDiscussionResponse);
    validateDiscussionResponseScCode(getDiscussionResponse);
  }
}
