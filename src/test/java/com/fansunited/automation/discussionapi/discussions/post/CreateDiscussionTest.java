package com.fansunited.automation.discussionapi.discussions.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.validators.DiscussionValidator.validateDiscussionCreationResponse;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.model.discussionapi.DiscussionType;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Discussion Api - POST /v1/discussions/ endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateDiscussionTest extends BaseTest {

  @ParameterizedTest(name = "Create discussion with type {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(
      value = DiscussionType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createDiscussionTest(DiscussionType discussionType) throws IllegalArgumentException {
    var createDiscussionRequest = new CreateDiscussionRequest();
    createDiscussionRequest.setDiscussionType(discussionType);
    var response = createDiscussion(createDiscussionRequest);

    currentTestResponse.set(response);
    validateDiscussionCreationResponse(response, createDiscussionRequest);
  }
}
