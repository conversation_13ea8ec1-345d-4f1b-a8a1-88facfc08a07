package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.INVALID_DISCUSSION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionsEndpoint.getDiscussion;
import static io.grpc.netty.shaded.io.netty.util.internal.StringUtil.EMPTY_STRING;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Discussion Api - GET /v1/discussions/{discussion_id} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetDiscussionByValidationTest extends DiscussionApiBaseTest {

  @ParameterizedTest(
      name =
          "Verify discussion cannot be fetched by ID with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getDiscussionByIdWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getDiscussion(
            EMPTY_STRING,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    response.then().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns a BAD_REQUEST when sending a request with a non-supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getDiscussionByIdWithNonSupportedContentType(ContentType contentType)
      throws HttpException {
    var response =
        getDiscussion(
            EMPTY_STRING,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);
    response
        .then()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .assertThat()
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify discussion data cannot be fetched with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getDiscussionByIdWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getDiscussion(
            EMPTY_STRING,
            invalidClientIdHolder.clintId(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response.then().assertThat().body("error.status", equalTo(invalidClientIdHolder.errorStatus()));
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns ERROR when getting discussion with invalid ID")
  public void getDiscussionWithInvalidId() throws HttpException {

    var response =
        getDiscussion(
            INVALID_DISCUSSION_ID,
            CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }
}
