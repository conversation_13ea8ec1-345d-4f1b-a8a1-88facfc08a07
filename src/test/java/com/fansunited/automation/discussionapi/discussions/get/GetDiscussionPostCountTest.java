package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsCountViaPostOrGetRequest.getDiscussionsPostsCountViaGetRequest;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.PostsGenerator.addPostToPrivateDiscussion;
import static com.fansunited.automation.validators.DiscussionValidator.verifyDiscussionPostCount;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.CacheValidator.CachePeriod;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Discussion Api - GET /v1/posts/count endpoint happy path tests")
public class GetDiscussionPostCountTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify post counts for valid discussion IDs")
  public void getPostCountsForValidDiscussionIdsTest() throws HttpException {

    var discussionIds = new ArrayList<String>();
    for (int i = 0; i < 3; i++) {
      var discussionId = createDiscussionForTests(false).getData().getId();
      discussionIds.add(discussionId);
      addPostToPrivateDiscussion(discussionId, getCurrentTestUser().getEmail(), i);
    }

    String ids = String.join(",", discussionIds);

    var response =
        getDiscussionsPostsCountViaGetRequest(
            ids,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200);

    for (int i = 0; i < discussionIds.size(); i++) {
      // Expected count of posts for discussionIds[i] should equal i
      verifyDiscussionPostCount(response, discussionIds.get(i), i);
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/discussions/posts/count response returned by the server is cached for 1 min")
  public void validateGetPostsCountByDiscussionIdCacheTest() throws HttpException {

    String discussionId = createDiscussionForTests(false).getData().getId();

    var response =
        getDiscussionsPostsCountViaGetRequest(
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    CacheValidator.validateCacheExpirationDate(response, CachePeriod.ONE_MINUTE);
  }
}
