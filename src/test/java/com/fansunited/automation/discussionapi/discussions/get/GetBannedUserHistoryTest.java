package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_USER_ID;
import static com.fansunited.automation.core.apis.discussionapi.BanUserEndpoint.banUser;
import static com.fansunited.automation.core.apis.discussionapi.GetBannedUserHistoryEndpoint.getBannedUserHistory;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.validators.DiscussionValidator.validateGetBannedUserResponse;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.BanUserRequest;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - GET /v1/discussions/users/{user_id}/ban endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetBannedUserHistoryTest extends DiscussionApiBaseTest {

  @Test
  @DisplayName("Verify get banned user information function works")
  public void getBannedUserHistoryInfo() {

    var createStaffResponse = createStaffUser();
    String staffEmail = createStaffResponse.jsonPath().getString("data.email");
    String bannedUserId = UUID.randomUUID().toString();

    var days = generateRandomNumber(1, 100);
    String banReason = String.format("Inappropriate behavior. Ban for %d day/s", days);
    var request = new BanUserRequest(days, banReason);

    banUser(request, bannedUserId).then().assertThat().statusCode(HttpStatus.SC_OK);

    days++;
    request.setBannedPeriodInDays(days);

    // Ban the user again to create a ban history for them.
    banUser(request, bannedUserId).then().assertThat().statusCode(HttpStatus.SC_OK);

    var response = getBannedUserHistory(staffEmail, bannedUserId);
    validateGetBannedUserResponse(response, request, CLIENT_AUTOMATION_USER_ID);
  }
}
