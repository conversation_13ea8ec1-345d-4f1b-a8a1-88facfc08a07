package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetPublicDiscussionsEndpoint.getPublicDiscussion;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.model.discussionapi.DiscussionType.COMMENT_SECTION;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.enums.DiscussionSortType;
import com.fansunited.automation.model.discussionapi.enums.DiscussionStatus;
import com.fansunited.automation.validators.DiscussionValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - GET /v1/discussions endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetAllPublicDiscussionsTest extends DiscussionApiBaseTest {
  private static boolean isInitialized = false;

  @BeforeEach
  public void createTestData() throws HttpException {
    if (!isInitialized) {
      for (int i = 0; i < 3; i++) {
        createDiscussionForTests(false);
        createDiscussionForTests(true);
      }
      isInitialized = true;
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get all discussions with discussion_type = COMMENT_SECTION")
  public void getPublicDiscussions() {

    var response = getPublicDiscussion(getCurrentTestUser().getEmail());
    currentTestResponse.set(response);

    DiscussionValidator.validateDiscussionResponseScCode(response);

    var getAllPublicDiscussionsDto = response.jsonPath().getList("data.discussion_type");
    boolean allDiscussionsArePublic =
        getAllPublicDiscussionsDto.stream().allMatch(type -> type.equals(COMMENT_SECTION.name()));
    Assertions.assertTrue(allDiscussionsArePublic);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE),@Tag(DISABLED),@Tag("FZ-3512")})
  @DisplayName("Get discussion by filed discussion_url")
  public void getDiscussionsByDiscussionUrlField() throws HttpException {
    String discussionUrl =
        getPublicDiscussion(getCurrentTestUser().getEmail())
            .jsonPath()
            .getList("data.discussion_url")
            .get(0)
            .toString();

    var response =
        getPublicDiscussion(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            null,
            null,
            DiscussionStatus.INACTIVE,
            DiscussionSortType.ASC.getValue(),
            null,
            null,
            null,
            null,
            null,
            null,
            discussionUrl,
            false);
    currentTestResponse.set(response);

    DiscussionValidator.validateDiscussionResponseScCode(response);

    String expectedDiscussionUrl = response.jsonPath().getString("data.discussion_url[0]");
    Assertions.assertEquals(expectedDiscussionUrl, discussionUrl);
  }

//  @Disabled("WIP FZ-3512")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Should ignore other fields when discussion URL is provided")
  public void verifyOtherFieldsAreIgnoredWhenDiscussionUrlIsProvided() throws HttpException {
      String discussionUrl =
        getPublicDiscussion(getCurrentTestUser().getEmail())
            .jsonPath()
            .getList("data.discussion_url")
            .get(0)
            .toString();
    Faker faker = new Faker();
    var response =
        getPublicDiscussion(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            20,
            null,
            DiscussionStatus.ACTIVE,
            DiscussionSortType.DESC.getValue(),
            faker.lorem().word(),
            null,
            null,
            5,
            6,
            List.of(faker.internet().uuid()),
            discussionUrl,
            true);
    currentTestResponse.set(response);

    DiscussionValidator.validateDiscussionResponseScCode(response);

    String expectedDiscussionUrl = response.jsonPath().getString("data.discussion_url[0]");
    Assertions.assertEquals(expectedDiscussionUrl, discussionUrl);
  }
}
