package com.fansunited.automation.firebasefunctions;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.AVATAR_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.BIRTHDATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_RESPONSE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.EMAIL_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.INTERESTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Firebase cloud functions tests")
@Execution(ExecutionMode.SAME_THREAD)
public class FirebaseCloudFunctionsTest extends BaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create user function works")
  public void createUserFunction()
      throws IOException, FirebaseAuthException, ExecutionException, InterruptedException {

    var userRecord = createUser();

    var userDocument = FirebaseHelper.getFirestoreCollection(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, FirebaseHelper.PROFILE_COLLECTION)
        .document(userRecord.getUid())
        .get()
        .get();

    Assertions.assertAll(() -> assertTrue(userDocument.exists(),
            "User with ID: " + userRecord.getUid() + " was not created in Firestore!"),
        () -> assertEquals(userRecord.getPhotoUrl(), userDocument.get(AVATAR_PROP)),
        () -> assertEquals(userRecord.getDisplayName(), userDocument.get(NAME_PROP)),
        () -> assertNull(userDocument.get(BIRTHDATE_PROP)),
        () -> assertEquals(userRecord.getEmail(), userDocument.get(EMAIL_PROP)),
        () -> assertNull(userDocument.get(COUNTRY_PROP_RESPONSE)),
        () -> assertEquals(List.of(), userDocument.get(INTERESTS_PROP)),
        () -> assertEquals(userDocument.get(GENDER_PROP),
            ProfileData.Profile.Gender.UNSPECIFIED.getValue()));
  }
}
