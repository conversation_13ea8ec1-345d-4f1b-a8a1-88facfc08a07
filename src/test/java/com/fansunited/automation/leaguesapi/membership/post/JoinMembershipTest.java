package com.fansunited.automation.leaguesapi.membership.post;

import static com.fansunited.automation.constants.ApiConstants.LeaguesApi.LEAGUE_MEMBERS;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.validators.LeaguesApiValidator.validateMembershipResponse;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint;
import com.fansunited.automation.core.apis.leagueapi.leagues.LeagueEndpoint;
import com.fansunited.automation.core.apis.leagueapi.membership.JoinEndpoint;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("League - Membership Api - POST /v1/membership/join endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class JoinMembershipTest extends LeagueBaseTest {

  @Test
  @DisplayName("Verify that users can join the Private League with an invitation code.")
  public void joinUserToLeague()
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {

    var league =
            CreateLeagueEndpoint.createLeague(
                            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                            AuthConstants.ENDPOINTS_API_KEY,
                            CLIENT_AUTOMATION_ID,
                            ContentType.JSON,
                            null,
                            leagueRequest,
                            creator.getEmail())
                    .as(LeagueData.class).getLeague();

    String leagueId = league.getId();
    var user = createUser();
    var response = JoinEndpoint.joinUserToLeague(user.getEmail(), league.getInvitationCode());

    validateMembershipResponse(response, league.getId(), user.getUid());

    response = LeagueEndpoint.getLeagueById(leagueId, creator.getEmail());
    currentTestResponse.set(response);

    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .assertThat()
        .body("data." + LEAGUE_MEMBERS, hasItem(user.getUid()));
  }
}
