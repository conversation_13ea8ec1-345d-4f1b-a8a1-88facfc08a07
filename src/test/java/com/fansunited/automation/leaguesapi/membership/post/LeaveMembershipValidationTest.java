package com.fansunited.automation.leaguesapi.membership.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.INVALID_LEAGUE_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;

import com.fansunited.automation.arguments.leagueapi.InvalidApiKeyArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.leagueapi.membership.JoinEndpoint;
import com.fansunited.automation.core.apis.leagueapi.membership.LeaveEndpoint;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName(
    "League - Membership Api - POST /v1/membership/leave/{league_id} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class LeaveMembershipValidationTest extends LeagueBaseTest {

  @ParameterizedTest(
      name = "Verify user cannot leave league with invalid/ missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidApiKeyArguments.class)
  public void leaveWithInvalidApiKey(InvalidApiKeyArgumentsHolder argumentsHolder) {

    var response =
        LeaveEndpoint.leaveLeague(
            leagueId, getCurrentTestUser().getEmail(), argumentsHolder.apiKey());

    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, argumentsHolder.statusCode());
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot leave league with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void leaveWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException {

    var league = createLeagueResponse.getLeague();
    var email = createUser().getEmail();
    JoinEndpoint.joinUserToLeague(email, league.getInvitationCode());

    var response =
        LeaveEndpoint.leaveLeague(
            leagueId, null, AuthConstants.ENDPOINTS_API_KEY, argumentsHolder.jwtToken());

    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, argumentsHolder.statusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify that the user cannot leave league without authentication.")
  public void leaveWithoutAuth() {

    var response = LeaveEndpoint.leaveLeague(leagueId, null, AuthConstants.ENDPOINTS_API_KEY, null);
    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns NOT_FOUND when sending a request with an invalid league ID. League ID {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_LEAGUE_ID)
  @EmptySource
  public void leaveNotExistingLeague(String leagueId) {

    var response = LeaveEndpoint.leaveLeague(leagueId, getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot leave league with an invalid/ missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void leaveWithInvalidClientId(String clientId) {

    var response =
        LeaveEndpoint.leaveLeague(
            leagueId,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            clientId);

    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when leave league with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void leaveWithNonSupportedContentType(ContentType contentType) {

    var response =
        LeaveEndpoint.leaveLeague(leagueId, getCurrentTestUser().getEmail(), contentType);

    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }
}
