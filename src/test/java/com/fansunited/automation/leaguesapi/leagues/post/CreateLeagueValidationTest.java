package com.fansunited.automation.leaguesapi.leagues.post;

import static com.fansunited.automation.constants.ApiErrorCodes.LeaguesErrorCodes.CODE_PROFILE_FETCH_FAILURE;
import static com.fansunited.automation.constants.ApiErrorCodes.LeaguesErrorCodes.INVALID_DESCRIPTION_LENGTH_ERROR_MSG;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.TOO_LONG_TEXT;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.WHITE_SPACES;
import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.NON_EXISTING_ADMINISTRATOR_ID;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.INVALID_TEMPLATE_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_USER_EMAIL;
import static com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint.createLeague;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToIsoDate;
import static com.fansunited.automation.validators.LeaguesApiValidator.createPrivateLeagueRequest;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fansunited.automation.arguments.commonarguments.InvalidContentTypeArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.leagueapi.InvalidNameArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidNameHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fansunited.automation.model.leaguesapi.request.CreateLeagueRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.time.LocalDate;
import java.util.List;
import lombok.SneakyThrows;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Private Leagues API - POST /v1/leagues endpoint negative tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateLeagueValidationTest extends LeagueBaseTest {

  private CreateLeagueRequest request;
  private static Response response;
  private final UserRecord creator = createTestUser();
  private final TemplateResponse templateResponse = createTemplateResponse();

  @BeforeEach
  public void setUp() {
    request = createRequest(templateResponse);
  }

  @ParameterizedTest(
      name = "Verify League cannot be created with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void createLeagueWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {
    response =
        createLeague(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            argumentsHolder.getApiKey(),
            CLIENT_AUTOMATION_ID,
            ContentType.JSON,
            null,
            request,
            creator.getEmail());
    currentTestResponse.set(response);
    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when trying to create League with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void createLeagueWithInvalidClientId(String clientId) throws HttpException {
    response =
        createLeague(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            ENDPOINTS_API_KEY,
            clientId,
            ContentType.JSON,
            null,
            request,
            creator.getEmail());
    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name = "Verify League cannot be created with invalid Content Type. Content Type: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ArgumentsSource(InvalidContentTypeArguments.class)
  public void createLeagueWithInvalidContentType(InvalidContentTypesHolder argumentsHolder)
      throws HttpException {
    response =
        createLeague(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            ENDPOINTS_API_KEY,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.contentType(),
            null,
            "",
            creator.getEmail());
    currentTestResponse.set(response);
    response.then().assertThat().statusCode(argumentsHolder.statusCode());
  }

  @ParameterizedTest(
      name = "Verify League cannot be created with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void createLeagueWithInvalidJwtToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {
    response =
        createLeague(CreateLeagueRequest.builder().build(), argumentsHolder.getJwtToken(), null);
    currentTestResponse.set(response);
    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when trying to create a League with invalid type. League type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = LeagueType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createLeagueWithInvalidType(LeagueType leagueType) throws HttpException {
    request.setType(leagueType.getValue());
    response = createLeague(request, null, getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name =
          "Verify League description cannot be longer than 50001 chars. Description: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = LeagueType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createLeagueWithLongDesc(LeagueType leagueType) throws HttpException {
    request.setType(leagueType.getValue());
    request.setDescription(TOO_LONG_TEXT);
    response = createLeague(request, null, creator.getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(INVALID_DESCRIPTION_LENGTH_ERROR_MSG));
  }

  @ParameterizedTest(
      name =
          "Verify League name cannot be longer than 101 chars, null or an empty string. Creating League: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidNameArguments.class)
  // Currently, this test is failing because we receive two error messages simultaneously.
  public void createLeagueWithInvalidName(InvalidNameHolder invalidNameHolder)
      throws HttpException {
    String name = invalidNameHolder.invalidName();
    request.setType(LeagueType.getRandomValidLeagueType().getValue());
    request.setName(name);
    response = createLeague(request, null, creator.getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(
      name = "Verify League cannot be created with invalid template id. Template id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {INVALID_TEMPLATE_ID, WHITE_SPACES})
  @NullAndEmptySource
  public void createLeagueWithInvalidTemplateId(String templateId) throws HttpException {
    request.setTemplateId(templateId);
    response = createLeague(request, null, creator.getEmail());
    currentTestResponse.set(response);
    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when creating League with invalid 'scoring_starts_at' param. scoring_starts_at param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"2025-03-05 00:00:00", "2025/01/01", "2025-05-16T05:50:06.7199222-04:00"})
  public void createLeagueWithInvalidScoringStartsAt(String fromDate) throws HttpException {
    request.setScoringStartsAt(fromDate);
    response = createLeague(request, null, creator.getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(SC_BAD_REQUEST)
        .body("status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(
      name =
          "Verify API throws HttpException when creating League with Non Existing User param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"", INVALID_USER_EMAIL})
  public void createLeagueWithNonExistentUser(String invalidUserEmail) {
    assertThrows(
        HttpException.class, () -> response = createLeague(request, null, invalidUserEmail));
  }

  @ParameterizedTest(
      name =
          "Verify API throws HttpException when creating League with Non Existing Administrator ID param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {NON_EXISTING_ADMINISTRATOR_ID})
  public void createLeagueWithNonExistentAdministrator(String invalidAdminId) throws HttpException {
    request.setAdministrators(List.of(invalidAdminId));
    response = createLeague(request, null, creator.getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(SC_BAD_REQUEST)
        .body("error.status", equalTo(CODE_PROFILE_FETCH_FAILURE));
  }

  @ParameterizedTest(
      name =
          "Verify API throws HttpException when creating League with invalid Administrator ID")
  @Tag(REGRESSION)
  @ValueSource(strings = {WHITE_SPACES})
  @EmptySource
  public void createLeagueWithInvalidAdministrator(String invalidAdminId) throws HttpException {
    request.setAdministrators(List.of(invalidAdminId));
    response = createLeague(request, null, creator.getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(name = "Verify league cannot be created with client from another project. Client: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {CLIENT_PRODUCTION_TESTING_ID})
  public void createLeagueWithDifferentClient(String clientId) throws HttpException {
    response =  CreateLeagueEndpoint.createLeague(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            clientId,
            ContentType.JSON,
            null,
            leagueRequest,
            creator.getEmail());

    currentTestResponse.set(response);
    response
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @SneakyThrows
  private UserRecord createTestUser() {
    return AuthBase.createUser();
  }

  private TemplateResponse createTemplateResponse() {
    return createTemplateForLeague();
  }

  private CreateLeagueRequest createRequest(TemplateResponse template) {
    var scoringStartAt = convertLocalDateToIsoDate(LocalDate.now());
    return createPrivateLeagueRequest(
        faker.funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "),
        LeagueType.PRIVATE,
        template.getId(),
        faker.lorem().paragraph(1),
        null,
        null,
        null,
        null,
        null,
        faker.bool().bool(),
        scoringStartAt);
  }
}
