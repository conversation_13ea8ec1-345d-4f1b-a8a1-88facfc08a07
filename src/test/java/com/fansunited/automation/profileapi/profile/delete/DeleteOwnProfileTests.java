package com.fansunited.automation.profileapi.profile.delete;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.ANONYMOUS;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.AVATAR_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.BIRTHDATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_REQUEST;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_RESPONSE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CREATED_AT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWERS_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWING_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.INTERESTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PHONE_COUNTRY_CODE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PHONE_NUMBER;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_IDS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.STAFF_MEMBER;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.VERIFIED;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_2_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamePredictionsEndpoint.getPredictionsForGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint.getWinnersByContestId;
import static com.fansunited.automation.core.apis.profileapi.CountriesEndpoint.getRandomCountryId;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteOwnProfile;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.updateCurrentTestUserProfile;
import static com.fansunited.automation.core.apis.profileapi.ProfilesEndpoint.createProfiles;
import static com.fansunited.automation.core.apis.reportingapi.helper.InterestGenerator.generateFootballInterest;
import static com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest.setExcludedProfileIdsWithPrediction;
import static com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest.setWinnerWithPrediction;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForDeletedProfileIdToBeCreated;
import static com.fansunited.automation.helpers.Helper.generateMD5;
import static com.fansunited.automation.helpers.Helper.generateRandomDate;
import static com.fansunited.automation.helpers.WaitHelper.waitProfileIDToBeUpdated;
import static com.fansunited.automation.helpers.bq.InsertBigQData.insertExcluded;
import static com.fansunited.automation.helpers.bq.InsertBigQData.insertProfileRow;
import static com.fansunited.automation.mappers.EventMapper.EVENT_TABLE;
import static com.fansunited.automation.mappers.EventMapper.EXCLUDED_TABLE;
import static com.fansunited.automation.mappers.EventMapper.PROFILE_TABLE;
import static com.fansunited.automation.mappers.EventMapper.RANK_TABLE;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType.TOP_X;
import static com.fansunited.automation.model.profileapi.profile.ProfileData.DeletedProfile.Gender.UNSPECIFIED;
import static com.fansunited.automation.model.profileapi.profile.ProfileData.DeletedProfile.Gender.getRandomGender;
import static com.fansunited.automation.validators.LoyaltyApiValidator.verifyProfileIdChanged;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertAll;

import com.fansunited.automation.constants.ApiConstants.ProfileApi.Interest.Football;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.profileapi.ProfileByIdEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.validators.RedisValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - DELETE /v1/user-data endpoint happy path tests")
public class DeleteOwnProfileTests extends ProfileApiBaseTest {

  @Test
  @Tag(REGRESSION)
  @Tag(SMOKE)
  @SuppressWarnings("unchecked")
  @DisplayName("Verify Delete own profile works")
  public void deleteProfileTest()
      throws HttpException,
          InterruptedException,
          IOException,
          ExecutionException,
          FirebaseAuthException {

    var updatedAvatarUrl = "https://www.gravatar.com/avatar/3b3be63a4c2a439b013787725dfce802?s=400";
    // This will be the new user id with the prefix 'deleted_' after deleting their own profile
    var userMD5ID = "deleted_" + generateMD5(getCurrentTestUser().getUid());
    var profileIdsToFollow = createProfiles(3).keySet().stream().toList();

    Faker faker = new Faker();
    var body = new JSONObject();
    body.put(COUNTRY_PROP_REQUEST, getRandomCountryId());
    body.put(BIRTHDATE_PROP, generateRandomDate(1950, 2006));
    body.put(PHONE_COUNTRY_CODE, "+359");
    body.put(PHONE_NUMBER, faker.phoneNumber().cellPhone());
    body.put(GENDER_PROP, getRandomGender().toString());
    body.put(AVATAR_PROP, updatedAvatarUrl);
    body.put(INTERESTS_PROP, List.of(generateFootballInterest(Football.TEAM, true)));
    body.put(NICKNAME, faker.name().firstName());
    body.put(PROFILE_IDS_PROP, profileIdsToFollow);

    updateCurrentTestUserProfile(body).then().assertThat().statusCode(HttpStatus.SC_OK);

    // Set following_count
    followProfilesForCurrentTestUser(body).then().assertThat().statusCode(HttpStatus.SC_OK);

    // Set followers_count
    body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(getCurrentTestUser().getUid()));
    followProfilesForUser(createUser().getEmail(), body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    // Delete own profile
    deleteOwnProfile(
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            CLIENT_PRODUCTION_TESTING_2_ID,
            null)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    // Wait for the new record to be saved in Firebase
    waitForDeletedProfileIdToBeCreated(userMD5ID, 1000, 5);

    var response = ProfileByIdEndpoint.getProfileById(userMD5ID);
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .rootPath("data")
        .body("id", equalTo(userMD5ID))
        .body(GENDER_PROP, equalTo(UNSPECIFIED.toString()))
        .body(FOLLOWING_COUNT, equalTo(0))
        .body(FOLLOWERS_COUNT, equalTo(0))
        .body(CREATED_AT, notNullValue())
        .body(VERIFIED, equalTo(false))
        .body(STAFF_MEMBER, equalTo(false))
        .body(ANONYMOUS, equalTo(false))
        .body(DISABLED, equalTo(false))
        .body(NAME_PROP, is(nullValue()))
        .body(AVATAR_PROP, is(nullValue()))
        .body(COUNTRY_PROP_RESPONSE, is(nullValue()))
        .body(BIRTHDATE_PROP, is(nullValue()))
        .body(NICKNAME, is(nullValue()));

    // Verify that the following_count and the followers_count for the user who was following and
    // was a follower of the deleted user are equal to zero
    ProfileByIdEndpoint.getProfileById(profileIdsToFollow.get(0))
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .rootPath("data")
        .body("following_count", equalTo(0))
        .body("followers_count", equalTo(0));
  }

  @Test
  @Tag(REGRESSION)
  @Tag(SMOKE)
  @DisplayName("Verify all profile data is deleted from Firebase")
  public void deleteProfileDataFromFirebaseTest() throws HttpException, InterruptedException {

    var currentProfileId = getCurrentTestUser().getUid();
    var userMD5ID = String.format("%s%s", "deleted_", generateMD5(currentProfileId));
    var gameInstance = PredictionApiBaseTest.createGameWithPredictions(getCurrentTestUser());

    // Set Redis data for the current test user
    var matchId = gameInstance.getFixtures().get(0).getMatchId();
    var predictionId =
        getPredictionsForGame(gameInstance.getId())
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_OK)
            .extract()
            .path("data[0].id")
            .toString();

    // Set excluded_profile_ids in a game
    var gameIdWithExcludedProfileIds = setExcludedProfileIdsWithPrediction(getCurrentTestUser());
    // Set Winner
    var gameIdWithWinner = setWinnerWithPrediction(getCurrentTestUser());

    // Delete own profile
    var response =
        deleteOwnProfile(
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            CLIENT_AUTOMATION_ID,
            null);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    currentTestResponse.set(response);

    // Wait for the new ID to be saved in Firebase
    waitForDeletedProfileIdToBeCreated(userMD5ID, 1000, 5);

    ProfileByIdEndpoint.getProfileById(userMD5ID)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .rootPath("data")
        .body("id", equalTo(userMD5ID));

    // Validates that the prediction has been successfully removed from Redis
    RedisValidator.validatePredictionForMatchNotExist(predictionId, matchId);

    var winnerId =
        getWinnersByContestId(
                gameIdWithWinner,
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
                ADMIN_USER,
                ContentType.JSON)
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_OK)
            .extract()
            .jsonPath()
            .getString("user_list[0].profile_id");
    // Validates that original user ID is replaced with new user ID in fu_winners collection
    Assertions.assertEquals(
        userMD5ID, winnerId, "The winner ID in the collection does not match the new profile ID");

    // Validates that user ID has been replaced in fu_games collection's excluded_profile_ids list
    var gameInstanceExcluded =
        GameEndpoint.getGameById(gameIdWithExcludedProfileIds).as(GameInstance.class);
    var excludedIds = gameInstanceExcluded.getExcludedProfileIds();

    assertAll(
        "Profile IDs in excluded list",
        () ->
            Assertions.assertFalse(
                excludedIds.contains(getCurrentTestUser().getUid()),
                "Original profile ID should not be in the excluded IDs list"),
        () ->
            Assertions.assertTrue(
                excludedIds.contains(userMD5ID),
                "New profile ID should be present in the excluded IDs list"));
  }

  @Test
  @Tag(REGRESSION)
  @Tag(SMOKE)
  @DisplayName("Verify all profile data is deleted from BQ")
  public void deleteProfileDataFromBQTest()
      throws InterruptedException,
          HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException {

    var localDateTimeMinus90Min = LocalDateTime.now(ZoneOffset.UTC).minusMinutes(90);

    // Insert profile to BQ
    var profile = createUser();
    var currentProfileId = profile.getUid();
    var currentProfileEmail = profile.getEmail();
    var updateAt = localDateTimeMinus90Min.toString();
    insertProfileRow(currentProfileId, updateAt);

    var userMD5ID = String.format("%s%s", "deleted_", generateMD5(currentProfileId));
    waitProfileIDToBeUpdated(currentProfileId, PROFILE_TABLE, 2, 10);

    // Insert event to BQ
    InsertBigQData.generateCorrectPredictionEvent(
        localDateTimeMinus90Min,
        currentProfileId,
        UUID.randomUUID().toString(),
        TOP_X,
        PredictionMarket.CORRECT_SCORE,
        10);

    waitProfileIDToBeUpdated(currentProfileId, EVENT_TABLE, 2, 10);

    // Insert rank to BQ
    var gameId = createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = localDateTimeMinus90Min.toString();

    InsertBigQData.insertSingleRankEvent(
        localDateTimeMinus90Min,
        currentProfileId,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X,
        30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate);

    waitProfileIDToBeUpdated(currentProfileId, RANK_TABLE, 2, 10);

    // Insert excluded BQ
    insertExcluded(currentProfileId);

    // Call user delete data
    var response =
        deleteOwnProfile(
            currentProfileEmail, AuthConstants.ENDPOINTS_API_KEY, CLIENT_AUTOMATION_ID, null);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    currentTestResponse.set(response);

    // Verifications
    verifyProfileIdChanged(PROFILE_TABLE, currentProfileId, userMD5ID);
    verifyProfileIdChanged(EVENT_TABLE, currentProfileId, userMD5ID);
    verifyProfileIdChanged(RANK_TABLE, currentProfileId, userMD5ID);
    verifyProfileIdChanged(EXCLUDED_TABLE, currentProfileId, userMD5ID);
  }
}
