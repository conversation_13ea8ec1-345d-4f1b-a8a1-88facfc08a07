package com.fansunited.automation.profileapi.profile.get;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.BIRTHDATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_RESPONSE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CREATED_AT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWERS_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWING_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.INTERESTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getCurrentTestUserProfileRequest;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getProfileRequest;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.updateCurrentTestUserProfile;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.apis.profileapi.ProfileByIdEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@DisplayName("Profile Api - GET /v1/profile endpoint happy path tests")
public class GetProfileTests extends ProfileApiBaseTest {

  Logger logger = LoggerFactory.getLogger(GetProfileTests.class);

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile data is successfully fetched and valid")
  public void getProfile() throws HttpException {

    var randomNicknameValue = new Faker().regexify("[A-Za-z0-9_]{15,100}");

    var body = new JSONObject();
    body.put(NICKNAME, randomNicknameValue);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + NICKNAME, equalTo(randomNicknameValue));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILE_SCHEMA))
        .body("data." + GENDER_PROP, is(ProfileData.Profile.Gender.UNSPECIFIED.getValue()))
        .body("data." + INTERESTS_PROP, is(empty()))
        .body("data." + BIRTHDATE_PROP, is(nullValue()))
        .body("data." + COUNTRY_PROP_RESPONSE, is(nullValue()))
        .body("data." + FOLLOWING_COUNT, equalTo(0))
        .body("data." + FOLLOWERS_COUNT, equalTo(0))
        .body("data." + NICKNAME, equalTo(randomNicknameValue))
        .body("data." + CREATED_AT, is(notNullValue()));
  }

  @ParameterizedTest(name = "Verify email part before domain is set as profile display name, if display name is {arguments} upon profile creation")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  public void getProfileWithoutDisplayName(String displayName)
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var email = new Faker().internet().emailAddress();

    var userRecord = createUser(email, displayName, null,null);

    var response = getProfileRequest(userRecord.getEmail());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILE_SCHEMA))
        .body("data." + NAME_PROP, equalTo(email.split("@")[0]))
        .body("data." + GENDER_PROP, is(ProfileData.Profile.Gender.UNSPECIFIED.getValue()))
        .body("data." + INTERESTS_PROP, is(empty()))
        .body("data." + BIRTHDATE_PROP, is(nullValue()))
        .body("data." + COUNTRY_PROP_RESPONSE, is(nullValue()))
        .body("data." + CREATED_AT, is(notNullValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile display name is autogenerated if user has no email and display name set upon profile creation")
  public void getProfileWithoutDisplayNameAndEmail()
      throws HttpException, IOException, InterruptedException {

    var userRecord = createUserWithoutNameAndEmail();

    var response = ProfileByIdEndpoint.getProfileById(userRecord.getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILE_SCHEMA))
        .body("data." + NAME_PROP, startsWith("user"))
        .body("data." + GENDER_PROP, is(ProfileData.Profile.Gender.UNSPECIFIED.getValue()))
        .body("data." + INTERESTS_PROP, is(empty()))
        .body("data." + BIRTHDATE_PROP, is(nullValue()))
        .body("data." + COUNTRY_PROP_RESPONSE, is(nullValue()))
        .body("data." + CREATED_AT, is(notNullValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/profile response returned by the server is NOT cached")
  public void verifyGetOwnProfileResponseIsNotCached() throws HttpException {

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateResponseIsNotCachedProfileApi(response);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify Deleted Profile can not get new token")
  public void verifyDeletedProfileCanNotGetNewToken()
      throws HttpException, IllegalArgumentException {

    deleteUser(getCurrentTestUser().getUid());

    try {
      FirebaseHelper.generateAuthToken(FANS_UNITED_CLIENTS,
          getCurrentTestUser().getEmail(), AuthConstants.DEFAULT_USER_PASS);

    } catch (HttpException e) {
      logger.info("\nThe Deleted User did not get new token as EXPECTED!\n");
      return;
    }

    Assert.fail("The Deleted User got new token - NOT EXPECTED!!!");
  }
}
