package com.fansunited.automation.profileapi.profile.patch;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.AVATAR_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.BIRTHDATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_REQUEST;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_RESPONSE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.EMAIL_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.INTERESTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PHONE_COUNTRY_CODE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PHONE_NUMBER;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.STAFF_MEMBER;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.VERIFIED;
import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.MESSAGE_EXISTING_NICKNAME;
import static com.fansunited.automation.constants.AuthConstants.DEFAULT_USER_PASS;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getRandomCompetitionId;
import static com.fansunited.automation.core.apis.footballapi.TeamsEndpoint.getRandomTeamId;
import static com.fansunited.automation.core.apis.footballapi.TopPlayersEndpoint.getRandomTopPlayerId;
import static com.fansunited.automation.core.apis.profileapi.CountriesEndpoint.getRandomCountryId;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getCurrentTestUserProfileRequest;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getProfileRequest;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.setVerifiedUserRequest;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.updateCurrentTestUserProfile;
import static com.fansunited.automation.helpers.Helper.generateRandomDate;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.hasValue;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.apis.profileapi.ProfileEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Stream;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - PATCH /v1/profile endpoint happy path tests")
@SuppressWarnings("unchecked")
public class UpdateProfileTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify can set verified user")
  public void setVerifiedUser()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    var createStaffRequest =
        CreateStaffMember.builder()
            .email(new Faker().internet().emailAddress())
            .name(new Faker().funnyName().name())
            .pass(DEFAULT_USER_PASS)
            .roles(List.of("client_admin"))
            .build();
    var response =
        StaffMemberEndpoint.createStaff(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            createStaffRequest,
            CLIENT_AUTOMATION_ID);
    var userRecord =
        createUser(
            new Faker().internet().emailAddress(), new Faker().funnyName().name(), null, null);

    String staffId =
        response.then().assertThat().statusCode(HttpStatus.SC_OK).extract().path("data.id");

    var body = new JSONObject();
    body.put(VERIFIED, true);
    body.put(STAFF_MEMBER, true);

    setVerifiedUserRequest(
            createStaffRequest.getEmail(),
            body,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            userRecord.getUid())
        .then()
        .log()
        .all()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var responseUser = getProfileRequest(userRecord.getEmail());

    currentTestResponse.set(responseUser);

    responseUser.then().assertThat().statusCode(HttpStatus.SC_OK);

    var updateBody = new JSONObject();
    updateBody.put(VERIFIED, true);
    updateBody.put(STAFF_MEMBER, false);

    setVerifiedUserRequest(
            createStaffRequest.getEmail(),
            updateBody,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            userRecord.getUid())
        .then()
        .log()
        .ifValidationFails()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    getProfileRequest(userRecord.getEmail())
        .then()
        .assertThat()
        .body("data.verified", is(true))
        .body("data.staff_member", is(false));

    // finally delete staff
    StaffMemberEndpoint.deleteStaffMembers(
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        CLIENT_AUTOMATION_ID,
        staffId,
        null,
        AuthConstants.ENDPOINTS_API_KEY,
        null);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile birth date can be updated")
  public void updateProfileBirthDate() throws HttpException {

    final var randomBirthDate = generateRandomDate(1920, 2000);
    var body = new JSONObject();
    body.put(BIRTHDATE_PROP, randomBirthDate);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomBirthDate));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomBirthDate));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile birth date can be removed(set to null)")
  public void removeProfileBirthDate() throws HttpException {

    final var randomBirthDate = generateRandomDate(1920, 2000);
    var body = new JSONObject();
    body.put(BIRTHDATE_PROP, randomBirthDate);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomBirthDate));

    getCurrentTestUserProfileRequest()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomBirthDate));

    body = new JSONObject();
    body.put(BIRTHDATE_PROP, null);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, is(nullValue()));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, is(nullValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile name can be updated")
  public void updateProfileDisplayName() throws HttpException {

    var faker = new Faker();
    var newName = faker.funnyName().name();

    var body = new JSONObject();
    body.put(NAME_PROP, newName);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + NAME_PROP, equalTo(newName));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + NAME_PROP, equalTo(newName));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile gender can be updated")
  public void updateProfileGender() throws HttpException {

    var newGender = ProfileData.Profile.Gender.MALE.getValue();

    var body = new JSONObject();
    body.put(GENDER_PROP, newGender);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GENDER_PROP, equalTo(newGender));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GENDER_PROP, equalTo(newGender));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile gender can be reset to unspecified")
  public void setProfileGenderToUnspecified() throws HttpException {

    var newGender = ProfileData.Profile.Gender.MALE.getValue();

    var body = new JSONObject();
    body.put(GENDER_PROP, newGender);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GENDER_PROP, equalTo(newGender));

    getCurrentTestUserProfileRequest()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GENDER_PROP, equalTo(newGender));

    body = new JSONObject();
    body.put(GENDER_PROP, ProfileData.Profile.Gender.UNSPECIFIED.getValue());

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GENDER_PROP, equalTo(ProfileData.Profile.Gender.UNSPECIFIED.getValue()));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GENDER_PROP, equalTo(ProfileData.Profile.Gender.UNSPECIFIED.getValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile country can be updated")
  public void updateProfileCountry() throws HttpException {

    var randomCountryId = getRandomCountryId();

    var body = new JSONObject();
    body.put(COUNTRY_PROP_REQUEST, randomCountryId);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP_RESPONSE + ".id", equalTo(randomCountryId));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP_RESPONSE + ".id", equalTo(randomCountryId));
  }

  @ParameterizedTest(
      name = "Verify profile country can be removed(set to null). Country: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  public void removeProfileCountry(String country) throws HttpException {

    String randomCountryId = getRandomCountryId();

    JSONObject body = new JSONObject();
    body.put(COUNTRY_PROP_REQUEST, randomCountryId);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP_RESPONSE + ".id", equalTo(randomCountryId));

    getCurrentTestUserProfileRequest()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP_RESPONSE + ".id", equalTo(randomCountryId));

    body = new JSONObject();
    body.put(COUNTRY_PROP_REQUEST, country);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP_RESPONSE + ".id", is(nullValue()));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP_RESPONSE + ".id", is(nullValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile avatar can be updated")
  public void updateProfileAvatar() throws HttpException {

    final var updatedAvatarUrl =
        "https://www.gravatar.com/avatar/3b3be63a4c2a439b013787725dfce802?s=400";

    var body = new JSONObject();
    body.put(AVATAR_PROP, updatedAvatarUrl);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + AVATAR_PROP, equalTo(updatedAvatarUrl));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + AVATAR_PROP, equalTo(updatedAvatarUrl));
  }

  @ParameterizedTest(
      name = "Verify profile avatar can be removed(set to null). Avatar: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"    "})
  @NullAndEmptySource
  public void removeAvatar(String avatar) throws HttpException {

    var body = new JSONObject();
    body.put(AVATAR_PROP, avatar);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + AVATAR_PROP, is(nullValue()));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + AVATAR_PROP, is(nullValue()));
  }

  @ParameterizedTest(
      name =
          "Verify profile interests for source: \"football\" can be updated. Football interest type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(ApiConstants.ProfileApi.Interest.Football.class)
  public void updateProfileInterestsForSourceFootball(
      ApiConstants.ProfileApi.Interest.Football type) throws HttpException {
    ProfileData.Interest interest = null;

    switch (type) {
      case PLAYER -> {
        String footballPlayerId = getRandomTopPlayerId();
        interest =
            new ProfileData.Interest(
                false,
                footballPlayerId,
                ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
                ApiConstants.ProfileApi.Interest.Football.PLAYER.getType());
      }
      case TEAM -> {
        String footballTeamId = getRandomTeamId();
        interest =
            new ProfileData.Interest(
                false,
                footballTeamId,
                ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
                ApiConstants.ProfileApi.Interest.Football.TEAM.getType());
      }
      case COMPETITION -> {
        String competitionId = getRandomCompetitionId();
        interest =
            new ProfileData.Interest(
                false,
                competitionId,
                ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
                ApiConstants.ProfileApi.Interest.Football.COMPETITION.getType());
      }
    }

    var body = new JSONObject();
    body.put(INTERESTS_PROP, List.of(interest));

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].favourite", equalTo(interest.getFavourite()))
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].favourite", equalTo(interest.getFavourite()))
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));
  }

  @ParameterizedTest(
      name =
          "Verify profile interests for source: \"tennis\" can be updated. Tennis interest type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(ApiConstants.ProfileApi.Interest.Tennis.class)
  public void updateProfileInterestsForSourceTennis(ApiConstants.ProfileApi.Interest.Tennis type)
      throws HttpException {

    ProfileData.Interest interest = null;

    switch (type) {
      case PLAYER -> {
        String tennisPlayerId = "fb:t:1342";
        interest =
            new ProfileData.Interest(
                false,
                tennisPlayerId,
                ApiConstants.ProfileApi.Interest.TENNIS.getSource(),
                ApiConstants.ProfileApi.Interest.Tennis.PLAYER.getTennisIntType());
      }
      case COMPETITION -> {
        String competitionId = "fb:c:1525";
        interest =
            new ProfileData.Interest(
                true,
                competitionId,
                ApiConstants.ProfileApi.Interest.TENNIS.getSource(),
                ApiConstants.ProfileApi.Interest.Tennis.COMPETITION.getTennisIntType());
      }
    }

    var body = new JSONObject();
    body.put(INTERESTS_PROP, List.of(interest));

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].favourite", equalTo(interest.getFavourite()))
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].favourite", equalTo(interest.getFavourite()))
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));
  }

  @ParameterizedTest(
      name =
          "Verify profile interests for source: \"sport\" can be updated. Sport type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(ApiConstants.ProfileApi.Interest.Sport.class)
  public void updateProfileInterestsForSourceSport(ApiConstants.ProfileApi.Interest.Sport type)
      throws HttpException {

    var interest =
        switch (type) {
          case FOOTBALL ->
              new ProfileData.Interest(
                  false,
                  ApiConstants.ProfileApi.Interest.Sport.FOOTBALL.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case CRICKET ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.CRICKET.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case FIELD_HOCKEY ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.FIELD_HOCKEY.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case TENNIS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.TENNIS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case VOLLEYBALL ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.VOLLEYBALL.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case TABLE_TENNIS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.TABLE_TENNIS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case BASKETBALL ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.BASKETBALL.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case BASEBALL ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.BASEBALL.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case RUGBY ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.RUGBY.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case GOLF ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.GOLF.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case ATHLETICS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.ATHLETICS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case FORMULA_1 ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.FORMULA_1.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case BOXING ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.BOXING.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case ICE_HOCKEY ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.ICE_HOCKEY.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case AMERICAN_FOOTBALL ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.AMERICAN_FOOTBALL.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case MMA ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.MMA.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case MOTO_GP ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.MOTO_GP.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case BADMINTON ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.BADMINTON.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case CYCLING ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.CYCLING.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case SNOOKER ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.SNOOKER.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case GYMNASTICS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.GYMNASTICS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case HANDBALL ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.HANDBALL.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case WRESTLING ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.WRESTLING.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case HORSE_RACING ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.HORSE_RACING.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case WEIGHT_LIFTING ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.WEIGHT_LIFTING.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case CHESS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.CHESS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case SQUASH ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.SQUASH.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case BIATHLON ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.BIATHLON.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case WINTER_SPORTS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.WINTER_SPORTS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case MARTIAL_ARTS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.MARTIAL_ARTS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case WATER_SPORTS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.WATER_SPORTS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case SHOOTING ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.SHOOTING.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case RALLY ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.RALLY.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
          case RHYTHMIC_GYMNASTICS ->
              new ProfileData.Interest(
                  true,
                  ApiConstants.ProfileApi.Interest.Sport.RHYTHMIC_GYMNASTICS.getSportIntType(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource(),
                  ApiConstants.ProfileApi.Interest.SPORT.getSource());
        };

    var body = new JSONObject();
    body.put(INTERESTS_PROP, List.of(interest));

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile interests for source: \"custom\" can be updated")
  public void updateProfileInterestsForSourceCustom() throws HttpException {

    var faker = new Faker();
    var sourceCustom = ApiConstants.ProfileApi.Interest.CUSTOM.getSource();
    var typeCustom = faker.howIMetYourMother().character();
    var idCustom = faker.howIMetYourMother().quote();

    var interest = new ProfileData.Interest(true, idCustom, sourceCustom, typeCustom);

    var body = new JSONObject();
    body.put(INTERESTS_PROP, List.of(interest));

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + "[0].id", equalTo(interest.getId()))
        .body("data." + INTERESTS_PROP + "[0].source", equalTo(interest.getSource()))
        .body("data." + INTERESTS_PROP + "[0].type", equalTo(interest.getType()))
        .body("data." + INTERESTS_PROP + ".size()", equalTo(1));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile can be updated with multiple interests")
  public void updateProfileWithMultipleInterests() throws HttpException {

    var interestList = new ArrayList<ProfileData.Interest>();

    var footballPlayerId = getRandomTopPlayerId();

    var interest =
        new ProfileData.Interest(
            false,
            footballPlayerId,
            ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
            ApiConstants.ProfileApi.Interest.Football.PLAYER.getType());

    var competitionId = getRandomCompetitionId();
    var interest2 =
        new ProfileData.Interest(
            false,
            competitionId,
            ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
            ApiConstants.ProfileApi.Interest.Football.COMPETITION.getType());

    var faker = new Faker();
    var sourceCustom = ApiConstants.ProfileApi.Interest.CUSTOM.getSource();
    var typeCustom = faker.howIMetYourMother().character();
    var idCustom = faker.howIMetYourMother().quote();

    var interest3 = new ProfileData.Interest(true, idCustom, sourceCustom, typeCustom);

    interestList.add(interest);
    interestList.add(interest2);
    interestList.add(interest3);

    var body = new JSONObject();
    body.put(INTERESTS_PROP, interestList);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + ".size()", equalTo(3))
        .body(
            "data." + INTERESTS_PROP,
            hasItems(
                hasEntry("id", interest.getId()),
                hasEntry("id", interest2.getId()),
                hasEntry("id", interest3.getId())))
        .body(
            "data." + INTERESTS_PROP,
            hasItems(
                (hasValue(interest.getType())),
                hasValue(interest2.getType()),
                hasValue(interest3.getType())))
        .body(
            "data." + INTERESTS_PROP,
            hasItems(
                (hasValue(interest.getSource())),
                hasValue(interest2.getSource()),
                hasValue(interest3.getSource())));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP + ".size()", equalTo(3))
        .body(
            "data." + INTERESTS_PROP,
            hasItems(
                hasEntry("id", interest.getId()),
                hasEntry("id", interest2.getId()),
                hasEntry("id", interest3.getId())))
        .body(
            "data." + INTERESTS_PROP,
            hasItems(
                (hasValue(interest.getType())),
                hasValue(interest2.getType()),
                hasValue(interest3.getType())))
        .body(
            "data." + INTERESTS_PROP,
            hasItems(
                (hasValue(interest.getSource())),
                hasValue(interest2.getSource()),
                hasValue(interest3.getSource())));
  }

  @ParameterizedTest(
      name =
          "Verify profile interests can be removed by passing {arguments} value for \"interests\" prop")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @MethodSource("emptyList")
  @NullSource
  public void removeProfileInterests(List<Object> list) throws HttpException {

    updateProfileWithMultipleInterests();

    var body = new JSONObject();
    body.put(INTERESTS_PROP, list);

    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP, is(empty()));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + INTERESTS_PROP, is(empty()));
  }

  private static Stream<Arguments> emptyList() {
    return Stream.of(Arguments.of(List.of()));
  }

  @ParameterizedTest(name = "Verify profile nickname can be updated to '{arguments}'")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullSource
  public void updateProfileNickname(String nickname) throws HttpException {

    // Given profile is set with a nickname
    var body = new JSONObject();
    body.put(NICKNAME, new Faker().name().fullName());

    updateCurrentTestUserProfile(body);

    // When the profile nickname is updated
    if ("null".equals(nickname)) {
      nickname = null;
    }

    var bodyUpdate = new JSONObject();
    bodyUpdate.put(NICKNAME, nickname);

    updateCurrentTestUserProfile(bodyUpdate)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + NICKNAME, equalTo(nickname));

    // Then the profile nickname is successfully updated
    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + NICKNAME, equalTo(nickname));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile nickname can not be updated with existing nickname")
  public void updateProfileWithExistingNickname()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    // Given new user profile is created
    UserRecord user =
        createUser(
            new Faker().internet().emailAddress().toLowerCase(),
            new Faker().name().firstName(),
            null,
            new Faker().funnyName().toString());

    // And the user profile is updated with a random nickname
    var body = new JSONObject();
    body.put(NICKNAME, new Faker().name().firstName());

    ProfileEndpoint.updateProfileRequest(user.getEmail(), body);

    // When another user (the current automation test user) is also updated with the same nickname
    var response = updateCurrentTestUserProfile(body);

    currentTestResponse.set(response);

    // Then error message is received - 'This nickname has already been taken.'
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(MESSAGE_EXISTING_NICKNAME));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profile data will be updated correctly and existing data will be saved")
  public void updateProfileDataExceptNickName() throws HttpException {

    final var randomBirthDate = generateRandomDate(1920, 2000);

    final var randomNickName = new Faker().name().firstName();
    var body = new JSONObject();
    body.put(BIRTHDATE_PROP, randomBirthDate);
    body.put(NICKNAME, randomNickName);

    final var randomNewBirthDate = generateRandomDate(1920, 2000);
    var bodyNew = new JSONObject();
    bodyNew.put(BIRTHDATE_PROP, randomNewBirthDate);

    var userName = getCurrentTestUser().getDisplayName();

    // update user birthdate and nickname
    updateCurrentTestUserProfile(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomBirthDate))
        .body("data." + NICKNAME, equalTo(randomNickName));

    // update only user birthdate
    updateCurrentTestUserProfile(bodyNew)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomNewBirthDate));

    var response = getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);
    // verify that the nickname is not null after second update
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + BIRTHDATE_PROP, equalTo(randomNewBirthDate))
        .body("data." + NICKNAME, equalTo(randomNickName))
        .body("data." + NAME_PROP, equalTo(userName));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify own profile email, phone number and phone country code can be updated")
  public void updateProfileContactFields() throws HttpException {

    var phoneCountryCode = "+359";
    var phoneNumber = "98777777777";
    var email = new Faker().internet().emailAddress();

    var body = new JSONObject();

    body.put(PHONE_COUNTRY_CODE, phoneCountryCode);
    body.put(PHONE_NUMBER, phoneNumber);
    body.put(EMAIL_PROP, email);

    var response = updateCurrentTestUserProfile(body);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + PHONE_COUNTRY_CODE, is(equalTo(phoneCountryCode)))
        .body("data." + PHONE_NUMBER, is(equalTo(phoneNumber)))
        .body("data." + EMAIL_PROP, is(equalTo(email)));
  }
}
