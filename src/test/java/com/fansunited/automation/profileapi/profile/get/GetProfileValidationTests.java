package com.fansunited.automation.profileapi.profile.get;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getCurrentTestUserProfileRequest;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getProfileRequestWithToken;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getProfileRequestWithoutToken;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - GET /v1/profile endpoint validation tests")
public class GetProfileValidationTests extends ProfileApiBaseTest {

  @ParameterizedTest(name = "Verify profile data cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getProfileWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    Response response =
        getCurrentTestUserProfileRequest(UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            ContentType.JSON, argumentsHolder.getApiKey());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify profile cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getProfileWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    Response response =
        getProfileRequestWithToken(argumentsHolder.getJwtToken(),
            UrlParamValues.Language.EN.getValue(),
            UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID,
            ContentType.JSON, AuthConstants.ENDPOINTS_API_KEY);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns 401 when fetching profile without JWT token")
  public void getProfileWithoutJwtToken() throws HttpException {

    Response response = getProfileRequestWithoutToken();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(name = "Verify profile data cannot be fetched with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getProfileWithInvalidClientId(String clientId) throws HttpException {

    Response response =
        getCurrentTestUserProfileRequest(UrlParamValues.Language.EN.getValue(), clientId,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify another client's user profile data cannot be accessed with token from different firebase project")
  public void getProfileForAnotherClient() throws HttpException {

    Response response =
        getCurrentTestUserProfileRequest(UrlParamValues.Language.EN.getValue(),
            CLIENT_PRODUCTION_TESTING_ID,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Disabled("WIP FZ-766")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting profile data with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getProfileWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    Response response =
        getCurrentTestUserProfileRequest(UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify profile data is still fetched in JSON format if content type is NOT specified")
  public void getProfileWithoutSpecifyingContentType() throws HttpException {

    Response response =
        getCurrentTestUserProfileRequest(UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILE_SCHEMA));
  }
}
