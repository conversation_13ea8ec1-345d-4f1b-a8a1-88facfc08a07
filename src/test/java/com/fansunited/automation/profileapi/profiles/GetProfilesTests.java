package com.fansunited.automation.profileapi.profiles;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.BIRTHDATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.COUNTRY_PROP_RESPONSE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CREATED_AT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.DELETED_USER_NAME;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWERS_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWING_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.INTERESTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.core.apis.profileapi.ProfilesEndpoint.createProfiles;
import static com.fansunited.automation.core.apis.profileapi.ProfilesEndpoint.getProfilesRequest;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.apis.profileapi.ProfileByIdEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.validators.CacheValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - GET /v1/profiles endpoint happy path tests")
public class GetProfilesTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify profiles are successfully fetched and valid")
  public void getProfiles()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int usersCount = 3;

    List<String> profileIds = createProfiles(usersCount).keySet().stream().toList();

    Response response = getProfilesRequest(profileIds);

    currentTestResponse.set(response);

    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILES_SCHEMA))
        .body("data.size()", equalTo(usersCount))
        .body("data." + GENDER_PROP,
            everyItem(is(ProfileData.Profile.Gender.UNSPECIFIED.getValue())))
        .body("data." + INTERESTS_PROP, everyItem(is(empty())))
        .body("data." + BIRTHDATE_PROP, everyItem(is(nullValue())))
        .body("data." + COUNTRY_PROP_RESPONSE, everyItem(is(nullValue())))
        .body("data." + FOLLOWING_COUNT, everyItem(equalTo(0)))
        .body("data." + FOLLOWERS_COUNT, everyItem(equalTo(0)))
        .body("data." + NICKNAME, everyItem(is(nullValue())))
        .body("data." + CREATED_AT, everyItem(is(notNullValue())));

  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify GET /v1/profiles/ response returned by the server is cached for 2h")
  public void verifyGetProfileResponseIsCached()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final var usersCount = 3;

    var profileIds = createProfiles(usersCount).keySet().stream().toList();

    var response = getProfilesRequest(profileIds);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify specific profile is successfully fetched and is valid")
  public void getProfileById()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int usersCount = 1;

    Map<String, Response> profileMap = createProfiles(usersCount);

    String profileId = profileMap.keySet().stream().toList().get(0);

    Response response = ProfileByIdEndpoint.getProfileById(profileId);

    ProfileData profile = response.as(ProfileData.class);

    currentTestResponse.set(response);

    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILE_SCHEMA))
        .body("data.id", equalTo(profile.getProfile().getId()))
        .body("data.name", equalTo(profile.getProfile().getName()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify GET /v1/profiles/{userId} response returned by the server is cached for 2h")
  public void verifyGetSpecificProfileResponseIsCached()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final var usersCount = 2;

    var profileIds = createProfiles(usersCount).keySet().stream().toList();

    var response =
        getProfilesRequest(profileIds, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify deleted profile fetched information is correct")
  public void getDeletedProfile() throws HttpException {

    var profileId = getCurrentTestUser().getUid();

    deleteUser(getCurrentTestUser().getUid());

    var getDeletedUserResponse = ProfileByIdEndpoint.getProfileById(profileId);

    getDeletedUserResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.PROFILES_DELETED_SCHEMA))
        .body("data." + NAME_PROP, equalTo(DELETED_USER_NAME));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify deleted profile is fetched with list of profiles")
  public void getDeletedProfileInProfilesList()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int usersCount = 3;

    List<String> profileIds = new ArrayList<>();
    Map<String, Response> profiles = createProfiles(usersCount);

    for(Map.Entry<String, Response> profile : profiles.entrySet()){
      profileIds.add(profile.getKey());
    }

    profileIds.add(getCurrentTestUser().getUid());

    deleteUser(getCurrentTestUser().getUid());

    Response response = getProfilesRequest(profileIds);

    List<ProfileData.DeletedProfile> responseProfilesList = response.body().jsonPath().getList("data", ProfileData.DeletedProfile.class);

    currentTestResponse.set(response);

    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_PROFILES_SCHEMA_WITH_DELETED_USERS))
        .body("data.size()", equalTo(usersCount + 1));

    Assertions.assertTrue(
        responseProfilesList
            .stream()
            .map(ProfileData.DeletedProfile::getId)
            .toList()
            .contains(getCurrentTestUser().getUid()));
  }
}
