package com.fansunited.automation.profileapi.profiles.delete;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_IDS_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForUser;
import static com.fansunited.automation.core.apis.profileapi.FollowersByIdEndpoint.getFollowersForSpecificUser;
import static com.fansunited.automation.core.apis.profileapi.FollowingsByIdEndpoint.getFollowingsForSpecificUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.validators.ProfileApiValidator.validateFollowProfilesResponse;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - GET/DELETE /v1/profiles endpoint validation tests")
public class DeleteProfilesValidationTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET followers for deleted user throws an error")
  public void verifyFollowersForDeletedUserByIdThrowsError()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var profiles = createUsers(1);

    var profileToBeFollowedAndWillBeDeleted = getCurrentTestUser();
    var profileThatFollows = profiles.get(0);

    /**
     * Below a follower is created for the 'profileToBeFollowedAndWillBeDeleted' (currentTestUser) that is the 'profileThatFollows'
     */
    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(profileToBeFollowedAndWillBeDeleted.getUid()));

    var postFollowProfileResponse = followProfilesForUser(profileThatFollows.getEmail(), body);

    validateFollowProfilesResponse(postFollowProfileResponse,
        List.of(profileToBeFollowedAndWillBeDeleted.getUid()), profileThatFollows.getUid());

    /**
     * Below the 'profileThatFollowsAndWillBeDeleted' is being deleted
     */
    deleteUser(profileToBeFollowedAndWillBeDeleted.getUid());

    /**
     * Below trying to fetch followers for deleted user by ID
     */
    var response = getFollowersForSpecificUser(profileToBeFollowedAndWillBeDeleted.getUid());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND)
        .body("error.message", equalTo("Profile not found or already deleted."));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET followings for deleted user throws an error")
  public void verifyFollowingsForDeletedUserByIdThrowsError()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var profiles = createUsers(1);

    var profileThatFollowsAndWillBeDeleted = getCurrentTestUser();
    var profileThatIsBeingFollowed = profiles.get(0);

    /**
     * Below a following is created for the 'profileToBeFollowingAndWillBeDeleted' (currentTestUser) that is the 'profileThatIsBeingFollowed'
     */
    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(profileThatIsBeingFollowed.getUid()));

    var postFollowProfileResponse = followProfilesForCurrentTestUser(body);

    validateFollowProfilesResponse(postFollowProfileResponse,
        List.of(profileThatIsBeingFollowed.getUid()), profileThatFollowsAndWillBeDeleted.getUid());

    /**
     * Below the 'profileThatFollowsAndWillBeDeleted' is being deleted
     */
    deleteUser(profileThatFollowsAndWillBeDeleted.getUid());

    /**
     * Below trying to fetch followings for deleted user by ID
     */
    var response = getFollowingsForSpecificUser(profileThatFollowsAndWillBeDeleted.getUid());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND)
        .body("error.message", equalTo("Profile not found or already deleted."));
  }
}
