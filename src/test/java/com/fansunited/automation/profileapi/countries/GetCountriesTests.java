package com.fansunited.automation.profileapi.countries;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.profileapi.CountriesEndpoint.getCountries;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.profileapi.CountriesEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - GET /v1/countries endpoint happy path tests")
public class GetCountriesTests extends ProfileApiBaseTest {

  private static final int EXPECTED_COUNTRY_COUNT = 250;

  @ParameterizedTest(name = "Verify list of countries for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"en", "bg", "ro"})
  public void getCountriesForLang(String lang) throws HttpException {

    var response =
        getCountries(lang, UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.ProfileApi.Endpoints.Countries.GET_COUNTRIES_SCHEMA))
        .body("data.name", hasSize(greaterThanOrEqualTo(EXPECTED_COUNTRY_COUNT)));

    switch (lang) {
      case "en" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("United Kingdom"));
      case "bg" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("България"));
      case "ro" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Croaţia"));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/countries response returned by the server is cached for 1 month")
  public void verifyGetCountriesResponseIsCached() throws HttpException {

    var response = CountriesEndpoint.getCountries();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }
}
