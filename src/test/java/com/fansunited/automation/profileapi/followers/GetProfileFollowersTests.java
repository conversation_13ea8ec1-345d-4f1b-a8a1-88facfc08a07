package com.fansunited.automation.profileapi.followers;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.generateFollowersForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileFollowersEndpoint.getCurrentTestUserFollowers;
import static com.fansunited.automation.validators.ProfileApiValidator.validateGetProfileFollowersResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.profileapi.follow.ProfileFollowersData;
import com.fansunited.automation.validators.CacheValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - GET /v1/profile/followers endpoint happy path tests")
public class GetProfileFollowersTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify own profile followers are successfully fetched and valid")
  public void getOwnProfileFollowers()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final var followersCount = Helper.generateRandomNumber(30, 50);

    var followersIdList = generateFollowersForCurrentTestUser(followersCount);

    var response = getCurrentTestUserFollowers();

    currentTestResponse.set(response);

    validateGetProfileFollowersResponse(response, followersIdList, -1,
        getCurrentTestUser(), false);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/profile/followers response returned by the server is NOT cached")
  public void verifyGetOwnProfilesFollowersResponseIsNotCached()
      throws HttpException {

    var response = getCurrentTestUserFollowers();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    
    CacheValidator.validateResponseIsNotCached(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting own profile followers")
  public void getYourOwnProfileFollowersPaginationParams()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final var followersCount = Helper.generateRandomNumber(10, 20);

    final var resultsLimit = 5;

    generateFollowersForCurrentTestUser(followersCount);

    var response = getCurrentTestUserFollowers(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    var profileFollowersData =
        response.as(ProfileFollowersData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.PROFILE_FOLLOWERS_SCHEMA))
        .body("data.size()", equalTo(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(profileFollowersData.getProfileFollowers()
                .get(profileFollowersData.getProfileFollowers().size() - 1)
                .getFollowerId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting own profile followers")
  public void getYourOwnProfileFollowersWithDefaultPaginationLimitParam()
      throws HttpException {

    var response = getCurrentTestUserFollowers();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting own profile followers works")
  public void getYourOwnProfileFollowersPaginationMoveToNextPage()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final var followersCount = Helper.generateRandomNumber(30, 50);

    final var resultsLimit = Helper.generateRandomNumber(1, 15);
    var followersIdList = generateFollowersForCurrentTestUser(followersCount);

    var getProfileFollowersResponse = getCurrentTestUserFollowers(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(getProfileFollowersResponse);

    validateGetProfileFollowersResponse(getProfileFollowersResponse, followersIdList, resultsLimit,
        getCurrentTestUser(), false);
  }
}