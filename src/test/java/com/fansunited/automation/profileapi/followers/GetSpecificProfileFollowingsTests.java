package com.fansunited.automation.profileapi.followers;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.AVATAR_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWING_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_IDS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_ID_PROP;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.generateFollowingsForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowingsByIdEndpoint.getFollowingsForSpecificUser;
import static com.fansunited.automation.core.apis.profileapi.ProfilesEndpoint.getProfilesRequest;
import static com.fansunited.automation.validators.ProfileApiValidator.validateGetProfileFollowingsResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.apis.profileapi.ProfileEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.profileapi.follow.ProfileFollowingsData;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - GET /v1/profile/{userId}/followings endpoint happy path tests")
public class GetSpecificProfileFollowingsTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify followings for specific profile are successfully fetched and valid")
  public void getFollowingsForSpecificProfile()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int followingCount = Helper.generateRandomNumber(30, 50);

    List<String> followingIdList = generateFollowingsForCurrentTestUser(followingCount);

    Response response =
        getFollowingsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    validateGetProfileFollowingsResponse(response, followingIdList, -1,
        getCurrentTestUser(), true);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting specific profile followings")
  public void getFollowingsForSpecificProfilePaginationParams()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int followingCount = Helper.generateRandomNumber(10, 20);

    final int resultsLimit = 5;

    generateFollowingsForCurrentTestUser(followingCount);

    Response response =
        getFollowingsForSpecificUser(getCurrentTestUser().getUid(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    ProfileFollowingsData profileFollowingsData =
        response.as(ProfileFollowingsData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.PROFILE_FOLLOWINGS_SCHEMA))
        .body("data.size()", equalTo(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(profileFollowingsData.getProfileFollowings()
                .get(profileFollowingsData.getProfileFollowings().size() - 1)
                .getFollowingId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting specific profile followings")
  public void getSpecificProfileFollowingsWithDefaultPaginationLimitParam()
      throws HttpException {

    Response response =
        getFollowingsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting specific profile followings works")
  public void getSpecificProfileFollowingsPaginationMoveToNextPage()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int followingCount = Helper.generateRandomNumber(30, 50);

    final int resultsLimit = Helper.generateRandomNumber(1, 15);

    List<String> followingIdList = generateFollowingsForCurrentTestUser(followingCount);

    Response response =
        getFollowingsForSpecificUser(getCurrentTestUser().getUid(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, resultsLimit, null);

    validateGetProfileFollowingsResponse(response, followingIdList,
        resultsLimit,
        getCurrentTestUser(), true);
  }

  @Deprecated
  @Disabled("Deprecated test - will not be used anymore")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1658"), @Tag(DISABLED)})
  @DisplayName("Verify /GET /v1/profile/{userId}/followings response contains nickname")
  public void verifyFollowingIsReturnedWithNicknameInTheResponse()
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException,
      HttpException {
    var profileFollowed = createUsers(1);

    JSONObject body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(profileFollowed.get(0).getUid()));

    var response = followProfilesForUser(getCurrentTestUser().getEmail(), body);

    //Update the nickname of the followed profile:
    body = new JSONObject();
    body.put(NAME_PROP, new Faker().name().fullName());
    body.put(AVATAR_PROP, "https://updated_gravatar.com/avatar/xx5b80de1da869c24cd7603bfb6361a4?s=400&d=robohash&r=x");
    body.put(NICKNAME, new Faker().name().firstName());

    ProfileEndpoint.updateProfileRequest(profileFollowed.get(0).getEmail(), body);

    List<ProfileData.Profile> profileFollowedForAssertion = getProfilesRequest(List.of(profileFollowed.get(0).getUid()))
        .then()
        .extract()
        .body()
        .jsonPath().getList("data", ProfileData.Profile.class);

    response =
        getFollowingsForSpecificUser(getCurrentTestUser().getUid());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.PROFILE_FOLLOWINGS_SCHEMA))
        .body("data[0]." + FOLLOWING_ID_PROP, equalTo(profileFollowedForAssertion.get(0).getId()))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(getCurrentTestUser().getUid()))
        .body("data[0]." + NAME_PROP, equalTo(profileFollowedForAssertion.get(0).getName()))
        .body("data[0]." + AVATAR_PROP, equalTo(profileFollowedForAssertion.get(0).getAvatar()))
        .body("data[0]." + NICKNAME, equalTo(profileFollowedForAssertion.get(0).getNickname()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify /GET /v1/profile/{userId}/following response returned by the server is cached for 2h")
  public void verifyGetSpecificProfilesFollowingsResponseIsCached()
      throws HttpException {

    getFollowingsForSpecificUser(getCurrentTestUser().getUid());

    var response =
        getFollowingsForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }
}
