package com.fansunited.automation.profileapi.followers;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.AVATAR_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWER_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_IDS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_ID_PROP;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.generateFollowersForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowersByIdEndpoint.getFollowersForSpecificUser;
import static com.fansunited.automation.validators.ProfileApiValidator.validateGetProfileFollowersResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.apis.profileapi.ProfileEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.profileapi.follow.ProfileFollowersData;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - GET /v1/profile/{userId}/followers endpoint happy path tests")
public class GetSpecificProfileFollowersTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify followers for specific profile are successfully fetched and valid")
  public void getFollowersForSpecificProfile()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int followersCount = Helper.generateRandomNumber(30, 50);

    List<String> followersIdList = generateFollowersForCurrentTestUser(followersCount);

    Response response =
        getFollowersForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    validateGetProfileFollowersResponse(response, followersIdList, -1,
        getCurrentTestUser(), true);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting specific profile followers")
  public void getFollowersForSpecificProfilePaginationParams()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int followersCount = Helper.generateRandomNumber(10, 20);

    final int resultsLimit = 5;

    generateFollowersForCurrentTestUser(followersCount);

    Response response =
        getFollowersForSpecificUser(getCurrentTestUser().getUid(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    ProfileFollowersData profileFollowersData =
        response.as(ProfileFollowersData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.PROFILE_FOLLOWERS_SCHEMA))
        .body("data.size()", equalTo(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(profileFollowersData.getProfileFollowers()
                .get(profileFollowersData.getProfileFollowers().size() - 1)
                .getFollowerId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting specific profile followers")
  public void getSpecificProfileFollowersWithDefaultPaginationLimitParam()
      throws HttpException {

    Response response =
        getFollowersForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting specific profile followers works")
  public void getSpecificProfileFollowersPaginationMoveToNextPage()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    final int followersCount = Helper.generateRandomNumber(30, 50);

    final int resultsLimit = Helper.generateRandomNumber(1, 15);

    List<String> followersIdList = generateFollowersForCurrentTestUser(followersCount);

    Response response =
        getFollowersForSpecificUser(getCurrentTestUser().getUid(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    validateGetProfileFollowersResponse(response, followersIdList,
        resultsLimit,
        getCurrentTestUser(), true);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify /GET /v1/profile/{userId}/followers response returned by the server is cached for 2h")
  public void verifyGetSpecificProfilesFollowersResponseIsCached()
      throws HttpException {

    var response =
        getFollowersForSpecificUser(getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Disabled("Deprecated in FZ-1556")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1556"), @Tag(DISABLED)})
  @DisplayName("Verify /GET /v1/profile/{userId}/followers response contains nickname")
  public void verifyFollowerIsReturnedWithNicknameInTheResponse()
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException,
      HttpException {
    var profileFollower = createUsers(1);

    JSONObject body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(getCurrentTestUser().getUid()));

    var response = followProfilesForUser(profileFollower.get(0).getEmail(), body);

    //Update the nickname of the follower profile:
    body = new JSONObject();
    body.put(NAME_PROP, new Faker().name().fullName());
    body.put(AVATAR_PROP, "https://updated_gravatar.com/avatar/xx5b80de1da869c24cd7603bfb6361a4?s=400&d=robohash&r=x");
    body.put(NICKNAME, new Faker().name().firstName());

    ProfileEndpoint.updateProfileRequest(profileFollower.get(0).getEmail(), body);

    response =
        getFollowersForSpecificUser(getCurrentTestUser().getUid());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.PROFILE_FOLLOWERS_SCHEMA))
        .body("data[0]." + FOLLOWER_ID_PROP, equalTo(profileFollower.get(0).getUid()))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(getCurrentTestUser().getUid()))
        .body("data[0]." + NAME_PROP, equalTo(profileFollower.get(0).getDisplayName()))
        .body("data[0]." + AVATAR_PROP, equalTo(profileFollower.get(0).getPhotoUrl()))
        .body("data[0]." + NICKNAME, equalTo(profileFollower.get(0).getDisplayName()));
  }
}
