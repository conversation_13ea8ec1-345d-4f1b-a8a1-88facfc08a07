package com.fansunited.automation.profileapi.followers;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.ProfileFollowersEndpoint.getCurrentTestUserFollowers;
import static com.fansunited.automation.core.apis.profileapi.ProfileFollowersEndpoint.getUserFollowersWithToken;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - GET /v1/profile/followers validation tests")
public class GetProfileFollowersValidationTests extends ProfileApiBaseTest {

  @ParameterizedTest(name = "Verify own profile followers cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getOwnProfileWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    Response response =
        getCurrentTestUserFollowers(CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify profile followers cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getOwnProfileFollowersWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    Response response =
        getUserFollowersWithToken(argumentsHolder.getJwtToken(),
            UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify own profile followers cannot be fetched with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getOwnProfileFollowersWithInvalidClientId(String clientId) throws HttpException {

    Response response =
        getCurrentTestUserFollowers(clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify own profile followers cannot be fetched with negative or 0(integers) for pagination \"limit\" param. "
          + "\"limit\" param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-2, 0})
  public void getOwnProfileFollowersWithInvalidLimitParam(int limit)
      throws HttpException {

    Response response =
        getCurrentTestUserFollowers(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, limit, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Disabled("WIP FZ-766")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting profile followers data with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getProfileFollowersWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    Response response =
        getCurrentTestUserFollowers(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @DisplayName("Verify profile followers data is still fetched in JSON format if content type is NOT specified")
  public void getProfileFollowersWithoutSpecifyingContentType()
      throws HttpException {

    Response response = getCurrentTestUserFollowers(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        null, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body("data.size()", equalTo(0));
  }
}
