package com.fansunited.automation.profileapi.badges.users;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_ACQUIRED_USERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.exparity.hamcrest.date.LocalDateMatchers.sameOrAfter;
import static org.exparity.hamcrest.date.LocalDateMatchers.sameOrBefore;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.UsersEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.reportingapi.users.registeredusers.UsersPerDateDto;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Map;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/users endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetRegisteredUsersTests extends ReportingApiBaseTest {

  private static Map<LocalDate, Long> dayEventMap;

  private static final LocalDate dayFromDate = LocalDate.now().minusYears(1).minusDays(40);
  private static final LocalDate dayToDate = LocalDate.now().minusDays(40);

  private static final LocalDate weekFromDate = LocalDate.now().minusWeeks(20);
  private static final LocalDate weekToDate = LocalDate.now().minusWeeks(15);

  private static final LocalDate monthFromDate = LocalDate.now().minusMonths(10);
  private static final LocalDate monthToDate = LocalDate.now().minusMonths(5);

  private static final LocalDate yearFromDate = LocalDate.now().minusYears(1).minusDays(40);
  private static final LocalDate yearToDate = LocalDate.now().minusDays(40);

  @BeforeAll
  public static void setup() throws Exception {
    dayEventMap = InsertBigQData.generateRegistrationEventsForPeriod(1, dayFromDate, dayToDate, Periodicity.DAY);
    BigQueryHelper.waitForEventsToBeSaved(10);  // Wait for events to be saved to BigQuery
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by day")
  public void getRegisteredUsersByDay() throws HttpException {

    truncateBigQueryTables();
    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        dayFromDate.toString(), dayToDate.toString(), GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);

    usersPerDateDto.getData().getBreakdown().forEach(dateBreakdownDto ->
        assertThat(dateBreakdownDto.getDate(),
            allOf(sameOrAfter(dayFromDate), sameOrBefore(dayToDate))
        )
    );

    // Validate users per day
    for (var date : usersPerDateDto.getData().getBreakdown()) {
      var expectedUsers = dayEventMap.get(date.getDate());
      assertThat("Users for date " + date.getDate() + " do not match",
          date.getUsers().getAll(), equalTo(expectedUsers));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by week")
  public void getRegisteredUsersByWeek() throws HttpException {

    truncateBigQueryTables();
    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        weekFromDate.toString(), weekToDate.toString(), GroupBy.WEEK.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);

    LocalDate startOfWeekFromDate = weekFromDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    LocalDate startOfWeekToDate = weekToDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

    usersPerDateDto.getData().getBreakdown().forEach(dateBreakdownDto ->
        assertThat("Date " + dateBreakdownDto.getDate() + " is outside the expected weekly range",
            dateBreakdownDto.getDate(),
            allOf(sameOrAfter(startOfWeekFromDate), sameOrBefore(startOfWeekToDate))
        )
    );

    // Validate users per week
    for (var date : usersPerDateDto.getData().getBreakdown()) {
      assertThat("Users for week " + date.getDate() + " do not match",
          date.getUsers().getAll(), equalTo(7L));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by month")
  public void getRegisteredUsersByMonth() throws HttpException {

    truncateBigQueryTables();
    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        monthFromDate.toString(), monthToDate.toString(), GroupBy.MONTH.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);

    LocalDate startOfMonthFromDate = monthFromDate.withDayOfMonth(1);
    LocalDate startOfMonthToDate = monthToDate.withDayOfMonth(1);

    usersPerDateDto.getData().getBreakdown().forEach(dateBreakdownDto ->
        assertThat(
            "Date " + dateBreakdownDto.getDate() + " is outside the valid month range",
            dateBreakdownDto.getDate(),
            allOf(sameOrAfter(startOfMonthFromDate), sameOrBefore(startOfMonthToDate))
        )
    );


    // Validate users per month
    for (var date : usersPerDateDto.getData().getBreakdown()) {
      // Calculate the number of days in the current month
      LocalDate breakdownDate = date.getDate();
      int expectedDaysInMonth = YearMonth.of(breakdownDate.getYear(), breakdownDate.getMonth()).lengthOfMonth();

      // Cast expectedDaysInMonth to long
      assertThat("Users for month " + breakdownDate + " do not match",
          date.getUsers().getAll(), equalTo((long) expectedDaysInMonth));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting registered users grouped by year")
  public void getRegisteredUsersByYear() throws HttpException {

    truncateBigQueryTables();
    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        yearFromDate.toString(), yearToDate.toString(), GroupBy.YEAR.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var usersPerDateDto = response.as(UsersPerDateDto.class);

    LocalDate startOfYearFromDate = yearFromDate.with(TemporalAdjusters.firstDayOfYear());
    LocalDate startOfYearToDate = yearToDate.with(TemporalAdjusters.firstDayOfYear());

    usersPerDateDto.getData().getBreakdown().forEach(dateBreakdownDto ->
        assertThat("Date " + dateBreakdownDto.getDate() + " is outside the expected yearly range",
            dateBreakdownDto.getDate(),
            allOf(sameOrAfter(startOfYearFromDate), sameOrBefore(startOfYearToDate))
        )
    );


    // Validate users per year
    for (var date : usersPerDateDto.getData().getBreakdown()) {
      LocalDate breakdownDate = date.getDate();
      int expectedDaysInYear;

      if (breakdownDate.equals(yearFromDate.withDayOfYear(1))) {
        // First year - count days from yearFromDate to December 31
        expectedDaysInYear = (int) ChronoUnit.DAYS.between(yearFromDate, LocalDate.of(yearFromDate.getYear(), 12, 31).plusDays(1));
      } else if (breakdownDate.equals(yearToDate.withDayOfYear(1))) {
        // Last year - count days from January 1 to yearToDate
        expectedDaysInYear = (int) ChronoUnit.DAYS.between(LocalDate.of(yearToDate.getYear(), 1, 1), yearToDate.plusDays(1));
      } else {
        // Full year in between - use 365 or 366 days based on whether it's a leap year
        expectedDaysInYear = YearMonth.of(breakdownDate.getYear(), 1).lengthOfYear();
      }

      // Assert that the number of users matches the expected days
      assertThat("Users for year " + breakdownDate + " do not match",
          date.getUsers().getAll(), equalTo((long) expectedDaysInYear));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users response returned by the server is NOT cached")
  public void verifyGetRegisteredUsersResponseIsCachedForEightHours()
      throws HttpException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
        GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.EIGHT_HOURS);
  }
}
