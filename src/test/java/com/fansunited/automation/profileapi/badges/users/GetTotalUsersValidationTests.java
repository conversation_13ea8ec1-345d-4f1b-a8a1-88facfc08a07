package com.fansunited.automation.profileapi.badges.users;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_TOTAL_USERS_PER_PERIOD_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.reportingapi.TotalUsersEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.reportingapi.users.totalusers.TotalUsersDto;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDate;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.CoreMatchers;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/users/total endpoint validation tests")
public class GetTotalUsersValidationTests extends ReportingApiBaseTest {

  @ParameterizedTest(name = "Verify total users cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getTotalUsersWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting total users with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getTotalUsersWithInvalidClientId(String clientId) throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(clientId,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(CoreMatchers.is(HttpStatus.SC_BAD_REQUEST), CoreMatchers.is(HttpStatus.SC_FORBIDDEN)))
        .equals(anyOf(CoreMatchers.is(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT),
            CoreMatchers.is(ApiErrorCodes.ClientErrorCodes.FORBIDDEN)));
  }

  @ParameterizedTest(name = "Verify total users cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getTotalUsersWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true,
            argumentsHolder.getJwtToken(), null, null,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users do not have access to /v1/users/total endpoint")
  public void getTotalUsersWithEndUserToken()
      throws IOException, ExecutionException, FirebaseAuthException,
      InterruptedException, HttpException {

    var createUser = AuthBase.createUser();

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(), true, null,
            createUser.getEmail(), FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions do not have access to total users reports")
  public void getTotalUsersByClientWithInsufficientPermissions()
      throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            true, null, BILLING_USER,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting total users with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getTotalUsersWithNonSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting total users with invalid date range")
  public void getTotalUsersWithInvalidDateRange() throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(LocalDate.now().plusDays(7).toString(),
            LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting total users with invalid 'from_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getTotalUsersWithInvalidFromDate(String fromDate)
      throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(fromDate, LocalDate.now().toString());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting total users with invalid 'to_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getTotalUsersWithInvalidToDate(String toDate)
      throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(
            LocalDate.now().minusDays(7).toString(), toDate);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns total users for period -> 1 month subtracted by 'to_date' param, when 'from_date' query param is null or empty"
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getTotalUsersWithEmptyFromDate(String from) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(from, toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOTAL_USERS_PER_PERIOD_SCHEMA));

    var totalUsersDto = response.as(TotalUsersDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        totalUsersDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        totalUsersDto.getMeta().getToDate(), equalTo(toDate));
  }

  @ParameterizedTest(name =
      "Verify API returns total users for period -> from_date to current date - 1 day, when 'to_date' query param is null or empty"
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getTotalUsersWithEmptyToDate(String to) throws HttpException {

    var fromDate = LocalDate.now().minusMonths(2);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(fromDate.toString(), to);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOTAL_USERS_PER_PERIOD_SCHEMA));

    var totalUsersDto = response.as(TotalUsersDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        totalUsersDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        totalUsersDto.getMeta().getToDate(), equalTo(toDate));
  }

  @ParameterizedTest(name =
      "Verify API returns total users for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty"
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getTotalUsersWithEmptyFromToDate(String fromTo) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(fromTo, fromTo);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOTAL_USERS_PER_PERIOD_SCHEMA));

    var totalUsersDto = response.as(TotalUsersDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        totalUsersDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        totalUsersDto.getMeta().getToDate(), equalTo(toDate));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify total users data is still fetched in JSON format if content type is NOT specified")
  public void getTotalUsersWithoutSpecifyingContentType()
      throws HttpException {

    var response = TotalUsersEndpoint.getTotalUsersForPeriod(CLIENT_AUTOMATION_ID,
        LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
        AuthConstants.ENDPOINTS_API_KEY,
        null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
