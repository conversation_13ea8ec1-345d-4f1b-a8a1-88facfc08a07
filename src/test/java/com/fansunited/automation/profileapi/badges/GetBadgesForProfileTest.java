package com.fansunited.automation.profileapi.badges;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.Interest.Football.TEAM;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.setDefaultLoyaltyRewards;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.FeatureType.CLASSIC_QUIZZES;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.FeatureType.DISCUSSION;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.FeatureType.EITHER_OR;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.FeatureType.FOOTBALL;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.FeatureType.PREDICTOR;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.core.apis.minigames.classicquiz.ParticipateQuizEndpoint.participateQuiz;
import static com.fansunited.automation.core.apis.minigames.eitherOr.GetEitherOrByIdEndpoint.getEitherOrById;
import static com.fansunited.automation.core.apis.minigames.eitherOr.ParticipateInTriviaGame.participateInEitherOr;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.classicQuizRequest;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.createTriviaGameForTests;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.eitherOrParticipation;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.participateClassicQuizRequest;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.PostsGenerator.createPublicPosts;
import static com.fansunited.automation.validators.LoyaltyApiValidator.validateStatisticsResponse;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.clientapi.ClientGetBadgesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsByUserIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.Tier;
import com.fansunited.automation.core.apis.profileapi.BadgesByProfileIdEndpoint;
import com.fansunited.automation.core.base.profileapi.BadgeUpdates;
import com.fansunited.automation.core.base.profileapi.BadgesBaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.clientapi.features.response.BadgeRewards;
import com.fansunited.automation.model.clientapi.features.response.BaseBadge;
import com.fansunited.automation.model.loyaltyapi.ProfileBadgesResponse;
import com.fansunited.automation.model.loyaltyapi.activity.request.ClassicQuizRequirements;
import com.fansunited.automation.model.loyaltyapi.activity.request.DiscussionsRequirements;
import com.fansunited.automation.model.loyaltyapi.activity.request.FootballRequirements;
import com.fansunited.automation.model.loyaltyapi.activity.request.PredictorRequirement;
import com.fansunited.automation.model.loyaltyapi.statistics.BreakdownItem;
import com.fansunited.automation.model.loyaltyapi.statistics.StatisticsData;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.utils.JsonReader;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Profile Api - GET /v1/profile/badges/{userId} endpoint happy path tests")
public class GetBadgesForProfileTest extends BadgesBaseTest {
  private static final BadgeRewards badgeRewards = JsonReader.getDefaultBadgesValuesFromJson();
  private static final BadgeUpdates badgeUpdates = new BadgeUpdates();

  // This variable can be removed once the issue with registration points is resolved
  private final int pointsForRegistration = 2;

  @BeforeAll
  public static void resetAllBadges() throws HttpException {
    // Reset all badges requirements as default (by default all badges are "enabled" = false)
    badgeUpdates.resetBadges(badgeRewards);
    setDefaultLoyaltyRewards();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting PREDICTOR badges for user if badge requirements are met")
  public void getPredictorBadges()
      throws InterruptedException, HttpException, IOException, ExecutionException,
      FirebaseAuthException {
    var user = createUser();
    for (int i = 0; i < 3; i++) {
      generateSinglePrediction(user.getEmail(), generateMatch());
      TimeUnit.SECONDS.sleep(5L);
    }

    String uId = user.getUid();

    Response statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, uId);

    currentTestResponse.set(statisticsResponse);
    validateStatisticsResponse(statisticsResponse, uId, 30, Tier.BRONZE);

    final String predictionsSinglePath = "data.predictions.single";
    PredictorRequirement predictorRequirement = new PredictorRequirement();

    predictorRequirement.setPoints(
        statisticsResponse.then().extract().path(predictionsSinglePath + ".points"));
    predictorRequirement.setPredictionsNumber(
        statisticsResponse.then().extract().path(predictionsSinglePath + ".predictions_made"));
    predictorRequirement.setCorrectPredictions(
        statisticsResponse.then().extract().path(predictionsSinglePath + ".correct"));

    var badges = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class).getPredictor();

    var expectedAchievedBadges =
        badges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getPoints() <= predictorRequirement.getPoints()
                        && badge.getRequirements().getPredictionsMade()
                            <= predictorRequirement.getPredictionsNumber()
                        && badge.getRequirements().getCorrectPredictions()
                            <= predictorRequirement.getCorrectPredictions()
                        && badge.getId().startsWith(PREDICTOR.name()))
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(uId)
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .filter(bId -> bId.startsWith(PREDICTOR.name()))
            .toList();

    assertThat(expectedAchievedBadges, containsInAnyOrder(actualAchievedBadges.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting TOP_X badges for user if badge requirements are met")
  public void getTopXBadges()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    var user = createUser();
    generateTopXPrediction(user);
    Response statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());
    currentTestResponse.set(statisticsResponse);
    validateStatisticsResponse(
        statisticsResponse, user.getUid(), 150, Tier.BRONZE);

    verifyBadgesForTopXGames(user, statisticsResponse);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting MACH_QUIZ badges for user if badge requirements are met")
  public void getMatchQuizBadges()
      throws InterruptedException, HttpException, IOException, ExecutionException,
      FirebaseAuthException {
    var privateUser = createUser();
    generateMatchQuizPrediction(privateUser);

    Response statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, privateUser.getUid());

    currentTestResponse.set(statisticsResponse);

    verifyBadgesForMatchQuizGame(privateUser, statisticsResponse);
  }

  // test is disabled, because there are no default discussions badges, so we do not verify anything here
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED)})
  @DisplayName("Verify getting DISCUSSION badges for user if badge requirements are met")
  public void getDiscussionBadges()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var user = createUser();
    createPublicPosts(1, user.getEmail());

    Response statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());
    currentTestResponse.set(statisticsResponse);
    validateStatisticsResponse(
        statisticsResponse, user.getUid(), 74 + pointsForRegistration, Tier.BRONZE);

    DiscussionsRequirements badgeRequirement = new DiscussionsRequirements();

    final String dataDiscussionsPath = "data.discussions.";
    badgeRequirement.setTotalDiscussionPoints(
        statisticsResponse.then().extract().path(dataDiscussionsPath + "total_points"));
    badgeRequirement.setPostsCount(
        statisticsResponse.then().extract().path(dataDiscussionsPath + "posts_made"));
    badgeRequirement.setPostPoints(
        statisticsResponse.then().extract().path(dataDiscussionsPath + "post_points"));
    badgeRequirement.setReactionsCount(
        statisticsResponse.then().extract().path(dataDiscussionsPath + "reaction_count"));

    var badges = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class).getDiscussions();

    var expectedAchievedBadges =
        badges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getPoints() <= badgeRequirement.getPostPoints()
                        && badge.getRequirements().getPostsCount()
                            <= badgeRequirement.getPostsCount()
                        && badge.getRequirements().getReactionsCount()
                            <= badgeRequirement.getReactionsCount()
                        && badge.getRequirements().getTotalDiscussionPoints()
                            <= badgeRequirement.getTotalDiscussionPoints()
                        && badge.getId().startsWith(DISCUSSION.name()))
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(user.getUid())
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .filter(bId -> bId.startsWith(DISCUSSION.name()))
            .toList();

    assertThat(expectedAchievedBadges, containsInAnyOrder(actualAchievedBadges.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting FOOTBALL badges for user if badge requirements are met")
  public void getFootballBadges()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    var user = createUser();
    var match = generateMatch();
    var homeTeamId = match.getHomeTeam().getId();

    // Set entity_id and entity_type for all football badges
    badgeRewards
        .getFootball()
        .forEach(
            badge -> {
              badge.getRequirements().setEntityId(homeTeamId);
              badge.getRequirements().setEntityType(TEAM.getType());
            });
    // Enable all badges for FOOTBALL
    badgeUpdates.enableBadgesForFeature(FOOTBALL, badgeRewards);

    generateSinglePrediction(user.getEmail(), match);

    var statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());
    currentTestResponse.set(statisticsResponse);
    validateStatisticsResponse(
        statisticsResponse, user.getUid(), 10, Tier.BRONZE);

    FootballRequirements badgeRequirement = new FootballRequirements();

    BreakdownItem breakdownItem =
        statisticsResponse
            .as(StatisticsData.class)
            .getData()
            .getPredictions()
            .getSinglePredictionDto()
            .getSinglePredictionBreakdownDto()
            .getItems()
            .get(homeTeamId);

    badgeRequirement.setEntityId(homeTeamId);
    badgeRequirement.setEntityType(breakdownItem.getEntityType());
    badgeRequirement.setPredictionsNumber(breakdownItem.getPredictionsMade());
    badgeRequirement.setCorrectPredictions(breakdownItem.getCorrect());
    badgeRequirement.setPoints(breakdownItem.getPoints());

    var badges = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class).getFootball();
    var expectedAchievedBadges =
        badges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getPoints() <= badgeRequirement.getPoints()
                        && badge.getRequirements().getPredictionsMade()
                            <= badgeRequirement.getPredictionsNumber()
                        && badge.getRequirements().getCorrectPredictions()
                            <= badgeRequirement.getCorrectPredictions()
                        && badge
                            .getRequirements()
                            .getEntityId()
                            .equals(badgeRequirement.getEntityId())
                        && badge
                            .getRequirements()
                            .getEntityType()
                            .equals(badgeRequirement.getEntityType())
                        && badge.getId().startsWith(FOOTBALL.name()))
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(user.getUid())
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .filter(bId -> bId.startsWith(FOOTBALL.name()))
            .toList();

    assertThat(expectedAchievedBadges, containsInAnyOrder(actualAchievedBadges.toArray()));
  }

  @ParameterizedTest(
      name =
          "Verify getting GAMES badges for user if badge requirements are met. Badge for game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(
      value = GameType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getGamesBadges(GameType gameType)
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    var user = createUser();
    Response statisticsResponse;

    switch (gameType) {
      case MATCH_QUIZ -> {
        generateMatchQuizPrediction(user);
        statisticsResponse =
            StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());
        verifyBadgesForMatchQuizGame(user, statisticsResponse);
      }
      case TOP_X -> {
        generateTopXPrediction(user);
        statisticsResponse =
            StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());
        verifyBadgesForTopXGames(user, statisticsResponse);
      }
      default -> throw new IllegalArgumentException("Invalid game type: " + gameType);
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting CLASSIC_QUIZZES badges for user if badge requirements are met")
  public void getClassicQuizzesBadges()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    var user = createUser();
    // Create multiple quizzes
    for (int i = 0; i < 3; i++) {
      String quizId =
          createQuiz(
                  classicQuizRequest(CommonStatus.ACTIVE),
                  CLIENT_AUTOMATION_ID,
                  AuthConstants.ENDPOINTS_API_KEY,
                  ContentType.JSON,
                  FANS_UNITED_CLIENTS,
                  null)
              .path("data.id");

      try {
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            user.getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
      } catch (HttpException e) {
        throw new RuntimeException(e);
      }
      Thread.sleep(1000);
    }

    Response statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());

    currentTestResponse.set(statisticsResponse);

    ClassicQuizRequirements badgeRequirement = new ClassicQuizRequirements();

    badgeRequirement.setParticipationCount(
        statisticsResponse.then().extract().path("data.classic_quizzes.participation_count"));

    var badges = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class).getClassicQuizzes();

    var expectedAchievedBadges =
        badges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getGameParticipationCount()
                            <= badgeRequirement.getParticipationCount()
                        && badge.getId().startsWith(CLASSIC_QUIZZES.name()))
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(user.getUid())
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .filter(bId -> bId.startsWith(CLASSIC_QUIZZES.name()))
            .toList();

    assertThat(expectedAchievedBadges, containsInAnyOrder(actualAchievedBadges.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting EITHER_OR badges for user if badge requirements are met")
  public void setEitherOrBadges()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var user = createUser();
    badgeUpdates.enableBadgesForFeature(EITHER_OR, badgeRewards);

    var trivia_game = createTriviaGameForTests(EitherOrWinningCondition.MORE);

    var email = user.getEmail();

    var participateFirstResponse =
        participateInEitherOr(
            "",
            trivia_game.getData().getId(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    participateFirstResponse.then().assertThat().statusCode(200);

    var answer = participateFirstResponse.jsonPath().get("data.steps[0].option_two.id").toString();
    var pair = participateFirstResponse.jsonPath().get("data.steps[0].pair_id").toString();

    var participateResponse =
        participateInEitherOr(
            eitherOrParticipation(answer, false, pair, 3F),
            trivia_game.getData().getId(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    int biggerOptionId;
    for (int i = 1; i < 45; i++) {
      String optionOneIdStr =
          participateResponse.jsonPath().get("data.steps[" + i + "].option_one.id");
      String optionTwoIdStr =
          participateResponse.jsonPath().get("data.steps[" + i + "].option_two.id");

      int optionOneId = Integer.parseInt(optionOneIdStr);
      int optionTwoId = Integer.parseInt(optionTwoIdStr);

      biggerOptionId = Math.max(optionOneId, optionTwoId);

      participateResponse =
          participateInEitherOr(
              eitherOrParticipation(
                  String.valueOf(biggerOptionId),
                  false,
                  participateResponse.jsonPath().get("data.steps[" + i + "].pair_id"),
                  3F),
              trivia_game.getData().getId(),
              CLIENT_AUTOMATION_ID,
              FANS_UNITED_PROFILE,
              email,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON);
      participateResponse.then().statusCode(200).log().body();
    }

    var getEitherOr =
        getEitherOrById(
            trivia_game.getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    getEitherOr.then().assertThat().log().body().statusCode(200);

    Response statisticsResponse =
        StatisticsByUserIdEndpoint.getStatisticsForUser(CLIENT_AUTOMATION_ID, user.getUid());

    currentTestResponse.set(statisticsResponse);

    ClassicQuizRequirements badgeRequirement = new ClassicQuizRequirements();

    badgeRequirement.setParticipationCount(
        statisticsResponse.then().extract().path("data.either_or.participation_count"));

    var badges = ClientGetBadgesEndpoint.getBadges().as(BadgeRewards.class).getEitherOr();

    var expectedAchievedBadges =
        badges.stream()
            .filter(
                badge ->
                    badge.getRequirements().getGameParticipationCount()
                            <= badgeRequirement.getParticipationCount()
                        && badge.getId().startsWith(EITHER_OR.name()))
            .map(BaseBadge::getId)
            .toList();

    var actualAchievedBadges =
        BadgesByProfileIdEndpoint.getBadgesForProfile(user.getUid())
            .as(ProfileBadgesResponse.class)
            .getBadges()
            .stream()
            .filter(bId -> bId.startsWith(EITHER_OR.name()))
            .toList();

    assertThat(expectedAchievedBadges, containsInAnyOrder(actualAchievedBadges.toArray()));
  }
}
