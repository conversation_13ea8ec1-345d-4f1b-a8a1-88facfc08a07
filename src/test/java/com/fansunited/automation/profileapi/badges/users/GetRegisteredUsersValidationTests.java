package com.fansunited.automation.profileapi.badges.users;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_ACQUIRED_USERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.UsersEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.PeriodGenerator;
import com.fansunited.automation.model.reportingapi.users.registeredusers.DateBreakdownDto;
import com.fansunited.automation.model.reportingapi.users.registeredusers.UsersPerDateDto;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDate;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/users endpoint validation tests")
public class GetRegisteredUsersValidationTests extends ReportingApiBaseTest {

  @ParameterizedTest(name = "Verify registered users cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getRegisteredUsersWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            GroupBy.DAY.getValue(), argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting registered users with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @CsvSource({
      "nonexistingclient123", // INVALID_CLIENT_ID
      ",",                    // null (null is treated as blank in CsvSource)
      "''"                    // empty string
  })
  public void getRegisteredUsersWithInvalidClientId(String clientId) throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(clientId, LocalDate.now().minusDays(30).toString(),
            LocalDate.now().toString(), GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    if (clientId == null) {
      response
          .then()
          .assertThat()
          .statusCode(anyOf(is(HttpStatus.SC_FORBIDDEN), is(HttpStatus.SC_FORBIDDEN)))
          .body("error.status", equalTo("forbidden"));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(anyOf(is(HttpStatus.SC_BAD_REQUEST), is(HttpStatus.SC_NOT_FOUND)))
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT));
    }

  }

  @ParameterizedTest(name = "Verify registered users cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getRegisteredUsersWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            GroupBy.DAY.getValue(), true, argumentsHolder.getJwtToken(), null, null,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users do not have access to /v1/users endpoint")
  public void getRegisteredUsersWithEndUserToken()
      throws IOException, ExecutionException, FirebaseAuthException,
      InterruptedException, HttpException {

    var createUser = AuthBase.createUser();

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            GroupBy.DAY.getValue(), true, null, createUser.getEmail(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions do not have access to registered users reports")
  public void getRegisteredUsersByClientWithInsufficientPermissions()
      throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(), LocalDate.now().toString(),
            GroupBy.DAY.getValue(), true, null, BILLING_USER,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting registered users with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getRegisteredUsersWithNonSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
            GroupBy.DAY.getValue(), AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting registered users with invalid 'group_by' param")
  public void getRegisteredUsersWithInvalidGroupBy() throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(1).toString(),
            LocalDate.now().toString(), "INVALID");

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting registered users with invalid 'from_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getRegisteredUsersWithInvalidFromDate(String fromDate)
      throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID, fromDate, LocalDate.now().toString(),
            GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting registered users with invalid 'to_date' date format. Date: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"01-01-2022", "2022-04-05 00:00"})
  public void getRegisteredUsersWithInvalidToDate(String toDate)
      throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().minusDays(7).toString(), toDate,
            GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name =
      "Verify API returns registered users for period -> 1 month subtracted by 'to_date' param, when 'from_date' query param is null or empty. Grouped by: DAY."
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyFromDateGroupByDay(String from) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now();

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID, from, toDate.toString(),
            GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates = response.as(UsersPerDateDto.class)
        .getData()
        .getBreakdown().stream().map(DateBreakdownDto::getDate).toList();

    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @ParameterizedTest(name =
      "Verify API returns registered users for period -> from_date to current date - 1 day, when 'to_date' query param is null or empty. Grouped by: DAY."
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyToDateGroupByDay(String to) throws HttpException {

    var fromDate = LocalDate.now().minusMonths(1);
    var toDate = LocalDate.now().minusDays(1);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID, fromDate.toString(), to,
            GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates = response.as(UsersPerDateDto.class)
        .getData()
        .getBreakdown().stream().map(DateBreakdownDto::getDate).toList();

    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @ParameterizedTest(name =
      "Verify API returns registered users for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty. Grouped by: DAY. "
          + "Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyFromToDateGroupByDay(String fromTo) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID, fromTo, fromTo,
            GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates = response.as(UsersPerDateDto.class)
        .getData()
        .getBreakdown().stream().map(DateBreakdownDto::getDate).toList();

    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @ParameterizedTest(
      name =
          "Verify API returns registered users for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty. Grouped by: WEEK."
              + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyToDateGroupByWeek(String nullOrEmptyDate)
      throws HttpException {

    // if no dates are specified - reporting api has range of 30 days (which is not 1 month).
    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.WEEK);

    var response =
        UsersEndpoint.getRegisteredUsers(
            CLIENT_AUTOMATION_ID, nullOrEmptyDate, nullOrEmptyDate, GroupBy.WEEK.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates =
        response.as(UsersPerDateDto.class).getData().getBreakdown().stream()
            .map(DateBreakdownDto::getDate)
            .toList();

    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @ParameterizedTest(name =
      "Verify API returns registered users for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty. Grouped by: MONTH."
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyToDateGroupByMonth(String fromTo) throws HttpException {

    var fromDate = LocalDate.now().minusDays(30);
    var toDate = LocalDate.now().minusDays(1);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.MONTH);

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID, fromTo, fromTo,
            GroupBy.MONTH.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates = response.as(UsersPerDateDto.class)
        .getData()
        .getBreakdown().stream().map(DateBreakdownDto::getDate).toList();

    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @ParameterizedTest(name =
      "Verify API returns registered users for period -> 1 month subtracted by current date - 1 day, when 'from_date' and 'to_date' query params are null or empty. Grouped by: YEAR."
          + " Date: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyToDateGroupByYear(String fromTo) throws HttpException {

    // Subtract 1 month and align to the start of the month
    var fromDate = LocalDate.now().minusMonths(1).withDayOfMonth(1);
    var toDate = LocalDate.now().minusDays(1);

    // Ensure the date range starts from at least two years before
    var expectedStartDate = LocalDate.of(fromDate.minusYears(2).getYear(), 1, 1);
    var expectedEndDate = toDate.withMonth(12).withDayOfMonth(31);

    // Generate the expected period dates (2023, 2024, 2025)
    var dates = PeriodGenerator.genPeriod(expectedStartDate, expectedEndDate, Periodicity.YEAR);
    System.out.println("Expected Period Dates: " + dates);  // Debugging output

    // Pass the correct date range to the API
    var response = UsersEndpoint.getRegisteredUsers(
        CLIENT_AUTOMATION_ID,
        expectedStartDate.toString(),
        expectedEndDate.toString(),
        GroupBy.YEAR.getValue()
    );

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates = response.as(UsersPerDateDto.class)
        .getData().getBreakdown().stream().map(DateBreakdownDto::getDate).toList();

    System.out.println("Actual API Response Dates: " + actualDates);  // Debugging output

    // Ensure both lists of dates match
    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting registered users with invalid date range")
  public void getRegisteredUsersWithInvalidDateRange() throws HttpException {

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            LocalDate.now().plusDays(7).toString(), LocalDate.now().toString(),
            GroupBy.DAY.getValue());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns registered users grouped by day, when 'group_by' query param is not specified or empty. Group by: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getRegisteredUsersWithEmptyOrMissingGroupBy(String groupBy) throws HttpException {

    var fromDate = LocalDate.now().minusMonths(1);
    var toDate = LocalDate.now();

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var response =
        UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
            fromDate.toString(), toDate.toString(), groupBy);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_ACQUIRED_USERS_SCHEMA));

    var actualDates = response.as(UsersPerDateDto.class)
        .getData()
        .getBreakdown().stream().map(DateBreakdownDto::getDate).toList();

    MatcherAssert.assertThat("List of breakdown dates is incorrect", actualDates, equalTo(dates));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify registered users data is still fetched in JSON format if content type is NOT specified")
  public void getRegisteredUsersWithoutSpecifyingContentType()
      throws HttpException {

    var response = UsersEndpoint.getRegisteredUsers(CLIENT_AUTOMATION_ID,
        LocalDate.now().minusDays(7).toString(), LocalDate.now().toString(),
        GroupBy.DAY.getValue(), AuthConstants.ENDPOINTS_API_KEY,
        null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
