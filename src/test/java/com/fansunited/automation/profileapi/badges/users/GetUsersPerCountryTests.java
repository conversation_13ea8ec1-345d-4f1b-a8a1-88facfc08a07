package com.fansunited.automation.profileapi.badges.users;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_USERS_PER_COUNTRY_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.profileapi.CountriesEndpoint;
import com.fansunited.automation.core.apis.reportingapi.UsersPerCountryEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.fansunited.automation.model.reportingapi.users.userspercountry.CountryBreakdownDto;
import com.fansunited.automation.model.reportingapi.users.userspercountry.UsersPerCountryDto;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Reporting Api - GET /v1/users/country endpoint happy path tests")
public class GetUsersPerCountryTests extends ReportingApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-772")})
  @DisplayName("Verify getting users per country")
  public void getUsersPerCountry() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(13);
    var toDate = LocalDate.now().minusDays(11);
    var events = InsertBigQData.generateRegistrationEvents(generateRandomNumber(1, 5), fromDate, toDate);

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    currentTestResponse.set(response);

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    events.keySet().forEach(countryId -> {
      var optional = usersPerCountryDto.getData()
          .getBreakdown()
          .stream()
          .filter(countryBreakdownDto -> countryId.equals(countryBreakdownDto.getId()))
          .findFirst();
      if (optional.isEmpty()) {
        throw new AssertionError(
            countryId + " is not present in country breakdown list: " + usersPerCountryDto.getData()
                .getBreakdown()
                .stream()
                .map(CountryBreakdownDto::getId)
                .toList());
      } else {
        MatcherAssert.assertThat("Incorrect users returned by the API for country: " + countryId,
            optional.get().getUsers().getAll(), equalTo(events.get(optional.get().getId())));
      }
    });

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        usersPerCountryDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-784")})
  @DisplayName("Verify events with same profile id are not counted multiple times when getting users per country for period")
  public void getUsersPerCountryDuplicateProfileId() throws Exception {

    var country = "profile:cnt:" + (new Random().nextInt(99999999 - 1000 + 1) + 1000);

    var fromDate = LocalDate.now().minusDays(15);
    var toDate = LocalDate.now().minusDays(13);

    var profileId = UUID.randomUUID().toString();
    var profileCreatedAt = convertLocalDateToDateTime(fromDate);
    var profileUpdatedAt = profileCreatedAt;

    var profile = RegistrationProfile.builder()
        .id(profileId)
        .name(new Faker().funnyName().name())
        .gender("unspecified")
        .country(CountryProfile.builder()
            .id(country)
            .name("Bulgaria")
            .assets(new Country.AssetsFlag(
                "https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
            .build())
        .followersCount(0)
        .followingCount(0)
        .email(new Faker().internet().emailAddress())
        .avatar("http://noavatar.com").build();

    InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, null);
    var eventsCount = 3;
    for (int i = 0; i < eventsCount; i++) {
      // Simulate profile update
      profileUpdatedAt = profileUpdatedAt.plusMinutes(1);
      profile.setUpdatedAt(profileUpdatedAt.format(
          DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
      InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, profileUpdatedAt);
    }

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    currentTestResponse.set(response);

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    var found = usersPerCountryDto.getData()
        .getBreakdown()
        .stream()
        .filter(b -> country.equals(b.getId()))
        .findAny()
        .get();

    // Validate the overall number of users (already provided in the example)
    MatcherAssert.assertThat("API returned incorrect country breakdown list size",
        found.getUsers().getAll(), equalTo(1L));

    // Validate the country id
    MatcherAssert.assertThat("Country ID is incorrect",
        found.getId(), equalTo(country));

    // Validate the country name
    MatcherAssert.assertThat("Country name is incorrect",
        found.getName(), equalTo("Bulgaria"));

    // Validate total male users
    MatcherAssert.assertThat("Total male users is incorrect",
        found.getUsers().getMale(), equalTo(0L));

    // Validate total female users
    MatcherAssert.assertThat("Total female users is incorrect",
        found.getUsers().getFemale(), equalTo(0L));

    // Validate total unspecified users
    MatcherAssert.assertThat("Total unspecified users is incorrect",
        found.getUsers().getUnspecified(), equalTo(1L));
  }

  @Disabled("WIP FZ-3671")
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-784"), @Tag("FZ-3671")})
  @DisplayName("Verify API counts only latest profile's country")
  public void getUsersPerCountryWithUpdatedCountry() throws Exception {

    List<CountryProfile> generatedCountries = new ArrayList<>();  // Keep track of all generated countries

    var country = "profile:cnt:" + (new Random().nextInt(99999999 - 1000 + 1) + 1000);
    var fromDate = LocalDate.now().minusDays(17);
    var toDate = LocalDate.now().minusDays(15);

    var profileId = UUID.randomUUID().toString();
    var profileCreatedAt = convertLocalDateToDateTime(fromDate);
    var profileUpdatedAt = profileCreatedAt;

    var profile = RegistrationProfile.builder()
        .id(profileId)
        .name(new Faker().funnyName().name())
        .gender("unspecified")
        .country(createCountryProfile(country, "Bulgaria"))
        .followersCount(0)
        .followingCount(0)
        .email(new Faker().internet().emailAddress())
        .avatar("http://noavatar.com").build();

    generatedCountries.add(profile.getCountry());  // Store the initial country

    InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, null);

    var eventsCount = 3;
    for (int i = 0; i < eventsCount; i++) {
      // Simulate profile update
      country = "profile:cnt:" + (new Random().nextInt(99999999 - 1000 + 1) + 1000);
      var updatedCountry = createCountryProfile(country, "Bulgaria");
      generatedCountries.add(updatedCountry);  // Store each updated country

      profile.setCountry(updatedCountry);
      profileUpdatedAt = profileUpdatedAt.plusMinutes(1);
      profile.setUpdatedAt(profileUpdatedAt.format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
      InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, profileUpdatedAt);
    }

    BigQueryHelper.waitForEventsToBeSaved(10);  // Wait for events to be saved to BigQuery

    var response = UsersPerCountryEndpoint.getRegisteredUsersPerCountry(fromDate.toString(), toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_USERS_PER_COUNTRY_SCHEMA));

    currentTestResponse.set(response);

    var usersPerCountryDto = response.as(UsersPerCountryDto.class);

    // Validate that only the last country has user data
    CountryBreakdownDto latestCountryBreakdown = usersPerCountryDto.getData().getBreakdown().stream()
        .filter(breakdown -> null != breakdown.getId())
        .filter(breakdown -> breakdown.getId().equals(generatedCountries.get(generatedCountries.size() - 1).getId()))
        .findFirst()
        .orElseThrow(() -> new AssertionError("Latest country profile not found in response"));

    // Validate correct user data for the latest country
    MatcherAssert.assertThat("API returned incorrect country id", latestCountryBreakdown.getId(),
        equalTo(generatedCountries.get(generatedCountries.size() - 1).getId()));
    MatcherAssert.assertThat("API returned incorrect user count for the latest country",
        latestCountryBreakdown.getUsers().getAll(), equalTo(1));

    // Validate that previous countries have no user data
    for (int i = 0; i < generatedCountries.size() - 1; i++) {
      CountryProfile previousCountry = generatedCountries.get(i);
      boolean isPreviousCountryPresent = usersPerCountryDto.getData().getBreakdown().stream()
          .anyMatch(breakdown -> breakdown.getId().equals(previousCountry.getId()) &&
              (breakdown.getUsers().getAll() == 0));

      MatcherAssert.assertThat("Previous country profiles should have no user data", isPreviousCountryPresent,
          equalTo(true));
    }

    // Validate meta fields
    MatcherAssert.assertThat("Meta prop -> from_date is incorrect", usersPerCountryDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect", usersPerCountryDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect", usersPerCountryDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));
  }

  // Utility method to create a CountryProfile
  private CountryProfile createCountryProfile(String countryId, String countryName) {
    return CountryProfile.builder()
        .id(countryId)
        .name(countryName)
        .assets(new Country.AssetsFlag("https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
        .build();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  // this is now cached
  @DisplayName("Verify GET /v1/users/country response returned by the server is cached")
  public void verifyGetRegisteredUsersPerCountryResponseIsCached()
      throws HttpException {

    var response =
        UsersPerCountryEndpoint.getRegisteredUsersPerCountry(
            LocalDate.now().minusDays(1).toString(),
            LocalDate.now().toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateResponseIsCached(response);
  }
}