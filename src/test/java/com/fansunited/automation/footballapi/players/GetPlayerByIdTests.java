package com.fansunited.automation.footballapi.players;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COMPETITIONS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.FU_PLAYER_ID_PREFIX;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAMS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Players.GET_PLAYER_BY_ID_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.PlayerByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.PlayersEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamByIdEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.model.footballapi.teams.Team;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.ArrayList;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Football Api - GET /v1/players/{id} endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPlayerByIdTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify getting player by id for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void getPlayerByIdForLang(UrlParamValues.Language lang) {

    var player = PlayersEndpoint.getRandomPlayerDto();

    var response =
        PlayerByIdEndpoint.getPlayerById(player.getId(), lang.getValue(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    var playerCompetitions = new ArrayList<String>();

    player.getTeams().forEach(team -> playerCompetitions.addAll(
        TeamByIdEndpoint.getTeamById(team.getId())
            .then()
            .extract()
            .body()
            .jsonPath()
            .getList("data." + COMPETITIONS_PROP + "." + ID_PROP)
    ));

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYER_BY_ID_SCHEMA))
        .body("data." + ID_PROP, equalTo(player.getId()))
        .body("data." + ID_PROP, startsWith(FU_PLAYER_ID_PREFIX))
        .body("data." + COUNTRY_PROP + "." + ID_PROP, equalTo(player.getCountry().getId()))
        .body("data." + TEAMS_PROP + "." + ID_PROP,
            containsInAnyOrder(player.getTeams().stream().map(Team::getId).toArray()))
        .body("data." + COMPETITIONS_PROP + "." + ID_PROP,
            containsInAnyOrder(playerCompetitions.toArray()));

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, not(containsCyrillic()))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, not(containsCyrillic()));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, containsCyrillic());
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/players/{id} response returned by the server is cached for 12 hours")
  public void verifyGetPlayerByIdResponseIsCached() {

    var playerId = PlayersEndpoint.getRandomPlayerDto().getId();

    var response = PlayerByIdEndpoint.getPlayerById(playerId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.HALF_DAY);
  }
}
