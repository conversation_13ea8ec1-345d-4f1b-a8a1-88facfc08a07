package com.fansunited.automation.footballapi.matches;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.AWAY_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.HOME_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.CODE_NO_NEXT_MATCH;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.MESSAGE_NO_NEXT_MATCH;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_MATCH_BY_ID_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.footballapi.PlayerByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.PlayerNextMatchEndpoint;
import com.fansunited.automation.core.apis.footballapi.TopPlayersEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.model.footballapi.matches.FootballMatchesData;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Football Api - GET /v1/player/{id}/next-match endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetNextMatchForPlayerTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify getting player next match for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void getPlayerNextMatchForLang(UrlParamValues.Language lang) {

    var player = PlayerByIdEndpoint.getPlayerDto(TopPlayersEndpoint.getRandomTopPlayerId());

    var response =
        PlayerNextMatchEndpoint.getNextMatchForPlayerId(player.getId(), lang.getValue(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    if (response.statusCode() == HttpStatus.SC_NOT_FOUND) {
      response
          .then()
          .assertThat()
          .body("error.status", equalTo(CODE_NO_NEXT_MATCH))
          .body("error.message", equalTo(MESSAGE_NO_NEXT_MATCH));
      return;
    }

    var matchList = new ArrayList<Match>();

    player.getTeams()
        .forEach(team -> {
          var res = MatchesEndpoint.getMatchesForTeamId(team.getId(),
              generateDateTimeInIsoFormat(ZonedDateTime.now()), null,
              UrlParamValues.Language.EN.getValue(), 1,
              -1, ApiConstants.FootballApi.SortField.DATE.getValue(),
              ApiConstants.SortOrder.ASC.getValue());
          if (res.as(FootballMatchesData.class).getData().isEmpty()) {
            return; // No matches for team found, assuming season has ended
          }
          matchList.add(res.as(FootballMatchesData.class).getData().get(0));
        });

    if (matchList.isEmpty()) {
      throw new RuntimeException(
          "Could NOT fetch any future matches for player: " + player.getId());
    }

    var earliestMatch = matchList.stream().min(Comparator.comparing(Match::getKickoffAt)).get();

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCH_BY_ID_SCHEMA))
        .body("data." + ID_PROP, equalTo(earliestMatch.getId()));

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              not(containsCyrillic()))
          .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              not(containsCyrillic()));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              containsCyrillic())
          .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              containsCyrillic());
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/player/{id}/next-match response returned by the server is cached for 1h")
  public void verifyPlayerNextMatchResponseIsCached() {

    var playerId = TopPlayersEndpoint.getRandomTopPlayerId();

    var response =
        PlayerNextMatchEndpoint.getNextMatchForPlayerId(playerId);

    currentTestResponse.set(response);

    if (response.statusCode() == HttpStatus.SC_NOT_FOUND) {

      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_NOT_FOUND)
          .body("error.status", equalTo(CODE_NO_NEXT_MATCH));
      return;
    }

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_HOUR);
  }
}
