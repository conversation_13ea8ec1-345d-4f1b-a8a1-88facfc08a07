package com.fansunited.automation.footballapi.matches;

import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_PLAYER_STATS_FOR_MATCH_BY_ID_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.MATCH_WITH_PLAYER_STATS_DATA;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.footballapi.PlayerStatsEndpoint.getPlayerStats;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Football Api - GET /v1/matches/player_stats endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetMatchPlayerStatsTest extends FootballApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/matches/player_stats happy path")
  public void verifyGetMatchByIdHappyPath() {

    var response =
        getPlayerStats(UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            MATCH_WITH_PLAYER_STATS_DATA);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].match_id", equalTo(MATCH_WITH_PLAYER_STATS_DATA))
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                GET_PLAYER_STATS_FOR_MATCH_BY_ID_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify GET /v1/matches/player_stats response returned by the server is cached for 2 minutes")
  public void verifyGetMatchByIdResponseIsCached() {

    var response =
        getPlayerStats(UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            MATCH_WITH_PLAYER_STATS_DATA);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_MINUTES);
  }
}
