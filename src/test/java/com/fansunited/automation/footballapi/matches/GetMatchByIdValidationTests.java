package com.fansunited.automation.footballapi.matches;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.AWAY_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.HOME_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_MATCH_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.CODE_ID_DOESNT_EXISTS;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_MATCH_BY_ID_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.footballapi.PlayerByIdEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/matches/{id} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetMatchByIdValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify match cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getMatchByIdWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var matchId = MatchesEndpoint.getRandomMatchDto().getId();

    var response = PlayerByIdEndpoint.getPlayerById(matchId, UrlParamValues.Language.EN.getValue(),
        argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns NOT_FOUND when getting match with invalid id")
  public void getMatchWithInvalidId() {

    var response = MatchByIdEndpoint.getMatchById(INVALID_MATCH_ID);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND)
        .body("error.status", equalTo(CODE_ID_DOESNT_EXISTS));
  }

  @ParameterizedTest(name = "Verify match cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getMatchWithInvalidOrEmptyLang(String lang) {

    var response =
        MatchByIdEndpoint.getMatchById(INVALID_MATCH_ID, lang, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);
    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_NOT_FOUND)
          .body("error.status", equalTo(ApiErrorCodes.FootballErrorCodes.CODE_ID_DOESNT_EXISTS));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify match is fetched in EN, if \"lang\" query param is not specified")
  public void getMatchWithoutLanguageParam() {

    var matchId = MatchesEndpoint.getRandomMatchDto().getId();

    var response =
        MatchByIdEndpoint.getMatchById(matchId, null, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCH_BY_ID_SCHEMA))
        .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
            not(containsCyrillic()))
        .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
            not(containsCyrillic()));
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting match with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getMatchWithNotSupportedContentType(ContentType contentType) {

    var matchId = MatchesEndpoint.getRandomMatchDto().getId();

    var response =
        MatchByIdEndpoint.getMatchById(matchId, UrlParamValues.Language.EN.getValue(),
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify match is fetched in JSON format if content type is NOT specified")
  public void getMatchWithoutSpecifyingContentType() {

    var matchId = MatchesEndpoint.getRandomMatchDto().getId();

    var response =
        MatchByIdEndpoint.getMatchById(matchId, UrlParamValues.Language.EN.getValue(),
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCH_BY_ID_SCHEMA));
  }
}
