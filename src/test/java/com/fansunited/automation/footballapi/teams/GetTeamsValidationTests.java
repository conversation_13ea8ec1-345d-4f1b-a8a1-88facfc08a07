package com.fansunited.automation.footballapi.teams;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_ENGLAND_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Teams.GET_TEAMS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/teams endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetTeamsValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify teams cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getTeamsWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var response = TeamsEndpoint.getTeams(UrlParamValues.Language.EN.getValue(),
        argumentsHolder.getApiKey(),
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify teams cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getTeamsWithInvalidOrEmptyLang(String lang) {

    var response =
        TeamsEndpoint.getTeams(lang, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify teams are fetched in EN, if \"lang\" query param is not specified")
  public void getTeamsWithoutLanguageParam() {

    var response =
        TeamsEndpoint.getTeams(null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting teams with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getTeamsWithNotSupportedContentType(ContentType contentType) {

    var response = TeamsEndpoint.getTeams(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify teams are fetched in JSON format if content type is NOT specified")
  public void getTeamsWithoutSpecifyingContentType() {

    var response = TeamsEndpoint.getTeams(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", not(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting teams with invalid 'country' param")
  public void getTeamsWithInvalidCountry() {

    var response =
        TeamsEndpoint.getTeamsWithFilterCountry("fb:c:1519259125912512", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'country' param when empty")
  public void getTeamsWithEmptyCountry() {

    var response = TeamsEndpoint.getTeamsWithFilterCountry("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting teams with invalid 'name' param")
  public void getTeamsWithInvalidName() {

    var response = TeamsEndpoint.getTeamsWithFilterName("INVALID_NAME", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'name' param when empty")
  public void getTeamsWithEmptyName() {

    var response = TeamsEndpoint.getTeamsWithFilterName("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting teams invalid 'gender' param")
  public void getTeamsWithInvalidGender() {

    var response = TeamsEndpoint.getTeamsWithFilterGender("shemale", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'gender' param when empty")
  public void getTeamsWithEmptyGender() {

    var response = TeamsEndpoint.getTeamsWithFilterGender("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting teams with invalid 'scope' param")
  public void getTeamsWithInvalidScope() {

    var response =
        TeamsEndpoint.getTeamsWithFilterScope("fb:c:155125125", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'scope' param when empty")
  public void getTeamsWithEmptyScope() {

    var response = TeamsEndpoint.getTeamsWithFilterScope("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting teams with invalid 'team_ids' param")
  public void getTeamsWithInvalidTeamIds() {

    var response = TeamsEndpoint.getTeamsWithFilterTeamIds(
        "fb:t:5101241204120412412421,fb:t:969129129411241241241124", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'team_ids' param when empty")
  public void getTeamsWithEmptyTeamIds() {

    var response = TeamsEndpoint.getTeamsWithFilterTeamIds("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting teams with invalid 'sort_field' param")
  public void getTeamsWithInvalidSortField() {

    var response =
        TeamsEndpoint.getTeamsWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, "INVALID", null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'sort_field' param when empty")
  public void getTeamsWithEmptySortField() {

    var response =
        TeamsEndpoint.getTeamsWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, "", null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting teams with invalid 'sort_order' param")
  public void getTeamsWithInvalidSortOrder() {

    var response =
        TeamsEndpoint.getTeamsWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, null, "INVALID");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'sort_order' param when empty")
  public void getTeamsWithEmptySortOrder() {

    var response =
        TeamsEndpoint.getTeamsWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, null, "");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));
  }
}
