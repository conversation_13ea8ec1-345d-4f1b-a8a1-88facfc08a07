package com.fansunited.automation.footballapi.search;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COMPETITIONS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PLAYERS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.SearchEntity.commaSeparatedSearchEntities;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAMS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Search.SEARCH_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.Language.EN;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.PlayerByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.SearchEndpoint;
import com.fansunited.automation.core.apis.footballapi.TopPlayersEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.model.footballapi.matches.Competition;
import com.fansunited.automation.model.footballapi.players.Player;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/search endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class SearchEntitiesTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify search by entity -> team for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void searchByEntityTeamForLang(UrlParamValues.Language lang) {

    var teamName = "";

    switch (lang) {
      case EN, RO -> teamName = "Arsenal";
      case SK -> teamName = "Bourgoin Jallieu";
      case BG -> teamName = "Левски";
      case EL -> teamName = "Μπόρνμουθ";
      // TODO Remove default when we have translations for  => SR NL FR HU IT SV ES DE PT
      default -> {teamName = "Arsenal"; lang = EN;}
    }

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.TEAMS.getValue(),
            teamName, null, lang.getValue(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP, hasSize(greaterThan(0)))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(teamName)))
        .body("data." + PLAYERS_PROP, is(empty()))
        .body("data." + COMPETITIONS_PROP, is(empty()));
  }

  @ParameterizedTest(name = "Verify search by entity -> player for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void searchByEntityPlayerForLang(UrlParamValues.Language lang) {

    var playerName = "";

    switch (lang) {
      case EN, RO, PT-> playerName = "ronaldo";
      case SR -> playerName = "Živković";
      case BG -> playerName = "роналдо";
      case EL -> playerName = "Ιωάννης";
      case SK -> playerName = "Marovic";
      case DE -> playerName = "Näsholm";
      // TODO Remove default when we have translations for  => NL FR HU IT SV ES
      default -> {playerName = "ronaldo"; lang = EN;}
    }

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.PLAYERS.getValue(),
            playerName, null, lang.getValue(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + PLAYERS_PROP, hasSize(greaterThan(0)))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(playerName)))
        .body("data." + TEAMS_PROP, is(empty()))
        .body("data." + COMPETITIONS_PROP, is(empty()));
  }

  @ParameterizedTest(name = "Verify search by entity -> competition for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void searchByEntityCompForLang(UrlParamValues.Language lang) {

    var competitionName = "";

    switch (lang) {
      case EN, RO -> competitionName = "premier";
      case BG -> competitionName = "премиър";
      case EL -> competitionName = "Κατηγορία";
      case SK -> competitionName = "Európska liga UEFA";
      // TODO Remove default when we have translations for  => SR NL FR HU IT SV ES SK EL DE PT
      default -> {competitionName = "premier"; lang = EN;}
    }

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.COMPETITIONS.getValue(),
            competitionName, null, lang.getValue(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + COMPETITIONS_PROP, hasSize(greaterThan(0)))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(competitionName)))
        .body("data." + PLAYERS_PROP, is(empty()))
        .body("data." + TEAMS_PROP, is(empty()));
  }

  @ParameterizedTest(name = "Verify search by all entities for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void searchByAllEntitiesForLang(UrlParamValues.Language lang) {

    var searchFilter = "";

    switch (lang) {
      case EN, RO -> searchFilter = "premier";
      case BG -> searchFilter = "Ювентус";
      case EL -> searchFilter = "Βραζιλία";
      //  TODO Remove default when we have translations for  => SR NL FR HU IT SV ES SK EL DE PT
      default -> {searchFilter = "premier"; lang = EN;}
    }

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), searchFilter, null, lang.getValue(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .log().body()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)));

    var teams = response.then().extract().body().jsonPath().getList("data." + TEAMS_PROP);
    var players = response.then().extract().body().jsonPath().getList("data." + PLAYERS_PROP);
    var competitions =
        response.then().extract().body().jsonPath().getList("data." + COMPETITIONS_PROP);

    Assertions.assertTrue(!teams.isEmpty() || !players.isEmpty() || !competitions.isEmpty(),
        "Did not find any teams/players/competitions with name: " + searchFilter);
  }

  @Test
  @Tags({@Tag(DISABLED), @Tag("FZ-1616")})
  @DisplayName("This test should be removed and the tested values should be added to the original test - 'searchByEntityPlayer'")
  public void toBeFixedOne() {
    searchByEntityPlayer("Moham");
  }

  @ParameterizedTest(name = "Verify search by entity -> player works. Player name: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"ronaldo", "Pierre-Emerick", "PETER", "O'Connor"})
  public void searchByEntityPlayer(String playerName) {

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.PLAYERS.getValue(),
            playerName, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + PLAYERS_PROP, hasSize(notNullValue()))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(playerName)))
        .body("data." + COMPETITIONS_PROP, is(empty()))
        .body("data." + TEAMS_PROP, is(empty()));
  }

  @ParameterizedTest(name = "Verify search by entity -> team works. Team name: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"ARSENAL", "pool", "manchester  "})
  public void searchByEntityTeam(String teamName) {

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.TEAMS.getValue(),
            teamName, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP, hasSize(greaterThan(0)))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(teamName.trim())))
        .body("data." + COMPETITIONS_PROP, is(empty()))
        .body("data." + PLAYERS_PROP, is(empty()));
  }

  @ParameterizedTest(name = "Verify search by entity -> competition works. Competition name: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-713")})
  @ValueSource(strings = {"premier", "primeira", "league", "champ"})//Fix wrong data for database
  public void searchByEntityCompetition(String compName) {

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.COMPETITIONS.getValue(),
            compName, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + COMPETITIONS_PROP, hasSize(greaterThan(0)))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(compName)))
        .body("data." + TEAMS_PROP, is(empty()))
        .body("data." + PLAYERS_PROP, is(empty()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify search by entity -> team with scope -> competition")
  public void searchByEntityTeamAndScopeCompetition() {

    var searchFilter = "liverpool";

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.TEAMS.getValue(),
            searchFilter, PREMIER_LEAGUE_COMP_ID);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP, hasSize(1))
        .body("data." + TEAMS_PROP + "." + ID_PROP, equalTo(List.of(TEAM_ID_LIVERPOOL)))
        .body("data." + COMPETITIONS_PROP, is(empty()))
        .body("data." + PLAYERS_PROP, is(empty()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify search by entity -> player with scope -> competition")
  public void searchByEntityPlayerAndScopeCompetition() {

    var player = PlayerByIdEndpoint.getPlayerDto(TopPlayersEndpoint.getRandomTopPlayerId());

    if (player.getCompetitions().isEmpty()) {
      throw new RuntimeException(player.getId() + " is NOT part of any competitions");
    }

    var playerComp = player.getCompetitions().get(0).getId();

    var response =
        SearchEndpoint.search(ApiConstants.FootballApi.SearchEntity.PLAYERS.getValue(),
            player.getName().split(" ")[0], playerComp);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + PLAYERS_PROP, hasSize(greaterThan(0)))
        .body("data." + PLAYERS_PROP + "." + ID_PROP, hasItem(player.getId()))
        .body("data." + COMPETITIONS_PROP, is(empty()))
        .body("data." + TEAMS_PROP, is(empty()));

    var playerIds = new ArrayList<String>(response.then()
        .extract()
        .body()
        .jsonPath()
        .getList("data." + PLAYERS_PROP + "." + ID_PROP));

    var playerDtoList = new ArrayList<Player>();

    playerIds.forEach(playerId -> playerDtoList.add(PlayerByIdEndpoint.getPlayerDto(playerId)));

    playerDtoList.forEach(
        playerDto -> Assertions.assertTrue(playerDto.getCompetitions()
                .stream()
                .map(Competition::getId)
                .toList()
                .contains(playerComp),
            "Player " + playerDto.getId() + " is not part of competition: " + playerComp));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify search by entity -> team and entity -> player works")
  public void searchByEntityTeamAndPlayer() {

    var searchFilter = "Real";

    var searchEntities = ApiConstants.FootballApi.SearchEntity.TEAMS.getValue()
        + ","
        + ApiConstants.FootballApi.SearchEntity.PLAYERS.getValue();

    var response =
        SearchEndpoint.search(searchEntities, searchFilter, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP, hasSize(greaterThan(0)))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            Every.everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + PLAYERS_PROP, hasSize(greaterThan(0)))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            Every.everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + COMPETITIONS_PROP, is(empty()));
  }

  @Test
  @Tags({@Tag(DISABLED), @Tag("FZ-1616")})
  @DisplayName("This test should be removed and the tested values should be added to the original test - 'searchByAllEntities'")
  public void toBeFixedTwo() {
    searchByAllEntities("Champions");
  }

  @ParameterizedTest(name = "Verify search by all entities works. Search filter: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"Juventus", "manchester  ", "Ronaldo", "Premier"})
  public void searchByAllEntities(String searchFilter) {

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), searchFilter, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter.trim())))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter.trim())))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter.trim())));

    var teams = response.then().extract().body().jsonPath().getList("data." + TEAMS_PROP);
    var players = response.then().extract().body().jsonPath().getList("data." + PLAYERS_PROP);
    var competitions =
        response.then().extract().body().jsonPath().getList("data." + COMPETITIONS_PROP);

    Assertions.assertTrue(teams.size() > 0 || players.size() > 0 || competitions.size() > 0,
        "Did not find any teams/players/competitions with name: " + searchFilter);
  }
}
