package com.fansunited.automation.footballapi.search;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COMPETITIONS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PLAYERS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.SearchEntity.commaSeparatedSearchEntities;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAMS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Search.SEARCH_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.INVALID_COMPETITION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.SearchEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/search endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class SearchEntitiesValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when searching entities with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void searchWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var searchFilter = "arsenal";

    var response =
        SearchEndpoint.search(null, searchFilter, null, UrlParamValues.Language.EN.getValue(),
        CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns empty list of {arguments}, when searching for entity: {arguments} with filter that do not match any entities")
  @Tag(REGRESSION)
  @EnumSource(value = ApiConstants.FootballApi.SearchEntity.class)
  public void searchForEntityWithInvalidFilter(ApiConstants.FootballApi.SearchEntity searchEntity) {

    var response = SearchEndpoint.search(searchEntity.getValue(), "INVALID_SEARCH_FILTER", null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + TEAMS_PROP, is(empty()))
        .body("data." + PLAYERS_PROP, is(empty()))
        .body("data." + COMPETITIONS_PROP, is(empty()));
  }

  @ParameterizedTest(name = "Verify API returns empty list of entities, when searching for entity with invalid scope filter. Scope: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {INVALID_COMPETITION_ID, "    "})
  public void searchForEntityWithInvalidFilter(String scope) {

    var searchFilter = "premier";

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), searchFilter, scope);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + TEAMS_PROP, is(empty()))
        .body("data." + PLAYERS_PROP, is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'scope' param when empty")
  public void searchForEntityWithEmptyFilter() {

    var searchFilter = "premier";

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), searchFilter, "");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)));

    var teams = response.then().extract().body().jsonPath().getList("data." + TEAMS_PROP);
    var players = response.then().extract().body().jsonPath().getList("data." + PLAYERS_PROP);
    var competitions =
        response.then().extract().body().jsonPath().getList("data." + COMPETITIONS_PROP);

    Assertions.assertTrue(teams.size() > 0 || players.size() > 0 || competitions.size() > 0,
        "Did not find any teams/players/competitions with name: " + searchFilter);
  }

  @Disabled("WIP FZ-716")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when searching entities with empty, missing or invalid search filter. Filter: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-716")})
  @ValueSource(strings = {"^Terry09-^@#", "@F(@!±#&%&@$@%()^_213", "@3Arsenal@", "    "})
  @NullAndEmptySource
  public void searchEntitiesWithInvalidSearchFilter(String searchFilter) {

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), searchFilter, null,
            UrlParamValues.Language.EN.getValue(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when searching for entities with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void searchEntitiesWithInvalidOrEmptyLang(String lang) {

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), "arsenal", null, lang,
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify search entity results are fetched in EN, if \"lang\" query param is not specified")
  public void searchForEntitiesWithoutLanguageParam() {

    var team = TeamByIdEndpoint.getTeamDto(
        TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID));

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), team.getName().split(" ")[0], null,
            null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            Every.everyItem(containsStringIgnoringCase(team.getName().split(" ")[0])));
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when sending GET request to /v1/search with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void searchWithNotSupportedContentType(ContentType contentType) {

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), "arsenal", null, null,
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify search results are returned in JSON format if content type is NOT specified")
  public void searchWithoutSpecifyingContentType() {

    var response =
        SearchEndpoint.search(commaSeparatedSearchEntities(), "arsenal", null, null,
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA));
  }

  @Disabled("WIP FZ-713")
  @ParameterizedTest(name = "Verify API searches through all entities by default when entity param is missing. Search filter: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(DISABLED), @Tag("FZ-713")})
  @ValueSource(strings = {"Juventus", "Champions", "James", "Premier"})
  public void searchByAllEntities(String searchFilter) {

    var response =
        SearchEndpoint.search(searchFilter, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter.trim())))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter.trim())))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter.trim())));

    var teams = response.then().extract().body().jsonPath().getList("data." + TEAMS_PROP);
    var players = response.then().extract().body().jsonPath().getList("data." + PLAYERS_PROP);
    var competitions =
        response.then().extract().body().jsonPath().getList("data." + COMPETITIONS_PROP);

    Assertions.assertTrue(teams.size() > 0 || players.size() > 0 || competitions.size() > 0,
        "Did not find any teams/players/competitions with name: " + searchFilter);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns list of empty entities when entity param invalid")
  public void searchWithInvalidEntityParam() {

    var searchFilter = "arsenal";

    var response =
        SearchEndpoint.search("INVALID", searchFilter, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP + "." + NAME_PROP, is(empty()))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP, is(empty()))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP, is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'entity' param when empty")
  public void searchWithEmptyEntityParam() {

    var searchFilter = "arsenal";

    var response =
        SearchEndpoint.search("", searchFilter, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(SEARCH_SCHEMA))
        .body("data." + TEAMS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + COMPETITIONS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)))
        .body("data." + PLAYERS_PROP + "." + NAME_PROP,
            everyItem(containsStringIgnoringCase(searchFilter)));

    var teams = response.then().extract().body().jsonPath().getList("data." + TEAMS_PROP);
    var players = response.then().extract().body().jsonPath().getList("data." + PLAYERS_PROP);
    var competitions =
        response.then().extract().body().jsonPath().getList("data." + COMPETITIONS_PROP);

    Assertions.assertTrue(teams.size() > 0 || players.size() > 0 || competitions.size() > 0,
        "Did not find any teams/players/competitions with name: " + searchFilter);
  }
}
