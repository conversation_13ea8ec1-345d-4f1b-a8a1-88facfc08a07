package com.fansunited.automation.footballapi.countries;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.footballapi.FootballCountriesEndpoint.getFootballCountries;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/countries endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetFootballCountriesTests extends FootballApiBaseTest {

  private static final int EXPECTED_COUNTRY_COUNT = 242;

  @ParameterizedTest(name = "Verify list of countries for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"en", "bg", "ro"})
  public void getFootballCountriesForLang(String lang) {

    var response = getFootballCountries(lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Countries.GET_COUNTRIES_SCHEMA))
        .body("data.name", hasSize(greaterThanOrEqualTo(EXPECTED_COUNTRY_COUNT)));

    switch (lang) {
      case "en" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Czech Republic"));
      case "bg" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Чехия"));
      case "ro" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Cehia"));
    }
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/countries response returned by the server is cached for 1 month")
  public void verifyGetFootballCountriesResponseIsCached() {

    var response = getFootballCountries();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_MONTH);
  }
}
