package com.fansunited.automation.footballapi.countries;

import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.core.apis.footballapi.FootballCountriesEndpoint.getFootballCountries;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/countries endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetFootballCountriesValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify countries cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getFootballCountriesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var response =
        getFootballCountries(UrlParamValues.Language.EN.getValue(),
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify countries cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getFootballCountriesWithInvalidOrEmptyLang(String lang) {

    var response =
        getFootballCountries(lang, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", Matchers.is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify countries are fetched in EN, if \"lang\" query param is not specified")
  public void getFootballCountriesWithoutLanguageParam() {

    var response =
        getFootballCountries(null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.name", Matchers.anyOf(hasItem("Aruba")));
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting countries with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getFootballCountriesWithNotSupportedContentType(ContentType contentType) {

    var response = getFootballCountries(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify countries are fetched in JSON format if content type is NOT specified")
  public void getFootballCountriesWithoutSpecifyingContentType() {

    var response = getFootballCountries(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body("data.name", Matchers.anyOf(hasItem("Aruba"), hasItem("Аруба")));
  }
}
