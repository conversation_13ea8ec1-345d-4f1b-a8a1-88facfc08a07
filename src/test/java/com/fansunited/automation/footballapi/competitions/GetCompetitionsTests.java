package com.fansunited.automation.footballapi.competitions;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_ENGLAND_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TYPE_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getCompetitions;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getCompetitionsWithFilters;
import static com.fansunited.automation.core.apis.footballapi.TopCompetitionsEndpoint.getTopCompetitions;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isInAscendingAlphabeticalOrder;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isInDescendingAlphabeticalOrder;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/competitions endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetCompetitionsTests extends FootballApiBaseTest {

  private static final int EXPECTED_COMPETITIONS_COUNT = 556;

  @ParameterizedTest(name = "Verify list of countries for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"en", "bg", "ro", "el", "sk", "pt", "sr", "hu", "sv", "es", "fr", "nl", "de", "it"})
  public void getCompetitionsForLang(String lang) {

    var response = getCompetitions(lang, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data." + NAME_PROP, hasSize(greaterThanOrEqualTo(EXPECTED_COMPETITIONS_COUNT)))
        .body("data." + ID_PROP, isInAscendingAlphabeticalOrder());

    switch (lang) {
      case "en" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Premier League"))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Bulgaria"));
      case "bg" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Премиър лийг"))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("България"));
      case "ro" -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Premier League"))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Ucraina"));
      case "el" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Ελλάδα"));
      case "sk" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Nemecko"));
      case "pt" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Portugal"));
      case "sr" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Serbia"));
      case "hu" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Hungary"));
      case "sv" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Slovenia"));
      case "es" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Spain"));
      case "fr" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("France"));
      case "nl" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Netherlands"));
      case "de" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Germany"));
      case "it" -> response
              .then()
              .assertThat()
              .body("data." + NAME_PROP, hasItem("Premier League"))
              .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Italy"));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be filtered by country")
  public void getCompetitionsByCountry() {

    var response = getCompetitionsWithFilters(COUNTRY_ENGLAND_ID, null, null, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + ID_PROP, everyItem(is(COUNTRY_ENGLAND_ID)));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be filtered by name")
  public void getCompetitionsByName() {

    var competitionName = "premier";

    var response =
        getCompetitionsWithFilters(null, competitionName, null, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, everyItem(containsStringIgnoringCase(competitionName)));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be filtered by gender")
  public void getCompetitionsByGender() {

    var gender = "male";

    var response =
        getCompetitionsWithFilters(null, null, gender, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + GENDER_PROP, everyItem(containsStringIgnoringCase(gender)));
  }

  @ParameterizedTest(name = "Verify competitions can be filtered by type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(ApiConstants.FootballApi.CompetitionType.class)
  public void getCompetitionsByType(ApiConstants.FootballApi.CompetitionType competitionType) {

    var response =
        getCompetitionsWithFilters(null, null, null, competitionType.getValue(), null,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + TYPE_PROP,
            everyItem(containsStringIgnoringCase(competitionType.getValue())));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be filtered by country id, name, gender and type")
  public void getCompetitionsByMultipleFilters() {

    var name = "premier";
    var gender = "male";
    var competitionType =
        ApiConstants.FootballApi.CompetitionType.LEAGUE.getValue();

    var response =
        getCompetitionsWithFilters(COUNTRY_ENGLAND_ID, name, gender, competitionType, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + ID_PROP, everyItem(is(COUNTRY_ENGLAND_ID)))
        .body("data." + NAME_PROP, everyItem(containsStringIgnoringCase(name)))
        .body("data." + GENDER_PROP, everyItem(containsStringIgnoringCase(gender)))
        .body("data." + TYPE_PROP, everyItem(containsStringIgnoringCase(competitionType)));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'id' in ascending order")
  public void getCompetitionsSortedByFieldIdInAscendingOrder() {

    var response =
        getCompetitionsWithFilters(null, null, null, null,
            ApiConstants.FootballApi.SortField.ID.getValue(),
            ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, isInAscendingAlphabeticalOrder());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'id' in descending order")
  public void getCompetitionsSortedByFieldIdInDescendingOrder() {

    var response =
        getCompetitionsWithFilters(COUNTRY_ENGLAND_ID, null, null, null,
            ApiConstants.FootballApi.SortField.ID.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, isInDescendingAlphabeticalOrder());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'name' in ascending order")
  public void getCompetitionsSortedByFieldNameInAscendingOrder() {

    var response =
        getCompetitionsWithFilters(COUNTRY_ENGLAND_ID, null, null, null,
            ApiConstants.FootballApi.SortField.NAME.getValue(),
            ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, isInAscendingAlphabeticalOrder());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'name' in descending order")
  public void getCompetitionsSortedByFieldNameInDescendingOrder() {

    var response =
        getCompetitionsWithFilters(COUNTRY_ENGLAND_ID, null, null, null,
            ApiConstants.FootballApi.SortField.NAME.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, isInDescendingAlphabeticalOrder());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'country' in ascending order")
  public void getCompetitionsSortedByFieldCountryInAscendingOrder() {

    var response =
        getCompetitionsWithFilters(null, null, null, null,
            ApiConstants.FootballApi.SortField.COUNTRY.getValue(),
            ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, isInAscendingAlphabeticalOrder());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'country' in descending order")
  public void getCompetitionsSortedByFieldCountryInDescendingOrder() {

    var response =
        getCompetitionsWithFilters(null, null, null, null,
            ApiConstants.FootballApi.SortField.COUNTRY.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, isInDescendingAlphabeticalOrder());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/countries response returned by the server is cached for 1 month")
  public void verifyGetFootballCountriesResponseIsCached() {

    var response = CompetitionsEndpoint.getCompetitions();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_MONTH);
  }

  @ParameterizedTest(name = "Verify list of top competitions for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  @ValueSource(strings = {"en", "bg", "ro", "el", "sk", "pt", "sr", "hu", "sv", "es", "fr", "nl", "de", "it"})
  public void getTopCompetitionsForLang(String lang) {

    var response = getTopCompetitions(
        lang,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(
                JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA));

    if(lang == null){
      lang="";
    }

    switch (lang) {
      case "en", "" -> response
          .then()
          .assertThat()
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("England"));
      case "bg" -> response
          .then()
          .assertThat()
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Англия"));
      case "ro" -> response
          .then()
          .assertThat()
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, hasItem("Anglia"));
    }
  }
}
