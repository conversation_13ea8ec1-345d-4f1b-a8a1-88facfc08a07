package com.fansunited.automation.footballapi.competitions;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.CODE_ID_DOESNT_EXISTS;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.INVALID_COMPETITION_ID;
import static com.fansunited.automation.core.apis.footballapi.CompetitionByIdEndpoint.getCompetitionById;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/competitions/{id} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetCompetitionByIdValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify competition cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getCompetitionWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var response =
        getCompetitionById(PREMIER_LEAGUE_COMP_ID, UrlParamValues.Language.EN.getValue(),
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns NOT_FOUND when getting competition with invalid id")
  public void getCompetitionWithInvalidCompetitionId() {

    var response = getCompetitionById(INVALID_COMPETITION_ID);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND)
        .body("error.status", equalTo(CODE_ID_DOESNT_EXISTS));
  }

  @ParameterizedTest(name = "Verify competition cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getCompetitionWithInvalidOrEmptyLang(String lang) {

    var response =
        getCompetitionById(PREMIER_LEAGUE_COMP_ID, lang, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", Matchers.is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify competition is fetched in EN, if \"lang\" query param is not specified")
  public void getCompetitionWithoutLanguageParam() {

    var response =
        getCompetitionById(PREMIER_LEAGUE_COMP_ID, null, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, equalTo("England"))
        .body("data." + NAME_PROP, equalTo("Premier League"));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting competition with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getCompetitionWithNotSupportedContentType(ContentType contentType) {

    var response =
        getCompetitionById(PREMIER_LEAGUE_COMP_ID, UrlParamValues.Language.EN.getValue(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify competition is fetched in JSON format if content type is NOT specified")
  public void getCompetitionWithoutSpecifyingContentType() {

    var response =
        getCompetitionById(PREMIER_LEAGUE_COMP_ID, UrlParamValues.Language.EN.getValue(),
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body("data." + NAME_PROP, equalTo("Premier League"));
  }
}
