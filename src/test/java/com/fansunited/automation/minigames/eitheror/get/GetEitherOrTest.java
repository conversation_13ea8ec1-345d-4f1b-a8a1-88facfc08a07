package com.fansunited.automation.minigames.eitheror.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.GetEitherOrEndpoint.getEitherOr;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get trivia games")
@Execution(ExecutionMode.SAME_THREAD)
public class GetEitherOrTest extends MiniGamesApiBaseTest {
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify all Either/Or can be exposed")
  public void getEitherOrHappyPath() throws IllegalArgumentException, HttpException {

    var triviaGame = createTriviaGameForTests(EitherOrWinningCondition.MORE);
    var triviaGameId = triviaGame.getData().getId();
    var response =
        getEitherOr(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id.findAll { it == '" + triviaGameId + "' }", hasSize(1));
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify all Either/Or returns 1h cache")
  public void getEitherOrCache() throws IllegalArgumentException, HttpException {

    var response =
        getEitherOr(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_HOUR);


  }
}
