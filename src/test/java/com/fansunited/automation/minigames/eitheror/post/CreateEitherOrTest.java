package com.fansunited.automation.minigames.eitheror.post;

import static com.fansunited.automation.constants.ApiConstants.AuthRequirement.getRandomAuthRequirementValue;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.CreateEitherOrEndpoint.createEitherOr;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.RelatedFieldGenerator.getAllEntityTypeRelationships;
import static com.fansunited.automation.model.CommonStatus.INACTIVE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.helpers.RequestGenerator;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.loyaltyapi.activity.request.Content;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.ImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrOption;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrPoint;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.model.minigamesapi.eitheror.request.EitherOrRequest;
import com.fansunited.automation.model.minigamesapi.eitheror.response.EitherOrResponse;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Create Either/Or happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateEitherOrTest extends MiniGamesApiBaseTest {

  private final RequestGenerator requestGenerator = new RequestGenerator();

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create Either/Or works")
  public void createTriviaGameTest()
      throws IllegalArgumentException,HttpException{

    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();
    var campaignId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(new Faker().howIMetYourMother().quote());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var campaign = new Campaign();
    campaign.setId(campaignId);
    campaign.setLabel("Carlsberg 2022");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);

    var createRequest =
        EitherOrRequest.builder()
            .title("Only for test")
            .description("Just testing")
            .flags(List.of("flag1", "flag2"))
            .images(ImagesDto.builder().main("test.jpg").mobile("test.jpg").cover("test").build())
            .status(String.valueOf(INACTIVE))
            .authRequirement(getRandomAuthRequirementValue())
            .winning_condition(EitherOrWinningCondition.MORE)
            .branding(BrandingDTO.builder()
                .colors(BrandingColorsDTO.builder()
                    .additionalColor("test")
                    .backgroundColor("")
                    .primaryColor("")
                    .contentColor("")
                    .borderColor("")
                    .secondaryColor("")
                    .build())
                .urls(BrandingUrlsDTO.builder()
                    .primaryUrl("тест")
                    .privacyPolicyUrl("тест")
                    .additionalUrl("тест")
                    .build())
                .images(BrandingImagesDTO.builder()
                    .additionalImage("тест")
                    .backgroundImage("тест")
                    .mainLogo("тест")
                    .mobileBackgroundImage("тест")
                    .build())
                .build())
            .question("How are you@")
            .context(context)
            .points(
                (List.of(
                    EitherOrPoint.builder().correct_steps(0).score(5).build(),
                    EitherOrPoint.builder().correct_steps(5).score(10).build(),
                    EitherOrPoint.builder().correct_steps(10).score(20).build())))
            .options(
                List.of(
                    (EitherOrOption.builder()
                        .id("1")
                        .label("label1")
                        .images(GameImagesDto.builder().main("soso.jpg").mobile("sad.jpg").build())
                        .value(1)
                        .build()),
                    (EitherOrOption.builder()
                        .id("2")
                        .label("label2")
                        .value(2)
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("3")
                        .value(3)
                        .label("label3")
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("4")
                        .value(5)
                        .label("label4")
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("5")
                        .value(5)
                        .label("label5")
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build())))
            .lives(3)
            .rules("no bugs")
            .time(3)
            .build();

    var createTriviaGameResponse =
        createEitherOr(
            createRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    createTriviaGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.title", equalTo(createRequest.getTitle()))
        .body("data.description", equalTo(createRequest.getDescription()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Create Either/Or with configured related field")
  public void createEitherOrWithConfiguredRelatedFieldTest() throws HttpException {

    var request = requestGenerator.getEitherOrRequest();
    List<RelatedDto> relatedDtoList = getAllEntityTypeRelationships();
    // Set related field and send the request
    request.setRelated(relatedDtoList);

    var response =
        createEitherOr(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response.then().assertThat().statusCode(SC_OK);

    var actualRelatedList = response.as(EitherOrResponse.class).getData().getRelated();
    assertThat(actualRelatedList)
        .hasSize(relatedDtoList.size())
        .containsExactlyInAnyOrderElementsOf(relatedDtoList);
  }
}
