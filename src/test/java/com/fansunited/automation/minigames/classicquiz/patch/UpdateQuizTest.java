package com.fansunited.automation.minigames.classicquiz.patch;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.UpdateQuizEndpoint.updateQuiz;
import static com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest.createQuizForTest;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.loyaltyapi.activity.request.Content;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.ImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizOptionsUpdateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizQuestionsUpdateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizUpdateRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Update quiz happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateQuizTest extends BaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify update classic quiz works")
  public void updateQuizTest() throws IllegalArgumentException, HttpException {

    var quizId = createQuizForTest(CommonStatus.ACTIVE).getData().getId();

    var tags = List.of(com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
        .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
        .type(ApiConstants.ProfileApi.Interest.Football.TEAM.getType())
        .id(TEAM_ID_LIVERPOOL).build());

    var contentId = UUID.randomUUID().toString();
    var campaignId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(new Faker().howIMetYourMother().quote());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var campaign = new Campaign();
    campaign.setId(campaignId);
    campaign.setLabel("Carlsberg 2022");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);

    var updateQuizRequest = ClassicQuizUpdateRequest.builder()
        .title("The best Classic Quiz")
        .language("fr")
        .alternative_title("tester")
        .branding(BrandingDTO.builder()
        .colors(BrandingColorsDTO.builder()
            .additionalColor("test")
            .backgroundColor("")
            .primaryColor("")
            .contentColor("")
            .borderColor("")
            .secondaryColor("")
            .build())
        .urls(BrandingUrlsDTO.builder()
            .primaryUrl("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .privacyPolicyUrl("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .primaryUrl("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .additionalUrl("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .build())
        .images(BrandingImagesDTO.builder()
            .additionalImage("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .backgroundImage("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .mainLogo("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .mobileBackgroundImage("https://bitbucket.org/fansunitedplatform/%7B08c4f586-eceb-4583-b74a-6972ee2b525c%7D/pull-requests/56/diff#chg-src/main/java/com/fansunited/minigames/dto/classicquiz/request/ClassicQuizRequest.java")
            .build())
        .build())
        .context(context)
        .status(CommonStatus.EXPIRED)
        .description("Only for tester")
        .flags(List.of("Premier League", "Classic Quiz"))
        .images(ImagesDto.builder()
            .main("test3.jpg")
            .mobile("test2.jpg")
            .cover("test1.jpg")
            .build())
        .questions(List.of(ClassicQuizQuestionsUpdateRequest.builder()
            .questionId(1)
            .question("How are you?")
            .options(List.of(
                ClassicQuizOptionsUpdateRequest.builder()
                    .optionId(1)
                    .option("Sad!")
                    .correct(true)
                    .images(GameImagesDto.builder()
                        .main("so_so.jpg")
                        .mobile("sad.jpg")
                        .build())
                    .build(),
                ClassicQuizOptionsUpdateRequest.builder()
                    .optionId(2)
                    .option("Very good!")
                    .correct(false)
                    .images((GameImagesDto.builder()
                        .main("happy.jpg")
                        .mobile("sad.jpg")
                        .build()))
                    .build()))
            .build()))
        .build();

    var response = updateQuiz(quizId, updateQuizRequest, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    response
        .then()
        .assertThat()
        .statusCode(org.apache.http.HttpStatus.SC_OK);
  }
}
