package com.fansunited.automation.minigames.classicquiz.participation;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizById.getQuizById;
import static com.fansunited.automation.core.apis.minigames.classicquiz.ParticipateQuizEndpoint.participateQuiz;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Participate to quiz happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class ParticipateQuizTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify participation works")
  public void participateQuizTest() throws IllegalArgumentException, HttpException,
      InterruptedException {

    var quizResponse =
        createQuiz(classicQuizRequest(CommonStatus.ACTIVE), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    quizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var quizId = quizResponse.body().jsonPath().get("data.id").toString();

    var response =
        participateQuiz(participateClassicQuizRequest(1,1,2,1), quizId, CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.classic_quiz_id", equalTo(quizId))
        .body("data.user_id", equalTo(getCurrentTestUser().getUid()))
        .body("data.questions[0].correct", equalTo(true))
        .body("data.questions[1].correct", equalTo(true))
        .body("data.personal_best", equalTo(2));

    var getQuizResponse = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(), AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getQuizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id", equalTo(quizId))
        .body("data.participation_count", equalTo(1));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify participation works and calculate correctly average and correct scores")
  public void averageAndCorrectScoresForQuiz() throws IllegalArgumentException, HttpException, IOException,
      ExecutionException, FirebaseAuthException, InterruptedException {

    int participantCount = 3;
    var participants = createUsers(participantCount);

    var quizResponse =
        createQuiz(classicQuizRequest(CommonStatus.ACTIVE), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    quizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var quizId = quizResponse.body().jsonPath().get("data.id").toString();

    for (int i = 0; i < participantCount; i++) {
      var participateQuizResponse = participateQuiz(
          participateClassicQuizRequest(1, 1, 2, i == 0 ? 1 : 2),
          quizId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_PROFILE,
          participants.get(i).getEmail(),
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON
      );

      participateQuizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);}


    var getQuizResponse = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getQuizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id", equalTo(quizId))
        .body("data.perfect_score",equalTo(1))
        .body("data.average_score",equalTo(1.3F))
        .body("data.participation_count", equalTo(participantCount));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify participation works and calculate correctly average and correct scores for all correct scores")
  public void averageForAllCorrectScoresForQuiz() throws IllegalArgumentException, HttpException, IOException,
      ExecutionException, FirebaseAuthException, InterruptedException {

    int participantCount = 3;
    var participants = createUsers(participantCount);

    var quizResponse =
        createQuiz(classicQuizRequest(CommonStatus.ACTIVE), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    quizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var quizId = quizResponse.body().jsonPath().get("data.id").toString();

    for (int i = 0; i < participantCount; i++) {
      var participateQuizResponse = participateQuiz(
          participateClassicQuizRequest(1, 1, 2,1),
          quizId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_PROFILE,
          participants.get(i).getEmail(),
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON
      );

      participateQuizResponse
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK);}


    var getQuizResponse = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getQuizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id", equalTo(quizId))
        .body("data.perfect_score",equalTo(3))
        .body("data.average_score",equalTo(2.0F))
        .body("data.participation_count", equalTo(participantCount));
  }


  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify participation works and calculate correctly average and correct scores for all correct scores")
  public void averageForAllWrongScoresForQuiz() throws IllegalArgumentException, HttpException, IOException,
      ExecutionException, FirebaseAuthException, InterruptedException {

    int participantCount = 30;
    var participants = createUsers(participantCount);

    var quizResponse =
        createQuiz(classicQuizRequest(CommonStatus.ACTIVE), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    quizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var quizId = quizResponse.body().jsonPath().get("data.id").toString();

    for (int i = 0; i < participantCount; i++) {
      var participateQuizResponse = participateQuiz(
          participateClassicQuizRequest(1, 2, 2,2),
          quizId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_PROFILE,
          participants.get(i).getEmail(),
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON
      );

      participateQuizResponse
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK);}


    var getQuizResponse = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getQuizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id", equalTo(quizId))
        .body("data.perfect_score",equalTo(0))
        .body("data.average_score",equalTo(0.0F))
        .body("data.participation_count", equalTo(participantCount));
  }
}
