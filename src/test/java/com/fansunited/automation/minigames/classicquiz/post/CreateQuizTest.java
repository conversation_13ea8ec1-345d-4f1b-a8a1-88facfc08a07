package com.fansunited.automation.minigames.classicquiz.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.RelatedFieldGenerator.getAllEntityTypeRelationships;
import static org.apache.http.HttpStatus.SC_OK;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.helpers.RequestGenerator;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.response.ClassicQuizResponse;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Create quiz happy path.")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateQuizTest extends MiniGamesApiBaseTest {

  private final RequestGenerator requestGenerator = new RequestGenerator();

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create classic quiz works")
  public void createQuizTest() throws IllegalArgumentException, HttpException {
    var createQuizRequest = requestGenerator.getClassicQuizRequest();

    createQuizRequest.getBranding().getUrls().setPrimaryUrl("ТЕСТ");
    createQuizRequest.getBranding().getUrls().setAdditionalUrl("ТЕСТ");
    createQuizRequest.getBranding().getUrls().setSecondaryUrl("ТЕСТ");

    createQuizRequest.getBranding().getImages().setAdditionalImage("ТЕСТ");
    createQuizRequest.getBranding().getImages().setBackgroundImage("ТЕСТ");
    createQuizRequest.getBranding().getImages().setMobileBackgroundImage("ТЕСТ");
    createQuizRequest.getBranding().getImages().setMainLogo("ТЕСТ");
    createQuizRequest.getBranding().getImages().setMobileLogo("ТЕСТ");

    var response =
        createQuiz(
            createQuizRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response
        .then()
        .assertThat()
        .statusCode(SC_OK)
        .body("data.labels.label1", equalTo(createQuizRequest.getLabels().getLabel1()))
        .body("data.custom_fields.label1", equalTo(createQuizRequest.getCustomFields().getLabel1()))
        .body("data.time", equalTo(5))
        .body("data.title", equalTo(createQuizRequest.getTitle()))
        .body("data.description", equalTo(createQuizRequest.getDescription()))
        .body("data.ad_content", equalTo(createQuizRequest.getAdContent()))
        .body("data.status", equalTo(createQuizRequest.getStatus().toString()))
        .body("data.language", equalTo(createQuizRequest.getLanguage()))
        .body("data.alternative_title", equalTo(createQuizRequest.getAlternative_title()))
        .body(
            "data.questions[0].question",
            equalTo(createQuizRequest.getQuestions().get(0).getQuestion()))
        .body(
            "data.questions[0].options[0].option",
            equalTo(createQuizRequest.getQuestions().get(0).getOptions().get(0).getOption()))
        .body(
            "data.questions[0].options[0].correct",
            equalTo(createQuizRequest.getQuestions().get(0).getOptions().get(0).isCorrect()))
        .body(
            "data.questions[0].options[0].option",
            equalTo(createQuizRequest.getQuestions().get(0).getOptions().get(0).getOption()))
        .body(
            "data.questions[0].options[1].correct",
            equalTo(createQuizRequest.getQuestions().get(0).getOptions().get(1).isCorrect()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Create quiz with configured related field")
  public void createQuizWithConfiguredRelatedField() throws HttpException {

    var request = requestGenerator.getClassicQuizRequest();
    List<RelatedDto> relatedDtoList = getAllEntityTypeRelationships();

    // Set related field and send the request
    request.setRelated(relatedDtoList);

    var response =
        createQuiz(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response.then().assertThat().statusCode(SC_OK);

    var actualRelatedList = response.as(ClassicQuizResponse.class).getData().getRelated();
    assertThat(actualRelatedList)
        .hasSize(relatedDtoList.size())
        .containsExactlyInAnyOrderElementsOf(relatedDtoList);
  }
}
