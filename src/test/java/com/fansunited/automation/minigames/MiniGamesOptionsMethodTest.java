package com.fansunited.automation.minigames;

import static com.fansunited.automation.constants.Endpoints.MiniGamesApi.CREATE_QUIZ;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.minigames.OptionsMiniGamesEndpoint.optionsMiniGamesApi;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.base.AuthBase;
import io.restassured.response.Response;
import java.util.Arrays;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("MiniGames API - OPTIONS method")
@Execution(ExecutionMode.SAME_THREAD)
public class MiniGamesOptionsMethodTest extends AuthBase {

  @Test
  @DisplayName(
      "Verify MiniGames API using the OPTIONS method. Endpoint: OPTIONS /v1/classic-quizzes")
  @Tag(SMOKE)
  public void optionsMethodMiniGamesTest() throws HttpException {
    Response response = optionsMiniGamesApi(CREATE_QUIZ);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var actualMethods =
        Arrays.stream(response.getHeaders().getValues("Allow").get(0).split(", ")).toList();
    var expectedMethods = ApiConstants.HttpMethods.getValuesAsList();

    assertThat(actualMethods).as(EMPTY_LIST_MESSAGE).isNotEmpty().isNotNull();
    Assertions.assertTrue(expectedMethods.containsAll(actualMethods), METHODS_MISMATCH_MESSAGE);
  }
}
