package com.fansunited.automation.selenium;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Basic Selenium WebDriver test to verify installation and functionality.
 */
@DisplayName("Selenium WebDriver Basic Tests")
public class SeleniumBasicTest {

    private WebDriver driver;
    private WebDriverWait wait;

    @BeforeEach
    void setUp() {
        // Setup ChromeDriver using WebDriverManager (automatically downloads driver)
        WebDriverManager.chromedriver().setup();
        
        // Configure Chrome options
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless"); // Run in headless mode for CI/CD
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--disable-gpu");
        options.addArguments("--window-size=1920,1080");
        
        // Initialize WebDriver
        driver = new ChromeDriver(options);
        
        // Initialize WebDriverWait
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        
        System.out.println("✅ Chrome WebDriver initialized successfully");
    }

    @AfterEach
    void tearDown() {
        if (driver != null) {
            driver.quit();
            System.out.println("✅ WebDriver closed successfully");
        }
    }

    @Test
    @DisplayName("Verify Google search functionality")
    void testGoogleSearch() {
        // Navigate to Google
        driver.get("https://www.google.com");
        System.out.println("📍 Navigated to Google");

        // Find search box and enter search term
        WebElement searchBox = wait.until(
            ExpectedConditions.elementToBeClickable(By.name("q"))
        );
        
        searchBox.sendKeys("Selenium WebDriver");
        searchBox.submit();
        System.out.println("🔍 Performed search for 'Selenium WebDriver'");

        // Wait for results and verify
        wait.until(ExpectedConditions.titleContains("Selenium WebDriver"));
        
        String pageTitle = driver.getTitle();
        assertTrue(pageTitle.contains("Selenium WebDriver"), 
                  "Page title should contain 'Selenium WebDriver'");
        
        System.out.println("✅ Search results verified successfully");
        System.out.println("📄 Page title: " + pageTitle);
    }

    @Test
    @DisplayName("Verify basic page navigation")
    void testPageNavigation() {
        // Navigate to a test page
        driver.get("https://example.com");
        System.out.println("📍 Navigated to example.com");

        // Verify page title
        String expectedTitle = "Example Domain";
        wait.until(ExpectedConditions.titleIs(expectedTitle));
        
        String actualTitle = driver.getTitle();
        assertTrue(actualTitle.equals(expectedTitle), 
                  "Page title should be '" + expectedTitle + "'");
        
        System.out.println("✅ Page navigation verified successfully");
        System.out.println("📄 Page title: " + actualTitle);
    }

    @Test
    @DisplayName("Verify element interaction")
    void testElementInteraction() {
        // Navigate to a form page
        driver.get("https://httpbin.org/forms/post");
        System.out.println("📍 Navigated to form page");

        // Find and interact with form elements
        WebElement customerNameField = wait.until(
            ExpectedConditions.elementToBeClickable(By.name("custname"))
        );
        
        customerNameField.clear();
        customerNameField.sendKeys("Test Customer");
        
        WebElement emailField = driver.findElement(By.name("custemail"));
        emailField.clear();
        emailField.sendKeys("<EMAIL>");
        
        System.out.println("✅ Form fields filled successfully");
        
        // Verify the values were entered
        String nameValue = customerNameField.getAttribute("value");
        String emailValue = emailField.getAttribute("value");
        
        assertTrue(nameValue.equals("Test Customer"), 
                  "Customer name should be 'Test Customer'");
        assertTrue(emailValue.equals("<EMAIL>"), 
                  "Email should be '<EMAIL>'");
        
        System.out.println("✅ Element interaction verified successfully");
    }
}
