package com.fansunited.automation.voting.poll.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.GetVotingByIdEndpoint.getPollById;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static com.fansunited.automation.validators.VotingApiValidator.verifyVotingForAPollResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Vote for a poll's option by poll id happy path PUT /v1/polls/{poll_id}/votes")
@Execution(ExecutionMode.SAME_THREAD)
public class VoteForAPollTests extends VotingApiPollBaseTest {

  private Poll poll;

  @BeforeEach
  public void setUp() {
    var pollResponse = createPollForTest();
    poll = pollResponse.as(Poll.class);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify voting for a poll's option by poll id works")
  public void voteForAPollTest() throws HttpException {
    VotingForPollOptionRequest voteRequest =
        VotingForPollOptionRequest.builder()
            .optionId(poll.getData().getOptions().get(0).getId())
            .build();
    String pollId = poll.getData().getId();

    var response =
        voteForeAPoll(
            pollId,
            voteRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    verifyVotingForAPollResponse(response, poll.getData(), 0, getCurrentTestUser().getUid());
    getPollById(pollId)
        .then()
        .assertThat()
        .statusCode(200)
        .body("data.options[0].votes", is(equalTo(1)));
  }
}
