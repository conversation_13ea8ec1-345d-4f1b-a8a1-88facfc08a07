package com.fansunited.automation.voting.poll.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.DeletePollEndpoint.deletePoll;
import static com.fansunited.automation.core.apis.voting.poll.GetVotingByIdEndpoint.getPollById;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class DeletePollTests extends VotingApiPollBaseTest {

  @Test
  @DisplayName("Delete poll successfully")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void deletePollSuccessfully() throws HttpException {
    String pollId =
        createPollForTest()
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_OK)
            .extract()
            .body()
            .jsonPath()
            .get("data.id");

    var response =
        deletePoll(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            true);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    // Verify poll is deleted
    getPollById(pollId).then().assertThat().statusCode(HttpStatus.SC_NOT_FOUND);
  }
}
