package com.fansunited.automation.voting.poll.get;

import static com.fansunited.automation.constants.ApiConstants.DEFAULT_PAGE_LIMIT;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.GetPollsEndpoint.getPolls;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import io.restassured.http.ContentType;
import java.util.Arrays;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Get Polls validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetAllPollsValidationTests extends VotingApiPollBaseTest {

  public static final List<String> flags = List.of(PREMIER_LEAGUE, TOP_SCORERS);

  @BeforeAll
  public static void setPolls() throws HttpException {
    PollRequest pollRequest = PollRequest.createPollRequestWithRandomData();
    for (int i = 0; i < 13; i++) {
      if (i % 2 == 0) {
        pollRequest.setStatus(CommonStatus.INACTIVE);
        pollRequest.setFlags(Arrays.asList("test1", "test2"));
        pollRequest.getContext().getTags().get(0).setId("tag:id:1");
        pollRequest.getContext().getTags().get(1).setId("tag:id:2");
        pollRequest.setFlags(List.of("flag1", "flag2"));

      } else {
        pollRequest.setStatus(CommonStatus.ACTIVE);
        pollRequest.getContext().getTags().get(0).setId(PREMIER_LEAGUE_COMP_ID);
        pollRequest.getContext().getTags().get(1).setId(BUNDESLIGA_COMP_ID);
        pollRequest.setFlags(flags);
      }
      createCustomPoll(pollRequest);
    }
  }

  @ParameterizedTest(
      name =
          "Verify Polls cannot be fetched with missing/invalid client ID: Client ID: {arguments}")
  @Tag(SMOKE)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getPollWithInvalidClientIdTest(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        getPolls(
            null,
            null,
            null,
            DEFAULT_PAGE_LIMIT,
            0,
            null,
            null,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail());

    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(invalidClientIdHolder.errorStatus()));
  }

  @EnabledIf("isUseStageEnvironment")
  @ParameterizedTest(
          name =
                  "Verify Poll cannot be fetched with an invalid or missing API key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getPollsWithInvalidApiKeyTest(
          InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
          throws IllegalArgumentException, HttpException {

    var response =
            getPolls(
                    null,
                    null,
                    null,
                    DEFAULT_PAGE_LIMIT,
                    0,
                    null,
                    null,
                    AuthConstants.ENDPOINTS_API_KEY,
                    argumentsHolder.getApiKey(),
                    ContentType.JSON,
                    FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                    getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
          name = "Verify Polls cannot be fetched with missing/non supported Content Type. ContentType: {arguments}")
  @Tag(SMOKE)
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void getPollsWithInvalidContentTypeTest(
          InvalidContentTypesHolder invalidContentTypesHolder) throws HttpException {

    var response =
            getPolls(
                    null,
                    null,
                    null,
                    DEFAULT_PAGE_LIMIT,
                    0,
                    null,
                    null,
                    CLIENT_AUTOMATION_ID,
                    AuthConstants.ENDPOINTS_API_KEY,
                    invalidContentTypesHolder.contentType(),
                    FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                    getCurrentTestUser().getEmail());

    currentTestResponse.set(response);
    response
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
            .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(name = "Verify Poll cannot be fetched with an invalid limit parameter.  Limit: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(ints = {0, -1})
  public void getPollsWithInvalidLimitTest(int invalidLimit)
          throws IllegalArgumentException, HttpException {

    var response =
            getPolls(
                    null,
                    null,
                    null,
                    invalidLimit,
                    0,
                    null,
                    null,
                    CLIENT_AUTOMATION_ID,
                    AuthConstants.ENDPOINTS_API_KEY,
                    ContentType.JSON,
                    FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                    getCurrentTestUser().getEmail());


    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }
}
