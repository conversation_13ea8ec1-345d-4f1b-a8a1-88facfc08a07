package com.fansunited.automation.voting.poll.delete;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.DeletePollEndpoint.deletePoll;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("DELETE /v1/polls/{poll_id} Delete poll validation tests")
public class DeletePollValidationTests extends VotingApiPollBaseTest {
  private String pollId;

  @BeforeEach
  public void setUp() {
    pollId = createPollForTest().then().extract().body().jsonPath().get("data.id");
  }

  @ParameterizedTest(
      name = "Verify poll cannot be deleted with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void deletePollWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        deletePoll(
            pollId,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            true);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify Polls cannot be deleted with missing/non supported Content Type. ContentType: {arguments}")
  @Tag(SMOKE)
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void deletePollsWithInvalidContentTypeTest(
      InvalidContentTypesHolder invalidContentTypesHolder) throws HttpException {

    var response =
        deletePoll(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            invalidContentTypesHolder.contentType(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            true);

    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @Test
  @DisplayName("Verify poll cannot be deleted by ordinary user")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyPollCannotBeDeletedByOrdinaryUser() throws HttpException {

    var response =
        deletePoll(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_FORBIDDEN);
  }

  @Test
  @DisplayName("Verify API returns NOT_FOUND when deleting poll with invalid poll id")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void deletePollWithInvalidPollId() throws HttpException {

    var response =
        deletePoll(
            new Faker().idNumber().valid(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            null,
            true);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_NOT_FOUND);
  }
}
