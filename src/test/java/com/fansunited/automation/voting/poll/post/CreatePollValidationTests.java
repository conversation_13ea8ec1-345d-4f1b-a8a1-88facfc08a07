package com.fansunited.automation.voting.poll.post;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.CreateVotingEndpoint.createPoll;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.model.voting.poll.request.PollRequest.createPollRequestWithRandomData;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import java.util.function.Consumer;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Create Poll validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreatePollValidationTests extends VotingApiPollBaseTest {
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that creating a poll is not allowed for users")
  public void createPollWithUserTokenTest() throws IllegalArgumentException, HttpException {

    var response =
        createPoll(
            PollRequest.createPollRequestWithRandomData(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that creating a poll is not allowed for billing manager role")
  public void createPollWithWrongRoleTest() throws IllegalArgumentException, HttpException {

    var response =
        createPoll(
            PollRequest.createPollRequestWithRandomData(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            BILLING_MANAGER_EMAIL);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @EnabledIf("isUseStageEnvironment")
  @ParameterizedTest(
      name = "Verify Poll cannot be created with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void createPollWithInvalidApiKeyTest(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        createPoll(
            PollRequest.createPollRequestWithRandomData(),
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify API returns error when creating Poll with invalid client ID. {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void createPollWithInvalidClientIdTest(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        createPoll(
            PollRequest.createPollRequestWithRandomData(),
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name =
          "Verify that a Poll cannot be created with a missing or unsupported Content Type. Content Type: {arguments}")
  @Tags({@Tag(SMOKE), @Tag(DISABLED)})
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void createPollWithInvalidContentTypeTest(
      InvalidContentTypesHolder invalidContentTypesHolder) throws HttpException {

    var response =
        createPoll(
            "",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            invalidContentTypesHolder.contentType(),
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(invalidContentTypesHolder.statusCode())
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @Test
  @DisplayName("Verify Poll cannot be created with missing Tags in context")
  @Tag(SMOKE)
  public void createPollWithMissingTagsTest() throws HttpException {

    var request = createPollRequestWithRandomData();
    request.getContext().setTags(null);

    var response =
        createPoll(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @Test
  @DisplayName("Verify Poll cannot be created with missing Tag's field")
  @Tag(SMOKE)
  public void createPollWithMissingTagsFieldTest() throws HttpException {
    var request = createPollRequestWithRandomData();
    var tag = request.getContext().getTags().get(0);

    List<Consumer<com.fansunited.automation.core.apis.voting.entities.Tag>> missingFieldSetters =
        List.of(t -> t.setId(null), t -> t.setType(null), t -> t.setSource(null));

    List<Consumer<com.fansunited.automation.core.apis.voting.entities.Tag>> restoreFieldSetters =
        List.of(t -> t.setId("someId"), t -> t.setType("someType"), t -> t.setSource("someSource"));

    for (int i = 0; i < missingFieldSetters.size(); i++) {

      missingFieldSetters.get(i).accept(tag);

      var response =
          createPoll(
              request,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON,
              FANS_UNITED_CLIENTS,
              null);

      currentTestResponse.set(response);

      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(STATUS_VALIDATION_ERROR));

      restoreFieldSetters.get(i).accept(tag);
    }
  }

  @Test
  @DisplayName("Verify Poll cannot be created with missing option")
  @Tag(SMOKE)
  public void createPollWithMissingOptionTest() throws HttpException {

    var request = createPollRequestWithRandomData();
    request.setOptions(null);

    var response =
        createPoll(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }
}
