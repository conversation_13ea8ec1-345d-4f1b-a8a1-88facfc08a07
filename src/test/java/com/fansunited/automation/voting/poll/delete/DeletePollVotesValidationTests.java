package com.fansunited.automation.voting.poll.delete;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.POLL_NOT_FOUND;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.DeletePollVotesEndpoint.deletePollVotes;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static com.fansunited.automation.validators.ErrorValidator.validateErrorResponse;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("DELETE /v1/polls/{poll_id}/votes Delete poll votes validation tests")
public class DeletePollVotesValidationTests extends VotingApiPollBaseTest {

  private String pollId;

  @BeforeEach
  public void setUp() {
    var pollResponse = createPollForTest();
    Poll poll = pollResponse.as(Poll.class);
    pollId = poll.getData().getId();

    // Create a vote for the poll to ensure there's something to delete
    try {

      VotingForPollOptionRequest voteRequest =
          VotingForPollOptionRequest.builder()
              .optionId(poll.getData().getOptions().get(0).getId())
              .build();

      voteForeAPoll(
              pollId,
              voteRequest,
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON,
              FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
              getCurrentTestUser().getEmail())
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK);
    } catch (HttpException e) {
      throw new RuntimeException("Failed to create vote for poll", e);
    }
  }

  @ParameterizedTest(
      name =
          "Verify poll votes cannot be deleted with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void deletePollVotesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        deletePollVotes(
            pollId,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify Poll votes cannot be deleted with missing/non supported Content Type. ContentType: {arguments}")
  @Tag(SMOKE)
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void deletePollVotesWithInvalidContentTypeTest(
      InvalidContentTypesHolder invalidContentTypesHolder) throws HttpException {

    var response =
        deletePollVotes(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            invalidContentTypesHolder.contentType(),
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true);

    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @Test
  @DisplayName("Verify poll votes cannot be deleted by ordinary user")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyPollVotesCannotBeDeletedByOrdinaryUser() throws HttpException {

    var response =
        deletePollVotes(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            false);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(SC_UNAUTHORIZED);
  }

  @Test
  @DisplayName("Verify poll votes cannot be deleted with invalid poll ID. Poll ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void verifyDeletePollVotesReturnsNotFoundForInvalidPollId() throws HttpException {

    var response =
        deletePollVotes(
            "fwef43f3",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true);

    currentTestResponse.set(response);

    validateErrorResponse(response, SC_NOT_FOUND, POLL_NOT_FOUND);
  }
}
