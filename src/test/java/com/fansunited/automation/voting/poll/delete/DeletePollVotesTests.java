package com.fansunited.automation.voting.poll.delete;

import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.POLL_VOTE_NOT_FOUND;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.DeletePollVotesEndpoint.deletePollVotes;
import static com.fansunited.automation.core.apis.voting.poll.GetUserVotesEndpoint.getUserVotes;
import static com.fansunited.automation.core.apis.voting.poll.GetUserVotesForPoll.getUserVotesForPoll;
import static com.fansunited.automation.core.apis.voting.poll.GetVotingByIdEndpoint.getPollById;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static com.fansunited.automation.validators.ErrorValidator.validateErrorResponse;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

public class DeletePollVotesTests extends VotingApiPollBaseTest {
  private Poll poll;

  @BeforeEach
  public void setUp() {
    var pollResponse = createPollForTest();
    poll = pollResponse.as(Poll.class);
  }

  @Test
  @DisplayName("Delete user's votes for a poll successfully")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void deletePollVotesSuccessfully() throws HttpException {

    String email = getCurrentTestUser().getEmail();

    VotingForPollOptionRequest voteRequest =
        VotingForPollOptionRequest.builder()
            .optionId(poll.getData().getOptions().get(0).getId())
            .build();

    String pollId = poll.getData().getId();

    voteForeAPoll(
            pollId,
            voteRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            email)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    getPollById(pollId)
        .then()
        .assertThat()
        .statusCode(200)
        .body("data.options[0].votes", is(equalTo(1)));

    // Delete the votes
    var deleteVotesResponse =
        deletePollVotes(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            email,
            true);

    currentTestResponse.set(deleteVotesResponse);

    // Verify votes were deleted successfully
    deleteVotesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    // Verify votes are actually gone by trying to get them
    validateErrorResponse(
        getUserVotesForPoll(email, pollId), HttpStatus.SC_NOT_FOUND, POLL_VOTE_NOT_FOUND);

    getUserVotes(email, true)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.poll_id", not(hasItem(pollId)));
  }
}
