package com.fansunited.automation.voting.poll.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.RelatedFieldGenerator.getAllEntityTypeRelationships;
import static com.fansunited.automation.validators.VotingApiValidator.verifyResponsePollData;
import static org.assertj.core.api.Assertions.assertThat;

import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Create Poll happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class CreatePollTests extends VotingApiPollBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that the create poll functionality works.")
  public void createPollTest() {
    var pollResponse = createPollForTest();
    currentTestResponse.set(pollResponse);
    verifyResponsePollData(pollResponse, request);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Create Poll with configured related field")
  public void createPollWithConfiguredRelatedFieldTest() throws HttpException {

    var request = PollRequest.createPollRequestWithRandomData();

    List<RelatedDto> relatedDtoList = getAllEntityTypeRelationships();
    // Set related field and send the request
    request.setRelated(relatedDtoList);
    var response = createCustomPoll(request);
    var actualRelatedList = response.as(Poll.class).getData().getRelated();

    currentTestResponse.set(response);

    verifyResponsePollData(response, request);
    assertThat(actualRelatedList)
        .hasSize(relatedDtoList.size())
        .containsExactlyInAnyOrderElementsOf(relatedDtoList);
  }
}
