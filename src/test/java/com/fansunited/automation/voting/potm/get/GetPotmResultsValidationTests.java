package com.fansunited.automation.voting.potm.get;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.MATCH_NOT_FINISHED;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.MATCH_NOT_STARTED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.potm.GetPotmResultsEndpoint.getPotmResults;
import static com.fansunited.automation.core.apis.voting.potm.GetPotmResultsEndpoint.getPotmResultsResponse;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.CoreMatchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPotmBaseTest;
import com.fansunited.automation.core.resolver.Resolver;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName(
    "Get results for 'Player of the match'. Validation tests GET /v1/football/potm/{match_id}")
public class GetPotmResultsValidationTests extends VotingApiPotmBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify Player of the Match results require match to be started")
  public void rejectResultsForUnfinishedMatch() throws HttpException {

    var match = generateMatch();
    var matchId = match.getId();
    Resolver.openMatchForPredictions(match);

    init();
    cleanUpMatchIdList.add(matchId);

    var response = getPotmResultsResponse(matchId, false);
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(MATCH_NOT_STARTED));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify voting for Player of the Match returns error when match ID is missing")
  public void getPotmResultsWithMissingMatchID() throws HttpException {
    var response = getPotmResultsResponse(" ", false);
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(
      name = "Verify getting Player of the Match results fails with unsupported Content-Type: {0}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void getPotmResultsWithInvalidContentType(
      InvalidContentTypesHolder invalidContentTypesHolder) throws HttpException {
    var response =
        getPotmResults(
            "m:1234",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            invalidContentTypesHolder.contentType(),
            FANS_UNITED_CLIENTS,
            false);
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify getting Player of the Match results fails with invalid/missing api key. Api key: {0}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getPotmResultsWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    getPotmResults(
            "m:1234",
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            false)
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify getting Player of the Match results fails with missing/invalid client ID: Client ID: {0}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getPotmResultsWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    getPotmResults(
            "m:1234",
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            false)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", Matchers.equalTo(invalidClientIdHolder.errorStatus()));
  }
}
