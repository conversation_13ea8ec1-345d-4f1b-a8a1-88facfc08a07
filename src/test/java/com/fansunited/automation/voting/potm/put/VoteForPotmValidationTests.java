package com.fansunited.automation.voting.potm.put;

import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.MATCH_IS_NOT_FULL_COVERAGE;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.MATCH_NOT_FOUND;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.MATCH_NOT_STARTED;
import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.PLAYER_ID_NOT_PARTICIPATED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.potm.VoteForPotmEndpoint.putVoteForPotm;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPotmBaseTest;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.model.voting.potm.VoteForPotmRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Vote for Player of the Match validation tests PUT /v1/football/potm/{match_id}")
public class VoteForPotmValidationTests extends VotingApiPotmBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify Player of the Match vote fails for non-participating player")
  public void voteForPotmWhoDoesNotPlay() throws HttpException {
    var playerId = "p:1234";
    var match = generateMatch();

    init();
    cleanUpMatchIdList.add(match.getId());
    Resolver.openMatchForPredictions(match);
    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    var response =
        putVoteForPotm(
            match.getId(), new VoteForPotmRequest(playerId), getCurrentTestUser().getEmail());
    response
        .then()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(PLAYER_ID_NOT_PARTICIPATED));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify Player of the Match vote requires match to be started")
  public void rejectVoteForNotStartedMatch() throws HttpException {

    var match = generateMatch();

    init();
    cleanUpMatchIdList.add(match.getId());
    Resolver.openMatchForPredictions(match);

    var response =
        putVoteForPotm(
            match.getId(), new VoteForPotmRequest("p:1234"), getCurrentTestUser().getEmail());
    response
        .then()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(MATCH_NOT_STARTED));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify Player of the Match vote fails with invalid match ID")
  public void voteForPotmWithInvalidMatchID() throws HttpException {
    var response =
        putVoteForPotm("m:1234", new VoteForPotmRequest("p:1234"), getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND)
        .body("error.status", equalTo(MATCH_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify voting for Player of the Match returns error when match ID is missing")
  public void voteForPotmWithMissingMatchID() throws HttpException {
    var response =
        putVoteForPotm(" ", new VoteForPotmRequest("p:1234"), getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify voting for PoTM returns error when match is NOT part of the client's full coverage competitions.")
  public void verifyPotmVoteErrorForNonFullCoverageMatch() throws HttpException {

    var match = generateMatch();
    match.getCompetition().setId("fb:c:151");
    Resolver.openMatchForPredictions(match);

    var response =
        putVoteForPotm(
            match.getId(), new VoteForPotmRequest("p:1234"), getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(MATCH_IS_NOT_FULL_COVERAGE));
  }

  @ParameterizedTest(
      name = "Verify Player of the Match vote requires valid client ID: Client ID: {0}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void voteForPotmWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        putVoteForPotm(
            "m:1234",
            new VoteForPotmRequest("p:1234"),
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(invalidClientIdHolder.errorStatus()));
  }

  @ParameterizedTest(
      name = "Verify Player of the Match vote fails with unsupported Content-Type: {0}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void voteForPotmWithInvalidContentType(InvalidContentTypesHolder invalidContentTypesHolder)
      throws HttpException {
    var response =
        putVoteForPotm(
            "m:1234",
            "",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            invalidContentTypesHolder.contentType(),
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    response.then().assertThat().statusCode(invalidContentTypesHolder.statusCode());
  }

  @ParameterizedTest(
      name = "Verify Player of the Match vote fails with invalid/missing api key. Api key: {0}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void voteForPotmWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {
    var response =
        putVoteForPotm(
            "m:1234",
            new VoteForPotmRequest("p:1234"),
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail());
    currentTestResponse.set(response);
    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }
}
