package com.fansunited.automation.voting.potm.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.voting.potm.GetPotmResultsEndpoint.getPotmResultsResponse;
import static com.fansunited.automation.core.apis.voting.potm.VoteForPotmEndpoint.putVoteForPotm;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.STATUS_FINISHED;
import static com.fansunited.automation.core.resolver.MatchGenerator.STATUS_TO_LIVE;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.core.resolver.MatchTimelineGenerator.generateMatchPlayers;
import static com.fansunited.automation.core.resolver.Resolver.updateEventPlayer;
import static com.fansunited.automation.helpers.PlayerMatchStatsGenerator.generatePlayerIds;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.core.resolver.hibernate.MatchPlayer;
import com.fansunited.automation.core.resolver.hibernate.MatchStatus;
import com.fansunited.automation.model.voting.potm.VoteForPotmRequest;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Vote for Player of the Match PUT /v1/football/potm/{match_id}")
public class VoteForPotmTests extends VotingApiPollBaseTest {

  private Match match;
  private List<String> playerIds;
  private Map<String, MatchPlayer> matchPlayersMap;
  private String email;

  @BeforeEach
  public void setupTestMatchWithPlayers() {
    // Initialize test data
    match = generateMatch();
    playerIds = generatePlayerIds(match);
    matchPlayersMap = generateMatchPlayers(match, playerIds);
    email = getCurrentTestUser().getEmail();
  }

  private void setupMatchAndPlayers(
      Map<String, MatchPlayer> matchPlayersMap, Match match, MatchStatus matchStatus) {
    // Set type "start" for all players
    matchPlayersMap.values().forEach(player -> player.setType("start"));

    init();
    cleanUpMatchIdList.add(match.getId());

    Resolver.openMatchForPredictions(match);
    match.setPlayers(new ArrayList<>(matchPlayersMap.values()));
    updateEventPlayer(matchPlayersMap);

    if (matchStatus.equals(STATUS_TO_LIVE)) {
      Resolver.updateMatchToAnotherStatus(match.getId(), MatchGenerator.STATUS_TO_LIVE);
    } else {
      Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);
    }
  }

  private void verifyVoteResponse(Response response, String expectedPlayerId) {
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.player_id", equalTo(expectedPlayerId));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify voting for Player of the Match")
  public void voteForPotm() throws HttpException {
    // Setup match and players
    setupMatchAndPlayers(matchPlayersMap, match, STATUS_FINISHED);
    var playerId = playerIds.get(0);
    var response = putVoteForPotm(match.getId(), new VoteForPotmRequest(playerId), email);
    currentTestResponse.set(response);
    verifyVoteResponse(response, playerId);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify Player of the Match vote can be updated")
  public void updateVoteForPotm() throws HttpException {
    // Setup match and players
    setupMatchAndPlayers(matchPlayersMap, match, STATUS_FINISHED);
    // Initial vote
    var firstPlayerId = playerIds.get(0);
    var response = putVoteForPotm(match.getId(), new VoteForPotmRequest(firstPlayerId), email);
    verifyVoteResponse(response, firstPlayerId);

    // Update vote
    var secondPlayerId = playerIds.get(1);
    response = putVoteForPotm(match.getId(), new VoteForPotmRequest(secondPlayerId), email);
    currentTestResponse.set(response);
    verifyVoteResponse(response, secondPlayerId);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify voting for Player of the Match for a match with status TO_LIVE")
  public void voteForPotmForFullCoverageMatch()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    // Setup match and players
    setupMatchAndPlayers(matchPlayersMap, match, STATUS_TO_LIVE);

    var users = createUsers(4);
    for (int i = 0; i < users.size(); i++) {
      var user = users.get(i);
      var playerId = playerIds.get(i);
      putVoteForPotm(match.getId(), new VoteForPotmRequest(playerId), user.getEmail());
    }

    var response = getPotmResultsResponse(match.getId(), false);
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.votes.size()", equalTo(users.size()))
        .body("meta.total", equalTo(users.size()));
  }
}
