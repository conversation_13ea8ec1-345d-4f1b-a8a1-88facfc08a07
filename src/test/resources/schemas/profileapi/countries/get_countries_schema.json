{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["id", "name", "assets"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "assets": {"type": "object", "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}}}]}}}}