{"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["id", "name", "avatar", "gender", "country", "birth_date", "interests", "following_count", "followers_count", "nickname"], "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1, "maxLength": 100}, "avatar": {"type": ["string", "null"]}, "gender": {"type": "string", "enum": ["male", "female", "unspecified", ""]}, "country": {"oneOf": [{"type": "null"}, {"type": "object", "required": ["id", "name", "assets"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "assets": {"type": "object", "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}}}]}, "birth_date": {"type": ["string", "null"]}, "interests": {"type": ["array", "null"], "items": {"type": "object", "required": ["favourite", "id", "source", "type"], "properties": {"favourite": {"type": "boolean"}, "id": {"type": "string"}, "source": {"type": "string"}, "type": {"type": "string"}}}}, "following_count": {"type": "integer"}, "followers_count": {"type": "integer"}, "nickname": {"type": "string"}}}]}}}}