{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["id", "name", "avatar", "gender", "country", "birth_date", "interests", "following_count", "followers_count", "nickname", "created_at"], "properties": {"id": {"type": "string"}, "name": {"type": "string", "minLength": 1, "maxLength": 100}, "avatar": {"type": ["string", "null"]}, "gender": {"type": "string", "enum": ["male", "female", "unspecified"]}, "country": {"oneOf": [{"type": "null"}, {"type": "object", "required": ["id", "name", "assets"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "assets": {"type": "object", "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}}}]}, "birth_date": {"type": ["string", "null"]}, "interests": {"type": "array", "items": {"type": "object", "required": ["favourite", "id", "source", "type"], "properties": {"favourite": {"type": "boolean"}, "id": {"type": "string"}, "source": {"type": "string"}, "type": {"type": "string"}}}}, "following_count": {"type": "integer"}, "followers_count": {"type": "integer"}, "nickname": {"type": ["string", "null"]}, "created_at": {"type": "string"}}}]}}}}