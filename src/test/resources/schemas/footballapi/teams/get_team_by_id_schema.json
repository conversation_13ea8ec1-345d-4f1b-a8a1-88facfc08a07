{"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "required": ["id", "country", "assets", "national", "code", "gender", "name", "full_name", "short_name", "squad", "competitions"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "national": {"type": "boolean"}, "code": {"type": ["string", "null"]}, "gender": {"type": "string"}, "name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "short_name": {"type": ["string", "null"]}, "squad": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "start_date", "end_date", "shirt_number", "loan", "country", "name", "assets", "birth_date", "position"], "properties": {"id": {"type": "string"}, "start_date": {"type": ["string", "null"]}, "end_date": {"type": ["string", "null"]}, "shirt_number": {"type": ["integer", "null"]}, "loan": {"type": "boolean"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": "string"}}}, "name": {"type": "string"}}}, "name": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["headshot"], "properties": {"headshot": {"type": ["string", "null"]}}}, "birth_date": {"type": ["string", "null"]}, "position": {"type": ["string", "null"]}}}]}}, "competitions": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "country", "gender", "assets", "type", "name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": "null"}, "country_code": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": "string"}}}, "name": {"type": "string"}}}, "gender": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "type": {"type": ["string", "null"]}, "name": {"type": "string"}}}]}}}}}}