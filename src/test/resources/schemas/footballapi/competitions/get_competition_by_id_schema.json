{"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "required": ["id", "country", "gender", "assets", "type", "name", "participants"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "gender": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "type": {"type": "string"}, "name": {"type": "string"}, "participants": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "country", "assets", "national", "code", "gender", "name", "full_name", "short_name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": "null"}, "country_code": {"type": ["string", "null"]}, "assets": {"type": "object", "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "assets": {"type": ["object", "null"]}, "national": {"type": "boolean"}, "code": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "short_name": {"type": ["string", "null"]}}}]}}}}}}