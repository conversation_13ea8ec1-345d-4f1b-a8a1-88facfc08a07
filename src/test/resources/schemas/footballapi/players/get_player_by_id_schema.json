{"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "required": ["id", "country", "birth_date", "first_name", "last_name", "position", "assets", "name", "teams", "competitions"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "birth_date": {"type": ["string", "null"]}, "first_name": {"type": ["string", "null"]}, "last_name": {"type": ["string", "null"]}, "position": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["headshot"], "properties": {"headshot": {"type": ["string", "null"]}}}, "name": {"type": "string"}, "teams": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "country", "assets", "national", "code", "gender", "name", "full_name", "short_name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "national": {"type": "boolean"}, "code": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "short_name": {"type": ["string", "null"]}}}]}}, "competitions": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "country", "gender", "assets", "type", "name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": "string"}}}, "name": {"type": "string"}}}, "gender": {"type": "string"}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "type": {"type": ["string", "null"]}, "name": {"type": "string"}}}]}}}}}}