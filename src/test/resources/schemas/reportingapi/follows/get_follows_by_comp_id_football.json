{"type": "object", "properties": {"data": {"type": "object", "properties": {"breakdown": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "users": {"type": "object", "properties": {"all": {"type": "integer"}, "male": {"type": "integer"}, "female": {"type": "integer"}, "unspecified": {"type": "integer"}}, "required": ["all", "male", "female", "unspecified"]}}, "required": ["id", "name", "users"]}]}, "total": {"type": "object", "properties": {"all": {"type": "integer"}, "male": {"type": "integer"}, "female": {"type": "integer"}, "unspecified": {"type": "integer"}}, "required": ["all", "male", "female", "unspecified"]}}, "required": ["breakdown", "total"]}, "meta": {"type": "object", "properties": {"from_date": {"type": "string"}, "to_date": {"type": "string"}, "grouped_by": {"type": "string"}}, "required": ["from_date", "to_date", "grouped_by"]}}, "required": ["data", "meta"]}