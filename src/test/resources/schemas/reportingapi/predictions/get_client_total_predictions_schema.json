{"type": "object", "required": ["data", "meta"], "properties": {"data": {"type": "object", "required": ["breakdown", "total"], "properties": {"breakdown": {"type": ["object", "null"], "items": {"type": "object", "required": ["predictions"], "properties": {"date": {"type": "string"}, "predictions": {"type": ["object", "null"], "properties": {"all": {"type": "integer"}, "HT_1X2": {"type": "integer"}, "FT_1X2": {"type": "integer"}, "BOTH_TEAMS_SCORE": {"type": "integer"}, "OVER_GOALS_0_5": {"type": "integer"}, "OVER_GOALS_1_5": {"type": "integer"}, "OVER_GOALS_2_5": {"type": "integer"}, "OVER_GOALS_3_5": {"type": "integer"}, "OVER_GOALS_4_5": {"type": "integer"}, "OVER_GOALS_5_5": {"type": "integer"}, "OVER_GOALS_6_5": {"type": "integer"}, "OVER_CORNERS_6_5": {"type": "integer"}, "OVER_CORNERS_7_5": {"type": "integer"}, "OVER_CORNERS_8_5": {"type": "integer"}, "OVER_CORNERS_9_5": {"type": "integer"}, "OVER_CORNERS_10_5": {"type": "integer"}, "OVER_CORNERS_11_5": {"type": "integer"}, "OVER_CORNERS_12_5": {"type": "integer"}, "OVER_CORNERS_13_5": {"type": "integer"}, "DOUBLE_CHANCE": {"type": "integer"}, "HT_FT": {"type": "integer"}, "PLAYER_SCORE": {"type": "integer"}, "PLAYER_YELLOW_CARD": {"type": "integer"}, "PLAYER_RED_CARD": {"type": "integer"}, "RED_CARD_MATCH": {"type": "integer"}, "PENALTY_MATCH": {"type": "integer"}, "PLAYER_SCORE_FIRST_GOAL": {"type": "integer"}, "CORNERS_MATCH": {"type": "integer"}, "CORRECT_SCORE": {"type": "integer"}, "CORRECT_SCORE_HT": {"type": "integer"}, "CORRECT_SCORE_ADVANCED": {"type": "integer"}, "PLAYER_SCORE_HATTRICK": {"type": "integer"}, "PLAYER_SCORE_TWICE": {"type": "integer"}}}}}}, "total": {"type": "object", "properties": {"total": {"type": "integer"}}}}}, "meta": {"type": "object", "required": ["from_date", "to_date", "grouped_by"], "properties": {"markets": {"type": "string"}, "from_date": {"type": ["string", "null"]}, "to_date": {"type": ["string", "null"]}, "grouped_by": {"type": "string"}}}}}