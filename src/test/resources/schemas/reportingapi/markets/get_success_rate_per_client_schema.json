{"type": "object", "properties": {"data": {"type": "object", "properties": {"total": {"type": "object", "properties": {"overall": {"type": ["integer", "null"]}}, "required": ["overall"]}, "breakdown": {"type": "object"}}, "required": ["total", "breakdown"]}, "meta": {"type": "object", "properties": {"from_date": {"type": "string"}, "to_date": {"type": "string"}}, "required": ["from_date", "to_date"]}}, "required": ["data", "meta"]}