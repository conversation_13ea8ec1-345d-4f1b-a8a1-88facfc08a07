{"type": "object", "required": ["data", "meta"], "properties": {"data": {"type": "object", "required": ["all", "male", "female", "unspecified"], "properties": {"all": {"type": "integer"}, "male": {"type": "integer"}, "female": {"type": "integer"}, "unspecified": {"type": "integer"}}}, "meta": {"type": "object", "required": ["from_date", "to_date", "grouped_by"], "properties": {"from_date": {"type": "string"}, "to_date": {"type": "string"}, "grouped_by": {"type": "string"}}}}}