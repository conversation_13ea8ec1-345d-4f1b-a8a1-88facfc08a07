{"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "client_id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "entities": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"entity_type": {"type": "string"}, "entity_id": {}}, "required": ["entity_type", "entity_id"]}}, "sort": {"type": ["object", "null"], "properties": {"sort_order": {"type": "string"}, "sort_by": {"type": "string"}}, "required": ["sort_order", "sort_by"]}}, "required": ["list", "sort"]}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "created_by": {"type": "string"}}, "required": ["id", "client_id", "name", "type", "entities", "created_at", "updated_at", "created_by"]}}, "required": ["data"]}