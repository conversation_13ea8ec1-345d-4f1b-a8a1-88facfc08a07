{"type": "object", "required": ["data"], "properties": {"data": {"type": ["object", "null"], "required": ["loyalty", "match_quiz", "predictor", "top_x"], "properties": {"predictor": {"type": ["object", "null"], "required": ["enabled", "full_coverage_competitions", "markets"], "properties": {"enabled": {"type": "boolean"}, "full_coverage_competitions": {"type": ["array", "null"]}, "markets": {"type": ["object", "null"]}}}, "top_x": {"type": ["object", "null"], "required": ["enabled", "min_fixtures", "max_fixtures", "competitions_whitelist"], "properties": {"enabled": {"type": "boolean"}, "min_fixtures": {"type": ["integer", "null"]}, "max_fixtures": {"type": ["integer", "null"]}, "competitions_whitelist": {"type": ["array", "null"]}}}, "match_quiz": {"type": ["object", "null"], "required": ["enabled", "default_markets", "competitions_whitelist"], "properties": {"enabled": {"type": "boolean"}, "default_markets": {"type": ["array", "null"]}, "competitions_whitelist": {"type": ["array", "null"]}}}, "loyalty": {"type": ["object", "null"], "required": ["enabled", "rewards"], "properties": {"enabled": {"type": "boolean"}, "rewards": {"type": ["object", "null"]}}}}}}}