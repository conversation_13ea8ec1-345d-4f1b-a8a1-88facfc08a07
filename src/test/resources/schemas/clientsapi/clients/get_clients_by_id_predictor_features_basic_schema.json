{"type": "object", "properties": {"enabled": {"type": "boolean"}, "markets": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "string"}}, "all": {"type": "array", "items": {"type": "string"}}}, "required": ["main", "all"]}, "client_id": {"type": "string"}, "full_coverage_competitions": {"type": "array", "items": {"type": "string"}}, "success_rate_scopes": {"type": "object", "properties": {"competitions": {"type": "array", "items": {"type": "string"}}, "teams": {"type": "array", "items": {"type": "string"}}, "markets": {"type": "array", "items": {"type": "string"}}}, "required": ["competitions", "teams", "markets"]}, "user_data_config": {"type": "object", "properties": {"store_ip": {"type": "boolean"}, "store_device_id": {"type": "boolean"}, "store_agent": {"type": "boolean"}}, "required": ["store_ip", "store_device_id", "store_agent"]}}, "required": ["enabled"]}