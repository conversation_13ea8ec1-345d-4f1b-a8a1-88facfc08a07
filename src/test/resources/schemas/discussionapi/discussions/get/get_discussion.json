{"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": ["string", "null"]}, "discussion_type": {"type": "string", "enum": ["COMMENT_SECTION", "PRIVATE_LEAGUE"]}, "moderation_type": {"type": "string", "enum": ["USER", "STAFF"]}, "posts_count": {"type": "integer"}, "last_post_id": {"type": "string"}, "context": {"type": ["object", "null"], "properties": {"content": {"type": ["string", "null"]}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "source": {"type": "string"}}, "required": ["id", "type", "source"]}}, "campaign": {"type": ["object", "null"]}}, "required": ["tags"]}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "pinned_posts": {"type": ["array", "null"], "items": {"type": "string"}}}, "required": ["id", "discussion_type", "moderation_type", "posts_count", "last_post_id", "created_at", "updated_at"]}}}, "required": ["data"]}