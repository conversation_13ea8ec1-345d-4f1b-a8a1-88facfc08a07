{"type": "object", "required": ["data"], "properties": {"data": {"type": "object", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["game_id", "results"], "properties": {"game_id": {"type": "string"}, "results": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["match_id", "market", "result"], "properties": {"match_id": {"type": "string"}, "market": {"type": "string", "enum": ["FT_1X2", "HT_1X2", "BOTH_TEAMS_SCORE", "OVER_GOALS_0_5", "OVER_GOALS_1_5", "OVER_GOALS_2_5", "OVER_GOALS_3_5", "OVER_GOALS_4_5", "OVER_GOALS_5_5", "OVER_GOALS_6_5", "OVER_CORNERS_6_5", "OVER_CORNERS_7_5", "OVER_CORNERS_8_5", "OVER_CORNERS_9_5", "OVER_CORNERS_10_5", "OVER_CORNERS_11_5", "OVER_CORNERS_12_5", "OVER_CORNERS_13_5", "DOUBLE_CHANCE", "HT_FT", "PLAYER_SCORE", "PLAYER_YELLOW_CARD", "PLAYER_RED_CARD", "RED_CARD_MATCH", "PENALTY_MATCH", "PLAYER_SCORE_FIRST_GOAL", "CORNERS_MATCH", "CORRECT_SCORE", "CORRECT_SCORE_HT", "CORRECT_SCORE_ADVANCED", "PLAYER_SCORE_HATTRICK", "PLAYER_SCORE_TWICE"]}, "result": {"type": "object"}}}]}}, "tie_breakers": {"type": "object", "required": ["golden_goals", "null"], "properties": {"golden_goals": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["match_id", "market", "result"], "properties": {"minute": {"type": "integer"}, "player_id": {"type": "string"}, "match_id": {"type": "string"}, "goal_type": {"type": "string"}}}]}}}}}}]}}}}