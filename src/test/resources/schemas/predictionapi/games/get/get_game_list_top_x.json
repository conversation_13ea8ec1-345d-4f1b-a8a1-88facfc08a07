{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["next_page_starts_after", "items_per_page"], "properties": {"next_page_starts_after": {"type": ["string", "null"]}, "items_per_page": {"type": "integer"}}}}}, "data": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["id", "title", "description", "type", "status", "predictions_cutoff", "fixtures", "created_at", "updated_at"], "properties": {"id": {"type": "string", "minLength": 1}, "title": {"type": ["string", "null"], "minLength": 1}, "description": {"type": ["string", "null"], "minLength": 1}, "type": {"type": "string", "enum": ["TOP_X"]}, "status": {"type": "string", "enum": ["CANCELED", "SETTLED", "CLOSED", "LIVE", "OPEN", "PENDING"]}, "predictions_cutoff": {"type": "string", "minLength": 1}, "fixtures": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["match_id", "match_type", "market"], "properties": {"match_id": {"type": "string", "minLength": 1}, "match_type": {"type": "string", "enum": ["FOOTBALL"]}, "market": {"type": "string", "enum": ["CORRECT_SCORE", "CORRECT_SCORE_ADVANCED"]}}}]}}, "rules": {"type": ["null", "string"]}, "flags": {"type": ["array", "null"], "items": [{"type": ["string", "null"]}]}, "created_at": {"type": "string", "minLength": 1}, "updated_at": {"type": ["string"]}}}]}}}}