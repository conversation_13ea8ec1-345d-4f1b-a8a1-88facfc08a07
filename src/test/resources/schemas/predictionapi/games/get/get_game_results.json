{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["next_page_starts_after", "items_per_page"], "properties": {"next_page_starts_after": {"type": "string"}, "items_per_page": {"type": "integer"}}}}}, "data": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["user_id", "points", "results", "tiebreaker"], "properties": {"user_id": {"type": "string"}, "points": {"type": "integer"}, "results": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["match_id", "outcome"], "properties": {"match_id": {"type": "string"}, "outcome": {"type": "string", "enum": ["NOT_VERIFIED", "CORRECT", "PARTIALLY_CORRECT", "INCORRECT"]}}}]}}, "tiebreaker": {"type": ["null", "object"]}}}]}}}}