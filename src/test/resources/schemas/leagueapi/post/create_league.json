{"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string", "minLength": 1}, "name": {"type": "string", "minLength": 1, "pattern": "^[a-zA-Z0-9 ]+$"}, "type": {"type": "string", "enum": ["PRIVATE", "ONE_VS_ONE"]}, "members": {"type": "array", "items": [{"type": "string"}]}, "administrators": {"type": "array", "items": [{"type": "string"}]}, "banned": {"type": ["array", "null"], "items": [{"type": "string"}]}, "invites": {"type": ["array", "null"], "items": [{"type": "string"}]}, "description": {"type": "string", "minLength": 1}, "users_can_invite_users": {"type": "boolean"}, "invitation_code": {"type": "string"}, "template_id": {"type": "string"}, "pinned_posts": {"type": ["array", "null"], "items": [{"type": "string"}]}, "scoring_starts_at": {"type": "string"}}, "required": ["id", "name", "type", "members", "administrators", "banned", "invites", "description", "users_can_invite_users", "invitation_code", "template_id", "pinned_posts", "scoring_starts_at"]}}, "required": ["data"]}