{"type": "object", "properties": {"data": {"type": "object", "properties": {"profile_id": {"type": "string"}, "tier": {"type": ["null", "string"]}, "points": {"type": "integer"}, "success_rates": {"type": "object", "properties": {"overall_percent": {"type": "integer"}, "by_competition": {"type": "object"}, "by_team": {"type": "object"}, "by_market": {"type": "object"}}, "required": ["overall_percent", "by_competition", "by_team", "by_market"]}}, "required": ["profile_id", "tier", "points", "success_rates"]}}}