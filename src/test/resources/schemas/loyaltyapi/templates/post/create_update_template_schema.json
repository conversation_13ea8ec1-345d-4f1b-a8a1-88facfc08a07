{"type": "object", "required": ["id", "name", "type", "from_date", "to_date", "markets", "team_ids", "match_ids", "game_ids", "game_types", "competition_ids", "rules", "flags"], "properties": {"id": {"type": ["string"]}, "name": {"type": ["string", "null"]}, "type": {"type": ["string"], "enum": ["PRIMARY", "CUSTOM", "FANTASY"]}, "from_date": {"type": ["string", "null"]}, "to_date": {"type": ["string", "null"]}, "markets": {"type": ["array", "null"]}, "team_ids": {"type": ["array", "null"]}, "match_ids": {"type": ["array", "null"]}, "game_ids": {"type": ["array", "null"]}, "game_types": {"type": ["array", "null"]}, "competition_ids": {"type": ["array", "null"]}, "rules": {"type": ["string", "null"]}, "flags": {"type": ["array", "null"], "items": [{"type": ["string", "null"]}]}}}