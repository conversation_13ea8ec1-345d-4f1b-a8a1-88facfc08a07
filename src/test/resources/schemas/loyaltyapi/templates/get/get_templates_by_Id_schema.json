{"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "from_date": {"type": "string"}, "to_date": {"type": "string"}, "markets": {"type": "null"}, "team_ids": {"type": "null"}, "match_ids": {"type": "null"}, "game_ids": {"type": "null"}, "game_types": {"type": "null"}, "competition_ids": {"type": "null"}, "rules": {"type": "null"}, "flags": {"type": "null"}, "excluded_profile_ids": {"type": "null"}, "description": {"type": "null"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "images": {"type": "null"}, "status": {"type": "string"}, "automatically_change_status": {"type": "boolean"}, "related": {"type": "null"}}, "required": ["id", "name", "type", "from_date", "to_date", "markets", "team_ids", "match_ids", "game_ids", "game_types", "competition_ids", "rules", "flags", "excluded_profile_ids", "description", "created_at", "updated_at", "images", "status", "automatically_change_status", "related"]}}, "required": ["data"]}