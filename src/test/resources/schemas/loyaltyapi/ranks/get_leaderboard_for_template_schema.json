{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["rank_type", "from_date", "to_date", "pagination"], "properties": {"rank_type": {"type": "string", "enum": ["PRIMARY", "CUSTOM", "GAME"]}, "from_date": {"type": ["string", "null"]}, "to_date": {"type": ["string", "null"]}, "pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "items": {"type": "object", "required": ["position", "profile_id", "points"], "properties": {"position": {"type": "integer"}, "profile_id": {"type": "string"}, "points": {"type": "integer"}, "predictions_made": {"type": "integer"}}}}}}