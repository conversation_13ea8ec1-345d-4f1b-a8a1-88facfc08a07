{"type": "object", "properties": {"action": {"type": "string"}, "context": {"type": "object", "properties": {"content": {"type": ["object", "null"], "properties": {"type": {"type": "string"}, "id": {"type": "string"}, "label": {"type": "string"}}, "required": ["type", "id", "label"]}, "tags": {"type": "array", "items": [{"type": "object", "properties": {"source": {"type": "string"}, "type": {"type": "string"}, "id": {"type": "string"}}, "required": ["source", "type", "id"]}, {"type": "object", "properties": {"source": {"type": "string"}, "type": {"type": "string"}, "id": {"type": "string"}}, "required": ["source", "type", "id"]}, {"type": "object", "properties": {"source": {"type": "string"}, "type": {"type": "string"}, "id": {"type": "string"}}, "required": ["source", "type", "id"]}]}, "campaign": {"type": ["object", "null"], "properties": {"id": {"type": "string"}, "label": {"type": ["string", "null"]}}, "required": ["id", "label"]}}, "required": ["content", "tags", "campaign"]}}, "required": ["action", "context"]}