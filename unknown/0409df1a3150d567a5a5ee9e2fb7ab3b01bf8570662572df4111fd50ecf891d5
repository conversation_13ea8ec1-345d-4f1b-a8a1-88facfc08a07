package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.INVALID_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GamePredictionsEndpoint.getPredictionsForGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/games/{gameId}/predictions endpoint validation tests")
public class GetGamePredictionsValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify API cannot get predictions for game with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void getPredictionsForGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response =
        getPredictionsForGame(gameId, CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(),
            ContentType.JSON, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting predictions for game with invalid/missing client id. Client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getPredictionsForGameWithInvalidClientId(String clientId) throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response =
        getPredictionsForGame(gameId, clientId, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns NOT_FOUND when getting predictions for game with invalid game id")
  public void getPredictionsForGameWithInvalidGameId() throws HttpException {

    var response =
        getPredictionsForGame(INVALID_GAME_ID, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting predictions for game with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getPredictionsForGameWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response =
        getPredictionsForGame(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            contentType, -1, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify predictions for game data is still fetched if content type is NOT specified")
  public void getPredictionsForGameWithoutSpecifyingContentType() throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response =
        getPredictionsForGame(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            null, -1, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name =
      "Verify API returns BAD_REQUEST when getting predictions for game with negative or 0(integers) for pagination \"limit\" param. "
          + "\"limit\" param: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-2, 0})
  public void getPredictionsForGameWithInvalidLimitParam(int limit) throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response =
        getPredictionsForGame(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, limit, null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }
}
