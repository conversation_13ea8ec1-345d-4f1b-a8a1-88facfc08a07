package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - GET /v1/games/{gameId} endpoint happy path tests")
public class GetSpecificGameTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify specific game is successfully fetched and valid. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getSpecificGameForType(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(gameType, 1).get(0);

    var response = GameEndpoint.getGameById(gameId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(gameType == GameType.TOP_X ?
            JsonSchemasPath.PredictionApi.Endpoints.Games.GET_SPECIFIC_GAME_TOP_X_SCHEMA
            : JsonSchemasPath.PredictionApi.Endpoints.Games.GET_SPECIFIC_GAME_MATCH_QUIZ_SCHEMA))
        .body(ID_PROP, equalTo(gameId));
  }

  @ParameterizedTest(name = "Verify GET /v1/game/'{gameId}' response returned by the server is cached for 1h. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  @EnabledIf("isUseStageEnvironment")
  public void verifyGetSpecificGameResponseIsCached(GameType gameType)
      throws HttpException {

    var gameId = createGames(gameType, 1).get(0);

    GameEndpoint.getGameById(gameId);

    var response = GameEndpoint.getGameById(gameId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }
}
