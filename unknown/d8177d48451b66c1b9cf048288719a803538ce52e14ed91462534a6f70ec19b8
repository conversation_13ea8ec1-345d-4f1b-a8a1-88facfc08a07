package com.fansunited.automation.footballapi.players;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_ENGLAND_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.FU_PLAYER_ID_PREFIX;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.LIMIT_PARAM_MAX_VALUE_PLAYERS;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.SQUAD_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Players.GET_PLAYERS_SCHEMA;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Players.GET_TOP_PLAYERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isInAscendingAlphabeticalOrder;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isInDescendingAlphabeticalOrder;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.listOfStringsContainsCyrillic;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static it.ozimov.cirneco.hamcrest.HamcrestMatchers.hasDistinctElements;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.PlayersEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamByIdEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.apis.footballapi.TopPlayersEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.FootballApiValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.Arrays;
import java.util.HashSet;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/players endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPlayersTests extends FootballApiBaseTest {

  private static final int EXPECTED_PLAYERS_COUNT = 250000;

  @ParameterizedTest(name = "Verify getting list of players for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void getPlayersForLang(UrlParamValues.Language lang) {

    var response = PlayersEndpoint.getPlayers(lang.getValue(), AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements())
        .body("meta.pagination.total_items", is(greaterThan(EXPECTED_PLAYERS_COUNT)));

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, listOfStringsContainsCyrillic());
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be filtered by country")
  public void getPlayersByCountry() {

    var response = PlayersEndpoint.getPlayersWithFilterCountry(COUNTRY_ENGLAND_ID,
        UrlParamValues.Language.EN.getValue(), LIMIT_PARAM_MAX_VALUE_PLAYERS, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + ID_PROP, everyItem(is(COUNTRY_ENGLAND_ID)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @ParameterizedTest(name = "Verify players can be filtered by name. Name: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"Ronaldo", "Mbappe", "Peter"})
  public void getPlayersByName(String playerName) {

    var response =
        PlayersEndpoint.getPlayersWithFilterName(playerName, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_PLAYERS, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, everyItem(containsStringIgnoringCase(playerName)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be filtered by scope(competition ids)")
  public void getPlayersByScope() {

    var competitionIds = PREMIER_LEAGUE_COMP_ID + "," + BUNDESLIGA_COMP_ID;

    var expectedTeamIds = new HashSet<String>();

    Arrays.stream(competitionIds.split(",")).toList().forEach(competitionId ->
        expectedTeamIds.addAll(TeamsEndpoint.getTeamsForCompetition(competitionId)));

    var expectedPlayers = new HashSet<String>();

    expectedTeamIds.forEach(
        teamId -> expectedPlayers.addAll(TeamByIdEndpoint.getTeamById(teamId)
            .then()
            .extract()
            .body()
            .jsonPath().getList("data." + SQUAD_PROP + "." + ID_PROP)));

    var response = PlayersEndpoint.getPlayersWithFilterScope(competitionIds,
        UrlParamValues.Language.EN.getValue(), LIMIT_PARAM_MAX_VALUE_PLAYERS, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", not(empty()));

    var playerIds = response.then().extract().body().jsonPath().getList("data." + ID_PROP);

    var totalPlayersCount =
        response.then().extract().body().jsonPath().getInt("meta.pagination.total_items");

    Assertions.assertAll(
        () -> Assertions.assertEquals(expectedPlayers.size(), totalPlayersCount,
            "Players returned by the API, does NOT match expected players size"),
        () -> Assertions.assertTrue(expectedPlayers.containsAll(playerIds),
            "There are players from competitions that are not part of competitions scope"));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be filtered by player ids")
  public void getPlayersByPlayerIds() {

    var playerIds = TopPlayersEndpoint.getTopPlayersIds();

    Assertions.assertNotNull(playerIds, "Players ID List should not be empty");

    var response =
        PlayersEndpoint.getPlayersWithFilterPlayerIds(String.join(",", playerIds), null,
            LIMIT_PARAM_MAX_VALUE_PLAYERS, -1,
            null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(playerIds.size()))
        .body("data." + ID_PROP, containsInAnyOrder(playerIds.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be sorted by field 'name' in ascending order")
  public void getPlayersSortedByFieldNameInAscendingOrder() {

    var response = PlayersEndpoint.getPlayersWithFilterScope(PREMIER_LEAGUE_COMP_ID, null,
        LIMIT_PARAM_MAX_VALUE_PLAYERS, -1,
        ApiConstants.FootballApi.SortField.NAME.getValue(), ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, isInAscendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be sorted by field 'name' in descending order")
  public void getPlayersSortedByFieldNameInDescendingOrder() {

    var response = PlayersEndpoint.getPlayersWithFilterScope(PREMIER_LEAGUE_COMP_ID, null,
        LIMIT_PARAM_MAX_VALUE_PLAYERS, -1,
        ApiConstants.FootballApi.SortField.NAME.getValue(), ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, isInDescendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be sorted by field 'country' in ascending order")
  public void getPlayersSortedByFieldCountryInAscendingOrder() {

    var filterName = "ronaldo";

    var response =
        PlayersEndpoint.getPlayersWithFilterName(filterName, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_PLAYERS, -1,
            ApiConstants.FootballApi.SortField.COUNTRY.getValue(),
            ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, isInAscendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify players can be sorted by field 'country' in descending order")
  public void getPlayersSortedByFieldCountryInDescendingOrder() {

    var filterName = "ronaldo";

    var response =
        PlayersEndpoint.getPlayersWithFilterName(filterName, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_PLAYERS, -1,
            ApiConstants.FootballApi.SortField.COUNTRY.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, isInDescendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify moving to next page for getting players works")
  public void getPlayersMoveToNextPage() {

    var resultsLimit = generateRandomNumber(30, 50);

    var response =
        PlayersEndpoint.getPlayersWithFilterScope(PREMIER_LEAGUE_COMP_ID, null, resultsLimit, -1,
            null, null);

    currentTestResponse.set(response);

    FootballApiValidator.validateGetPlayersByScopePaginationMoveToNextPage(response,
        PREMIER_LEAGUE_COMP_ID, resultsLimit);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting players")
  public void getPlayersWithDefaultPaginationLimitParam() {

    var response = PlayersEndpoint.getPlayers();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/players response returned by the server is cached for 1 day")
  public void verifyGetPlayersResponseIsCached() {

    var response = PlayersEndpoint.getPlayers();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_DAY);
  }

  @ParameterizedTest(name = "Verify getting list of top players for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void getTopPlayersForLang(UrlParamValues.Language lang) {

    var response = TopPlayersEndpoint.getTopPlayers(lang.getValue(),
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOP_PLAYERS_SCHEMA))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, listOfStringsContainsCyrillic());
    }
  }

  @ParameterizedTest()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  @DisplayName(value = "Verify getting list of top players for empty or null language: {arguments}")
  public void getTopPlayersForLangAsEmptyOrNullString(String lang) {

    var response = TopPlayersEndpoint.getTopPlayers(lang,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOP_PLAYERS_SCHEMA))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_PLAYER_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements())
        .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
  }
}
