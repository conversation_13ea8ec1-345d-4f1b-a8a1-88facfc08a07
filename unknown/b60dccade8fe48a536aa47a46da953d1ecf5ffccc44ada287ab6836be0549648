package com.fansunited.automation.predictionapi.predictions.delete;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.INVALID_PREDICTION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;
import static com.fansunited.automation.core.apis.predictionapi.PredictionEndpoint.deletePrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionEndpoint.deletePredictionUsingStaffUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionEndpoint.deletePredictionUsingUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createSinglePredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictions;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - DELETE /v1/predictions endpoint validation tests")
public class DeletePredictionsValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify API cannot delete prediction with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void deletePredictionWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var response = deletePrediction(predictionId, CLIENT_AUTOMATION_ID,
        argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API cannot delete prediction with invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void deletePredictionWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var response =
        deletePrediction(predictionId, argumentsHolder.getJwtToken());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API cannot delete prediction without auth")
  public void deletePredictionWithoutAuth() throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var response =
        deletePrediction(predictionId, null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify one user cannot delete predictions of another user within the same project")
  public void deleteAnotherUserPrediction() throws HttpException, IOException,
      FirebaseAuthException, InterruptedException, ExecutionException {

    var predictionId = createSinglePredictions(1).get(0);

    var response = deletePredictionUsingUser(predictionId, createUser().getEmail());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify user from one project cannot delete predictions of user from another project")
  @EnabledIf("isUseStageEnvironment")
  public void deleteAnotherProjectsUsersPrediction() throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var response = deletePrediction(predictionId, CLIENT_PRODUCTION_TESTING_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify staff user cannot delete end user's predictions")
  public void deleteUserPredictionUsingStaffAccount() throws HttpException {

    var predictionId = createSinglePredictions(2).get(0);

    var response = deletePredictionUsingStaffUser(predictionId, ADMIN_USER);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when deleting prediction with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void deletePredictionWithInvalidClientId(String clientId) throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var response =
        deletePrediction(predictionId, clientId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when deleting prediction with invalid prediction id")
  public void deletePredictionWithInvalidPredictionId() throws HttpException {

    var response =
        deletePrediction(INVALID_PREDICTION_ID, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when deleting prediction with non supported content type")
  public void deletePredictionWithNonSupportedContentType() throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var deletePredictionResponse =
        deletePrediction(predictionId, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.TEXT);

    currentTestResponse.set(deletePredictionResponse);

    ErrorValidator.validateErrorResponse(deletePredictionResponse, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API successfully deletes prediction if content type is NOT specified")
  public void deletePredictionWithoutSpecifyingContentType() throws HttpException {

    var predictionId = createSinglePredictions(1).get(0);

    var deletePredictionResponse =
        deletePrediction(predictionId, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(deletePredictionResponse);

    deletePredictionResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

      getOwnPredictions()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }
}
