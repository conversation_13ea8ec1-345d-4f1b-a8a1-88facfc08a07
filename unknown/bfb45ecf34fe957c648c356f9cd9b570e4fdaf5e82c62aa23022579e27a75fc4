package com.fansunited.automation.discussionapi.posts.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_USER_ID;
import static com.fansunited.automation.core.apis.discussionapi.BanUserEndpoint.banUser;
import static com.fansunited.automation.core.apis.discussionapi.GetPostsByUserIdEndpoint.getPostByUserIdPost;
import static com.fansunited.automation.core.apis.discussionapi.UnbanUsersEndpoint.usersUnban;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.core.Is.is;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.model.discussionapi.request.BanUserRequest;
import com.fansunited.automation.model.discussionapi.request.UnbanUserRequest;
import com.fansunited.automation.validators.DiscussionValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - /v1/discussions/users/{user_id}/ban endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UnbanUserTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify -Unban user happy path")
  public void unbanUserHappyPath() throws HttpException {
    var request = new BanUserRequest(-1, "Only for test");

    var response = banUser(request, getCurrentTestUser().getUid());

    currentTestResponse.set(response);

    DiscussionValidator.validateBanUserResponse(response, request, CLIENT_AUTOMATION_USER_ID);

    var object = UnbanUserRequest.builder().unban_reason("Only for test").build();
    var unbanResponse = usersUnban(object, getCurrentTestUser().getUid());
    currentTestResponse.set(unbanResponse);

    unbanResponse.then().statusCode(HttpStatus.SC_NO_CONTENT);

    PostsGenerator.createPublicPosts(1, getCurrentTestUser().getEmail());

    var getUserPostAfterUnbanResponse =
        getPostByUserIdPost(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, null);

    getUserPostAfterUnbanResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.id", is(notNullValue()));
  }
}
