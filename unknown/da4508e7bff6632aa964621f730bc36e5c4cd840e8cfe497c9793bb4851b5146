{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "kickoff_at", "finished_at", "updated_at", "home_team", "away_team", "lineups_confirmed", "started_at", "minute", "scores", "context", "status"], "properties": {"id": {"type": "string"}, "kickoff_at": {"type": "string"}, "finished_at": {"type": ["string", "null"]}, "updated_at": {"type": ["string", "null"]}, "home_team": {"type": "object", "required": ["id", "country", "assets", "national", "code", "gender", "name", "full_name", "short_name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "national": {"type": "boolean"}, "code": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "short_name": {"type": ["string", "null"]}}}, "away_team": {"type": "object", "required": ["id", "country", "assets", "national", "code", "gender", "name", "full_name", "short_name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "national": {"type": "boolean"}, "code": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "short_name": {"type": ["string", "null"]}}}, "lineups_confirmed": {"type": ["boolean", "null"]}, "started_at": {"type": ["string", "null"]}, "minute": {"type": ["string", "null"]}, "scores": {"type": "object", "required": ["ft_score", "ht_score", "aet_score", "agg_score", "pen_score"], "properties": {"ft_score": {"type": "object", "required": ["home_goals", "away_goals"], "properties": {"home_goals": {"type": ["integer", "null"]}, "away_goals": {"type": ["integer", "null"]}}}, "ht_score": {"type": "object", "required": ["home_goals", "away_goals"], "properties": {"home_goals": {"type": ["integer", "null"]}, "away_goals": {"type": ["integer", "null"]}}}, "aet_score": {"type": "object", "required": ["home_goals", "away_goals"], "properties": {"home_goals": {"type": ["integer", "null"]}, "away_goals": {"type": ["integer", "null"]}}}, "agg_score": {"type": "object", "required": ["home_goals", "away_goals"], "properties": {"home_goals": {"type": ["integer", "null"]}, "away_goals": {"type": ["integer", "null"]}}}, "pen_score": {"type": "object", "required": ["home_goals", "away_goals"], "properties": {"home_goals": {"type": ["integer", "null"]}, "away_goals": {"type": ["integer", "null"]}}}}}, "context": {"type": "object", "required": ["competition"], "properties": {"competition": {"type": ["object", "null"], "required": ["id", "country", "gender", "assets", "type", "name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "gender": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "type": {"type": ["string", "null"]}, "name": {"type": "string"}}}}}, "status": {"type": "object", "required": ["type", "sub_type"], "properties": {"type": {"type": "string"}, "sub_type": {"type": "string", "enum": ["finished", "cancelled", "postponed", "halftime", "not_started", "interrupted", "1st_half", "2nd_half", "finished_after_awarded_win", "to_finish", "finished_aet", "finished_ap", "kick_off_delayed", "abandoned", "waiting_for_penalty", "penalty", "pause", "extra_time_1st_half", "extra_time_end_of_1st_half", "extra_time_2nd_half", "waiting_for_extra_time", "no_info_yet", "awaiting_info", "finished_asg"]}}}}}]}}}}