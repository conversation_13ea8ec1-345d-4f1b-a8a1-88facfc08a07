{"type": "object", "properties": {"$schema": {"type": "string"}, "$id": {"type": "string"}, "type": {"type": "string"}, "default": {"type": "object"}, "title": {"type": "string"}, "required": {"type": "array", "items": [{"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}, {"type": "string"}]}, "properties": {"type": "object", "properties": {"checked": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "boolean"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "boolean"}]}}, "required": ["type", "default", "title", "examples"]}, "dimensions": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "object"}, "title": {"type": "string"}, "required": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}, "properties": {"type": "object", "properties": {"width": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "integer"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "integer"}]}}, "required": ["type", "default", "title", "examples"]}, "height": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "integer"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "integer"}]}}, "required": ["type", "default", "title", "examples"]}}, "required": ["width", "height"]}, "examples": {"type": "array", "items": [{"type": "object", "properties": {"width": {"type": "integer"}, "height": {"type": "integer"}}, "required": ["width", "height"]}]}}, "required": ["type", "default", "title", "required", "properties", "examples"]}, "id": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "integer"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "integer"}]}}, "required": ["type", "default", "title", "examples"]}, "name": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "string"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "string"}]}}, "required": ["type", "default", "title", "examples"]}, "price": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "number"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "number"}]}}, "required": ["type", "default", "title", "examples"]}, "tags": {"type": "object", "properties": {"type": {"type": "string"}, "default": {"type": "array", "items": {}}, "title": {"type": "string"}, "items": {"type": "object", "properties": {"type": {"type": "string"}, "title": {"type": "string"}, "examples": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["type", "title", "examples"]}, "examples": {"type": "array", "items": [{"type": "array", "items": [{"type": "string"}, {"type": "string"}]}]}}, "required": ["type", "default", "title", "items", "examples"]}}, "required": ["checked", "dimensions", "id", "name", "price", "tags"]}, "examples": {"type": "array", "items": [{"type": "object", "properties": {"checked": {"type": "boolean"}, "dimensions": {"type": "object", "properties": {"width": {"type": "integer"}, "height": {"type": "integer"}}, "required": ["width", "height"]}, "id": {"type": "integer"}, "name": {"type": "string"}, "price": {"type": "number"}, "tags": {"type": "array", "items": [{"type": "string"}, {"type": "string"}]}}, "required": ["checked", "dimensions", "id", "name", "price", "tags"]}]}}}