package com.fansunited.automation.predictionapi.predictions.post;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.FU_MATCH_ID_PREFIX;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_MATCH_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.MatchStatus.UPCOMING;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.MatchStatus.getStatusSubTypes;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.SUB_TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_OUTCOME;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_RESETTLED_AT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_SETTLED_AT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_STATUS;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_MATCHES_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.Language.EN;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.INVALID_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getFullCoverageCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getFinishedMatchesIdList;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getRandomMatchDto;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createMatchQuizGameForMarkets;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getAllFixtures;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getPlayerFixtures;
import static com.fansunited.automation.core.resolver.MatchGenerator.STATUS_POSTPONED;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGameStatusToUpdate;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.helpers.WaitHelper.waitGameStatusToBeUpdated;
import static com.fansunited.automation.model.predictionapi.games.enums.GameStatus.SETTLED;
import static com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket.getPlayerMarkets;
import static it.ozimov.cirneco.hamcrest.HamcrestMatchers.hasDistinctElements;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.BothTeamsScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CornersMatchPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.DoubleChancePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.FullTimeOneXTwoPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.HalfTimeFullTimePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.HalfTimeOneXTwoPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_7_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_2_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PenaltyMatchPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerRedCardPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScoreFirstGoalPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScoreHattrickPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScoreTwicePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerYellowCardPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionResult;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.RedCardMatchPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.DoubleChanceOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.HalfTimeFullTimeOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OneXTwoOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.invalid.InvalidMarketPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - POST /v1/predictions endpoint validation tests")
public class CreatePredictionsValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify prediction cannot be created with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void createPredictionWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response = createPrediction(CreatePredictionRequest.builder().build(), CLIENT_AUTOMATION_ID,
        argumentsHolder.getApiKey(),
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify prediction cannot be created with invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void createPredictionWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        createPrediction(CreatePredictionRequest.builder().build(), argumentsHolder.getJwtToken());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create prediction with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void createPredictionWithInvalidClientId(String clientId)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction =
        generateValidSinglePredictionFixture(PredictionMarket.FT_1X2, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response =
        createPrediction(createSinglePredictionRequest, clientId, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create a prediction with non supported content type")
  public void createPredictionWithInvalidContentType()
      throws HttpException {

    var response = createPrediction("", CLIENT_PRODUCTION_TESTING_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.TEXT);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify predictions cannot be created by clients from different project")
  @EnabledIf("isUseStageEnvironment")
  public void createPredictionForAnotherClient()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction =
        generateValidSinglePredictionFixture(PredictionMarket.FT_1X2, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest, CLIENT_PRODUCTION_TESTING_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction for finished match")
  public void createPredictionForFinishedMatch()
      throws HttpException {

    var matchId = getFinishedMatchesIdList(getFullCoverageCompetitionsWhitelist(), 1).get(0);

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction =
        generateValidSinglePredictionFixture(PredictionMarket.FT_1X2, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with invalid market. Market: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  public void createSinglePredictionWithInvalidMarket(PredictionMarket market)
      throws HttpException {

    var matchId = getFinishedMatchesIdList(getFullCoverageCompetitionsWhitelist(), 1).get(0);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(
            InvalidMarketPredictionFixture.builder()
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .market(market.getValue())
                .prediction(true)
                .ignoreGoals(true)
                .build()
        )).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'market' field")
  public void createSinglePredictionWithoutMarketField()
      throws HttpException {

    var matchId = getFinishedMatchesIdList(getFullCoverageCompetitionsWhitelist(), 1).get(0);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(
            InvalidMarketPredictionFixture.builder()
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .ignoreMarket(true)
                .ignoreGoals(true)
                .prediction(true)
                .build()
        )).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API successfully creates single prediction and ignores predefined result passed in the request body")
  public void createPredictionWithPredefinedResult()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = FullTimeOneXTwoPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(OneXTwoOutcome.TWO)
        .result(new PredictionResult(generateDateTimeInIsoFormat(ZonedDateTime.now()), null,
            ResultStatus.SETTLED,
            ResultOutcome.CORRECT, 0)).build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            contains(nullValue()))
        .body(FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            contains(nullValue()))
        .body(FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_STATUS,
            contains(ResultStatus.NOT_SETTLED.name()))
        .body(FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            contains(ResultOutcome.NOT_VERIFIED.name()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction with empty prediction fixture list")
  public void createPredictionWithEmptyPredictionFixtureList()
      throws HttpException {

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of()).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction with more than one prediction fixture")
  public void createSinglePredictionWithMoreThanOnePredictionFixture()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = List.of(
        FullTimeOneXTwoPredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(OneXTwoOutcome.TWO)
            .build(),
        HalfTimeFullTimePredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(HalfTimeFullTimeOutcome.ONE_ONE)
            .build()
    );

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }


  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single invalid prediction for market FT_1X2. Prediction outcome: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = OneXTwoOutcome.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  @NullSource
  public void createPredictionWithInvalidPredictionForMarketFT1X2(OneXTwoOutcome outcome)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = FullTimeOneXTwoPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(outcome)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with invalid match id for market FT_1X2. Match id: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @ValueSource(strings = INVALID_MATCH_ID)
  @NullAndEmptySource
  public void createSinglePredictionWithInvalidMatchIdForMarketFT1X2(String matchId)
      throws HttpException {

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(
            FullTimeOneXTwoPredictionFixture.builder()
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(OneXTwoOutcome.ONE)
                .build()
        )).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market FT_1X2")
  public void createSinglePredictionWithoutMatchIdFieldForMarketFT1X2()
      throws HttpException {

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(
            FullTimeOneXTwoPredictionFixture.builder()
                .matchType(MatchType.FOOTBALL.getValue())
                .ignoreMatchId(true)
                .prediction(OneXTwoOutcome.ONE)
                .build()
        )).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market FT_1X2")
  public void createSinglePredictionWithoutMatchTypeFieldForMarketFT1X2()
      throws HttpException {

    var matchId = getFinishedMatchesIdList(getFullCoverageCompetitionsWhitelist(), 1).get(0);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(
            FullTimeOneXTwoPredictionFixture.builder()
                .matchId(matchId)
                .ignoreMatchType(true)
                .prediction(OneXTwoOutcome.ONE)
                .build()
        )).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction for market FT_1X2 without 'prediction' field in the request body.")
  public void createPredictionWithoutPredictionFieldForMarketFT1X2()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = FullTimeOneXTwoPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single invalid prediction for market HT_1X2. Prediction outcome: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = OneXTwoOutcome.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  @NullSource
  public void createPredictionWithInvalidPredictionForMarketHT1X2(OneXTwoOutcome outcome)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = HalfTimeOneXTwoPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(outcome)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market HT_1X2.")
  public void createPredictionWithoutPredictionFieldForMarketTypeHT1X2()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = HalfTimeOneXTwoPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market HT_1X2.")
  public void createPredictionWithoutMatchIdFieldForMarketTypeHT1X2()
      throws HttpException {

    var prediction = HalfTimeOneXTwoPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(OneXTwoOutcome.ONE)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market HT_1X2.")
  public void createPredictionWithoutMatchTypeFieldForMarketTypeHT1X2()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = HalfTimeOneXTwoPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(OneXTwoOutcome.ONE)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single invalid prediction for market HT_FT. Prediction outcome: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = HalfTimeFullTimeOutcome.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  @NullSource
  @Disabled("Should be fixed in FZ-3872")
  public void createPredictionWithInvalidPredictionForMarketTypeHTFT(
      HalfTimeFullTimeOutcome outcome)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = HalfTimeFullTimePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(outcome)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market HT_FT.")
  public void createPredictionWithoutPredictionFieldForMarketHTFT()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = HalfTimeFullTimePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market HT_FT.")
  public void createPredictionWithoutMatchIdFieldForMarketHTFT()
      throws HttpException {

    var prediction = HalfTimeFullTimePredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(HalfTimeFullTimeOutcome.ONE_ONE)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market HT_FT.")
  public void createPredictionWithoutMatchTypeFieldForMarketHTFT()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = HalfTimeFullTimePredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(HalfTimeFullTimeOutcome.ONE_DRAW)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market BOTH_TEAMS_TO_SCORE.")
  public void createPredictionWithInvalidPredictionForMarketBothTeamsToScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = BothTeamsScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market BOTH_TEAMS_TO_SCORE.")
  public void createPredictionWithoutMatchIdFieldForMarketBothTeamsToScore()
      throws HttpException {

    var prediction = BothTeamsScorePredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market BOTH_TEAMS_TO_SCORE.")
  public void createPredictionWithoutMatchTypeFieldForMarketBothTeamsToScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = BothTeamsScorePredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single invalid prediction for market CORRECT_SCORE.")
  public void createPredictionWithInvalidPredictionForMarketCorrectScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = CorrectScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsAway(-1)
        .goalsHome(-2)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'goals_home' and 'goals_away' fields for market CORRECT_SCORE.")
  public void createPredictionWithoutPredictionFieldForMarketCorrectScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = CorrectScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignoreGoals(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market CORRECT_SCORE.")
  public void createPredictionWithoutMatchIdFieldForMarketCorrectScore()
      throws HttpException {

    var prediction = CorrectScorePredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(2)
        .goalsAway(1)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market CORRECT_SCORE.")
  public void createPredictionWithoutMatchTypeFieldForMarketCorrectScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = CorrectScorePredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .goalsHome(3)
        .goalsAway(1)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single invalid prediction for market CORNERS_MATCH.")
  public void createPredictionWithInvalidPredictionForMarketCornersMatch()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = CornersMatchPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(-1)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market CORNERS_MATCH.")
  public void createPredictionWithoutPredictionFieldForMarketCorrectCorners()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = CornersMatchPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market CORNERS_MATCH.")
  public void createPredictionWithoutMatchIdFieldForMarketCorrectCorners()
      throws HttpException {

    var prediction = CornersMatchPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(7)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market CORNERS_MATCH.")
  public void createPredictionWithoutMatchTypeFieldForMarketCorrectCorners()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = CornersMatchPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(8)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single invalid prediction for market DOUBLE_CHANCE. Prediction outcome: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = DoubleChanceOutcome.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_WORD_INVALID)
  @NullSource
  @Disabled("Should be fixed in FZ-3872")
  public void createPredictionWithInvalidPredictionForMarketDoubleChance(
      DoubleChanceOutcome outcome)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = DoubleChancePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(outcome)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market DOUBLE_CHANCE.")
  public void createPredictionWithoutPredictionFieldForMarketDoubleChance()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = DoubleChancePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market DOUBLE_CHANCE.")
  public void createPredictionWithoutMatchIdFieldForMarketDoubleChance()
      throws HttpException {

    var prediction = DoubleChancePredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(DoubleChanceOutcome.DRAW_TWO)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market DOUBLE_CHANCE.")
  public void createPredictionWithoutMatchTypeFieldForMarketDoubleChance()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = DoubleChancePredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(DoubleChanceOutcome.ONE_TWO)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single invalid prediction for market OVER_CORNERS. Prediction target: {arguments}")
  @Tag(REGRESSION) @Disabled("The Market OVER_CORNERS and OVER_GOALS does not require 'target' field anymore!")
  @ValueSource(floats = {-1.5f, 0.0f, 3.0f})
  public void createPredictionWithInvalidPredictionForMarketOverCorners(float target)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverCorners_7_5_PredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns STATUS OK when trying to create single prediction without 'prediction' and 'target' fields for some of the OVER_CORNER market.")
  public void createPredictionWithoutPredictionAndTargetFieldsForMarketOverCorners()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverCorners_7_5_PredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", containsString ("Prediction field is missing."));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns OK when trying to create single prediction without 'target' field for some of OVER_CORNERS markets.")
  public void createPredictionWithoutTargetFieldForMarketOverCorners()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverCorners_7_5_PredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns OK when trying to create single prediction without 'prediction' field for some of the OVER_CORNERS markets.")
  public void createPredictionWithoutPredictionFieldForMarketOverCorners()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverCorners_7_5_PredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", containsString ("Prediction field is missing."));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market OVER_CORNERS.")
  public void createPredictionWithoutMatchIdFieldForMarketOverCorners()
      throws HttpException {

    var prediction = OverCorners_7_5_PredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market OVER_CORNERS.")
  public void createPredictionWithoutMatchTypeFieldForMarketOverCorners()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverCorners_7_5_PredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns OK when trying to create single prediction without 'prediction' field for some of the OVER_GOALS markets.")
  public void createPredictionWithoutPredictionFieldForMarketOverGoals()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverGoals_2_5_PredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);
    response
        .then()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", containsString ("Prediction field is missing."));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for some of the OVER_GOALS markets.")
  public void createPredictionWithoutMatchIdFieldForMarketOverGoals()
      throws HttpException {

    var prediction = OverGoals_2_5_PredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' for some of the OVER_GOALS markets.")
  public void createPredictionWithoutMatchTypeFieldForMarketOverGoals()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = OverGoals_2_5_PredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PENALTY_MATCH")
  public void createPredictionWithoutPredictionFieldForPenaltyMatch()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PenaltyMatchPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PENALTY_MATCH")
  public void createPredictionWithoutMatchIdFieldForPenaltyMatch()
      throws HttpException {

    var prediction = PenaltyMatchPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PENALTY_MATCH")
  public void createPredictionWithoutMatchTypeFieldForPenaltyMatch()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PenaltyMatchPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with null or empty player id for market PLAYER_RED_CARD. Prediction player id: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void createPredictionWithInvalidPlayerIdForMarketPlayerRedCard(String playerId)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerRedCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'player_id' field for market PLAYER_RED_CARD")
  public void createPredictionWithoutPlayerIdFieldForPlayerRedCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerRedCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PLAYER_RED_CARD")
  public void createPredictionWithoutPredictionFieldForPlayerRedCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerRedCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' and 'player_id' fields for market PLAYER_RED_CARD")
  public void createPredictionWithoutPredictionAndPlayerIdFieldsForPlayerRedCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerRedCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PLAYER_RED_CARD")
  public void createPredictionWithoutMatchIdFieldForPlayerRedCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerRedCardPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PLAYER_RED_CARD")
  public void createPredictionWithoutMatchTypeFieldForPlayerRedCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerRedCardPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns OK when trying to create single prediction with null or empty player id for market PLAYER_SCORE_FIRST_GOAL. Prediction player id: {arguments}")
  public void createPredictionWithNobodyPlayerIdForMarketPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);
    // it is changed because Allow users to specify “nobody“ when predicting for market “first goalscorrer in match“, since 0:0 is a valid and frequent result.
    currentTestResponse.set(response);
    response
        .then()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @Tag(value = REGRESSION)
  @DisplayName("Verify API returns OK when trying to create single prediction with null or empty player id for market PLAYER_SCORE_FIRST_GOAL")
  public void createPredictionWithInvalidPlayerIdForMarketPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId("")
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);
    // it is changed because Allow users to specify “nobody“ when predicting for market “first goalscorrer in match“, since 0:0 is a valid and frequent result.
    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response,HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns OK when trying to create single prediction without 'player_id' field for market PLAYER_SCORE_FIRST_GOAL")
  public void createPredictionWithoutPlayerIdFieldForPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);
    // it is changed because Allow users to specify “nobody“ when predicting for market “first goalscorrer in match“, since 0:0 is a valid and frequent result.
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PLAYER_SCORE_FIRST_GOAL")
  public void createPredictionWithoutPredictionFieldForPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' and 'player_id' fields for market PLAYER_SCORE_FIRST_GOAL")
  public void createPredictionWithoutPredictionAndPlayerIdFieldsForPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PLAYER_SCORE_FIRST_GOAL")
  public void createPredictionWithoutMatchIdFieldForPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PLAYER_SCORE_FIRST_GOAL")
  public void createPredictionWithoutMatchTypeFieldForPlayerScoreFirstGoal()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreFirstGoalPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with null or empty player id for market PLAYER_SCORE_HATTRICK. Prediction player id: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void createPredictionWithInvalidPlayerIdForMarketPlayerScoreHattrick(String playerId)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreHattrickPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'player_id' field for market PLAYER_SCORE_HATTRICK")
  public void createPredictionWithoutPlayerIdFieldForPlayerScoreHattrick()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreHattrickPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PLAYER_SCORE_HATTRICK")
  public void createPredictionWithoutPredictionFieldForPlayerScoreHattrick()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreHattrickPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' and 'player_id' fields for market PLAYER_SCORE_HATTRICK")
  public void createPredictionWithoutPredictionAndPlayerIdFieldsForPlayerScoreHattrick()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreHattrickPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PLAYER_SCORE_HATTRICK")
  public void createPredictionWithoutMatchIdFieldForPlayerScoreHattrick()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreHattrickPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PLAYER_SCORE_HATTRICK")
  public void createPredictionWithoutMatchTypeFieldForPlayerScoreHattrick()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreHattrickPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with null or empty player id for market PLAYER_SCORE. Prediction player id: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void createPredictionWithInvalidPlayerIdForMarketPlayerScore(String playerId)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'player_id' field for market PLAYER_SCORE")
  public void createPredictionWithoutPlayerIdFieldForPlayerScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PLAYER_SCORE")
  public void createPredictionWithoutPredictionFieldForPlayerScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' and 'player_id' fields for market PLAYER_SCORE")
  public void createPredictionWithoutPredictionAndPlayerIdFieldsForPlayerScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScorePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PLAYER_SCORE")
  public void createPredictionWithoutMatchIdFieldForPlayerScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScorePredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PLAYER_SCORE")
  public void createPredictionWithoutMatchTypeFieldForPlayerScore()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScorePredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with null or empty player id for market PLAYER_SCORE_TWICE. Prediction player id: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void createPredictionWithInvalidPlayerIdForMarketPlayerScoreTwice(String playerId)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreTwicePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'player_id' field for market PLAYER_SCORE_TWICE")
  public void createPredictionWithoutPlayerIdFieldForPlayerScoreTwice()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreTwicePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PLAYER_SCORE_TWICE")
  public void createPredictionWithoutPredictionFieldForPlayerScoreTwice()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreTwicePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' and 'player_id' fields for market PLAYER_SCORE_TWICE")
  public void createPredictionWithoutPredictionAndPlayerIdFieldsForPlayerScoreTwice()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerScoreTwicePredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PLAYER_SCORE_TWICE")
  public void createPredictionWithoutMatchIdFieldForPlayerScoreTwice()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreTwicePredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PLAYER_SCORE_TWICE")
  public void createPredictionWithoutMatchTypeFieldForPlayerScoreTwice()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerScoreTwicePredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create single prediction with null or empty player id for market PLAYER_YELLOW_CARD. Prediction player id: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void createPredictionWithInvalidPlayerIdForMarketPlayerYellowCard(String playerId)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerYellowCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'player_id' field for market PLAYER_YELLOW_CARD")
  public void createPredictionWithoutPlayerIdFieldForPlayerYellowCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerYellowCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market PLAYER_YELLOW_CARD")
  public void createPredictionWithoutPredictionFieldForPlayerYellowCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerYellowCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' and 'player_id' fields for market PLAYER_YELLOW_CARD")
  public void createPredictionWithoutPredictionAndPlayerIdFieldsForPlayerYellowCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = PlayerYellowCardPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .ignorePlayer(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market PLAYER_YELLOW_CARD")
  public void createPredictionWithoutMatchIdFieldForPlayerYellowCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerYellowCardPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market PLAYER_YELLOW_CARD")
  public void createPredictionWithoutMatchTypeFieldForPlayerYellowCard()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = PlayerYellowCardPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .playerId(playerId)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'prediction' field for market RED_CARD_MATCH.")
  public void createPredictionWithInvalidPredictionForMarketRedCardMatch()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = RedCardMatchPredictionFixture.builder()
        .matchId(matchId)
        .matchType(MatchType.FOOTBALL.getValue())
        .ignorePrediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_id' field for market RED_CARD_MATCH.")
  public void createPredictionWithoutMatchIdFieldForMarketRedCardMatch()
      throws HttpException {

    var prediction = RedCardMatchPredictionFixture.builder()
        .ignoreMatchId(true)
        .matchType(MatchType.FOOTBALL.getValue())
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create single prediction without 'match_type' field for market RED_CARD_MATCH.")
  public void createPredictionWithoutMatchTypeForMarketRedCardMatch()
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var prediction = RedCardMatchPredictionFixture.builder()
        .matchId(matchId)
        .ignoreMatchType(true)
        .prediction(true)
        .build();

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of(prediction)).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X with missing prediction for one of the game fixtures")
  public void createPredictionForTopXWithMissingFixture()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.subList(0, matchesIdList.size() - 1).forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, matchId,
                null)));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X with same match id for all prediction fixtures")
  public void createPredictionForTopXWithSameMatchId()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE,
                matchesIdList.get(0),
                null)));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag("FZ-475")})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X without 'match_id' field")
  public void createPredictionForTopXWithoutMatchIdField()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.add(
            CorrectScorePredictionFixture
                .builder()
                .ignoreMatchId(true)
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(2)
                .build())
    );

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag("FZ-475")})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X without 'match_type' field")
  public void createPredictionForTopXWithoutMatchTypeField()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.add(
            CorrectScorePredictionFixture
                .builder()
                .matchId(matchId)
                .ignoreMatchType(true)
                .goalsHome(3)
                .goalsAway(2)
                .build())
    );

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X without 'goals_home' and 'goals_away' fields")
  public void createPredictionForTopXWithoutGoalsHomeGoalsAwayFields()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.add(
            CorrectScorePredictionFixture
                .builder()
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .ignoreGoals(true)
                .build())
    );

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X without 'market' field")
  public void createPredictionForTopXWithoutMarketField()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.add(
            InvalidMarketPredictionFixture
                .builder()
                .ignoreMarket(true)
                .ignorePrediction(true)
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(3)
                .build())
    );

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X with prediction market different than CORRECT_SCORE")
  public void createPredictionForTopXWithInvalidPredictionMarket()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    var validMarkets = PredictionMarket.getValidMarkets()
        .stream()
        .filter(market -> market != PredictionMarket.CORRECT_SCORE)
        .toList();

    matchesIdList.forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(
                validMarkets.get(generateRandomNumber(0, validMarkets.size() - 1)),
                matchId,
                null)));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type MATCH_QUIZ with missing prediction for one of the game fixtures")
  public void createPredictionForMatchQuizWithMissingFixture()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions.subList(0, predictions.size() - 1))
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction with different match id for game type MATCH_QUIZ")
  public void createPredictionForMatchQuizWithInvalidFixture()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = getSingleMatchIdAfterDate(getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(generateFutureDate(720)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag("FZ-475")})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type MATCH_QUIZ without 'match_id' field")
  public void createPredictionForMatchQuizWithoutMatchIdField()
      throws HttpException, IllegalArgumentException {

    var markets = List.of(PredictionMarket.CORRECT_SCORE, PredictionMarket.FT_1X2);

    var createGameResponse = createMatchQuizGameForMarkets(GameStatus.OPEN, markets);

    var gameObject = createGameResponse.as(GameInstance.class);

    var predictions =
        new ArrayList<>(List.of(
            CorrectScorePredictionFixture.builder()
                .ignoreMatchId(true)
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsAway(4)
                .goalsAway(2)
                .build(),
            FullTimeOneXTwoPredictionFixture.builder()
                .ignoreMatchId(true)
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(OneXTwoOutcome.ONE)
                .build()
        ));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag("FZ-475")})
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type MATCH_QUIZ without 'match_type' field")
  public void createPredictionForMatchQuizWithoutMatchTypeField()
      throws HttpException, IllegalArgumentException {

    var markets = List.of(PredictionMarket.CORRECT_SCORE, PredictionMarket.FT_1X2);

    var createGameResponse = createMatchQuizGameForMarkets(GameStatus.OPEN, markets);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var predictions =
        new ArrayList<>(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchId)
                .ignoreMatchType(true)
                .goalsAway(4)
                .goalsAway(2)
                .build(),
            FullTimeOneXTwoPredictionFixture.builder()
                .matchId(matchId)
                .ignoreMatchType(true)
                .prediction(OneXTwoOutcome.ONE)
                .build()
        ));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type MATCH_QUIZ without 'market' field")
  public void createPredictionForMatchQuizWithoutMarketField()
      throws HttpException, IllegalArgumentException {

    var markets = List.of(PredictionMarket.CORRECT_SCORE, PredictionMarket.BOTH_TEAMS_SCORE);

    var createGameResponse = createMatchQuizGameForMarkets(GameStatus.OPEN, markets);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var predictions =
        new ArrayList<>(List.of(
            InvalidMarketPredictionFixture.builder()
                .ignoreMarket(true)
                .ignorePrediction(true)
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsAway(4)
                .goalsAway(2)
                .build(),
            InvalidMarketPredictionFixture.builder()
                .ignoreMarket(true)
                .ignoreGoals(true)
                .matchId(matchId)
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .build()
        ));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST/NOT_FOUND when trying to create prediction for game with empty/invalid id: Game ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_GAME_ID)
  @EmptySource
  public void createPredictionForGameWithInvalidId(String id)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        generateDateTimeInIsoFormat(generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(id)
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response,
        List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_NOT_FOUND));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to create prediction for game with empty prediction fixture list. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void createPredictionForGameWithInvalidId(GameType gameType)
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(gameType, 1).get(0);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(List.of())
        .gameInstanceId(gameId)
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type TOP_X that has status which does NOT allow predictions")
  public void createPredictionForGameTopXWithNotAllowedStatus()
      throws HttpException, InterruptedException {

    var predictionsCutoff = ZonedDateTime.now().plusMinutes(2);

    var gameFixtureList =
        generateValidFixturesForGameType(GameType.TOP_X, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.TOP_X + " " + UUID.randomUUID())
        .description(GameType.TOP_X + " " + UUID.randomUUID())
        .type(GameType.TOP_X.getValue())
        .predictionsCutoff(generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var createGameResponse = createGame(createGameRequest);

    var specificGameResponse =
        GameEndpoint.getGameById(
            createGameResponse.then().extract().body().jsonPath().get(ID_PROP));

    var gameObject = specificGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var predictions = new ArrayList<PredictionFixture>();

    matchesIdList.forEach(
        matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE,
                matchId,
                null)));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

// Wait for game status to be updated to "SETTLED"
waitGameStatusToBeUpdated(gameObject, SETTLED, 5, 240);
    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to create prediction for game type MATCH_QUIZ that has status which does NOT allow predictions")
  public void createPredictionForGameMatchQuizWithNotAllowedStatus()
      throws HttpException, InterruptedException {

    var predictionsCutoff = ZonedDateTime.now().plusMinutes(4);

    var gameFixtureList =
        generateValidFixturesForGameType(GameType.MATCH_QUIZ, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .type(GameType.MATCH_QUIZ.getValue())
        .predictionsCutoff(generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var createGameResponse = createGame(createGameRequest);

    var specificGameResponse =
        GameEndpoint.getGameById(
            createGameResponse.then().extract().body().jsonPath().get(ID_PROP));

    var gameObject = specificGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    var predictions = getAllFixtures(matchId, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    waitForGameStatusToUpdate(gameObject.getId(), SETTLED.getValue(), 10000, 1000);
    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify creation of prediction for Player Markets with player from different than the two teams")
  public void createPredictionsForMatchQuizWithNotAllowedPlayer()
      throws HttpException {

    var gameId =  createMatchQuizGameForMarkets(GameStatus.OPEN, getPlayerMarkets()).getBody().jsonPath().getString("id");

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var matchId = gameInstance.getFixtures().get(0).getMatchId();

    var invalidPlayerId = "fb:p:1234";

    var predictionFixtures = getPlayerFixtures(matchId, invalidPlayerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictionFixtures)
        .gameInstanceId(gameInstance.getId())
        .build();

    var response = createPrediction(createPredictionRequest);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo(String.format("Player with an id %s is not part of the participating teams. Field: fixtures",
            invalidPlayerId)));
  }

  @Test
  @DisplayName("Verify that cannot make predictions when match is postponed")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(LOCAL)})
  public void verifyCreationOfPredictionForPostponedMatches() throws HttpException {

    var matchId = getRandomMatchDto().getId();
    var matchType = STATUS_POSTPONED.getType();

    Resolver.updateMatchToAnotherStatus(matchId, STATUS_POSTPONED);

    var response =
        MatchesEndpoint.getMatches(
            null,
            null,
            null,
            matchId,
            null,
            null,
            null,
            1,
            1,
            null,
            null,
            EN.getValue(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            CLIENT_AUTOMATION_ID);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body(
            "data." + ApiConstants.FootballApi.ID_PROP,
            Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ApiConstants.FootballApi.ID_PROP, hasDistinctElements())
        .body("data." + STATUS_PROP + "." + TYPE_PROP, everyItem(is(matchType)))
        .body(
            "data." + STATUS_PROP + "." + SUB_TYPE_PROP,
            Every.everyItem(is(in(getStatusSubTypes(UPCOMING)))));

    var playerId = getRandomPlayerFromMatch(matchId);
    var prediction =
        generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, matchId, playerId);
    var createSinglePredictionRequest =
        CreatePredictionRequest.builder().fixtures(prediction).build();
    var predictionResponse = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(predictionResponse);

    predictionResponse.then().statusCode(HttpStatus.SC_BAD_REQUEST);
  }
}
