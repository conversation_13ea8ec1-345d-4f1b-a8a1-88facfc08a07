package com.fansunited.automation.reportingapi.follows;

import static com.fansunited.automation.constants.ApiConstants.ReportingApi.ALL_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.BREAKDOWN_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.FOLLOWS_PROP;
import static com.fansunited.automation.constants.ApiConstants.ReportingApi.ID_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Follows.GET_TOP_FOLLOWED_COMPETITIONS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getFakeCompetitionIds;
import static com.fansunited.automation.core.apis.reportingapi.helper.InterestGenerator.generateFootballInterestForCompType;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint;
import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.TopFollowedCompsEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.core.base.reportingapi.TruncateTablesHelper;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.CustomHamcrestMatchers;
import com.fansunited.automation.helpers.PeriodGenerator;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.footballapi.matches.Competition;
import com.fansunited.automation.model.reportingapi.follows.topfollowedcomps.FollowBreakdownDto;
import com.fansunited.automation.model.reportingapi.follows.topfollowedcomps.FollowsPerUserDto;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/follows/football/competitions endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetTopFollowedCompsTests extends ReportingApiBaseTest {

  private static final int TOP_FOLLOWED_COMP_LIMIT = 20;

  @AfterAll
  public static void afterAll() {
    // this should not be done in separate test classes, but in base test only. Unfortunately, some
    // tests in reporting api need this to be run once more
    TruncateTablesHelper.executeTruncateTables();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns top 20 followed competitions in DESC order")
  public void getTopFollowedComps() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(10);
    var toDate = LocalDate.now().minusDays(9);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var compList = getFakeCompetitionIds(20);

    Map<String, AtomicInteger> compFollowMap =
        compList.stream().collect(Collectors.toMap(compId -> compId, comp -> new AtomicInteger()));

    dates.forEach(date ->
        compList.stream().parallel().forEach(
            comp -> {
              var followCount = generateRandomNumber(1, 3);
              InsertBigQData.generateFollowEvents(followCount,
                  convertLocalDateToDateTime(date),
                  generateFootballInterestForCompType(comp, true), true, 10);
              compFollowMap.get(comp).getAndAdd(followCount);
            }));

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        TopFollowedCompsEndpoint.getTopFollowedComps(fromDate.toString(), toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_TOP_FOLLOWED_COMPETITIONS_SCHEMA))
        .body("data." + BREAKDOWN_PROP, hasSize(compList.size()))
        .body("data." + BREAKDOWN_PROP + "." + FOLLOWS_PROP + "." + ALL_PROP,
            CustomHamcrestMatchers.IsInDescendingNumericalOrder());

    currentTestResponse.set(response);

    var followsPerUserDto = response.as(FollowsPerUserDto.class);

    compList.forEach(comp -> {
          var optional = followsPerUserDto.getData()
              .getBreakdown()
              .stream()
              .filter(followBreakdownDto -> followBreakdownDto.getId()
                  .equals(comp)).findFirst();
          if (optional.isEmpty()) {
            throw new AssertionError(
                "API returned incorrect competition breakdown list. Expected: '"
                    + comp
                    + "' to be in the list, Actual: "
                    + followsPerUserDto.getData()
                    .getBreakdown()
                    .stream()
                    .map(FollowBreakdownDto::getId)
                    .toList());
          } else {
            MatcherAssert.assertThat(
                "API returned incorrect follow count for competition: " + optional.get().getId(),
                optional.get().getFollows().getAll(),
                equalTo(compFollowMap.get(optional.get().getId()).longValue()));
          }
        }
    );

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerUserDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerUserDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerUserDto.getMeta().getGroupedBy(), equalTo(GroupBy.COMPETITION));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns only top 20 followed competitions when having more than 20 competitions for a specified date range")
  public void verifyTopFollowedCompLimit() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(8);
    var toDate = LocalDate.now().minusDays(7);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var compList = getFakeCompetitionIds(25);

    dates.forEach(date ->
        compList.stream().parallel().forEach(
            comp -> {
              var followCount = generateRandomNumber(1, 3);
              InsertBigQData.generateFollowEvents(followCount,
                  convertLocalDateToDateTime(date),
                  generateFootballInterestForCompType(comp, true), true, 10);
            }));

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        TopFollowedCompsEndpoint.getTopFollowedComps(fromDate.toString(), toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_TOP_FOLLOWED_COMPETITIONS_SCHEMA))
        .body("data." + BREAKDOWN_PROP, hasSize(TOP_FOLLOWED_COMP_LIMIT))
        .body("data." + BREAKDOWN_PROP + "." + FOLLOWS_PROP + "." + ALL_PROP,
            CustomHamcrestMatchers.IsInDescendingNumericalOrder());

    currentTestResponse.set(response);

    var followsPerUserDto = response.as(FollowsPerUserDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerUserDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerUserDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerUserDto.getMeta().getGroupedBy(), equalTo(GroupBy.COMPETITION));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-799")})
  @DisplayName("Verify API returns correct follow count when following/unfollowing a competition")
  public void verifyFollowUnfollowComp() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(6);
    var toDate = LocalDate.now().minusDays(5);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);

    var compList = getFakeCompetitionIds(5);

    Map<String, AtomicInteger> compFollowMap =
        compList.stream().collect(Collectors.toMap(compId -> compId,
            comp -> new AtomicInteger()));

    // Generate follow events
    dates.forEach(date ->
        compList.stream().parallel().forEach(
            comp -> {
              var followCount = 3;
              InsertBigQData.generateFollowEvents(followCount,
                  convertLocalDateToDateTime(date),
                  generateFootballInterestForCompType(comp, true), true, 10);
              compFollowMap.get(comp).getAndAdd(followCount);
            }));

    // Generate unfollow events
    dates.forEach(date ->
        compList.stream().parallel().forEach(
            comp -> {
              InsertBigQData.generateFollowEvents(1,
                  convertLocalDateToDateTime(date),
                  generateFootballInterestForCompType(comp, true), false, -10);
              compFollowMap.get(comp).getAndDecrement();
            }));

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        TopFollowedCompsEndpoint.getTopFollowedComps(fromDate.toString(), toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_TOP_FOLLOWED_COMPETITIONS_SCHEMA))
        .body("data." + BREAKDOWN_PROP, hasSize(compList.size()))
        .body("data." + BREAKDOWN_PROP + "." + FOLLOWS_PROP + "." + ALL_PROP,
            CustomHamcrestMatchers.IsInDescendingNumericalOrder());

    currentTestResponse.set(response);

    var followsPerUserDto = response.as(FollowsPerUserDto.class);

    compList.forEach(comp -> {
          var optional = followsPerUserDto.getData()
              .getBreakdown()
              .stream()
              .filter(followBreakdownDto -> followBreakdownDto.getId()
                  .equals(comp)).findFirst();
          if (optional.isEmpty()) {
            throw new AssertionError(
                "API returned incorrect competition breakdown list. Expected: '"
                    + comp
                    + "' to be in the list, Actual: "
                    + followsPerUserDto.getData()
                    .getBreakdown()
                    .stream()
                    .map(FollowBreakdownDto::getId)
                    .toList());
          } else {
            MatcherAssert.assertThat(
                "API returned incorrect follow count for competition: " + optional.get().getId(),
                optional.get().getFollows().getAll(),
                equalTo(compFollowMap.get(optional.get().getId()).longValue()));
          }
        }
    );

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerUserDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerUserDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerUserDto.getMeta().getGroupedBy(), equalTo(GroupBy.COMPETITION));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-772")})
  @DisplayName("Verify API returns top followed competition data gathered only from the specified date range")
  public void verifyTopFollowedCompDateRange() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(4);
    var toDate = LocalDate.now().minusDays(3);

    var dates = PeriodGenerator.genPeriod(fromDate, toDate, Periodicity.DAY);
    var compSubList = getFakeCompetitionIds(3);
    // Generate followers for competitions within the specified date range
    dates.forEach(date ->
        compSubList.stream().parallel().forEach(
            comp -> {
              var followCount = 1;
              InsertBigQData.generateFollowEvents(followCount,
                  convertLocalDateToDateTime(date),
                  generateFootballInterestForCompType(comp, true), true, 10);
            }));

    var compSubListOutsideTheDateRange = getFakeCompetitionIds(3);

    // Generate followers for competitions before from date
    compSubListOutsideTheDateRange.stream().parallel().forEach(
        comp -> {
          var followCount = 1;
          InsertBigQData.generateFollowEvents(followCount,
              convertLocalDateToDateTime(dates.get(0).minusDays(7)),
              generateFootballInterestForCompType(comp, true), true, 10);
        });

    // Generate followers for competitions after to date
    compSubListOutsideTheDateRange.stream().parallel().forEach(
        comp -> {
          var followCount = 1;
          InsertBigQData.generateFollowEvents(followCount,
              convertLocalDateToDateTime(dates.get(dates.size() - 1).plusDays(7)),
              generateFootballInterestForCompType(comp, true), true, 10);
        });

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response =
        TopFollowedCompsEndpoint.getTopFollowedComps(fromDate.toString(), toDate.toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_TOP_FOLLOWED_COMPETITIONS_SCHEMA))

        // Ensure every item in compSubList is contained in data.breakdown.id
        .body("data." + BREAKDOWN_PROP + "." + ID_PROP, hasItems(compSubList.toArray()))

        // Ensure no items from compSubListOutsideTheDateRange are present
        .body("data." + BREAKDOWN_PROP + "." + ID_PROP, not(hasItems(compSubListOutsideTheDateRange.toArray())))

        // Ensure breakdown size is at least the size of compSubList
        .body("data." + BREAKDOWN_PROP, hasSize(greaterThanOrEqualTo(compSubList.size())))

        // Custom check for follows property in descending order
        .body("data." + BREAKDOWN_PROP + "." + FOLLOWS_PROP + "." + ALL_PROP,
            CustomHamcrestMatchers.IsInDescendingNumericalOrder());

    var followsPerUserDto = response.as(FollowsPerUserDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        followsPerUserDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        followsPerUserDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        followsPerUserDto.getMeta().getGroupedBy(), equalTo(GroupBy.COMPETITION));
  }
}
