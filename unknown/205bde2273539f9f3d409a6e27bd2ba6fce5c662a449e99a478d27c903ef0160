package com.fansunited.automation.minigames.classicquiz.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizById.getQuizById;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizEndpoint.getQuiz;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.model.CommonStatus.ACTIVE;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get quiz happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class GetQuizTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get classic quiz works")
  public void getClassicQuizTest() throws IllegalArgumentException, HttpException {

    var quizResponse =
        createQuiz(classicQuizRequest(ACTIVE), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    String quizID = quizResponse.body().jsonPath().get("data.id");
    String quizTitle = quizResponse.body().jsonPath().get("data.title");

    quizResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var response =  getQuizById(quizID, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(String.format("data.id", quizID), notNullValue())
        .body(String.format("data.title", quizTitle), notNullValue())
        .body(String.format("data.status", ACTIVE), notNullValue());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify classic quiz cache")
  public void getClassicQuizCacheTest()
      throws IllegalArgumentException, HttpException {

    var quizResponse =
        createQuiz(classicQuizRequest(ACTIVE), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    quizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var response = getQuiz(CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_HOUR);
  }
}
