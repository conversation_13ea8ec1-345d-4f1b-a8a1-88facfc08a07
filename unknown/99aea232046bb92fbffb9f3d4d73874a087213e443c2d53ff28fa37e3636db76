package com.fansunited.automation.clientsapi.ttl.get;

import static com.fansunited.automation.constants.ApiConstants.ApiIdConstants.PROFILE_API_ID;
import static com.fansunited.automation.constants.ApiConstants.EndpointConstants.ENDPOINT_COUNTRY;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.TtlCacheEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Client Api - GET /client/config/cache_ttl/{client_id} endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetTtlValidationTests extends ClientApiBaseTest {

  @ParameterizedTest(name = "Verify cache ttl cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getCacheTtlWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        TtlCacheEndpoint.getCacheTtlConfig(CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            PROFILE_API_ID, ENDPOINT_COUNTRY, argumentsHolder.getApiKey(),
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Disabled("WIP FZ-1640")
  @ParameterizedTest(name = "Verify client API returns BAD_REQUEST when getting cache ttl with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getCacheTtlWithNotSupportedContentType(ContentType contentType) throws HttpException {

    var response =
        TtlCacheEndpoint.getCacheTtlConfig(CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            PROFILE_API_ID, ENDPOINT_COUNTRY, AuthConstants.ENDPOINTS_API_KEY,
            AuthConstants.ENDPOINTS_API_KEY,contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify cache ttl endpoint is fetched in JSON format if content type is NOT specified")
  public void getCacheTtlWithoutSpecifyingContentType() throws HttpException {

    var response =
        TtlCacheEndpoint.getCacheTtlConfig(CLIENT_AUTOMATION_ID,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            PROFILE_API_ID, ENDPOINT_COUNTRY, AuthConstants.ENDPOINTS_API_KEY,
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }
}
