package com.fansunited.automation.predictionapi.games.patch;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_MATCH_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.CODE_INVALID_JSON;
import static com.fansunited.automation.constants.ApiErrorCodes.PredictionErrorCodes.MESSAGE_CANNOT_UPDATE_GAME_PROPERTIES;
import static com.fansunited.automation.constants.ApiErrorCodes.PredictionErrorCodes.STATUS_CANNOT_UPDATE_OPEN_GAME;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.NON_EXISTING_STRING;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.SPECIAL_CHARACTER;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.core.apis.loyaltyapi.enums.UserRankingTypes.GAME;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGamesInTwoHours;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createMatchQuizGameForMarkets;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.setUpImages;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.GAME_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionField;
import static com.fansunited.automation.helpers.FirebaseHelper.waitForGameStatusToUpdate;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGameCreationResponse;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGameStatus;
import static java.time.ZoneOffset.UTC;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.GameTiebreaker;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.ValidRelatedEntity;
import com.fansunited.automation.validators.ErrorValidator;
import com.fansunited.automation.validators.RedisValidator;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - PATCH /v1/games endpoint validation tests")
public class UpdateGameValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify game cannot be updated with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void updateGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify game cannot be updated with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void updateGameWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest, argumentsHolder.getJwtToken());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(name = "Verify games cannot be updated with invalid/missing client id. Client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1687")})
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void updateGameWithInvalidClientId(String clientId) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, clientId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if (clientId == null) {
      ErrorValidator.validateErrorResponse(response, HttpStatus.SC_FORBIDDEN);

    } else {
      ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify games cannot be updated by clients from different project")
  @EnabledIf("isUseStageEnvironment")
  public void updateGameForAnotherClient() throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().status(GameStatus.OPEN.getValue()).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_PRODUCTION_TESTING_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users cannot update games")
  public void updateGameByEndUser() throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions cannot update games")
  @EnabledIf("isUseStageEnvironment")
  public void updateGameByUserWithInsufficientPermissions() throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            BILLING_USER, CLIENT_PRODUCTION_TESTING_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify game cannot be updated without auth")
  public void updateGameWithoutAuth() throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest, null,
            null, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when trying to update a game with non supported content type")
  public void updateGameWithNotSupportedContentType()
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var response =
        updateGame(gameId, "", FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_PRODUCTION_TESTING_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.TEXT);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify game cannot be updated with empty body")
  public void updateGameWithEmptyBody() throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    UpdateGameRequest updateGameRequest = UpdateGameRequest.builder().build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify game cannot be updated with empty fixture list. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateGameWithEmptyFixtureList(GameType gameType) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().fixtures(List.of()).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify game cannot be updated with past predictions cutoff date. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateGameWithPastPredictionsCutoff(GameType gameType) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var updateGameRequest =
        UpdateGameRequest.builder().predictionsCutoff(Helper.generateDateTimeInIsoFormat(
            ZonedDateTime.now().minusHours(1))).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to update a game with non supported date format for predictions_cutoff prop. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateGameWithNonSupportedDateFormatForPredictionsCutoff(GameType gameType)
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var predictionsCutoff = generateFutureDate(12);

    var updateGameRequest = UpdateGameRequest.builder()
        .predictionsCutoff(predictionsCutoff.toString())
        .build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when trying to update a game with empty string for predictions_cutoff prop. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateGameWithEmptyPredictionsCutoff(GameType gameType)
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var gameFixtureList = new ArrayList<GameFixture>();
    gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(PredictionMarket.CORRECT_SCORE.getValue())
            .matchId(MatchGenerator
                    .generateMatch().getId())
            .build());
    var updateGameRequest = UpdateGameRequest.builder()
            .fixtures(gameFixtureList)
        .predictionsCutoff("")
        .build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify MATCH_QUIZ game cannot be updated with invalid/empty status. Game status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"INVALID"})
  @EmptySource
  public void updateMatchQuizGameWithInvalidStatus(String status) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().status(status).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify TOP_X game cannot be updated with invalid/empty status. Game status: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"INVALID"})
  @EmptySource
  public void updateTopXGameWithInvalidStatus(String status) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder().status(status).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify MATCH_QUIZ game cannot be updated with invalid/empty fixture match id. Match id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_MATCH_ID)
  @NullAndEmptySource
  public void updateMatchQuizGameFixturesWithInvalidMatchId(String matchId) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var gameFixtureList = new ArrayList<GameFixture>();

    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      gameFixtureList.add(GameFixture.builder()
          .matchId(matchId)
          .matchType(MatchType.FOOTBALL.getValue())
          .market(market.getValue())
          .build());
    }

    var updateGameRequest =
        UpdateGameRequest.builder().fixtures(gameFixtureList).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify TOP_X game cannot be updated with invalid/empty fixture match id. Match id: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_MATCH_ID)
  @NullAndEmptySource
  public void updateTopXGameFixturesWithInvalidMatchId(String matchId) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0);

    var gameFixtureList = new ArrayList<GameFixture>();

    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      gameFixtureList.add(GameFixture.builder()
          .matchId(matchId)
          .matchType(MatchType.FOOTBALL.getValue())
          .market(market.getValue())
          .build());
    }

    var updateGameRequest =
        UpdateGameRequest.builder().fixtures(gameFixtureList).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify MATCH_QUIZ game cannot be updated with invalid/empty fixture match type. Match type: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "INVALID")
  @NullAndEmptySource
  public void updateMatchQuizGameFixturesWithInvalidMatchType(String matchType)
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var gameFixtureList = new ArrayList<GameFixture>();

    for (GameFixture fixture : gameInstance.getFixtures()) {
      gameFixtureList.add(GameFixture.builder()
          .matchId(fixture.getMatchId())
          .matchType(matchType)
          .market(fixture.getMarket())
          .build());
    }

    var updateGameRequest =
        UpdateGameRequest.builder().fixtures(gameFixtureList).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify TOP_X game cannot be updated with invalid/empty fixture match type. Match type: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "INVALID")
  @NullAndEmptySource
  public void updateTopXGameFixturesWithInvalidMatchType(String matchType)
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var gameFixtureList = new ArrayList<GameFixture>();

    for (GameFixture fixture : gameInstance.getFixtures()) {
      gameFixtureList.add(GameFixture.builder()
          .matchId(fixture.getMatchId())
          .matchType(matchType)
          .market(fixture.getMarket())
          .build());
    }

    var updateGameRequest =
        UpdateGameRequest.builder().fixtures(gameFixtureList).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify MATCH_QUIZ game cannot be updated with invalid/empty fixture market. Market: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "INVALID")
  @NullAndEmptySource
  public void updateMatchQuizGameFixturesWithInvalidMarket(String market)
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.MATCH_QUIZ, 1).get(0);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var gameFixtureList = new ArrayList<GameFixture>();

    for (GameFixture fixture : gameInstance.getFixtures()) {
      gameFixtureList.add(GameFixture.builder()
          .matchId(fixture.getMatchId())
          .matchType(MatchType.FOOTBALL.getValue())
          .market(market)
          .build());
    }

    var updateGameRequest =
        UpdateGameRequest.builder().fixtures(gameFixtureList).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify TOP_X game cannot be updated with invalid/empty fixture market. Market: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = "INVALID")
  @NullAndEmptySource
  public void updateTopXGameFixturesWithInvalidMarket(String market)
      throws HttpException {

    var gameId = createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var gameFixtureList = new ArrayList<GameFixture>();

    for (GameFixture fixture : gameInstance.getFixtures()) {
      gameFixtureList.add(GameFixture.builder()
          .matchId(fixture.getMatchId())
          .matchType(MatchType.FOOTBALL.getValue())
          .market(market)
          .build());
    }

    var updateGameRequest =
        UpdateGameRequest.builder().fixtures(gameFixtureList).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify fixtures for game with 'OPEN' status cannot be updated. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateOpenGameFixtureList(GameType gameType) throws HttpException {

    var gameId = createGames(GameStatus.OPEN, gameType, 1).get(0);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var updateGameRequest = UpdateGameRequest.builder()
        .fixtures(List.of(
            gameInstance.getFixtures().get(0)
        )).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify predictions cutoff for game with 'OPEN' status cannot be updated. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateOpenGamePredictionsCutoff(GameType gameType) throws HttpException {

    var gameId = createGamesInTwoHours(GameStatus.OPEN, gameType, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder()
        .predictionsCutoff(generateDateTimeInIsoFormat(generateFutureDate(1))).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify description and title for game with OPEN status cannot be updated. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1600")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateOpenGameTitleAndDesc(GameType gameType) throws HttpException {

    var gameId = createGames(GameStatus.OPEN, gameType, 1).get(0);
    final String updatedTitle = gameType + " updated title";
    final String updatedDescription = gameType + " updated description";
    final String updatedRules = gameType + " updated rules";
    var updatedFlags = List.of(gameType + " updated flags");
    var updatedImages = new Images();

    var updateGameRequest = UpdateGameRequest.builder()
        .title(updatedTitle)
        .description(updatedDescription)
        .rules(updatedRules)
        .flags(updatedFlags)
        .images(updatedImages).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);
    response
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_OK)
            .body("title", equalTo(updatedTitle))
            .body("description", equalTo(updatedDescription))
            .body("rules", equalTo(updatedRules))
            .body("flags", notNullValue())
            .body("images", notNullValue());
  }

  @ParameterizedTest(name = "Verify game title cannot be updated to string longer than 255 chars. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updatePendingGameWithLongTitle(GameType gameType) throws HttpException {

    var title = RandomStringUtils.randomAlphabetic(256);

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder()
        .title(title).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(name = "Verify game description cannot be updated to string longer than 50000 chars. Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updatePendingGameWithLongDesc(GameType gameType) throws HttpException {

    var desc = RandomStringUtils.randomAlphabetic(50001);

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder()
        .description(desc).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(name = "Verify MATCH_QUIZ game with 'OPEN' status cannot have its status updated to {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.GAME_STATUSES_NOT_AVAILABLE_FOR_UPDATE_IF_GAME_IS_OPENED)
  public void updateOpenMatchQuizGameStatus(GameStatus gameStatus) throws HttpException {

    var gameId = createGames(GameStatus.OPEN, GameType.MATCH_QUIZ, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder()
        .status(gameStatus.getValue()).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify TOP_X game with 'OPEN' status cannot have its status updated to {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.GAME_STATUSES_NOT_AVAILABLE_FOR_UPDATE_IF_GAME_IS_OPENED)
  public void updateOpenTopXGameStatus(GameStatus gameStatus) throws HttpException {

    var gameId = createGames(GameStatus.OPEN, GameType.TOP_X, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder()
        .status(gameStatus.getValue()).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify game with 'CANCELED' status cannot be updated back to OPEN: Game type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateCanceledGame(GameType gameType) throws HttpException {

    var gameId = createGames(GameStatus.PENDING, gameType, 1).get(0);

    var updateGameRequest = UpdateGameRequest.builder()
        .status(GameStatus.CANCELED.getValue()).build();

    updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
        ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    updateGameRequest = UpdateGameRequest.builder()
        .status(GameStatus.OPEN.getValue()).build();

    var response =
        updateGame(gameId, updateGameRequest, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify can not update schedule data time with incorrect time: {arguments}")
  @Tags(@Tag(REGRESSION))
  @ValueSource(strings = {NON_EXISTING_STRING, SPECIAL_CHARACTER})
  public void updateGameWithIncorrectScheduleTime(String strings) throws HttpException {

    var markets = PredictionMarket.getValidMarkets();

    var gameInstance = createMatchQuizGameForMarkets(GameStatus.PENDING,
        markets).as(GameInstance.class);

    var updatedGameRequest = UpdateGameRequest.builder()
        .scheduleOpenAt(strings)
        .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    updateGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(name = "Verify updating TIME TIEBREAKER field for game not allowed when Game Status = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameStatus.class, names = {"OPEN", "LIVE", "SETTLED", "CLOSED", "CANCELED"})
  public void updateTopXGameTimebreakerForAllStatusesNotAllowed(GameStatus gameStatus)
      throws HttpException, IllegalArgumentException, IOException, ExecutionException,
      InterruptedException {

    var gameInstance =
        GameEndpoint.getGameById(createGames(GameStatus.PENDING, GameType.TOP_X, 1).get(0)).as(
            GameInstance.class);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        ApiConstants.FootballApi.STATUS_PROP, gameStatus.getValue());

    validateGameStatus(gameInstance, gameStatus);

    var body = new JSONObject();

    body.put("tiebreaker", new GameTiebreaker(true));

    updateGame(gameInstance.getId(), body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error." + ApiConstants.FootballApi.STATUS_PROP,
            equalTo(STATUS_CANNOT_UPDATE_OPEN_GAME))
        .body("error.message", equalTo(MESSAGE_CANNOT_UPDATE_GAME_PROPERTIES));
  }

  @ParameterizedTest(name = "Verify updating PREDICTION CUTOFF field for game not allowed when Game Status = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameStatus.class, names = {"OPEN", "LIVE", "SETTLED", "CLOSED", "CANCELED"})
  public void updateTopXGamePredictionCutOffForAllStatusesNotAllowed(GameStatus gameStatus)
      throws HttpException, IllegalArgumentException, IOException, ExecutionException,
      InterruptedException {

    var predictionsCutoff = generateFutureDate(12);

    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(GameType.TOP_X)
        .predictionsCutoff(predictionsCutoff)
        .build();

    var gameInstance =
        createGameEndpoint.createGame().as(
            GameInstance.class);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        ApiConstants.FootballApi.STATUS_PROP, gameStatus.getValue());

    validateGameStatus(gameInstance, gameStatus);

    var body = new JSONObject();

    body.put("predictions_cutoff", generateDateTimeInIsoFormat(predictionsCutoff.plusMinutes(1)));

    if (gameStatus == GameStatus.OPEN) {

      updateGame(gameInstance.getId(), body)
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK);
    } else {
      updateGame(gameInstance.getId(), body)
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error." + ApiConstants.FootballApi.STATUS_PROP,
              equalTo(STATUS_CANNOT_UPDATE_OPEN_GAME))
          .body("error.message", equalTo(MESSAGE_CANNOT_UPDATE_GAME_PROPERTIES));
    }
  }

  @ParameterizedTest(name = "Verify updating SCHEDULE OPEN AT field for game not allowed when Game Status = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE),@Tag("FZ-1775")})
  @EnumSource(value = GameStatus.class, names = {"OPEN", "LIVE", "SETTLED", "CLOSED", "CANCELED"})
  public void updateTopXGameScheduleOpenAtForAllStatusesNotAllowed(GameStatus gameStatus)
      throws HttpException, IllegalArgumentException, IOException, ExecutionException,
      InterruptedException {

    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(GameType.TOP_X)
        .gameStatus(GameStatus.PENDING).numberOfTopXFixtures(1)
        .build();

    var gameInstance =
        createGameEndpoint.createGame().as(
            GameInstance.class);

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION),
        gameInstance.getId(),
        ApiConstants.FootballApi.STATUS_PROP, gameStatus.getValue());

    validateGameStatus(gameInstance, gameStatus);

    var body = new JSONObject();

    body.put("schedule_open_at",
        Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusMinutes(2)));

    updateGame(gameInstance.getId(), body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error." + ApiConstants.FootballApi.STATUS_PROP,
            equalTo(STATUS_CANNOT_UPDATE_OPEN_GAME))
        .body("error.message", equalTo(MESSAGE_CANNOT_UPDATE_GAME_PROPERTIES));
  }

  @ParameterizedTest(name = "Verify MATCH_QUIZ game cannot be updated with invalid/empty related. Game status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"INVALID", "%&*@"})
  @EmptySource
  public void updateMatchQuizGameRelatedFunctionalityWithInvalidData(String type_id)
      throws HttpException, IllegalArgumentException {


    var markets = PredictionMarket.getValidMarkets();

    var gameInstance = createMatchQuizGameForMarkets(GameStatus.PENDING,
        markets.subList(0, markets.size() - 3)).as(GameInstance.class);


    var matchId = gameInstance.getFixtures().get(0).getMatchId();

    var updatedFixturesList =
        generateValidFixturesForGameType(GameType.MATCH_QUIZ, List.of(matchId));

    var updatedGameRequest = UpdateGameRequest.builder()
        .fixtures(updatedFixturesList)
        .related(List.of(ValidRelatedEntity.builder()
            .entityType(String.valueOf(GAME))
            .entityId(type_id)
            .build()))
        .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then()
        .log().body()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify {arguments} game cannot be updated with empty body.")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE),@Tag("FZ-526")})
  @EnumSource(value = GameType.class, names = {"MATCH_QUIZ", "TOP_X"})

  public void updateGameWithEmptyBody(GameType type)
      throws HttpException, IllegalArgumentException {

    var gameInstance = createGames(type, 2);

    var updatedGameRequest = UpdateGameRequest.builder()
        .build();

    var updateGameResponse = updateGame(String.valueOf(gameInstance), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then()
        .log().body()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }
  @ParameterizedTest(name = "Verify description and title for game with OPEN status and schedule_open_at can be updated. Game type: {arguments}")
  @Disabled("The update of description for game with OPEN status is allowed with FZ-1600")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void updateOpenGameTitleWithScheduleOpenAt(GameType gameType)
      throws HttpException, InterruptedException {

    var predictionsCutoff = generateFutureDate(12);

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    var mainImageUrl = "https://example/imageurl.png";
    var coverImageUrl = "https://example/folder/imageurl.jpg";
    var mobileImageUrl = "http://example/imageurl.gif";

    var images = setUpImages(mainImageUrl, coverImageUrl, mobileImageUrl);
    var generateScheduleTime =
        Helper.generateDateTimeInIsoFormat(ZonedDateTime.now(UTC).plusMinutes(2));

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .scheduleOpenAt(generateScheduleTime)
        .fixtures(gameFixtureList)
        .status(GameStatus.PENDING.getValue())
        .images(images)
        .build();

    var createGameResponse = createGame(createGameRequest);

    validateGameCreationResponse(gameType, createGameResponse, createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    var getGameResponse = GameEndpoint.getGameById(gameInstance.getId());

    currentTestResponse.set(getGameResponse);

    var redisGamesResponse = RedisEndpoint.getGames();

    RedisValidator.validateGameInRedis(gameInstance, redisGamesResponse);

    getGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    waitForGameStatusToUpdate(gameInstance.getId(), GameStatus.OPEN.getValue(), 10000, 124);

    var getGameToCheckOpenStatus = GameEndpoint.getGameById(gameInstance.getId());
    getGameToCheckOpenStatus
        .then()
        .body( STATUS_PROP,equalTo(GameStatus.OPEN.getValue()));

    var updateGameRequest = UpdateGameRequest.builder()
        .title(gameType + " updated title")
        .description(gameType + " updated description").build();

    var updateGameResponse =
        updateGame(gameInstance.getId(), updateGameRequest,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            ADMIN_USER, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(updateGameResponse);

    ErrorValidator.validateErrorResponse(updateGameResponse, HttpStatus.SC_OK);
  }
}
