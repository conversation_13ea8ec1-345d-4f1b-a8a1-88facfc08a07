package com.fansunited.automation.reportingapi.follows;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.reportingapi.AverageFollowsEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.core.base.reportingapi.TruncateTablesHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/follows/averages endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetAverageFollowsPerInterestValidationTests extends ReportingApiBaseTest {

  @AfterAll
  public static void afterAll() {
    // this should not be done in separate test classes, but in base test only. Unfortunately, some
    // tests in reporting api need this to be run once more
    TruncateTablesHelper.executeTruncateTables();
  }

  @ParameterizedTest(name = "Verify average follows per interest cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getAverageFollowsPerInterestWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        AverageFollowsEndpoint.getAverageFollowsPerInterest(CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting average follows per interest with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getAverageFollowsPerInterestWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        AverageFollowsEndpoint.getAverageFollowsPerInterest(clientId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_BAD_REQUEST),is(HttpStatus.SC_FORBIDDEN)))
        .equals(anyOf(is(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT),is(ApiErrorCodes.ClientErrorCodes.FORBIDDEN)));
  }

  @ParameterizedTest(name = "Verify average follows per interest cannot be fetched with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void getAverageFollowsPerInterestWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        AverageFollowsEndpoint.getAverageFollowsPerInterest(CLIENT_AUTOMATION_ID, true,
            argumentsHolder.getJwtToken(), null, null, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users do not have access to /v1/follows/averages endpoint")
  public void getAverageFollowsPerInterestWithEndUserToken()
      throws IOException, ExecutionException, FirebaseAuthException,
      InterruptedException, HttpException {

    var createUser = AuthBase.createUser();

    var response =
        AverageFollowsEndpoint.getAverageFollowsPerInterest(CLIENT_AUTOMATION_ID, true, null,
            createUser.getEmail(), FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients with insufficient permissions do not have access to average follows per interest reports")
  public void getAverageFollowsPerInterestByClientWithInsufficientPermissions()
      throws HttpException {

    var response =
        AverageFollowsEndpoint.getAverageFollowsPerInterest(CLIENT_AUTOMATION_ID, true, null,
            BILLING_USER, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting average follows per interest with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getAverageFollowsPerInterestWithNonSupportedContentType(
      ContentType contentType)
      throws HttpException {

    var response =
        AverageFollowsEndpoint.getAverageFollowsPerInterest(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify average follows per interest data is still fetched in JSON format if content type is NOT specified")
  public void getAverageFollowsPerInterestWithoutSpecifyingContentType()
      throws HttpException {

    var response = AverageFollowsEndpoint.getAverageFollowsPerInterest(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
