package com.fansunited.automation.reportingapi.follows;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Follows.GET_AVERAGE_FOLLOWS_PER_INTEREST_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.reportingapi.AverageFollowsEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.apis.reportingapi.helper.InterestGenerator;
import com.fansunited.automation.core.apis.reportingapi.helper.ProfileGenerator;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.core.base.reportingapi.TruncateTablesHelper;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.reportingapi.follows.averagefollowspersport.AverageFollowDto;
import com.fansunited.automation.model.reportingapi.follows.averagefollowspersport.AverageFollowsDto;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/follows/averages endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetAverageFollowsPerInterestTests extends ReportingApiBaseTest {

  @AfterAll
  public static void afterAll() {
    // this should not be done in separate test classes, but in base test only. Unfortunately, some
    // tests in reporting api need this to be run once more
    TruncateTablesHelper.executeTruncateTables();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting average follows per interest")
  public void getAverageFollowsPerInterest() throws HttpException, InterruptedException {
    final var count = generateRandomNumber(2, 4);

    var profileList =
        ProfileGenerator.generateProfiles(count, List.of(InterestGenerator.generateFootballInterest(
            ApiConstants.ProfileApi.Interest.Football.TEAM, true)));

    profileList.addAll(
        ProfileGenerator.generateProfiles(count, List.of(InterestGenerator.generateTennisInterest(
            ApiConstants.ProfileApi.Interest.Tennis.COMPETITION, true))));

    profileList.addAll(ProfileGenerator.generateProfiles(count, List.of(
        InterestGenerator.generateSportInterest(ApiConstants.ProfileApi.Interest.Sport.BOXING,
            true))));

    profileList.addAll(ProfileGenerator.generateProfiles(count, List.of(
        InterestGenerator.generateCustomInterest(true,
            "custom"))));

    profileList.stream().parallel().forEach(profile ->
        InsertBigQData.generateProfileEvent(profile, LocalDateTime.now(), LocalDateTime.now()));

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = AverageFollowsEndpoint.getAverageFollowsPerInterest();

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_AVERAGE_FOLLOWS_PER_INTEREST_SCHEMA));

    currentTestResponse.set(response);

    var averageFollowsDto = response.as(AverageFollowsDto.class);

    profileList.forEach(profile -> {
          var optional = averageFollowsDto.getData()
              .getBreakdown()
              .stream()
              .filter(averageFollowDto -> averageFollowDto.getSource()
                  .equals(profile.getInterests().get(0).getSource())).findFirst();
          if (optional.isEmpty()) {
            throw new AssertionError(
                "API returned incorrect follow breakdown list. Expected: '" + profile.getInterests()
                    .get(0)
                    .getType() + "' to be in the list, Actual: " + averageFollowsDto.getData()
                    .getBreakdown()
                    .stream()
                    .map(AverageFollowDto::getSource)
                    .toList());
          } else {
            // Skip football. Test will never pass, because other tests are filling random football data
            if (!optional.get().getSource().equals("football")) {
              MatcherAssert.assertThat(
                  "API returned incorrect average value for source: " + optional.get().getSource(),
                  optional.get().getAverage(),
                  equalTo(BigDecimal.valueOf((double) count / profileList.size()).setScale(2,
                      RoundingMode.HALF_UP).doubleValue()));
            }
          }
        }
    );

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        averageFollowsDto.getMeta().getFromDate(), nullValue());
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        averageFollowsDto.getMeta().getToDate(), nullValue());
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        averageFollowsDto.getMeta().getGroupedBy(), equalTo(GroupBy.AVERAGE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/follows/averages response returned by the server is cached for eight hours")
  public void verifyAverageFollowsPerInterestResponseIsCached()
      throws HttpException {

    var response = AverageFollowsEndpoint.getAverageFollowsPerInterest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.EIGHT_HOURS);
  }
}
