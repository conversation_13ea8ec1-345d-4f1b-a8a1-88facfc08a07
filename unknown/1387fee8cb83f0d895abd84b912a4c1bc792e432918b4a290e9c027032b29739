package com.fansunited.automation.footballapi.matches;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.AWAY_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.HOME_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.INVALID_MATCH_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.CODE_NO_PREV_MATCH;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_MATCH_BY_ID_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.TeamPreviousMatchEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/team/{id}/previous-match endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPreviousMatchForTeamValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify previous match for team cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getPreviousMatchForTeamWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response =
        TeamPreviousMatchEndpoint.getPreviousMatchForTeamId(teamId,
            UrlParamValues.Language.EN.getValue(), CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns NOT_FOUND when getting previous match for team with invalid id")
  public void getNextMatchForPlayerWithInvalidId() {

    var response = TeamPreviousMatchEndpoint.getPreviousMatchForTeamId(INVALID_MATCH_ID);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND)
        .body("error.status", equalTo(CODE_NO_PREV_MATCH));
  }

  @ParameterizedTest(name = "Verify previous match for team cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getPreviousMatchForTeamWithInvalidOrEmptyLang(String lang) {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response =
        TeamPreviousMatchEndpoint.getPreviousMatchForTeamId(teamId, lang, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);
    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify previous match for team is fetched in EN, if \"lang\" query param is not specified")
  public void getPreviousMatchForTeamWithoutLanguageParam() {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response =
        TeamPreviousMatchEndpoint.getPreviousMatchForTeamId(teamId, null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCH_BY_ID_SCHEMA))
        .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
            not(containsCyrillic()))
        .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
            not(containsCyrillic()));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting previous match for team with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getPreviousMatchForTeamWithNotSupportedContentType(ContentType contentType) {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response =
        TeamPreviousMatchEndpoint.getPreviousMatchForTeamId(teamId,
            UrlParamValues.Language.EN.getValue(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify previous match for team is fetched in JSON format if content type is NOT specified")
  public void getPreviousMatchForTeamWithoutSpecifyingContentType() {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response =
        TeamPreviousMatchEndpoint.getPreviousMatchForTeamId(teamId,
            UrlParamValues.Language.EN.getValue(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCH_BY_ID_SCHEMA));
  }
}
