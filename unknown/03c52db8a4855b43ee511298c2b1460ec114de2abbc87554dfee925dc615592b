package com.fansunited.automation.footballapi.teams;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_ENGLAND_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.FU_TEAM_ID_PREFIX;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.LIMIT_PARAM_MAX_VALUE_TEAMS;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NATIONAL_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Teams.GET_TEAMS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getRandomCompetitionIdsCommaSeparated;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isInAscendingAlphabeticalOrder;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isInDescendingAlphabeticalOrder;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static it.ozimov.cirneco.hamcrest.HamcrestMatchers.hasDistinctElements;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.lessThan;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.apis.footballapi.TopTeamsEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.model.footballapi.teams.FootballTeamsData;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.FootballApiValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/teams endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetTeamsTests extends FootballApiBaseTest {

  private static final int EXPECTED_TEAMS_COUNT = 25000;

  @ParameterizedTest(name = "Verify getting list of teams for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void getTeamsForLang(UrlParamValues.Language lang) {

    var response =
        TeamsEndpoint.getTeamsWithFilterScope(PREMIER_LEAGUE_COMP_ID, lang.getValue(), -1,
            -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Liverpool"))
          .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Ливърпул"))
          .body("data." + NAME_PROP, Every.everyItem(containsCyrillic()))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(containsCyrillic()));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify teams can be filtered by country")
  public void getTeamsByCountry() {

    var response =
        TeamsEndpoint.getTeamsWithFilterCountry(COUNTRY_ENGLAND_ID,
            UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_TEAMS, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + ID_PROP, everyItem(is(COUNTRY_ENGLAND_ID)));
  }

  @ParameterizedTest(name = "Verify teams can be filtered by name. Name: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"Arsenal", "juventus", "Bayern"})
  public void getTeamsByName(String teamName) {

    var response =
        TeamsEndpoint.getTeamsWithFilterName(teamName, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_TEAMS,
            -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, everyItem(containsStringIgnoringCase(teamName)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());;
  }

  @ParameterizedTest(name = "Verify teams can be filtered by gender. Gender: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = {"male", "female", "INVALID"})
  public void getTeamsByGender(String gender) {

    var response =
        TeamsEndpoint.getTeamsWithFilterGender(gender, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_TEAMS,
            -1, null, null);
    currentTestResponse.set(response);
    if (response.statusCode() == HttpStatus.SC_BAD_REQUEST || gender == "INVALID") {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.STATUS_INVALID_GENDER))
          .body("error.message", equalTo(ApiErrorCodes.FootballErrorCodes.MESSAGE_INVALID_GENDER));
      return;
    }

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(Matchers.allOf(greaterThan(0), lessThan(EXPECTED_TEAMS_COUNT))))
        .body("data." + GENDER_PROP, everyItem(containsStringIgnoringCase(gender)));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify teams can be filtered by scope(competition ids)")
  public void getTeamsByScope() {

    var competitionIds = getRandomCompetitionIdsCommaSeparated(generateRandomNumber(2, 6));

    var expectedTeamIds = new HashSet<String>();

    Arrays.stream(competitionIds.split(",")).toList().forEach(competitionId ->
        expectedTeamIds.addAll(TeamsEndpoint.getTeamsForCompetition(competitionId)));

    var response =
        TeamsEndpoint.getTeamsWithFilterScope(competitionIds, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_TEAMS,
            -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));

    var actualTeamIds =
        TeamsEndpoint.extractTeamIdsFromResponseWithFilterScope(response, competitionIds,
            UrlParamValues.Language.EN.getValue(), null, null);

    Assertions.assertAll(
        () -> Assertions.assertEquals(expectedTeamIds.size(), actualTeamIds.size(),
            "Expected team size does not match actual team size"),
        () -> Assertions.assertTrue(expectedTeamIds.containsAll(actualTeamIds),
            "Teams are missing from competitions: " + competitionIds));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify teams can be filtered by team ids")
  public void getTeamsByTeamIds() {

    var teamIds = new ArrayList<String>(
        TeamsEndpoint.getTeamsWithFilterScope(PREMIER_LEAGUE_COMP_ID, null, -1, -1, null, null)
            .then()
            .extract()
            .jsonPath()
            .getList("data." + ID_PROP));

    var response =
        TeamsEndpoint.getTeamsWithFilterTeamIds(String.join(",", teamIds), null, -1, -1, null,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(teamIds.size()))
        .body("data." + ID_PROP, containsInAnyOrder(teamIds.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting only national teams(national filter=true)")
  public void getOnlyNationalTeams() {

    var response =
        TeamsEndpoint.getTeamsWithFilterNational(true, null, LIMIT_PARAM_MAX_VALUE_TEAMS, -1, null,
            null);

    currentTestResponse.set(response);

    var footballData = response.as(FootballTeamsData.class);
    var currentPage = footballData.getMeta().getPagination().getCurrentPage();
    var totalPages = footballData.getMeta().getPagination().getNumberOfPages();

    int counter = 0;
    while (currentPage <= totalPages && counter < 150) {
      TeamsEndpoint.getTeamsWithFilterNational(true, null, LIMIT_PARAM_MAX_VALUE_TEAMS, currentPage,
              null,
              null)
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
          .body("data", not(empty()))
          .body("data." + NATIONAL_PROP, Every.everyItem(is(true)))
          .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
          .body("data." + ID_PROP, hasDistinctElements());
      currentPage++;
      counter++; // to avoid endless looping in case pagination is broken
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify getting only club teams(national filter = false)")
  public void getOnlyClubTeams() {

    var response =
        TeamsEndpoint.getTeamsWithFilterNational(false, null, LIMIT_PARAM_MAX_VALUE_TEAMS, -1, null,
            null);

    currentTestResponse.set(response);

    var footballData = response.as(FootballTeamsData.class);
    var currentPage = footballData.getMeta().getPagination().getCurrentPage();
    var totalPages = footballData.getMeta().getPagination().getNumberOfPages();

    int counter = 0;
    while (currentPage <= totalPages && counter < 150) {
      TeamsEndpoint.getTeamsWithFilterNational(false, null, LIMIT_PARAM_MAX_VALUE_TEAMS,
              currentPage,
              null,
              null)
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
          .body("data", not(empty()))
          .body("data." + NATIONAL_PROP, Every.everyItem(is(false)))
          .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
          .body("data." + ID_PROP, hasDistinctElements());
      currentPage++;
      counter++; // to avoid endless looping in case pagination is broken
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify teams can be sorted by field 'name' in ascending order")
  public void getTeamsSortedByFieldNameInAscendingOrder() {

    var response = TeamsEndpoint.getTeamsWithFilterScope(PREMIER_LEAGUE_COMP_ID, null,
        LIMIT_PARAM_MAX_VALUE_TEAMS, -1,
        ApiConstants.FootballApi.SortField.NAME.getValue(), ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, isInAscendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify teams can be sorted by field 'name' in descending order")
  public void getTeamsSortedByFieldNameInDescendingOrder() {

    var response = TeamsEndpoint.getTeamsWithFilterScope(PREMIER_LEAGUE_COMP_ID, null,
        LIMIT_PARAM_MAX_VALUE_TEAMS, -1,
        ApiConstants.FootballApi.SortField.NAME.getValue(), ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + NAME_PROP, isInDescendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'country' in ascending order")
  public void getTeamsSortedByFieldCountryInAscendingOrder() {

    var filterName = "arsenal";

    var response =
        TeamsEndpoint.getTeamsWithFilterName(filterName, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_TEAMS, -1,
            ApiConstants.FootballApi.SortField.COUNTRY.getValue(),
            ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, isInAscendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify competitions can be sorted by field 'country' in descending order")
  public void getTeamsSortedByFieldCountryInDescendingOrder() {

    var filterName = "arsenal";

    var response =
        TeamsEndpoint.getTeamsWithFilterName(filterName, UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_TEAMS, -1,
            ApiConstants.FootballApi.SortField.COUNTRY.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, isInDescendingAlphabeticalOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_TEAM_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify moving to next page for getting teams work")
  public void getTeamsMoveToNextPage() {

    var resultsLimit = generateRandomNumber(160, 200);

    var response = TeamsEndpoint.getTeams(resultsLimit, -1, null, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    FootballApiValidator.validateGetTeamsPaginationMoveToNextPage(response, resultsLimit);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting teams")
  public void getTeamsWithDefaultPaginationLimitParam() {

    var response = TeamsEndpoint.getTeams();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/teams response returned by the server is cached for 1 day")
  public void verifyGetTeamsResponseIsCached() {

    var response = TeamsEndpoint.getTeams();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_DAY);
  }

  @ParameterizedTest(name = "Verify getting list of top teams for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = UrlParamValues.Language.class, names = {"EN", "RO", "BG"})
  public void getTopTeamsForLang(UrlParamValues.Language lang) {

    var response =
        TopTeamsEndpoint.getTopTeams(lang.getValue(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA));

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Liverpool"))
          .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + NAME_PROP, hasItem("Ливърпул"))
          .body("data." + NAME_PROP, Every.everyItem(containsCyrillic()))
          .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(containsCyrillic()));
    }
  }

  @ParameterizedTest()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  @DisplayName(value = "Verify getting list of top teams for empty or null language")
  public void getTopTeamsForLangAsEmptyOrNullString(String lang) {

    var response =
        TopTeamsEndpoint.getTopTeams(lang,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TEAMS_SCHEMA))
    .body("data." + NAME_PROP, hasItem("Liverpool"))
        .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
  }
}
