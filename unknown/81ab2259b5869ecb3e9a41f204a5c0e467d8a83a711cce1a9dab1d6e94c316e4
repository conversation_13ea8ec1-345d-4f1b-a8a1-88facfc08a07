package com.fansunited.automation.minigames.classicquiz.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizById.getQuizById;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.oneOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Get quiz by id validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetQuizByIdValidationTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get quiz by id as a user")
  public void getQuizByIdWithUserTokenTest()
      throws IllegalArgumentException, HttpException, IOException, ExecutionException,
      FirebaseAuthException, InterruptedException {

    var user = createUser();
    var quizResponse = createQuiz(classicQuizRequest(CommonStatus.ACTIVE),
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FANS_UNITED_CLIENTS, null);

    quizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    var quizId = quizResponse.body().jsonPath().get("data.id").toString();

    var response = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_PROFILE, user.getEmail(), AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify cannot get quiz by id with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getQuizByIdWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response = getQuizById("quizId", CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when get quiz by id for user with action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getQuizByIdWithInvalidClientId(String clientId)
      throws HttpException {

    var response = getQuizById("quizId", clientId,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response,
        oneOf(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when get quiz for user with action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  public void getQuizByIdWithInvalidId(String quizId)
      throws HttpException {

    var response = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, (HttpStatus.SC_NOT_FOUND));
  }
}
