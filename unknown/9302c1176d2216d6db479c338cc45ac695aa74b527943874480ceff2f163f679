package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Predictions.GET_PREDICTIONS_FOR_GAME_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.GamePredictionsEndpoint.getPredictionsForGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGameRequestForTopXGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionFixtureForAdvancedCorrectScore;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionsForGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGetGamePredictionsResponse;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.predictions.AdvancedPredictions;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.RedisValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

@DisplayName("Prediction Api - GET /v1/games/{gameId}/predictions endpoint happy path tests")
public class GetGamePredictionsTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify game predictions are successfully fetched and valid. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void getPredictionsForGameType(GameType gameType)
      throws HttpException, IllegalArgumentException, IOException, FirebaseAuthException,
      InterruptedException, ExecutionException {

    final var predictionsCount = Helper.generateRandomNumber(10, 20);

    var gameId = createGames(gameType, 1).get(0);

    var predictionsIdList = createPredictionsForGame(gameId, gameType, predictionsCount);

    var response = getPredictionsForGame(gameId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PREDICTIONS_FOR_GAME_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify /GET/v1/games/{gameId}/predictions response returned by the server is cached for 1h")
  @EnabledIf("isUseStageEnvironment")
  public void verifyGetPredictionsForGameResponseIsCached() throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response = getPredictionsForGame(gameId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting game predictions")
  public void getPredictionsForGameWithDefaultPaginationLimitParam()
      throws HttpException {

    var gameId = createGames(GameType.MATCH_QUIZ, 1).get(0);

    var response = getPredictionsForGame(gameId);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting game predictions")
  public void getPredictionsForGamePaginationParams()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
        .matchesIdList(matchList.stream().map(Match::getId).toList())
        .build()
        .createGame()
        .as(GameInstance.class);

    final var predictionsCount = Helper.generateRandomNumber(10, 15);

    createPredictionsForGame(gameInstance.getId(), GameType.MATCH_QUIZ, predictionsCount);

    final var resultsLimit = Helper.generateRandomNumber(1, 5);

    var response =
        getPredictionsForGame(gameInstance.getId(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    var predictionsForGameResponse = response.as(PredictionsData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(predictionsForGameResponse.getData()
                .get(predictionsForGameResponse.getData().size() - 1)
                .getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting game predictions works")
  public void getPredictionsForGamePaginationMoveToNextPage()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
        .matchesIdList(matchList.stream().map(Match::getId).toList())
        .build()
        .createGame()
        .as(GameInstance.class);

    final var predictionsCount = Helper.generateRandomNumber(30, 60);

    var predictionsIdList = createPredictionsForGame(gameInstance.getId(), GameType.MATCH_QUIZ, predictionsCount);

    final int resultsLimit = Helper.generateRandomNumber(1, 5);

    var response =
        getPredictionsForGame(gameInstance.getId(), CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    validateGetGamePredictionsResponse(gameInstance.getId(), response,
        predictionsIdList, resultsLimit);
  }

  @ParameterizedTest(name = "Verify game predictions of Top X game with market: {arguments} are successfully fetched and valid")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @MethodSource("com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket#getTopXGameMarkets")
  public void getPredictionsForTopXGameWithDifferentMarkets(PredictionMarket market)
      throws HttpException, IllegalArgumentException {

    var matchList = MatchGenerator.generateMatches(6, false);

    matchList.forEach(match -> match.setKickoffAt((LocalDateTime.now().plusMinutes(300))));
    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) 4);
      match.setGoalsFullTimeAway((byte) 2);
    });

    Resolver.openMatchesForPredictions(matchList);

    var createGameRequest = createGameRequestForTopXGame(
        matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusMinutes(76),
        market, matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createTopXGameForSpecificMarket(createGameRequest).as(GameInstance.class);

    var updatedGameRequest = UpdateGameRequest.builder()
            .status(GameStatus.OPEN.getValue())
            .build();

    updateGame(gameInstance.getId(), updatedGameRequest);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    if (market.getValue().equals("CORRECT_SCORE")) {
      matchList.forEach(match ->
          predictionFixtures.add(generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, match.getId(),
              null).get(0)));
    } else {
      matchList.forEach(match ->
          createPredictionFixtureForAdvancedCorrectScore(AdvancedPredictions.CORRECT_RESULT, match,
              predictionFixtures));
    }

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    var predictionId = PredictionsEndpoint.createPrediction(createPredictionRequest)
        .then()
        .extract()
        .body()
        .jsonPath()
        .get("id");

    ResolverBase.cleanUp(matchList.stream().map(Match::getId).toList());

    var response = getPredictionsForGame(gameInstance.getId());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PREDICTIONS_FOR_GAME_SCHEMA))
        .body("data." + ID_PROP, equalTo(List.of(predictionId)));
  }
}
