package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.INVALID_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.getGameById;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Prediction Api - GET /v1/games/{gameId} endpoint validation tests")
public class GetSpecificGameValidationTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify specific game cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @EnabledIf("isUseStageEnvironment")
  public void getSpecificGameWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var response = getGameById(gameId, CLIENT_AUTOMATION_ID, argumentsHolder.getApiKey(),
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify specific game cannot be fetched with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getSpecificGameWithInvalidClientId(String clientId)
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var response = getGameById(gameId, clientId, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting specific game with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getSpecificGameWithWithNotSupportedContentType(ContentType contentType)
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    var response = getGameById(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        contentType);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify specific game is still fetched if content type is NOT specified")
  public void getSpecificGameWithoutSpecifyingContentType()
      throws HttpException, IllegalArgumentException {

    var gameId = createGames(GameType.TOP_X, 1).get(0);

    Response response =
        getGameById(gameId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify specific game cannot be fetched with invalid game id")
  public void getSpecificGameWithInvalidGameId()
      throws HttpException {

    var response =
        getGameById(INVALID_GAME_ID, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }
}
