package com.fansunited.automation.leaguesapi.leagues.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.leagueapi.leagues.LeagueEndpoint.updateLeagueTemplate;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fansunited.automation.model.leaguesapi.request.UpdateLeagueTemplateRequest;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName(
    "Private Leagues API - POST /v1/leagues/{league_id}/template endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateLeagueTemplateTest extends LeagueBaseTest {

  private static final UpdateLeagueTemplateRequest updateLeagueTemplateRequest =
      UpdateLeagueTemplateRequest.builder().build();
  private static final String templateId = LeagueBaseTest.createTemplateForLeague().getId();

  @ParameterizedTest(name = "Verifying the update of a league template for all types of leagues - league type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(
      value = LeagueType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void verifyUpdatingLeagueTemplate(LeagueType leagueType)
      throws HttpException{

    var leagueRequestWithType = createLeagueWithTypeRequest(leagueType, leagueRequest);
    createLeagueResponse =
        CreateLeagueEndpoint.createLeague(
                FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                AuthConstants.ENDPOINTS_API_KEY,
                CLIENT_AUTOMATION_ID,
                ContentType.JSON,
                null,
                leagueRequestWithType,
                creator.getEmail())
            .as(LeagueData.class);
    leagueId = createLeagueResponse.getLeague().getId();

    var templateName = faker.funnyName().name();
    var oldTemplateId = createLeagueResponse.getLeague().getTemplateId();
    updateLeagueTemplateRequest.setOldTemplateName(templateName);
    updateLeagueTemplateRequest.setTemplateId(templateId);
    var updateLeagueTemplateResponse =
        updateLeagueTemplate(leagueId, updateLeagueTemplateRequest, creator.getEmail());
    currentTestResponse.set(updateLeagueTemplateResponse);

    updateLeagueTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.past_templates[0].id", equalTo(oldTemplateId));
  }
}
