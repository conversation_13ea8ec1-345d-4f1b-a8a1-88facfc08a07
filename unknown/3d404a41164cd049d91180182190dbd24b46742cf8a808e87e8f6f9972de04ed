{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "items": {"type": "object", "required": ["id", "profile_id", "action", "context", "points"], "properties": {"id": {"type": "string"}, "profile_id": {"type": "string"}, "action": {"type": "string"}, "context": {"type": "object", "required": ["content", "tags", "campaign"], "properties": {"content": {"type": ["object", "null"], "required": ["id", "type", "label"], "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "label": {"type": ["string", "null"]}}}, "tags": {"type": ["array", "null"], "items": {"type": "object", "required": ["id", "type", "source"], "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "source": {"type": "string"}}}}, "campaign": {"type": ["object", "null"], "required": ["id", "label"], "properties": {"id": {"type": "string"}, "label": {"type": ["null", "string"]}}}}}, "points": {"type": "integer"}}}}}}