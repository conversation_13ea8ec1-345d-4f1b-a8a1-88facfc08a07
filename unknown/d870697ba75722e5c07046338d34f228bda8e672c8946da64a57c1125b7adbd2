package com.fansunited.automation.reportingapi.predictions;

import static com.fansunited.automation.constants.ApiErrorCodes.PredictionErrorCodes.STATUS_INVALID_ARGUMENT;
import static com.fansunited.automation.constants.ApiErrorCodes.PredictionErrorCodes.STATUS_INVALID_DATE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.core.apis.reportingapi.ClientPredictionsEndpoint;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import java.time.LocalDate;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Reporting Api - GET /v1/predictions/football endpoint happy path tests")
public class GetClientPredictionsValidationTests extends ReportingApiBaseTest {

  @Test()
  @DisplayName("Get client predictions without auth")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsWithoutAuth()
      throws HttpException {

    var response =
        ClientPredictionsEndpoint.builder()
            .authToken(null)
            .withoutAuth(true)
            .build()
            .getClientPredictionCount();

    response.then().log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED)
        .body("error.message", equalTo("Unauthorized: Anonymous users not allowed."))
        .body("error.status", equalTo("unauthorized"));
  }

  @Test()
  @DisplayName("Get client predictions with 'from_date' after 'to_date'")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsWithFromDateAfterToDate()
      throws HttpException {

    var response =
        ClientPredictionsEndpoint.builder()
            .fromDate(LocalDate.now().plusMonths(1).toString())
            .toDate(LocalDate.now().toString())
            .build()
            .getClientPredictionCount();

    response.then().log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo("From date should be before to date."))
        .body("error.status", equalTo(STATUS_INVALID_ARGUMENT));
  }

  @ParameterizedTest(name = "Get client predictions with invalid date format={arguments}")
  @ValueSource(strings = {"2022/11/10", "19-01-2022", "2022 Oct 6"})
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsWithInvalidDateFormat(String invalidFromDate)
      throws HttpException {

    var response =
        ClientPredictionsEndpoint.builder()
            .fromDate(invalidFromDate)
            .toDate(LocalDate.now().toString())
            .build()
            .getClientPredictionCount();

    response.then().log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", startsWith(String.format("Text '%s' could not be parsed at index ", invalidFromDate)))
        .body("error.status", equalTo(STATUS_INVALID_DATE));
  }

  @ParameterizedTest(name = "Get client predictions with wrong date format={arguments}")
  @ValueSource(strings = {"2022-15-10", "2022-11-31", "2022-02-29"})
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsWithWrongDate(String wrongDate)
      throws HttpException {

    var response =
        ClientPredictionsEndpoint.builder()
            .fromDate(wrongDate)
            .toDate(LocalDate.now().toString())
            .build()
            .getClientPredictionCount();

    response.then().log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", startsWith(String.format("Text '%s' could not be parsed: Invalid ", wrongDate)))
        .body("error.status", equalTo(STATUS_INVALID_DATE));
  }
}
