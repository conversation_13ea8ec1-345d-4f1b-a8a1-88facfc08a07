package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetReportedPostsByLeagueIdEndpoint.getReportedPostsForLeague;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import lombok.SneakyThrows;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName(
    "Discussion Api - GET /v1/discussions/{league_id}/posts/reported endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetReportedPostsByLeagueIdValidationTest extends DiscussionApiBaseTest {

  private final String leagueAdminEmail = getUserEmail();

  private final String leagueId = getLeagueId(leagueAdminEmail);

  @ParameterizedTest(
      name =
          "Verify users are unable to get reported posts with an invalid/missing API key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getReportedPostsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getReportedPostsForLeague(
            leagueId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            leagueAdminEmail,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot get reported posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void getRepliesWithWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        getReportedPostsForLeague(
            leagueId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            argumentsHolder.jwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify users are unable to get reported posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getRepliesWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {
    var response =
        getReportedPostsForLeague(
            leagueId,
            invalidClientIdHolder.clintId(),
            FANS_UNITED_PROFILE,
            leagueAdminEmail,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when getting reported posts with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getReportedNonSupportedContentType(ContentType contentType) throws HttpException {
    var response =
        getReportedPostsForLeague(
            leagueId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            leagueAdminEmail,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that API returns NOT_FOUND when send request with non existing post ID")
  public void getReportedPostsWithInvalidLeagueIdTest() throws HttpException {
    var response =
        getReportedPostsForLeague(
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            leagueAdminEmail,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_NOT_FOUND));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that only leagues admin can view the reported posts")
  public void onlyLeagueAdminCanViewReportedPostsTest() throws HttpException {
    var response =
        getReportedPostsForLeague(
            leagueId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_FORBIDDEN));
  }

  @SneakyThrows
  private String getUserEmail() {
    return createUser().getEmail();
  }

  private String getLeagueId(String email) {
    try {
      return PostsGenerator.createPrivateLeague(1, email);
    } catch (IOException
        | ExecutionException
        | FirebaseAuthException
        | InterruptedException
        | HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
