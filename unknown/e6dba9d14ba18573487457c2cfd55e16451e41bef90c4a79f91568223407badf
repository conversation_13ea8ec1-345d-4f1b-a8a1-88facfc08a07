package com.fansunited.automation.footballapi.matches;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.AWAY_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COMPETITIONS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.CONTEXT_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_ENGLAND_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_GERMANY_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.FU_MATCH_ID_PREFIX;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.HOME_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.KICK_OFF_AT_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.LIMIT_PARAM_MAX_VALUE_MATCHES;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.MatchStatus.FINISHED;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.MatchStatus.UPCOMING;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.SUB_TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_CHELSEA;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_MAN_UTD;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TYPE_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_MATCHES_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getRandomMatchDto;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isoDatesAreInAscendingOrder;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.isoDatesAreInDescendingOrder;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.listOfStringsContainsCyrillic;
import static com.fansunited.automation.helpers.Helper.convertStringDateTimeToZonedTime;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static it.ozimov.cirneco.hamcrest.HamcrestMatchers.hasDistinctElements;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.oneOf;
import static org.hamcrest.Matchers.startsWith;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.FootballApiValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Football Api - GET /v1/matches endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetMatchesTests extends FootballApiBaseTest {

  private static final int EXPECTED_MATCHES_COUNT = 183000;

  @ParameterizedTest(name = "Verify getting list of matches for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(UrlParamValues.Language.class)
  public void getMatchesForLang(UrlParamValues.Language lang) {

    var response =
        MatchesEndpoint.getMatches(lang.getValue(), 100, -1,
            ApiConstants.FootballApi.SortField.DATE.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements())
        .body("meta.pagination.total_items", is(greaterThan(EXPECTED_MATCHES_COUNT)));

    switch (lang) {
      case EN, RO -> response
          .then()
          .assertThat()
          .body("data." + HOME_TEAM_PROP + "." + NAME_PROP,
              Every.everyItem(not(containsCyrillic())))
          .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              Every.everyItem(not(containsCyrillic())))
          .body("data." + AWAY_TEAM_PROP + "." + NAME_PROP,
              Every.everyItem(not(containsCyrillic())))
          .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              Every.everyItem(not(containsCyrillic())));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              listOfStringsContainsCyrillic())
          .body("data." + HOME_TEAM_PROP + "." + NAME_PROP, listOfStringsContainsCyrillic())
          .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              listOfStringsContainsCyrillic())
          .body("data." + AWAY_TEAM_PROP + "." + NAME_PROP, listOfStringsContainsCyrillic());
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered by single country(competition)")
  public void getMatchesByCountry() {

    var response =
        MatchesEndpoint.getMatchesWithFilterCountries(COUNTRY_ENGLAND_ID,
            UrlParamValues.Language.EN.getValue(), LIMIT_PARAM_MAX_VALUE_MATCHES, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + CONTEXT_PROP + "." + COMPETITIONS_PROP + "." + COUNTRY_PROP + "." + ID_PROP,
            everyItem(is(COUNTRY_ENGLAND_ID)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered by multiple countries(competition) -> comma separated ids")
  public void getMatchesByCountries() {

    var countries = COUNTRY_ENGLAND_ID + "," + COUNTRY_GERMANY_ID;

    var response =
        MatchesEndpoint.getMatchesWithFilterCountries(countries,
            UrlParamValues.Language.EN.getValue(), LIMIT_PARAM_MAX_VALUE_MATCHES, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + CONTEXT_PROP + "." + COMPETITIONS_PROP + "." + COUNTRY_PROP + "." + ID_PROP,
            everyItem(is(oneOf(COUNTRY_ENGLAND_ID, COUNTRY_GERMANY_ID))))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered by competition ids")
  public void getMatchesByComp() {

    var competitionIds = PREMIER_LEAGUE_COMP_ID + "," + BUNDESLIGA_COMP_ID;

    var response =
        MatchesEndpoint.getMatchesWithFilterCompetitions(competitionIds,
            UrlParamValues.Language.EN.getValue(), LIMIT_PARAM_MAX_VALUE_MATCHES, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + CONTEXT_PROP + "." + COMPETITIONS_PROP + "." + ID_PROP,
            everyItem(is(oneOf(PREMIER_LEAGUE_COMP_ID, BUNDESLIGA_COMP_ID))))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered by match ids")
  public void getMatchesByMatchIds() throws HttpException {

    var matchIds =
        MatchesEndpoint.getMatchesIdListAfterDate(getCompetitionsWhitelist(GameType.TOP_X),
            generateDateTimeInIsoFormat(ZonedDateTime.now()), 4);

    var response =
        MatchesEndpoint.getMatchesWithFilterMatchIds(String.join(",", matchIds),
            UrlParamValues.Language.EN.getValue(), -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(matchIds.size()))
        .body("data." + ID_PROP, containsInAnyOrder(matchIds.toArray()))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered by team ids")
  public void getMatchesByTeamIds() {

    var expectedTeamIds = List.of(TEAM_ID_LIVERPOOL, TEAM_ID_MAN_UTD, TEAM_ID_CHELSEA);

    var response =
        MatchesEndpoint.getMatchesWithFilterTeamIds(String.join(",", expectedTeamIds),
            UrlParamValues.Language.EN.getValue(), -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());

    var actualTeamIds = new HashSet<String>(
        response.then().extract().jsonPath().getList("data." + HOME_TEAM_PROP + "." + ID_PROP));
    actualTeamIds.addAll(
        response.then().extract().jsonPath().getList("data." + AWAY_TEAM_PROP + "." + ID_PROP));

    assertThat(actualTeamIds, containsInAnyOrder(expectedTeamIds.toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered after date")
  public void getMatchesAfterDate() {

    var fromDate = generateDateTimeInIsoFormat(ZonedDateTime.now());

    var response =
        MatchesEndpoint.getMatchesWithFilterDate(fromDate, null,
            UrlParamValues.Language.EN.getValue(), -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());

    var matchesKickOff = new ArrayList<String>(
        response.then().extract().body().jsonPath().getList("data." + KICK_OFF_AT_PROP));

    matchesKickOff.forEach(
        matchKickOff -> Assertions.assertTrue(convertStringDateTimeToZonedTime(matchKickOff)
                .isAfter(convertStringDateTimeToZonedTime(fromDate)),
            "There are matches that starts before 'from_date' query filter"));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered to date")
  public void getMatchesToDate() {

    var toDate = generateDateTimeInIsoFormat(ZonedDateTime.now());

    var response =
        MatchesEndpoint.getMatchesWithFilterDate(null, toDate,
            UrlParamValues.Language.EN.getValue(), -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());

    var matchesKickOff = new ArrayList<String>(
        response.then().extract().body().jsonPath().getList("data." + KICK_OFF_AT_PROP));

    matchesKickOff.forEach(
        matchKickOff -> Assertions.assertTrue(convertStringDateTimeToZonedTime(matchKickOff)
                .isBefore(convertStringDateTimeToZonedTime(toDate)),
            "There are matches that starts before 'to_date' query filter"));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be filtered from date to date")
  public void getMatchesFromDateToDate() {

    var fromDate = generateDateTimeInIsoFormat(ZonedDateTime.now());

    var toDate = generateDateTimeInIsoFormat(generateFutureDate(336));

    var response =
        MatchesEndpoint.getMatchesWithFilterDate(fromDate, toDate,
            UrlParamValues.Language.EN.getValue(), -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());

    var matchesKickOff = new ArrayList<String>(
        response.then().extract().body().jsonPath().getList("data." + KICK_OFF_AT_PROP));

    matchesKickOff.forEach(
        matchKickOff -> Assertions.assertTrue(convertStringDateTimeToZonedTime(matchKickOff)
                .isAfter(convertStringDateTimeToZonedTime(fromDate))
                && convertStringDateTimeToZonedTime(
                matchKickOff).isBefore(convertStringDateTimeToZonedTime(toDate)),
            "There are matches that start outside of provided range 'from_date' 'to_date' query filter"));
  }

  @ParameterizedTest(name = "Verify matches can be filtered by status. Status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(ApiConstants.FootballApi.MatchStatus.class)
  public void getMatchesByStatus(ApiConstants.FootballApi.MatchStatus matchStatus) {

    var response =
        MatchesEndpoint.getMatchesWithFilterStatus(matchStatus.getValue(),
            UrlParamValues.Language.EN.getValue(), -1,
            -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements())
        .body("data." + STATUS_PROP + "." + TYPE_PROP, everyItem(is(in(
            ApiConstants.FootballApi.MatchStatus.getStatusType(matchStatus)))))
        .body("data." + STATUS_PROP + "." + SUB_TYPE_PROP, Every.everyItem(is(in(
            ApiConstants.FootballApi.MatchStatus.getStatusSubTypes(matchStatus)))));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be sorted by field 'date' -> kickoff in ascending order")
  public void getMatchesSortedByFieldDateInAscendingOrder() {

    var response =
        MatchesEndpoint.getMatchesWithFilterCountries(COUNTRY_ENGLAND_ID,
            UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_MATCHES, -1,
            ApiConstants.FootballApi.SortField.DATE.getValue(),
            ApiConstants.SortOrder.ASC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + KICK_OFF_AT_PROP, isoDatesAreInAscendingOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify matches can be sorted by field 'date' -> kickoff in descending order")
  public void getMatchesSortedByFieldDateInDescendingOrder() {

    var response =
        MatchesEndpoint.getMatchesWithFilterCountries(COUNTRY_ENGLAND_ID,
            UrlParamValues.Language.EN.getValue(),
            LIMIT_PARAM_MAX_VALUE_MATCHES, -1,
            ApiConstants.FootballApi.SortField.DATE.getValue(),
            ApiConstants.SortOrder.DESC.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + KICK_OFF_AT_PROP, isoDatesAreInDescendingOrder())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify moving to next page for getting matches works")
  public void getMatchesMoveToNextPage() {

    var resultsLimit = generateRandomNumber(30, 50);

    var response =
        MatchesEndpoint.getMatchesWithFilterCompetitions(PREMIER_LEAGUE_COMP_ID, null, resultsLimit,
            -1,
            null, null);

    currentTestResponse.set(response);

    FootballApiValidator.validateGetMatchesByCompetitionsPaginationMoveToNextPage(response,
        PREMIER_LEAGUE_COMP_ID, resultsLimit);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/matches response returned by the server is cached for 2 minutes")
  public void verifyGetMatchesResponseIsCached() {

    var response = MatchesEndpoint.getMatches();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_MINUTES);
  }

  @Test
  @DisplayName("Verify matches status are returned as follow")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(LOCAL)})
  public void verifyFinishedMatchesAreReturnedAsFinish() {

    var matchId = getRandomMatchDto().getId();
    var matchType = "";
    ApiConstants.FootballApi.MatchStatus matchStatusEnum;

    matchType = MatchGenerator.STATUS_TO_FINISH.getType();
    matchStatusEnum = FINISHED;
    Resolver.updateMatchToAnotherStatus(matchId, MatchGenerator.STATUS_TO_FINISH);

    var response = MatchesEndpoint.getMatches(null, null, FINISHED.toString(), matchId,
        null, null, null, -1, -1, null, null,
        UrlParamValues.Language.EN.getValue(), AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
        null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", hasSize(greaterThan(0)))
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements())
        .body("data." + STATUS_PROP + "." + TYPE_PROP,
            everyItem(is(matchType)))
        .body("data." + STATUS_PROP + "." + SUB_TYPE_PROP, Every.everyItem(
            is(in(ApiConstants.FootballApi.MatchStatus.getStatusSubTypes(matchStatusEnum)))));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(LOCAL), @Tag("FZ-2047")})
  @DisplayName("Verify postponed matches are not returned as upcoming")
  public void verifyPostponedMatchesAreNotReturnedAsUpcoming() {

    var matchId = getRandomMatchDto().getId();
    var matchType = "";

    ApiConstants.FootballApi.MatchStatus matchStatusEnum;
    matchType = MatchGenerator.STATUS_POSTPONED.getType();
    matchStatusEnum = ApiConstants.FootballApi.MatchStatus.UPCOMING;
    Resolver.updateMatchToAnotherStatus(matchId, MatchGenerator.STATUS_POSTPONED);

    var response = MatchesEndpoint.getMatches(null, null, UPCOMING.toString(), matchId,
        null, null, null, -1, -1, null, null,
        UrlParamValues.Language.EN.getValue(), AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON,
        null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCHES_SCHEMA))
        .body("data", empty())
        .body("data." + ID_PROP, Every.everyItem(startsWith(FU_MATCH_ID_PREFIX)))
        .body("data." + ID_PROP, hasDistinctElements())
        .body("data." + STATUS_PROP + "." + TYPE_PROP,
            everyItem(is(matchType)))
        .body("data." + STATUS_PROP + "." + SUB_TYPE_PROP, Every.everyItem(
            is(in(ApiConstants.FootballApi.MatchStatus.getStatusSubTypes(matchStatusEnum)))));
  }
}
