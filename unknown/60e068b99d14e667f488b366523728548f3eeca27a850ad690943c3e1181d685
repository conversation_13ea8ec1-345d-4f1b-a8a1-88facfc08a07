package com.fansunited.automation.discussionapi.discussions.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_USER_ID;
import static com.fansunited.automation.core.apis.discussionapi.BanUserEndpoint.banUser;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.discussionapi.request.BanUserRequest;
import com.fansunited.automation.validators.DiscussionValidator;
import io.restassured.http.ContentType;
import java.util.Arrays;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

@DisplayName("Discussion Api - POST /v1/discussions/users/{user_id}/ban endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class BanUserTest extends DiscussionApiBaseTest {
  static List<Integer> testData() {
    return Arrays.asList(-1, 0, 3, **********);
  }

  @Test
  @DisplayName("Billing manager can not ban user")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void clientBillingManagerCanNotBanUsers() throws HttpException {

    String banReason = String.format("Inappropriate behavior. Ban for %d day/s", 3);
    var request = new BanUserRequest(3, banReason);

    String staffEmail = createStaffUser().jsonPath().getString("data.email");
    var response =
        banUser(
            request,
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            staffEmail,
            null);

    response.then().assertThat().statusCode(403);
  }

  @ParameterizedTest(name = "Ban user for given period. Days: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @MethodSource("testData")
  public void banUserForGivenPeriod(int days) {

    String banReason = String.format("Inappropriate behavior. Ban for %d day/s", days);
    var request = new BanUserRequest(days, banReason);
    var response = banUser(request, getCurrentTestUser().getUid());
    currentTestResponse.set(response);
    DiscussionValidator.validateBanUserResponse(response, request, CLIENT_AUTOMATION_USER_ID);
  }
}
