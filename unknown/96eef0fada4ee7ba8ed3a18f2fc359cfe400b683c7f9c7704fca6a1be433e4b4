package com.fansunited.automation.footballapi.matches;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.AWAY_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.HOME_TEAM_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiErrorCodes.FootballErrorCodes.CODE_NO_NEXT_MATCH;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Matches.GET_MATCH_BY_ID_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsGreek;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamNextMatchEndpoint;
import com.fansunited.automation.core.apis.footballapi.TeamsEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import com.fansunited.automation.model.footballapi.matches.FootballMatchesData;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.ZonedDateTime;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Football Api - GET /v1/teams/{id}/next-match endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetNextMatchForTeamTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify getting team next match for language: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-910")})
  @EnumSource(UrlParamValues.Language.class)
  public void getTeamNextMatchForLang(UrlParamValues.Language lang) {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response =
        TeamNextMatchEndpoint.getNextMatchForTeamId(teamId, lang.getValue(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    var match = MatchesEndpoint.getMatchesForTeamId(teamId,
        generateDateTimeInIsoFormat(ZonedDateTime.now().minusDays(10)),  generateDateTimeInIsoFormat(ZonedDateTime.now()),
        UrlParamValues.Language.EN.getValue(), 1,
        -1, ApiConstants.FootballApi.SortField.DATE.getValue(),
        ApiConstants.SortOrder.ASC.getValue()).as(FootballMatchesData.class).getData().get(0);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_MATCH_BY_ID_SCHEMA))
        .body("data." + ID_PROP, equalTo(match.getId()));

    switch (lang) {
      case EN, RO, SK, PT, SR, HU, ES, SV, NL, FR, DE, IT -> response
          .then()
          .assertThat()
          .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              not(containsCyrillic()))
          .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              not(containsCyrillic()));
      case BG -> response
          .then()
          .assertThat()
          .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              containsCyrillic())
          .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
              containsCyrillic());
      case EL -> response
              .then()
              .assertThat()
              .body("data." + HOME_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
                      containsGreek())
              .body("data." + AWAY_TEAM_PROP + "." + COUNTRY_PROP + "." + NAME_PROP,
                      not(containsCyrillic()));
    }
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/team/{id}/next-match response returned by the server is cached for 1h")
  public void verifyTeamNextMatchResponseIsCached() {

    var teamId = TeamsEndpoint.getRandomTeamIdFromCompetition(PREMIER_LEAGUE_COMP_ID);

    var response = TeamNextMatchEndpoint.getNextMatchForTeamId(teamId);

    currentTestResponse.set(response);

    if (response.statusCode() == HttpStatus.SC_NOT_FOUND) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_NOT_FOUND)
          .body("error.status", equalTo(CODE_NO_NEXT_MATCH));
      return;
    }

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_HOUR);
  }
}
