package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.PostsGenerator.createPublicPosts;
import static com.fansunited.automation.helpers.PostsGenerator.reportPost;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - POST /v1/posts/{post_id}/report endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class ReportUsersPostTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("")
  public void reportPostTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    var user = getCurrentTestUser();
    var postId = createPublicPosts(1, user.getEmail()).get(0);
    var response = reportPost(postId, null, createUser());

    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .assertThat()
        .body("data.reports_count", equalTo(1));
  }
}
