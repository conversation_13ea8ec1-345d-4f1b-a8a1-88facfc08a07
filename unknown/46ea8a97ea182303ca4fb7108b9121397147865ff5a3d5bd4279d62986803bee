package com.fansunited.automation.predictionapi.games.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionForUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getAllFixtures;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.ListOfGamesData;
import com.fansunited.automation.validators.RedisValidator;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.response.Response;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.http.HttpException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGamesInTwoMinutes;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;

@DisplayName("Prediction Api - GET /v1/games/{gameId} endpoint participants happy path tests")
public class GetGameParticipantsTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Number of Game Participants is correctly exposed for Game by Id for game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1627")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void participantsForGameById(GameType gameType)
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var numberOfUsers = 3;

    var gameId = createGames(gameType, 1).get(0);

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

    var playerId = getRandomPlayerFromMatch(matchesIdList.get(0));

    List<PredictionFixture> predictions = new ArrayList<>();

    if (gameType.equals(GameType.MATCH_QUIZ)) {
      predictions = getAllFixtures(matchesIdList.get(0), playerId);
    } else {
        for (String matchId : matchesIdList) {
            predictions.addAll(generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE,
                    matchId,
                    null));
        }
    }
    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameObject.getId())
        .build();

    List<String> predictionIdList = new ArrayList<>();

    for (int i = 0; i < numberOfUsers; i++) {

      UserRecord user = createUser();

      var response = createPredictionForUser(createPredictionRequest, user.getEmail());

      currentTestResponse.set(response);

      predictionIdList.add(response.body().jsonPath().get("id"));
    }

      for (String predictionId : predictionIdList) {
          if (gameType.equals(GameType.MATCH_QUIZ)) {
              RedisValidator.validatePredictionForMatch(predictionId, matchesIdList.get(0));
          } else {
              RedisValidator.validateGamePredictionForMatches(predictionId, matchesIdList);
          }
      }

    var response = GameEndpoint.getGameById(gameId);

    currentTestResponse.set(response);

    var gameInstance = response.as(GameInstance.class);

    Assertions.assertEquals(numberOfUsers, gameInstance.getParticipantsCount(),
        String.format("""
                The participants_count does not match the actual!
                Expected %s
                Actual   %s""", numberOfUsers, gameInstance.getParticipantsCount()));

    //Delete user
    //deleteUser(getCurrentTestUser().getUid());

    response = GameEndpoint.getGameById(gameId);

    currentTestResponse.set(response);

    gameInstance = response.as(GameInstance.class);

    Assertions.assertEquals(numberOfUsers, gameInstance.getParticipantsCount(),
        String.format("""
                The participants_count does not match the actual!
                Expected %s
                Actual   %s""", numberOfUsers, gameInstance.getParticipantsCount()));

    //TODO: Uncomment the below lines in case there is a way to delete ONLY one prediction from Redis. Now it deletes all, and then the game can not be gotten!
    //Remove prediction for one user:
    //deletePrediction(predictionId);
    //
    //response = GameEndpoint.getGameById(gameId);
    //
    //currentTestResponse.set(response);
    //
    //gameInstance = response.as(GameInstance.class);
    //
    //Assertions.assertEquals(numberOfUsers + 1, gameInstance.getParticipantsCount(),
    //    String.format("The participants_count does not match the actual!\n"
    //        + "Expected %s\n"
    //        + "Actual   %s", numberOfUsers, gameInstance.getParticipantsCount()));

    //TODO: Uncomment the below lines in case there is a way to delete ONLY the predictions from Redis. Now it deletes all, and then the game can not be gotten!

    //response = GameEndpoint.getGameById(gameId);
    //
    //currentTestResponse.set(response);
    //
    //gameInstance = response.as(GameInstance.class);
    //
    //Assertions.assertEquals(0, gameInstance.getParticipantsCount(),
    //    String.format("The participants_count does not match the actual!\n"
    //        + "Expected %s\n"
    //        + "Actual   %s", 0, gameInstance.getParticipantsCount()));

  }

  @ParameterizedTest(name = "Number of Game Participants is correctly exposed for List of Games for game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1627")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
    public void participantsForGameList(GameType gameType)
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var numberOfGames = 3;

    List<String> matchIdsList = new ArrayList<>();
    List<Integer> numberOfUsersPerGameList = new ArrayList<>();

    IntStream.range(1, 4).forEach(index -> numberOfUsersPerGameList.add(Helper.generateRandomNumber(1, 4)));

    List<String> gameIdList = new ArrayList<>();

    //Games and predictions are created in the below for cycle
    for (int i = 0; i < numberOfGames; i++) {
      var gameId = createGamesInTwoMinutes(gameType, 1).get(0);

      gameIdList.add(gameId);

      var gameObject = GameEndpoint.getGameById(gameId).as(GameInstance.class);

      var matchesIdList = gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

      var playerId = getRandomPlayerFromMatch(matchesIdList.get(0));

      List<PredictionFixture> predictions = new ArrayList<>();

      if (gameType.equals(GameType.MATCH_QUIZ)) {
        predictions = getAllFixtures(matchesIdList.get(0), playerId);
      } else {
          for (String matchId : matchesIdList) {
              predictions.addAll(generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE,
                      matchId,
                      null));
          }
      }
      var createPredictionRequest = CreatePredictionRequest.builder()
          .fixtures(predictions)
          .gameInstanceId(gameObject.getId())
          .build();

      List<String> predictionIdList = new ArrayList<>();

      for (int k = 0; k < numberOfUsersPerGameList.get(i); k++) {

        UserRecord user = createUser();

        var response = createPredictionForUser(createPredictionRequest, user.getEmail());

        currentTestResponse.set(response);

        predictionIdList.add(response.body().jsonPath().get("id"));
      }

        for (String predictionId : predictionIdList) {
            if (gameType.equals(GameType.MATCH_QUIZ)) {
                RedisValidator.validatePredictionForMatch(predictionId, matchesIdList.get(0));
            } else {
                RedisValidator.validateGamePredictionForMatches(predictionId, matchesIdList);
            }
        }

      matchIdsList.addAll(matchesIdList);
      matchesIdList.forEach(match -> updateMatchToAnotherStatus(match, MatchGenerator.STATUS_TO_LIVE));
    }

    var response = GamesEndpoint.getGamesList(gameType.getValue());

    currentTestResponse.set(response);
    var listOfGamesData = getGameList(gameType.getValue(), gameIdList);

    List<Integer> responseListForParticipantsCount =
        listOfGamesData.stream().map(GameInstance::getParticipantsCount).toList();

    int expectedParticipantsCount = numberOfUsersPerGameList.stream().reduce(0, Integer::sum);
    int actualParticipantsCount = responseListForParticipantsCount.stream().reduce(0, Integer::sum);

    Assertions.assertEquals(expectedParticipantsCount, actualParticipantsCount,
        String.format("""
                The participants_count does not match the actual!
                Expected %s
                Actual   %s""", expectedParticipantsCount, actualParticipantsCount));
  }

  private List<GameInstance> getGameList(String gameType, List<String> ids) throws HttpException {
    var gameInstances = new ArrayList<GameInstance>();
    var response = GamesEndpoint.getGamesList(gameType);

    // Parse the first page
    var currentPageOfGames = response.as(ListOfGamesData.class);

    // Filter the first page for matching IDs
    gameInstances.addAll(
        currentPageOfGames.getData().stream()
            .filter(game -> ids.contains(game.getId()))
            .toList()
    );

    // Start pagination
    var startAfter = currentPageOfGames.getMeta().getPagination().getNextPageStartsAfter();

    // Loop through remaining pages
    while (startAfter != null) {
      currentPageOfGames = GamesEndpoint.getListOfGamesAfter(gameType, startAfter).as(ListOfGamesData.class);

      // Filter the games for matching IDs
      gameInstances.addAll(
          currentPageOfGames.getData().stream()
              .filter(game -> ids.contains(game.getId()))
              .toList()
      );

      // Get the next page's starting point
      startAfter = currentPageOfGames.getMeta().getPagination().getNextPageStartsAfter();
    }

    return gameInstances;
  }
}
