package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.ApiErrorCodes.DiscussionErrorCodes.POST_DUPLICATION;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreatePostEndpoint.createPostForDiscussion;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.CreatePostRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Discussion Api - POST /v1/discussions/{discussionId}/posts endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreatePostValidationTest extends DiscussionApiBaseTest {

  private final CreatePostRequest request =
      CreatePostRequest.builder().content(new Faker().internet().uuid()).build();
  private final String discussionId = getDiscussionId();

  @ParameterizedTest(
      name =
          "Verify user is unable to create posts with an invalid/missing API key. API key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void createPostsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        createPostForDiscussion(
            request,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that users cannot create posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void createPostsWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        createPostForDiscussion(
            request,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            argumentsHolder.jwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify user is unable to create posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void createPostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        createPostForDiscussion(
            request,
            discussionId,
            invalidClientIdHolder.clintId(),
            FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that user cannot create post with missing content")
  public void creatPostWithoutContentTest() throws HttpException {
    request.setContent(null);
    var response =
        createPostForDiscussion(
            request,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(HttpStatus.SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that the user cannot submit a post with the same content.")
  public void shouldPreventDuplicatePosts() throws HttpException {
    String contentText = new Faker().lorem().sentence();
    request.setContent(contentText);
    createPostForDiscussion(
            request,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var response =
        createPostForDiscussion(
            request,
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    ErrorValidator.validateErrorResponse(
        response, List.of(HttpStatus.SC_BAD_REQUEST), POST_DUPLICATION);
  }

  private String getDiscussionId() {
    try {
      return createDiscussionForTests(false).getData().getId();
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
