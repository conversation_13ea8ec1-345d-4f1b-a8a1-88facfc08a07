package com.fansunited.automation.voting.poll.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.GetVotingByIdEndpoint.getPollById;
import static com.fansunited.automation.validators.VotingApiValidator.verifyResponsePollData;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get Poll by id happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPollByIdTests extends VotingApiPollBaseTest {

  private String pollId;

  @BeforeEach
  public void setUp() {
    var pollResponse = createPollForTest();
    pollId = pollResponse.then().extract().body().jsonPath().get("data.id");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get Poll by ID works")
  public void getPollByIdTest() throws HttpException {

    var response =
        getPollById(
            pollId,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    verifyResponsePollData(response, request);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get Poll by ID cache works")
  public void getPollByIdCacheTest() throws HttpException {

    var response =
            getPollById(
                    pollId,
                    CLIENT_AUTOMATION_ID,
                    AuthConstants.ENDPOINTS_API_KEY,
                    ContentType.JSON,
                    FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
                    getCurrentTestUser().getEmail());

     currentTestResponse.set(response);

    response.then().statusCode(HttpStatus.SC_OK);
    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_HOUR);
  }
}
