package com.fansunited.automation.discussionapi;

import static com.fansunited.automation.constants.Endpoints.DiscussionApi.CREATE_DISCUSSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.OptionsDiscussionsEndpoint.optionsDiscussionApi;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import io.restassured.response.Response;
import java.util.Arrays;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion API - OPTIONS method")
@Execution(ExecutionMode.SAME_THREAD)
public class DiscussionOptionsMethodTest extends DiscussionApiBaseTest {

  @Test
  @DisplayName(
      "Verify the discussions API using the OPTIONS method. Endpoint: OPTIONS /v1/discussions")
  @Tag(SMOKE)
  public void optionsMethodDiscussionTest() throws HttpException {
    Response response = optionsDiscussionApi(CREATE_DISCUSSION);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    var actualMethods =
        Arrays.stream(response.getHeaders().getValues("Allow").get(0).split(", ")).toList();
    var expectedMethods = ApiConstants.HttpMethods.getValuesAsList();

    assertThat(actualMethods).as(EMPTY_LIST_MESSAGE).isNotEmpty().isNotNull();
    Assertions.assertTrue(expectedMethods.containsAll(actualMethods), METHODS_MISMATCH_MESSAGE);
  }
}
