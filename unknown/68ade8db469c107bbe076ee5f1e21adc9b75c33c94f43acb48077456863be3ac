package com.fansunited.automation.footballapi;

import static com.fansunited.automation.constants.Endpoints.FootballApi.PLAYERS;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.footballapi.OptionsFootballEndpoint.optionsFootballApi;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.response.Response;
import java.util.Arrays;
import org.apache.http.HttpStatus;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Football API - OPTIONS method")
@Execution(ExecutionMode.SAME_THREAD)
public class FootballOptionsMethodTest extends FootballApiBaseTest {

  @Test
  @DisplayName("Verify Football API using the OPTIONS method. Endpoint: OPTIONS /v1/players")
  @Tag(SMOKE)
  public void optionsMethodFootballTest() {
    Response response = optionsFootballApi(PLAYERS);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var actualMethods =
        Arrays.stream(response.getHeaders().getValues("Allow").get(0).split(", ")).toList();
    var expectedMethods = ApiConstants.HttpMethods.getValuesAsList();

    assertThat(actualMethods).as(EMPTY_LIST_MESSAGE).isNotEmpty().isNotNull();
    Assertions.assertTrue(expectedMethods.containsAll(actualMethods), METHODS_MISMATCH_MESSAGE);
  }
}
