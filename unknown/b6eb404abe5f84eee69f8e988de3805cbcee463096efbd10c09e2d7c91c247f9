package com.fansunited.automation.predictionapi.predictions.get;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MATCH_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.PredictionApi.Endpoints.Predictions.GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createMatchQuizGameForMarkets;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createGamePredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createMatchQuizPredictionForGameWithSingleMarketAndSameUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionForGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionsForGameWithSpecificUser;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createSinglePrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createSinglePredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictionsForGameTypes;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.PREDICTION_COLLECTION;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGetOwnPredictionsResponse;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGetOwnPredictionsResponseWithGameTypeFilter;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GetPredictionsEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.RedisValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - GET /v1/predictions endpoint happy path tests")
public class GetOwnPredictionsTests extends PredictionApiBaseTest {

  @ParameterizedTest(name = "Verify getting own predictions(single and game) for all prediction statuses. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getMyPredictions(GameType gameType)
      throws HttpException, IllegalArgumentException, ExecutionException, InterruptedException,
      IOException {

    var predictionStatuses = PredictionStatus.getValidStatuses();

    var predictionsIdList = createSinglePredictions(predictionStatuses.size());
    predictionsIdList.addAll(createGamePredictions(gameType, predictionStatuses.size()));

    for (int i = 0; i < predictionsIdList.subList(0, predictionStatuses.size()).size(); i++) {
      FirebaseHelper.updateCollectionField(
          FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, PREDICTION_COLLECTION),
          predictionsIdList.get(i), STATUS_PROP,
          PredictionStatus.getValidStatuses().get(i).getValue());
    }

    var response = getOwnPredictions(PredictionStatus.listToCommaSeparated(predictionStatuses));

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()))
        .body("data." + STATUS_PROP,
            hasItems(predictionStatuses.stream().map(PredictionStatus::getValue).toArray()));
  }

  @ParameterizedTest(name = "Verify getting own predictions filtered by status. Prediction status: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = PredictionStatus.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getMyPredictionsForEachStatus(PredictionStatus predictionStatus)
      throws HttpException, ExecutionException, InterruptedException, IOException {

    var predictionStatuses = PredictionStatus.getValidStatuses();

    var predictionsIdList = createSinglePredictions(predictionStatuses.size());

    for (int i = 0; i < predictionsIdList.size(); i++) {
      FirebaseHelper.updateCollectionField(
          FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, PREDICTION_COLLECTION),
          predictionsIdList.get(i), STATUS_PROP,
          PredictionStatus.getValidStatuses().get(i).getValue());
    }

    var response = getOwnPredictions(predictionStatus.getValue());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data", hasSize(1))
        .body("data." + STATUS_PROP, contains(predictionStatus.getValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify all predictions are returned by the API, if 'status' query parameter is missing")
  public void getMyPredictionsWithoutStatus()
      throws HttpException, ExecutionException, InterruptedException, IOException {

    var predictionStatuses = PredictionStatus.getValidStatuses();

    var predictionsIdList = createSinglePredictions(predictionStatuses.size());

    for (int i = 0; i < predictionsIdList.size(); i++) {
      FirebaseHelper.updateCollectionField(
          FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, PREDICTION_COLLECTION),
          predictionsIdList.get(i), STATUS_PROP,
          PredictionStatus.getValidStatuses().get(i).getValue());
    }

    var response = getOwnPredictions();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()))
        .body("data." + STATUS_PROP,
            hasItems(predictionStatuses.stream().map(PredictionStatus::getValue).toArray()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/predictions response returned by the server is NOT cached")
  public void verifyGetOwnPredictionsResponseIsNotCached() throws HttpException {

    var response = getOwnPredictions();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateResponseIsNotCached(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify default limit pagination param for getting own predictions")
  public void getOwnPredictionsWithDefaultPaginationLimitParam()
      throws HttpException {

    var response = getOwnPredictions();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.items_per_page",
            equalTo(ApiConstants.LIMIT_PARAM_DEFAULT_VALUE));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination params for getting own predictions")
  public void getOwnPredictionsPaginationParams()
      throws HttpException {

    final var predictionsCount = Helper.generateRandomNumber(10, 15);

    final var resultsLimit = 5;

    createSinglePredictions(predictionsCount);

    var response =
        getOwnPredictions(null, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    var getOwnPredictionsResponse = response.as(PredictionsData.class);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(resultsLimit))
        .body("meta.pagination.items_per_page", equalTo(resultsLimit))
        .body("meta.pagination.next_page_starts_after",
            is(getOwnPredictionsResponse.getData()
                .get(getOwnPredictionsResponse.getData().size() - 1)
                .getId()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify move to next page for getting own predictions works")
  public void getOwnPredictionsPaginationMoveToNextPage() throws HttpException {

    final var predictionsCount = Helper.generateRandomNumber(20, 40);

    final var resultsLimit = Helper.generateRandomNumber(1, 5);

    var predictionsIdList = createSinglePredictions(predictionsCount);

    var response = getOwnPredictions(null, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, resultsLimit, null);

    currentTestResponse.set(response);

    validateGetOwnPredictionsResponse(response, predictionsIdList, resultsLimit);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns only predictions matching game type in game_types query filter. Filter: MATCH_QUIZ,TOP_X")
  public void getPredictionsWithMultipleGameTypesFilter()
      throws HttpException, IllegalArgumentException {

    createSinglePredictions(10);

    var predictionIds = new ArrayList<String>();
    predictionIds.addAll(createGamePredictions(GameType.MATCH_QUIZ, 10));
    predictionIds.addAll(createGamePredictions(GameType.TOP_X, 10));

    final var resultsLimit = Helper.generateRandomNumber(1, 5);

    var gameTypesCommaSeparated =
        String.join(",",
            List.of(PredictionType.MATCH_QUIZ.getValue(), PredictionType.TOP_X.getValue()));

    var response =
        getOwnPredictionsForGameTypes(gameTypesCommaSeparated, resultsLimit, null);

    currentTestResponse.set(response);

    validateGetOwnPredictionsResponseWithGameTypeFilter(response, gameTypesCommaSeparated,
        predictionIds, resultsLimit);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns only predictions matching game type in game_types query filter. Filter: SINGLE")
  public void getPredictionsWithSingleGameTypesFilter()
      throws HttpException, IllegalArgumentException {

    final var predictionsCount = Helper.generateRandomNumber(10, 20);

    var predictionIds = createSinglePredictions(predictionsCount);

    createGamePredictions(GameType.MATCH_QUIZ, 20);

    final var resultsLimit = Helper.generateRandomNumber(1, 5);

    var response =
        getOwnPredictionsForGameTypes(PredictionType.SINGLE.getValue(), resultsLimit, null);

    currentTestResponse.set(response);

    validateGetOwnPredictionsResponseWithGameTypeFilter(response, PredictionType.SINGLE.getValue(),
        predictionIds, resultsLimit);
  }

  @Test()
  @DisplayName("Verify API returns only own predictions matching match id")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getMyPredictionsByMatchId()
      throws HttpException, IllegalArgumentException {

    List<Match> matchList = MatchGenerator.generateMatches(1, false);

    Resolver.openMatchesForPredictions(matchList);

    LocalDateTime localDateTime =
        matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

    var gameInstance = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
        .matchesIdList(matchList.stream().map(Match::getId).collect(Collectors.toList()))
        .build()
        .createGame()
        .as(GameInstance.class);

    var predictionsCount = 1;

    var predictionsIdList = createPredictionsForGameWithSpecificUser(gameInstance.getId(), GameType.MATCH_QUIZ, getCurrentTestUser().getEmail(), predictionsCount);

    GetPredictionsEndpoint getPredictionsEndpoint = GetPredictionsEndpoint.builder()
        .matchIds(matchList.stream().map(Match::getId).toList())
        .build();

    var response = getPredictionsEndpoint.getOwnPredictions();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, containsInAnyOrder(predictionsIdList.toArray()))
        .body("data[0].fixtures[0]." + MATCH_ID_PROP, equalTo(matchList.get(0).getId()));

    CacheValidator.validateResponseIsNotCached(response);
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns own predictions ordered by kickoff time of the matches for Game type: MATCH QUIZ")
  public void getMyPredictionsOrderForMatchQuizGame()
      throws HttpException, IllegalArgumentException, IOException, FirebaseAuthException,
      InterruptedException, ExecutionException {

    var numberOfMatchQuizGames = 3;

    HashMap<String, LocalDateTime> predictionIdsMatchDate = new HashMap<>();

    var user = createUser();

    var matchList = MatchGenerator.generateMatches(numberOfMatchQuizGames, true);

    for (int i = 0; i < numberOfMatchQuizGames; i++) {

      Resolver.openMatchesForPredictions(List.of(matchList.get(i)));
      ResolverBase.init();
      ResolverBase.cleanUpMatchIdList.addAll(matchList.stream().map(
          com.fansunited.automation.core.resolver.hibernate.Match::getId).toList());

      LocalDateTime localDateTime =
          matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

      var gameInstance =
          createMatchQuizGameForMarkets(
              matchList.get(i).getId(),
              GameStatus.OPEN,
              List.of(PredictionMarket.CORRECT_SCORE),
              localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16)).as(GameInstance.class);

      RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

      var predictionId =createMatchQuizPredictionForGameWithSingleMarketAndSameUser(
          gameInstance.getId(),
          PredictionMarket.CORRECT_SCORE,
          user.getEmail());

      predictionIdsMatchDate.put(predictionId, matchList.get(i).getKickoffAt());
    }

    var sortedMapOfPredictionIdsAndKickoffTimesInDescOrder = new LinkedHashMap<>();

    predictionIdsMatchDate.entrySet()
        .stream()
        .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
        .forEachOrdered(
            m -> sortedMapOfPredictionIdsAndKickoffTimesInDescOrder.put(m.getKey(), m.getValue()));

    List<String> sortedPredictionIdsInDescOrder =
        sortedMapOfPredictionIdsAndKickoffTimesInDescOrder.entrySet().stream().map(e ->
            e.getKey().toString()
        ).collect(Collectors.toList());

    var response =
        GetPredictionsEndpoint.builder().build().getOwnPredictionsWithSpecificUser(user.getEmail());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, equalTo(sortedPredictionIdsInDescOrder));

    CacheValidator.validateResponseIsNotCached(response);

    ResolverBase.cleanUp();
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns own predictions ordered by kickoff time of the matches for Game type: TOP X")
  public void getMyPredictionsOrderForTopXGame()
      throws HttpException, IllegalArgumentException, IOException, FirebaseAuthException,
      InterruptedException, ExecutionException {

    var numberOfTopXGames = 3;
    List<List<String>> predictionIdList = new ArrayList<>();
    var user = createUser();
    HashMap<String, LocalDateTime> predictionIdsMatchDate = new HashMap<>();

    for (int i = 0; i < numberOfTopXGames; i++) {

      var matchList = MatchGenerator.generateMatches(6, true);

      Resolver.openMatchesForPredictions(matchList);
      ResolverBase.init();
      ResolverBase.cleanUpMatchIdList.addAll(matchList.stream().map(
          com.fansunited.automation.core.resolver.hibernate.Match::getId).toList());

      LocalDateTime localDateTime =
          matchList.stream().map(Match::getKickoffAt).sorted().collect(
              Collectors.toList()).get(0);

      var gameInstance =
          GamesEndpoint.createGame(matchList.stream().map(
                      com.fansunited.automation.core.resolver.hibernate.Match::getId).toList(),
                  GameType.TOP_X,
                  GameStatus.OPEN, localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
              .as(GameInstance.class);

      RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

      predictionIdList.add(List.of(createPredictionForGame(user.getEmail(),
          gameInstance.getId(),
          GameType.TOP_X)));

      var earliestKickoffTime = matchList
          .stream()
          .map(Match::getKickoffAt)
          .sorted(Comparator.reverseOrder())
          .findFirst()
          .orElseThrow(() -> new NoSuchElementException("Could not get Kickoff Time from Match"));
      predictionIdsMatchDate.put(predictionIdList.get(i).get(0), earliestKickoffTime);
    }

    var sortedMapOfPredictionIdsAndKickoffTimesInDescOrder = new LinkedHashMap<>();

    predictionIdsMatchDate.entrySet()
        .stream()
        .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
        .forEachOrdered(
            m -> sortedMapOfPredictionIdsAndKickoffTimesInDescOrder.put(m.getKey(), m.getValue()));

    List<String> sortedPredictionIdsInDescOrder =
        sortedMapOfPredictionIdsAndKickoffTimesInDescOrder.entrySet().stream().map(e ->
            e.getKey().toString()
        ).collect(Collectors.toList());

    var response =
        GetPredictionsEndpoint.builder().build().getOwnPredictionsWithSpecificUser(user.getEmail());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, equalTo(sortedPredictionIdsInDescOrder));

    CacheValidator.validateResponseIsNotCached(response);

    ResolverBase.cleanUp();
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify API returns own predictions ordered by kickoff time of the matches for Game type: SINGLE PREDICTION")
  public void getMyPredictionsOrderForSinglePredictionGame()
      throws HttpException, IllegalArgumentException, IOException, FirebaseAuthException,
      InterruptedException, ExecutionException {

    var numberOfSinglePredictionGames = 3;

    HashMap<String, LocalDateTime> predictionIdsMatchDate = new HashMap<>();

    var matchList = MatchGenerator.generateMatches(3, true);

    for (int i = 0; i < numberOfSinglePredictionGames; i++) {

      Resolver.openMatchesForPredictions(List.of(matchList.get(i)));
      ResolverBase.init();
      ResolverBase.cleanUpMatchIdList.addAll(matchList.stream().map(
          com.fansunited.automation.core.resolver.hibernate.Match::getId).toList());

      var playerId = getRandomPlayerFromMatch(matchList.get(i).getId());

      var predictionId = createSinglePrediction(matchList.get(i).getId(), playerId,
          PredictionMarket.CORRECT_SCORE);

      predictionIdsMatchDate.put(predictionId, matchList.get(i).getKickoffAt());
    }

    var sortedMapOfPredictionIdsAndKickoffTimesInDescOrder = new LinkedHashMap<>();

    predictionIdsMatchDate.entrySet()
        .stream()
        .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
        .forEachOrdered(
            m -> sortedMapOfPredictionIdsAndKickoffTimesInDescOrder.put(m.getKey(), m.getValue()));

    List<String> sortedPredictionIdsInDescOrder =
        sortedMapOfPredictionIdsAndKickoffTimesInDescOrder.entrySet().stream().map(e ->
            e.getKey().toString()
        ).collect(Collectors.toList());

    var response =
        GetPredictionsEndpoint.builder()
            .build()
            .getOwnPredictionsWithSpecificUser(getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_OWN_OR_SPECIFIC_USER_PREDICTIONS_SCHEMA))
        .body("data." + ID_PROP, equalTo(sortedPredictionIdsInDescOrder));

    CacheValidator.validateResponseIsNotCached(response);

    ResolverBase.cleanUp();
  }
}
