package com.fansunited.automation.automation;


import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@DisplayName("Automation testing component - common tests")
public class AutomationComponentTests {

  @ParameterizedTest(name = "Happy path SyncHelper - Threads: {0}, Midcondition Delay: {1}ms")
  @MethodSource("testParameters")
  public void happyPathSyncHelper(long delay) {
    sleep(delay);
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        TestSynchronizationHelper.DEFAULT_MIDCONDITION, "dummy");
    sleep(delay);
  }

  @Disabled
  @ParameterizedTest(name = "Happy path SyncHelper - Threads: {0}, Midcondition Delay: {1}ms")
  @MethodSource("get100RandomTimes")
  public void syncHelper100Tests(long delay) {
    TestSynchronizationHelper.getInstance().setChunkTimeoutSeconds(5);
    sleep(delay);
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        TestSynchronizationHelper.DEFAULT_MIDCONDITION, "dummy");
    sleep(delay);
  }

  @Disabled
  @ParameterizedTest(name = "Happy path SyncHelper - Threads: {0}, Midcondition Delay: {1}ms")
  @MethodSource("get100RandomTimes")
  public void syncHelper100Tests1Thread(long delay) {
    TestSynchronizationHelper.getInstance().setChunkTimeoutSeconds(5);
    TestSynchronizationHelper.getInstance().setMaxThreads(1);
    sleep(delay);
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        TestSynchronizationHelper.DEFAULT_MIDCONDITION, "dummy");
    sleep(delay);
  }

  @Disabled
  @ParameterizedTest(name = "Happy path SyncHelper - Threads: {0}, Midcondition Delay: {1}ms")
  @MethodSource("get100RandomTimes")
  public void syncHelper100Tests10Threads(long delay) {
    TestSynchronizationHelper.getInstance().setChunkTimeoutSeconds(5);
    TestSynchronizationHelper.getInstance().setMaxThreads(10);
    sleep(delay);
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        TestSynchronizationHelper.DEFAULT_MIDCONDITION, "dummy");
    sleep(delay);
  }

  @Disabled
  @ParameterizedTest(name = "Happy path SyncHelper - Threads: {0}, Midcondition Delay: {1}ms")
  @MethodSource("get100RandomTimes")
  public void syncHelper100TestsThreads200Midcondition1000(long delay) {
    TestSynchronizationHelper.getInstance().setChunkTimeoutSeconds(5);
    TestSynchronizationHelper.getInstance().setMaxThreads(200);
    sleep(delay);
    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        TestSynchronizationHelper.DEFAULT_MIDCONDITION, "dummy");
    sleep(delay);
  }

  private static Stream<Arguments> testParameters() {
    return Stream.of(
        Arguments.of(1),
        Arguments.of(50),
        Arguments.of(500),
        Arguments.of(2000),
        Arguments.of(4000)
    );
  }

  private static Stream<Arguments> get100RandomTimes() {
    return IntStream.rangeClosed(1, 100)
        .mapToObj(Arguments::of).toList()
        .stream();
  }


  private static void sleep(long ms) {
    try {
      Thread.sleep(ms);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
  }
}
