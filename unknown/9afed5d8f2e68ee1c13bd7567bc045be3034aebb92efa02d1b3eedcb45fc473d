package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsEndpoint.getDiscussionsPosts;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import com.fansunited.automation.validators.PostValidator;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Discussion Api - GET /v1/discussions/{discussionId}/posts endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPostsForDiscussionTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get posts by discussion ID")
  public void getPostsByDiscussionIdTest() throws HttpException {
    int postsCount = generateRandomNumber(3, 8);
    String discussionId = createDiscussion(new CreateDiscussionRequest()).jsonPath().get("data.id");
    PostsGenerator.addPostToPrivateDiscussion(
        discussionId, getCurrentTestUser().getEmail(), postsCount);

    var response = getDiscussionsPosts(discussionId);

    currentTestResponse.set(response);
    PostValidator.validateMetaTotalItems(response, postsCount);
  }
}
