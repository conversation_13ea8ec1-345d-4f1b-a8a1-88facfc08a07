package com.fansunited.automation.footballapi.players;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_ENGLAND_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.COUNTRY_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Players.GET_PLAYERS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.helpers.CustomHamcrestMatchers.containsCyrillic;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.footballapi.PlayersEndpoint;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.hamcrest.core.Every;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/players endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPlayersValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify players cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getPlayersWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var response = PlayersEndpoint.getPlayers(UrlParamValues.Language.EN.getValue(),
        argumentsHolder.getApiKey(),
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify players cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getPlayersWithInvalidOrEmptyLang(String lang) {

    var response =
        PlayersEndpoint.getPlayers(lang, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify players are fetched in EN, if \"lang\" query param is not specified")
  public void getPlayersWithoutLanguageParam() {

    var response =
        PlayersEndpoint.getPlayers(null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data." + NAME_PROP, Every.everyItem(not(containsCyrillic())))
        .body("data." + COUNTRY_PROP + "." + NAME_PROP, Every.everyItem(not(containsCyrillic())));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting players with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getPlayersWithNotSupportedContentType(ContentType contentType) {

    var response = PlayersEndpoint.getPlayers(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify players are fetched in JSON format if content type is NOT specified")
  public void getPlayersWithoutSpecifyingContentType() {

    var response = PlayersEndpoint.getPlayers(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA))
        .body("data", not(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting players with invalid 'country' param")
  public void getPlayersWithInvalidCountry() {

    var response =
        PlayersEndpoint.getPlayersWithFilterCountry("fb:c:1519259125912512", null, -1, -1, null,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'country' param when empty")
  public void getPlayersWithEmptyCountry() {

    var response = PlayersEndpoint.getPlayersWithFilterCountry("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting players with invalid 'name' param")
  public void getPlayersWithInvalidName() {

    var response =
        PlayersEndpoint.getPlayersWithFilterName("INVALID_NAME", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'name' param when empty")
  public void getPlayersWithEmptyName() {

    var response = PlayersEndpoint.getPlayersWithFilterName("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting players with invalid 'scope' param")
  public void getPlayersWithInvalidScope() {

    var response =
        PlayersEndpoint.getPlayersWithFilterScope("fb:c:155125125", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'scope' param when empty")
  public void getPlayersWithEmptyScope() {

    var response =
        PlayersEndpoint.getPlayersWithFilterScope("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting players with invalid 'player_ids' param")
  public void getPlayersWithInvalidPlayersIds() {

    var response =
        PlayersEndpoint.getPlayersWithFilterPlayerIds(
            "fb:p:5101241204120412412421,fb:p:969129129411241241241124", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'player_ids' param when empty")
  public void getPlayersWithEmptyPlayersIds() {

    var response =
        PlayersEndpoint.getPlayersWithFilterPlayerIds("", null, -1, -1, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting players with invalid 'sort_field' param")
  public void getPlayersWithInvalidSortField() {

    var response =
        PlayersEndpoint.getPlayersWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, "INVALID",
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'sort_field' param when empty")
  public void getPlayersWithEmptySortField() {

    var response =
        PlayersEndpoint.getPlayersWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, "",
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting players with invalid 'sort_order' param")
  public void getPlayersWithInvalidSortOrder() {

    var response =
        PlayersEndpoint.getPlayersWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, null,
            "INVALID");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'sort_order' param when empty")
  public void getPlayersWithEmptySortOrder() {

    var response =
        PlayersEndpoint.getPlayersWithFilterCountry(COUNTRY_ENGLAND_ID, null, -1, -1, null,
            "");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PLAYERS_SCHEMA));
  }
}
