
package com.fansunited.automation.clientsapi.rewards;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_REWARDS;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.ActivityActionType;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.Point;
import com.fansunited.automation.model.clientapi.features.response.Rewards;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;

@DisplayName("Client Api - GET /v1/clients/{clientId}/feature/external_points validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateRewardsValidationTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1843")})
  @DisplayName("Verify external points can not be updated when the permission is missing")
  public void canNotUpdateRewardsWhenHaveNoPermission() throws HttpException {

    var rewardBody = Rewards.builder()
        .enabled(true)
        .points(List.of(Point.builder()
            .points(20)
            .id(ActivityActionType.DISLIKE.getValue())
            .maxCount(23456)
            .build(), Point.builder()
            .points(24)
            .id(ActivityActionType.LIKE.getValue())
            .maxCount(23456)
            .build()))
        .build();

    var response =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, BILLING_USER
            , rewardBody, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_REWARDS);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1843")})
  @DisplayName("Verify external points can not be updated when the permission is missing")
  public void canNotUpdateRewardsWithInvalidOrEmptyJwtToken() throws HttpException {

    var rewardBody = Rewards.builder()
        .enabled(true)
        .points(List.of(Point.builder()
            .points(20)
            .id(ActivityActionType.DISLIKE.getValue())
            .maxCount(23456)
            .build(), Point.builder()
            .points(24)
            .id(ActivityActionType.LIKE.getValue())
            .maxCount(23456)
            .build()))
        .build();

    var response =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, BILLING_USER
            , rewardBody, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_REWARDS);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify average can not update features with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void updateFeatureWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var rewardBody = Rewards.builder()
        .enabled(true)
        .points(List.of(Point.builder()
            .points(20)
            .id(ActivityActionType.LIKE.getValue())
            .maxCount(23456)
            .build(), Point.builder()
            .points(24)
            .id(ActivityActionType.DISLIKE.getValue())
            .maxCount(23456)
            .build()))
        .build();

    var response =
        ClientFeaturesEndpoint.updateFeaturesWithInvalidToken(rewardBody,
            String.valueOf(argumentsHolder), null, null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_REWARDS);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_FORBIDDEN), is(HttpStatus.SC_UNAUTHORIZED)));
  }
}