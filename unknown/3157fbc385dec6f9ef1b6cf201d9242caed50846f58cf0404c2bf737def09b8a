package com.fansunited.automation.predictionapi.predictions.delete;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.MATCH_ID_PROP;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getFullCoverageCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.generateValidFixturesForGameType;
import static com.fansunited.automation.core.apis.predictionapi.PredictionEndpoint.deletePrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createGamePredictions;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPrediction;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.generateValidSinglePredictionFixture;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getAllFixtures;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.getOwnPredictions;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.validators.PredictionApiValidator.validateCreatePredictionResponse;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import com.fansunited.automation.validators.RedisValidator;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Prediction Api - DELETE /v1/predictions endpoint happy path tests")
public class DeletePredictionTests extends PredictionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify deleting single prediction")
  public void deleteSinglePrediction() throws HttpException {

    //Create a game
    var predictionsCutoff = generateFutureDate(12);

    var gameType = GameType.MATCH_QUIZ;

    var gameFixtureList =
        generateValidFixturesForGameType(gameType, predictionsCutoff);

    var createGameRequest = CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.OPEN.getValue())
        .build();

    var createGameResponse = createGame(createGameRequest);

    var gameInstance = createGameResponse.as(GameInstance.class);

    currentTestResponse.set(createGameResponse);

    var match = gameInstance.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(match);

    var predictions = getAllFixtures(match, playerId);

    var createPredictionRequest = CreatePredictionRequest.builder()
        .fixtures(predictions)
        .gameInstanceId(gameInstance.getId())
        .build();

    createPrediction(createPredictionRequest);
    var matchId = getOwnPredictions().then()
        .extract()
        .jsonPath()
        .getList("data." + FIXTURES_PROP + "." + MATCH_ID_PROP)
        .get(0).toString();

    var predictionId = getOwnPredictions().then()
        .extract()
        .jsonPath()
        .getList("data." + ID_PROP)
        .get(0).toString();

    var deletePredictionResponse = deletePrediction(predictionId);

    currentTestResponse.set(deletePredictionResponse);

    deletePredictionResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    getOwnPredictions()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));

    RedisValidator.validatePredictionForMatchNotExist(predictionId, matchId);
  }

  @ParameterizedTest(name = "Verify deleting game prediction. Game type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_INVALID_OR_SINGLE)
  public void deleteGamePrediction(GameType gameType) throws HttpException {

    var predictionId = createGamePredictions(gameType, 1).get(0);

    var matchIdList = getOwnPredictions().then()
        .extract()
        .jsonPath()
        .getList("data." + FIXTURES_PROP + "." + MATCH_ID_PROP)
        .stream()
        .map(Object::toString)
        .toList();

    var deletePredictionResponse = deletePrediction(predictionId);

    currentTestResponse.set(deletePredictionResponse);

    deletePredictionResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    getOwnPredictions()
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));

    RedisValidator.validateGamePredictionForMatchesNotExist(predictionId, matchIdList);
  }


  @ParameterizedTest(name = "Delete single prediction for BigQ: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void deleteSinglePredictionInBQ(PredictionMarket market)
      throws HttpException {

    var matchId = getSingleMatchIdAfterDate(getFullCoverageCompetitionsWhitelist(),
        Helper.generateDateTimeInIsoFormat(Helper.generateFutureDate(12)));

    var playerId = getRandomPlayerFromMatch(matchId);

    var prediction = generateValidSinglePredictionFixture(market, matchId, playerId);

    var createSinglePredictionRequest = CreatePredictionRequest.builder()
        .fixtures(prediction).build();

    var response = createPrediction(createSinglePredictionRequest);

    currentTestResponse.set(response);

    validateCreatePredictionResponse(response, createSinglePredictionRequest, true, getCurrentTestUser());

    var predictionInstance = response.as(PredictionInstance.class);

    RedisValidator.validatePredictionForMatch(predictionInstance.getId(), matchId);

    var getOwnPredictionsResponse = getOwnPredictions();

    getOwnPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var deletePredictionResponse = deletePrediction(predictionInstance.getId());

    currentTestResponse.set(deletePredictionResponse);

    deletePredictionResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    Assertions.assertEquals(predictionInstance,
        getOwnPredictionsResponse.as(PredictionsData.class).getData().get(0),
        "Prediction instance in own predictions does NOT match made prediction");
  }
}
