package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreateDiscussionEndpoint.createDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.GetPostsByUserIdEndpoint.getPostByUserIdPost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.PostsGenerator.getDiscussionPostsIds;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.model.discussionapi.request.CreateDiscussionRequest;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get post by user Id - GET /v1/posts/users/{userId} happy path.")
@Execution(ExecutionMode.SAME_THREAD)
public class GetPostByUserIdTest extends DiscussionApiBaseTest {

  private static String email;

  @BeforeEach
  public void createTestData() {
    email = getCurrentTestUser().getEmail();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get discussions posts by users id is cached for 30 min")
  public void getPostByUserIdTest() throws IllegalArgumentException, HttpException {

    var response =
        getPostByUserIdPost(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.THIRTY_MINUTES);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Get discussions posts by users id with skipModerated parameter")
  public void getPostByUserIdWithSkipModeratedTest()
      throws IllegalArgumentException, HttpException {

    // Create a discussion and add posts
    var discussionId =
        createDiscussion(new CreateDiscussionRequest()).jsonPath().get("data.id").toString();
    PostsGenerator.addPostToPrivateDiscussion(discussionId, getCurrentTestUser().getEmail(), 4);

    var postIds = getDiscussionPostsIds(discussionId);

    // Moderate some posts
    PostsGenerator.moderatePosts(postIds.subList(0, 2));

    // First, get posts without skipModerated parameter
    var responseWithoutSkip =
        getPostByUserIdPost(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    responseWithoutSkip
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(2))
        .body("data.findAll { it.moderated == true }.size()", equalTo(0))
        .body("meta.pagination.items_per_page", equalTo(postIds.size() - 2));

    // Then get posts with skipModerated=false
    var responseWithSkip =
        getPostByUserIdPost(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            email,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            false);

    responseWithSkip
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(4))
        .body("data.findAll { it.moderated == true }.size()", equalTo(2))
        .body("meta.pagination.items_per_page", equalTo(postIds.size()));
  }
}
