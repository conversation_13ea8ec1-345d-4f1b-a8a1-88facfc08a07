
package com.fansunited.automation.clientsapi.features.patch_either_or;

import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_EITHER_OR;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint.updateFeaturesWithInvalidClientIdPathParam;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static java.util.List.of;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.BasicObject;
import com.fansunited.automation.model.clientapi.features.response.EitherOrFeature;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Client Api - GET /v1/clients/{clientId}/feature/either_or endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFeaturesEitherOrValidationTests extends ClientApiBaseTest {

  public static String label = new Faker().animal().name();

  public static String id = new Faker().number().digits(10);

  @Test
  @DisplayName("Verify that billing manager can not update either_or features")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void billingManagerCantUpdateQuizFeatures() throws HttpException {

    EitherOrFeature eitherOrFeature = EitherOrFeature.builder().types(of(BasicObject.builder()
            .label(label)
            .id(id)
            .build()))
        .build();

    var updateEitherOrFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, BILLING_MANAGER_EMAIL,
            eitherOrFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_EITHER_OR);

    currentTestResponse.set(updateEitherOrFeaturesResponse);

    updateEitherOrFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify client can not {arguments} action")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @NullAndEmptySource
  public void clientByIdUpdatesIllegallyActionWeightValue(String invalidData) throws HttpException {

    EitherOrFeature eitherOrFeature = EitherOrFeature.builder().types(of(BasicObject.builder()
            .label(invalidData)
            .id(invalidData)
            .build()))
        .build();

    var updateEitherOrFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, null,
            eitherOrFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_EITHER_OR);

    currentTestResponse.set(updateEitherOrFeaturesResponse);

    updateEitherOrFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update either_or features with no body ")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void cantUpdateEitherOrFeaturesWithNoBody() throws HttpException {

    var updateEitherOrFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, null,
            "null",
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_EITHER_OR);

    currentTestResponse.set(updateEitherOrFeaturesResponse);

    updateEitherOrFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Verify that can not update either_or features with not specified content type")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void cantUpdateQuizFeaturesWithNotSpecifiedContentType() throws HttpException {
    
    EitherOrFeature eitherOrFeature = EitherOrFeature.builder().types(of(BasicObject.builder()
            .label(label)
            .id(id)
            .build()))
        .build();

    var updateEitherOrFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, null,
            eitherOrFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,null, FEATURES_EITHER_OR);

    currentTestResponse.set(updateEitherOrFeaturesResponse);

    updateEitherOrFeaturesResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @ParameterizedTest(
      name = "Verify API returns BAD_REQUEST when update either_or features. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  public void updateQuizFeaturesInvalidClientId(String clientId) throws HttpException {

    EitherOrFeature quizFeature =
        EitherOrFeature.builder()
            .types(of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateEitherOrFeaturesResponse =
        updateFeaturesWithInvalidClientIdPathParam(clientId, quizFeature);

    currentTestResponse.set(updateEitherOrFeaturesResponse);

    ErrorValidator.validateErrorResponse(
        updateEitherOrFeaturesResponse, List.of(HttpStatus.SC_BAD_REQUEST));
  }
}
