package com.fansunited.automation.loyaltyapi.statistics;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.potm.VoteForPotmEndpoint.putVoteForPotm;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.MatchGenerator.generateMatch;
import static com.fansunited.automation.core.resolver.MatchTimelineGenerator.generateMatchPlayers;
import static com.fansunited.automation.core.resolver.Resolver.updateEventPlayer;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.PlayerMatchStatsGenerator.generatePlayerIds;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.GeneralActivityId;
import com.fansunited.automation.core.apis.loyaltyapi.enums.Tier;
import com.fansunited.automation.core.apis.mockapi.MockEventEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.model.clientapi.features.response.FeaturesResponse;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultStatus;
import com.fansunited.automation.model.voting.potm.VoteForPotmRequest;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Loyalty Api - GET /v1/statistics endpoint happy path tests")
public class GetOwnStatisticsTests extends AuthBase {

  @ParameterizedTest(
      name =
          "Verify getting own statistics and validate user is placed in the respective tier based on earned points. Tier: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = Tier.class)
  public void getOwnStatistics(Tier tier) throws Exception {

    var resp = FeaturesEndpoint.getClientFeatures(CLIENT_AUTOMATION_ID);
    var features = resp.as(FeaturesResponse.class);

    var registrationPoints =
        features.getData().getLoyalty().getRewards().getPoints().getGeneral().stream()
            .filter(
                point -> point.getId().equalsIgnoreCase(GeneralActivityId.REGISTRATION.getValue()))
            .findFirst()
            .get()
            .getPoints();

    var points =
        features.getData().getLoyalty().getRewards().getTiers().stream()
            .filter(tierReward -> tierReward.getId().equalsIgnoreCase(tier.name()))
            .findFirst()
            .get()
            .getPoints();

    if (tier != Tier.BRONZE) { // User starts from bronze tier upon registration
      var predictionLastUpdate =
          LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

      MockEventEndpoint.generateRankingEvent(
          LocalDateTime.now().minusDays(5),
          getCurrentTestUser().getUid(),
          PredictionMarket.FT_1X2,
          PredictionType.SINGLE,
          points,
          null,
          ResultOutcome.CORRECT,
          ResultStatus.SETTLED,
          MatchByIdEndpoint.getMatchDtoById("fb:m:158507"),
          1,
          "1",
          predictionLastUpdate);
    }

    BigQueryHelper.waitForEventsToBeSaved(10);

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail());

    currentTestResponse.set(response);
    points += registrationPoints;
    LoyaltyApiValidator.validateStatisticsResponse(
        response, getCurrentTestUser().getUid(), points, tier);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/statistics response returned by the server is NOT cached")
  public void verifyOwnStatisticsResponseIsNotCached() throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    CacheValidator.validateResponseIsNotCached(response);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getOwnPotmStatistics() throws Exception {

    // Initialize test data
    var match = generateMatch();
    var playerIds = generatePlayerIds(match);
    var matchPlayersMap = generateMatchPlayers(match, playerIds);
    var email = getCurrentTestUser().getEmail();

    // Set type "start" for all players
    matchPlayersMap.values().forEach(player -> player.setType("start"));

    init();
    cleanUpMatchIdList.add(match.getId());

    Resolver.openMatchForPredictions(match);
    match.setPlayers(new ArrayList<>(matchPlayersMap.values()));

    updateEventPlayer(matchPlayersMap);
    Resolver.updateMatchToBeFinishedInThePast(match.getId(), 31);

    var playerId = playerIds.get(0);
    var response = putVoteForPotm(match.getId(), new VoteForPotmRequest(playerId), email);
    currentTestResponse.set(response);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.player_id", equalTo(playerId));

    var clientFicturesResponse = FeaturesEndpoint.getClientFeatures(CLIENT_AUTOMATION_ID);
    int registrationPoints =
        clientFicturesResponse
            .jsonPath()
            .getInt("data.loyalty.rewards.points.general.find{it.id == 'registration'}.points");

    int expectedPotmVotePoints =
        clientFicturesResponse
            .jsonPath()
            .getInt("data.loyalty.rewards.points.voting.potm[0].points");

    // Get own statistics and ensure potm vote points are positive
    var actualPotmVotePoints =
        Math.abs(
            StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, email)
                    .jsonPath()
                    .getInt("data.points")
                - registrationPoints);

    Assertions.assertEquals(
        expectedPotmVotePoints,
        actualPotmVotePoints,
        "POTM vote points do not match expected value");
  }
}
