{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["next_page_starts_after", "items_per_page"], "properties": {"next_page_starts_after": {"type": ["string", "null"]}, "items_per_page": {"type": "integer"}}}}}, "data": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "required": ["id", "game_instance_id", "game_type", "wager", "total_fixtures", "settled_fixtures", "user_id", "fixtures", "status", "tiebreaker", "points", "created_at", "updated_at"], "properties": {"id": {"type": "string"}, "game_instance_id": {"type": ["null", "string"]}, "game_type": {"type": "string", "enum": ["TOP_X", "MATCH_QUIZ", "SINGLE"]}, "wager": {"type": "null"}, "total_fixtures": {"type": "integer"}, "settled_fixtures": {"type": "integer"}, "user_id": {"type": "string"}, "fixtures": {"type": "array", "minItems": 1, "items": {"anyOf": [{"type": "object", "properties": {"match_id": {"type": "string"}, "match_type": {"type": "string"}, "result": {"type": "object", "required": ["settled_at", "resettled_at", "status", "outcome", "points"], "properties": {"settled_at": {"type": ["string", "null"]}, "resettled_at": {"type": ["string", "null"]}, "status": {"type": "string", "enum": ["NOT_SETTLED", "SETTLED", "RESETTLED", "VOID"]}, "outcome": {"type": "string", "enum": ["NOT_VERIFIED", "CORRECT", "PARTIAL", "INCORRECT", "VOID"]}, "points": {"type": "integer"}}}, "market": {"type": "string", "enum": ["FT_1X2", "HT_1X2", "BOTH_TEAMS_SCORE", "OVER_GOALS_0_5", "OVER_GOALS_1_5", "OVER_GOALS_2_5", "OVER_GOALS_3_5", "OVER_GOALS_4_5", "OVER_GOALS_5_5", "OVER_GOALS_6_5", "OVER_CORNERS_6_5", "OVER_CORNERS_7_5", "OVER_CORNERS_8_5", "OVER_CORNERS_9_5", "OVER_CORNERS_10_5", "OVER_CORNERS_11_5", "OVER_CORNERS_12_5", "OVER_CORNERS_13_5", "DOUBLE_CHANCE", "HT_FT", "PLAYER_SCORE", "PLAYER_YELLOW_CARD", "PLAYER_RED_CARD", "RED_CARD_MATCH", "PENALTY_MATCH", "PLAYER_SCORE_FIRST_GOAL", "CORNERS_MATCH", "CORRECT_SCORE", "CORRECT_SCORE_HT", "CORRECT_SCORE_ADVANCED", "PLAYER_SCORE_HATTRICK", "PLAYER_SCORE_TWICE"]}, "prediction": {"type": ["string", "boolean", "integer"]}}, "oneOf": [{"properties": {"market": {"enum": ["CORRECT_SCORE", "CORRECT_SCORE_HT", "CORRECT_SCORE_ADVANCED"]}}, "required": ["match_id", "match_type", "result", "market"]}, {"properties": {"market": {"enum": ["FT_1X2", "HT_1X2", "BOTH_TEAMS_SCORE", "OVER_GOALS_0_5", "OVER_GOALS_1_5", "OVER_GOALS_2_5", "OVER_GOALS_3_5", "OVER_GOALS_4_5", "OVER_GOALS_5_5", "OVER_GOALS_6_5", "OVER_CORNERS_6_5", "OVER_CORNERS_7_5", "OVER_CORNERS_8_5", "OVER_CORNERS_9_5", "OVER_CORNERS_10_5", "OVER_CORNERS_11_5", "OVER_CORNERS_12_5", "OVER_CORNERS_13_5", "DOUBLE_CHANCE", "HT_FT", "PLAYER_SCORE", "PLAYER_YELLOW_CARD", "PLAYER_RED_CARD", "RED_CARD_MATCH", "PENALTY_MATCH", "PLAYER_SCORE_FIRST_GOAL", "CORNERS_MATCH", "CORRECT_SCORE", "CORRECT_SCORE_HT", "CORRECT_SCORE_ADVANCED", "PLAYER_SCORE_HATTRICK", "PLAYER_SCORE_TWICE"]}}, "required": ["match_id", "match_type", "result", "market", "prediction"]}]}]}}, "status": {"type": "string", "enum": ["ACTIVE", "WON", "LOST", "PARTIALLY_WON", "CANCELED"]}, "tiebreaker": {"type": ["null", "object"]}, "points": {"type": "integer"}, "created_at": {"type": "string"}, "updated_at": {"type": ["string"]}}}]}}}}