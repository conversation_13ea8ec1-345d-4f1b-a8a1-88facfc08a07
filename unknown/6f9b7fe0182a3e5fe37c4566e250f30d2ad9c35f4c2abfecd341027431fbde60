package com.fansunited.automation.reportingapi.predictions;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.validators.ReportingValidator.validateClientPredictionsFilteredByMarket;
import static com.fansunited.automation.validators.ReportingValidator.validateClientPredictionsForPeriod;
import static com.fansunited.automation.validators.ReportingValidator.validateClientPredictionsGroupedBy;
import static com.fansunited.automation.validators.ReportingValidator.validateClientPredictionsWithoutParamsReturnsThirtyDays;
import static com.fansunited.automation.validators.ReportingValidator.validateClientTotalPredictions;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.core.apis.mockapi.ReportingEndpoint;
import com.fansunited.automation.core.apis.reportingapi.ClientPredictionsEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.core.base.reportingapi.TruncateTablesHelper;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.MockHelper;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@DisplayName("Reporting Api - GET /v1/predictions/football endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetClientPredictionsTests extends ReportingApiBaseTest {
  static int predictionsCount = 9;
  static List<LocalDateTime> timeStamps = MockHelper.generateTimeStampsForEvent(predictionsCount);
  static List<String> predictionLastUpdates = MockHelper.generatePredictionLastUpdatesForEvent(timeStamps, predictionsCount);

  @BeforeAll
  public static void beforeAll() {
    var userId = UUID.randomUUID().toString();

    MockHelper.mockEventForEachMarket(timeStamps, predictionLastUpdates, userId);
    BigQueryHelper.waitForEventsToBeSaved(10);
  }

  @AfterAll
  public static void afterAll() {
    // this should not be done in separate test classes, but in base test only. Unfortunately, some
    // tests in reporting api need this to be run once more
    TruncateTablesHelper.executeTruncateTables();
  }

  @ParameterizedTest(name = "Get client predictions returns correct number of predictions grouped by = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  @EnumSource(value = GroupBy.class)
  @NullSource
  public void getClientPredictionsGroupedBy(GroupBy groupBy)
      throws Exception {

    var dateFrom = timeStamps.get(timeStamps.size() - 1).format(DateTimeFormatter.ISO_LOCAL_DATE);
    var dateTo = timeStamps.get(0).format(DateTimeFormatter.ISO_LOCAL_DATE);

    /**
     * When returning predictionCount for Week, the toDate when not set equals to LocalDateTime.now().minusDays(1).
     * This is why the first event must be saved one day earlier.
     */
    timeStamps.set(0, LocalDateTime.now().minusDays(1));

    /**
     * In the below request the dateFrom and dateTo are set, because otherwise the response
     * will return data only for the last 30 days.
     */
    var response =
        ClientPredictionsEndpoint.builder()
            .groupBy(groupBy)
            .fromDate(dateFrom)
            .marketList(PredictionMarket.getValidMarkets().stream().map(m -> m.getValue()).collect(Collectors.toList()))
            .toDate(dateTo)
            .build()
            .getClientPredictionCount();

    response.then().log().all();

    validateClientPredictionsGroupedBy(response, groupBy, predictionsCount, dateFrom, dateTo);
  }

  @ParameterizedTest(name = "Get client predictions returns correct number of predictions filtered by valid market = {arguments}")
  @EnumSource(value = PredictionMarket.class, mode = EnumSource.Mode.MATCH_ANY,
      names = {"CORRECT_SCORE", "PENALTY_MATCH", "OVER_CORNERS_12_5", "OVER_CORNERS_6_5", "FT_1X2"} )
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsFilteredByMarket(PredictionMarket market)
      throws Exception {

    /**
     * When returning predictionCount for Week, the toDate when not set equals to LocalDateTime.now().minusDays(1).
     * This is why the first event must be saved one day earlier.
     */
    timeStamps.set(0, LocalDateTime.now().minusDays(1));

    var response =
        ClientPredictionsEndpoint.builder()
            .marketList(List.of(market.getValue()))
            .fromDate(timeStamps.get(timeStamps.size() - 1).format(DateTimeFormatter.ISO_LOCAL_DATE).toString())
            .build()
            .getClientPredictionCount();

    response.then().log().all();

    if (PredictionMarket.CORRECT_SCORE.equals(market)) {
      validateClientPredictionsFilteredByMarket(response, market.getValue(), 2);
    } else {
      validateClientPredictionsFilteredByMarket(response, market.getValue(), 1);
    }
  }

  @Test()
  @DisplayName("Get client predictions returns zero prediction when filtered by invalid market")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsFilteredByInvalidMarket()
      throws Exception {
    String invalidMarket = "INVALID_MARKET";

    var response =
        ClientPredictionsEndpoint.builder()
            .marketList(List.of(invalidMarket))
            .build()
            .getClientPredictionCount();

    response.then().log().ifValidationFails()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST) // Assert on 400 Bad Request
        .body("error.code", equalTo(400))
        .body("error.message", equalTo("Invalid or deprecated market."))
        .body("error.status", equalTo("invalid_market"));
  }

  @Test()
  @DisplayName("Get client predictions by day returns predictions for the last 30 days")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsWithNoParamsReturnsLastThirtyDays()
      throws Exception {

    var response =
        ClientPredictionsEndpoint.builder()
            .build()
            .getClientPredictionCount();

    response.then().log().all();

    validateClientPredictionsWithoutParamsReturnsThirtyDays(response);
  }

  @Test()
  @DisplayName("Get client predictions for period")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsForPeriod()
      throws Exception {

    String fromDate =
        LocalDateTime.now()
        .minusMonths(Helper.generateRandomNumber(1, predictionsCount - 1))
            .minusDays(Helper.generateRandomNumber(1, 30))
            .format(DateTimeFormatter.ISO_LOCAL_DATE);

    String toDate =
        LocalDateTime.now()
            .minusDays(Helper.generateRandomNumber(1, 25))
            .format(DateTimeFormatter.ISO_LOCAL_DATE);

    var response =
        ClientPredictionsEndpoint.builder()
            .fromDate(fromDate)
            .toDate(toDate)
            .build()
            .getClientPredictionCount();

    response.then().log().all();

    validateClientPredictionsForPeriod(response, fromDate, toDate, timeStamps);
  }

  @Test()
  @DisplayName("Get client predictions for period with fromDate only")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsForPeriodWithFromDateOnly()
      throws Exception {

    String fromDate =
        LocalDateTime.now()
            .minusMonths(Helper.generateRandomNumber(1, predictionsCount - 1))
            .minusDays(Helper.generateRandomNumber(1, 30))
            .format(DateTimeFormatter.ISO_LOCAL_DATE);

    var response =
        ClientPredictionsEndpoint.builder()
            .fromDate(fromDate)
            .build()
            .getClientPredictionCount();

    response.then().log().all();

    validateClientPredictionsForPeriod(response, fromDate, LocalDate.now().minusDays(1).toString(), timeStamps);
  }

  @Test()
  @DisplayName("Get client predictions for period with toDate only")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientPredictionsForPeriodWithToDateOnly()
      throws Exception {

    String fromDate = LocalDate.now().minusDays(30).toString();

    var response =
        ClientPredictionsEndpoint.builder()
            .toDate(LocalDate.now().toString())
            .build()
            .getClientPredictionCount();

    response.then().log().all();

    validateClientPredictionsForPeriod(response, fromDate, LocalDate.now().toString(), timeStamps);
  }

  @Test()
  @DisplayName("Get client total predictions")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-266")})
  public void getClientTotalPredictions()
      throws Exception {

    var response =
        ClientPredictionsEndpoint.builder()
            .build()
            .getClientTotalPredictions();

    validateClientTotalPredictions(response, predictionsCount);
  }

}
