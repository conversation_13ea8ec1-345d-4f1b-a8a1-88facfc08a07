package com.fansunited.automation.loyaltyapi.activity.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProviderUser;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.loyaltyapi.ActivitiesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.ActivityBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - GET /v1/activities endpoint validation tests")
public class GetOwnActivitiesValidationTests extends ActivityBaseTest {

  @ParameterizedTest(name = "Verify own activities cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void getOwnActivitiesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(null, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own activities with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getOwnActivitiesWithInvalidClientId(String clientId)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(null, user.get().getEmail(), clientId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify own activities cannot be fetched with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProviderUser.class)
  public void getOwnActivitiesWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(argumentsHolder.getJwtToken(), null, null, true,
            null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify activities cannot be fetched without JWT token")
  public void getOwnActivitiesWithoutJwtToken() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(null, "xyz123", null, false,
            null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when getting own activities with invalid action filter")
  public void getOwnActivitiesWithInvalidActionFilter() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(user.get().getEmail(), "INVALID");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns all activities when getting own activities with empty action filter")
  public void getOwnActivitiesWithEmptyActionFilter() throws HttpException, InterruptedException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(user.get().getEmail(), "");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own activities with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getOwnActivitiesWithNonSupportedContentType(ContentType contentType)
      throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(user.get().getEmail(), null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify own activities are fetched in JSON format if content type is NOT specified")
  public void getOwnActivitiesWithoutSpecifyingContentType() throws HttpException {
    BigQueryHelper.waitForEventsToBeSaved(60);
    var response =
        ActivitiesEndpoint.getOwnActivities(null, user.get().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }
}
