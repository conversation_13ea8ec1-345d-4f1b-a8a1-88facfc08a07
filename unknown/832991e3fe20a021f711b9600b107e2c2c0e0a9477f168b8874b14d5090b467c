package com.fansunited.automation.reportingapi.markets;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Markets.GET_SUCCESS_RATE_PER_CLIENT_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.reportingapi.SuccessRateEndpoint;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/success-rates/football/markets endpoint happy path tests")
public class GetSuccessRatesPerUserTests extends ReportingApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("success rate happy path")
  public void getSuccessRateForClient() throws Exception {

    var gameId = GamesEndpoint.createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1", LocalDateTime.now().toString());
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1", LocalDateTime.now().toString());
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1", LocalDateTime.now().toString());

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1", LocalDateTime.now().toString());
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1", LocalDateTime.now().toString());

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1", LocalDateTime.now().toString());
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1", LocalDateTime.now().toString());

    Thread.sleep(10000);

    var fromDate = LocalDate.now().minusMonths(1).toString();
    var toDate = LocalDate.now().minusDays(1).toString();

    var response =
        SuccessRateEndpoint.getSuccessRateForClient(CLIENT_AUTOMATION_ID, fromDate, toDate,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            GET_SUCCESS_RATE_PER_CLIENT_SCHEMA));
  }
}
