package com.fansunited.automation.footballapi.competitions;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.NAME_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.FootballApi.Endpoints.Competitions.GET_COMPETITIONS_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getCompetitions;
import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getCompetitionsWithFilters;
import static com.fansunited.automation.core.apis.footballapi.FootballCountriesEndpoint.getFootballCountries;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.footballapi.FootballApiBaseTest;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.apache.http.HttpStatus;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Football Api - GET /v1/competitions endpoint validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetCompetitionsValidationTests extends FootballApiBaseTest {

  @ParameterizedTest(name = "Verify competitions cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getCompetitionsWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) {

    var response = getCompetitions(UrlParamValues.Language.EN.getValue(),
        argumentsHolder.getApiKey(),
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify competitions cannot be fetched with invalid or non-supported language. Language: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = {"tr", "bgg"})
  @EmptySource
  public void getCompetitionsWithInvalidOrEmptyLang(String lang) {

    var response =
        getCompetitions(lang, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    if (lang.isEmpty()) {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_OK)
          .body("data.", is(notNullValue()));
    } else {
      response
          .then()
          .assertThat()
          .statusCode(HttpStatus.SC_BAD_REQUEST)
          .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_LANGUAGE));
    }
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify competitions are fetched in EN, if \"lang\" query param is not specified")
  public void getCompetitionsWithoutLanguageParam() {

    var response =
        getCompetitions(null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + NAME_PROP, Matchers.anyOf(hasItem("Romania Cup")));
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting competitions with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED),@Tag("FZ-1640")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getCompetitionsWithNotSupportedContentType(ContentType contentType) {

    var response = getFootballCountries(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify competitions are fetched in JSON format if content type is NOT specified")
  public void getCompetitionsWithoutSpecifyingContentType() {

    var response = getFootballCountries(UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body("data." + NAME_PROP, hasItem("Aruba"));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting competitions with invalid 'country' param")
  public void getCompetitionsWithInvalidCountry() {

    var response =
        getCompetitionsWithFilters("fb:c:1519259125912512", null, null, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'country' param when empty")
  public void getCompetitionsWithEmptyCountry() {

    var response = getCompetitionsWithFilters("", null, null, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_COMPETITIONS_SCHEMA));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns empty data when getting competitions with invalid 'name' param")
  public void getCompetitionsWithInvalidName() {

    var response = getCompetitionsWithFilters(null, "INVALID_NAME", null, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'name' param when empty")
  public void getCompetitionsWithEmptyName() {

    var response = getCompetitionsWithFilters(null, "", null, null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", not(empty()))
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_COMPETITIONS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting competitions with invalid 'gender' param")
  public void getCompetitionsWithInvalidGender() {

    var response = getCompetitionsWithFilters(null, null, "INVALID", null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores gender param when empty")
  public void getCompetitionsWithEmptyGender() {

    var response = getCompetitionsWithFilters(null, null, "", null, null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_COMPETITIONS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting competitions with invalid 'type' param")
  public void getCompetitionsWithInvalidType() {

    var response = getCompetitionsWithFilters(null, null, null, "INVALID", null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'type' param when empty")
  public void getCompetitionsWithEmptyType() {

    var response = getCompetitionsWithFilters(null, null, null, "", null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_COMPETITIONS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting competitions with invalid 'sort_field' param")
  public void getCompetitionsWithInvalidSortField() {

    var response = getCompetitionsWithFilters(null, null, null, null, "invalid", null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'sort_field' param when empty")
  public void getCompetitionsWithEmptySortField() {

    var response = getCompetitionsWithFilters(null, null, null, null, "", null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_COMPETITIONS_SCHEMA));
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Verify API returns BAD_REQUEST when getting competitions with invalid 'sort_order' param")
  public void getCompetitionsWithInvalidSortOrder() {

    var response = getCompetitionsWithFilters(null, null, null, null, null, "invalid");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API ignores 'sort_order' param when empty")
  public void getCompetitionsWithEmptySortOrder() {

    var response = getCompetitionsWithFilters(null, null, null, null, null, "");

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_COMPETITIONS_SCHEMA));
  }
}
