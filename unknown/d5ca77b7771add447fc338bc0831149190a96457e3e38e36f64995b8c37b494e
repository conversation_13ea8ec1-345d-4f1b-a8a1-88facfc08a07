package com.fansunited.automation.predictionapi.userdata;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.CURRENT_PROFILE_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.NEW_PROFILE_ID_PROP;
import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.predictionapi.DeleteUserDataPredictionEndpoint.deleteUserData;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGame;
import static com.fansunited.automation.core.apis.predictionapi.WinnersEndpoint.getWinnersByContestId;
import static com.fansunited.automation.helpers.Helper.generateMD5;
import static com.fansunited.automation.helpers.RequestGenerator.getCreateGameRequest;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;
import static org.junit.jupiter.api.Assertions.assertAll;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.apis.predictionapi.RedisEndpoint;
import com.fansunited.automation.core.base.predictionapi.PredictionApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.FullTimeOneXTwoPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OneXTwoOutcome;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.games.response.PredictionInstance;
import com.fansunited.automation.validators.RedisValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@SuppressWarnings("unchecked")
@DisplayName("Prediction Api - DELETE /v1/user-data endpoint happy path tests")
public class DeleteUserDataPredictionTest extends PredictionApiBaseTest {

  @ParameterizedTest(
      name = "Verify user data can be deleted from Redis collection - Game type: {arguments}")
  @Tag(REGRESSION)
  @Tag(SMOKE)
  @EnumSource(
      value = GameType.class,
      mode = EnumSource.Mode.INCLUDE,
      names = {"TOP_X", "MATCH_QUIZ"})
  public void deleteUserDataFromRedisCollectionTest(GameType gameType) throws HttpException {

    var game = createGame(getCreateGameRequest(gameType)).as(GameInstance.class);
    var matchId = game.getFixtures().get(0).getMatchId();

    var createPredictionRequest =
        CreatePredictionRequest.builder()
            .fixtures(
                List.of(
                    FullTimeOneXTwoPredictionFixture.builder()
                        .matchId(matchId)
                        .matchType(MatchType.FOOTBALL.getValue())
                        .prediction(OneXTwoOutcome.getRandomOneXTwoOutcome())
                        .build()))
            .build();

    var predictionId =
        PredictionsEndpoint.createPredictionForUser(
                createPredictionRequest, getCurrentTestUser().getEmail())
            .as(PredictionInstance.class)
            .getId();

    // Validates that the prediction exists in Redis and is linked to the current match
    RedisValidator.validatePredictionForMatch(predictionId, matchId);

    var currentProfileId = getCurrentTestUser().getUid();
    var newProfileId = String.format("%s%s", "delete_", generateMD5(currentProfileId));

    JSONObject body = new JSONObject();
    body.put(CURRENT_PROFILE_ID_PROP, currentProfileId);
    body.put(NEW_PROFILE_ID_PROP, newProfileId);

    var response = deleteUserData(CLIENT_AUTOMATION_ID, ENDPOINTS_API_KEY, ContentType.JSON, body);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    currentTestResponse.set(response);

    // Validates that the prediction is not existing anymore in Redis
    RedisEndpoint.getPredictionsForMatch(matchId)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(not(containsString(predictionId)));
  }

  @Test
  @DisplayName("Verify user data is successfully deleted from fu_winners collection")
  @Tag(REGRESSION)
  @Tag(SMOKE)
  public void deleteUserDataFromFuWinners() throws HttpException {

    var profileId = getCurrentTestUser().getUid();

    var gameId = setWinnerWithPrediction(getCurrentTestUser());
    // Delete user data request
    var newProfileId = String.format("%s%s", "delete_", generateMD5(profileId));
    JSONObject body = new JSONObject();
    body.put(CURRENT_PROFILE_ID_PROP, profileId);
    body.put(NEW_PROFILE_ID_PROP, newProfileId);

    var response = deleteUserData(CLIENT_AUTOMATION_ID, ENDPOINTS_API_KEY, ContentType.JSON, body);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    currentTestResponse.set(response);

    var winnerId =
        getWinnersByContestId(
                gameId,
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
                ADMIN_USER,
                ContentType.JSON)
            .then()
            .assertThat()
            .statusCode(HttpStatus.SC_OK)
            .extract()
            .jsonPath()
            .getString("user_list[0].profile_id");

    Assertions.assertEquals(
        newProfileId,
        winnerId,
        "The winner ID in the collection does not match the new profile ID");
  }

  @Test
  @DisplayName(
      "Verify user data is successfully deleted from excluded profile IDs list in fu_game collection")
  @Tag(REGRESSION)
  @Tag(SMOKE)
  public void deleteUserDataFromExcludedProfileIds() throws HttpException {

    var profileId = getCurrentTestUser().getUid();
    var gameId = setExcludedProfileIdsWithPrediction(getCurrentTestUser());
    var newProfileId = String.format("%s%s", "delete_", generateMD5(profileId));

    JSONObject body = new JSONObject();
    body.put(CURRENT_PROFILE_ID_PROP, profileId);
    body.put(NEW_PROFILE_ID_PROP, newProfileId);

    // Delete user data request
    deleteUserData(CLIENT_AUTOMATION_ID, ENDPOINTS_API_KEY, ContentType.JSON, body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var excludedIds = gameInstance.getExcludedProfileIds();

    assertAll(
        "Profile IDs in excluded list",
        () ->
            Assertions.assertFalse(
                excludedIds.contains(profileId),
                "Original profile ID should not be in the excluded IDs list"),
        () ->
            Assertions.assertTrue(
                excludedIds.contains(newProfileId),
                "New profile ID should be present in the excluded IDs list"));
  }
}
