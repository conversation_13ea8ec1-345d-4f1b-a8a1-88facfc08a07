{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["rank_type", "from_date", "to_date", "pagination"], "properties": {"rank_type": {"type": "null"}, "from_date": {"type": "null"}, "to_date": {"type": "null"}, "pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "items": {"type": "object", "required": ["id", "name", "type", "from_date", "to_date", "markets", "team_ids", "match_ids", "game_ids", "game_types", "competition_ids", "rules", "flags"], "properties": {"id": {"type": ["string"]}, "name": {"type": ["string", "null"]}, "type": {"type": ["string"], "enum": ["PRIMARY", "CUSTOM", "COMPETITION", "FANTASY"]}, "from_date": {"type": ["string", "null"]}, "to_date": {"type": ["string", "null"]}, "markets": {"type": ["array", "null"]}, "team_ids": {"type": ["array", "null"]}, "match_ids": {"type": ["array", "null"]}, "game_ids": {"type": ["array", "null"]}, "game_types": {"type": ["array", "null"]}, "competition_ids": {"type": ["array", "null"]}, "rules": {"type": ["string", "null"]}, "flags": {"type": ["array", "null"], "items": [{"type": ["string", "null"]}]}}}}}}