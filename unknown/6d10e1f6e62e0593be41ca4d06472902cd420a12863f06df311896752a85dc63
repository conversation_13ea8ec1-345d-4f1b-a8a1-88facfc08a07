package com.fansunited.automation.reportingapi.audit;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;

import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import com.github.javafaker.Faker;
import java.time.LocalDate;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Reporting Api - GET /v1/audit/logs endpoint happy path tests")
public class GetAuditLogsTests extends ReportingApiBaseTest {

  @Test()
  @DisplayName("Get all audit results for client")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1540")})
    public void getAuditResults() throws HttpException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(Helper.convertLocalDateToIsoDate(LocalDate.now()))
        .toDate(Helper.convertLocalDateToIsoDate(LocalDate.now().plusMonths(1)))
        .markets(markets)
        .labels(Fields.builder()
            .label1("test")
            .label2("test1_?123!?/")
            .build())
        .customFields(Fields.builder()
            .label2("123test")
            .label2("tesgf!/_")
            .build())
        .ad_content("<p>test</p>")
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

  }
}
