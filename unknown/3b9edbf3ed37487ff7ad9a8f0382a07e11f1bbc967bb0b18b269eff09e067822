package com.fansunited.automation.discussionapi.posts.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.discussionapi.ModeratePostEndpoint.moderatePost;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_NOT_FOUND;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenArguments;
import com.fansunited.automation.arguments.leagueapi.InvalidJwtTokenHolder;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.model.discussionapi.request.ModeratePostRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

@DisplayName("Discussion Api - POST /v1/posts/{post_id}/admin/moderate endpoint moderation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class ModeratePostByAdminValidationTest extends DiscussionApiBaseTest {
  private final ModeratePostRequest request =
      ModeratePostRequest.builder().moderationReason(new Faker().lorem().sentence(1)).build();

  @ParameterizedTest(
      name =
          "Verify admin is unable to moderate posts with an invalid/missing API key. API key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void moderatePostWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        moderatePost(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(argumentsHolder.getStatusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify that admin cannot moderate posts with an invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArguments.class)
  public void moderatePostWithInvalidJwtToken(InvalidJwtTokenHolder argumentsHolder)
      throws HttpException {
    var response =
        moderatePost(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            null,
            argumentsHolder.jwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(argumentsHolder.statusCode()));
  }

  @ParameterizedTest(
      name =
          "Verify admin is unable to moderate posts with an invalid/missing client ID. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(value = InvalidClientIdArguments.class)
  public void moderatePostWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    var response =
        moderatePost(
            request,
            createPostForDiscussionsTests().getData().getId(),
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @ParameterizedTest(
      name =
          "Verify admin is unable to moderate posts with an invalid/missing moderation reason. Moderation Reason: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void moderatePostWithInvalidModerationReason(String invalidReason) throws HttpException {
    request.setModerationReason(invalidReason);

    var response =
        moderatePost(
            request,
            createPostForDiscussionsTests().getData().getId(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_BAD_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that admin cannot moderate post with invalid/missing post id")
  public void moderatePostWithInvalidPostId() throws HttpException {
    var response =
        moderatePost(
            request,
            INVALID_ID,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            null);

    ErrorValidator.validateErrorResponseEmptyBody(response, List.of(SC_NOT_FOUND));
  }
}
