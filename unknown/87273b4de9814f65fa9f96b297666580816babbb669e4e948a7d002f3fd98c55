{"type": "object", "required": ["meta", "data"], "properties": {"meta": {"type": "object", "required": ["pagination"], "properties": {"pagination": {"type": "object", "required": ["current_page", "items_per_page", "total_items", "number_of_pages"], "properties": {"current_page": {"type": "integer"}, "items_per_page": {"type": "integer"}, "total_items": {"type": "integer"}, "number_of_pages": {"type": "integer"}}}}}, "data": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "country", "birth_date", "first_name", "last_name", "position", "assets", "name", "teams"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "birth_date": {"type": ["string", "null"]}, "first_name": {"type": ["string", "null"]}, "last_name": {"type": ["string", "null"]}, "position": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["headshot"], "properties": {"headshot": {"type": ["string", "null"]}}}, "name": {"type": "string"}, "teams": {"type": "array", "items": {"anyOf": [{"type": "object", "required": ["id", "country", "assets", "national", "code", "gender", "name", "full_name", "short_name"], "properties": {"id": {"type": "string"}, "country": {"type": "object", "required": ["id", "alias", "country_code", "assets", "name"], "properties": {"id": {"type": "string"}, "alias": {"type": ["string", "null"]}, "country_code": {"type": ["string", "null"]}, "assets": {"type": ["object", "null"], "required": ["flag"], "properties": {"flag": {"type": ["string", "null"]}}}, "name": {"type": "string"}}}, "assets": {"type": ["object", "null"], "required": ["logo"], "properties": {"logo": {"type": ["string", "null"]}}}, "national": {"type": "boolean"}, "code": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "short_name": {"type": ["string", "null"]}}}]}}}}]}}}}