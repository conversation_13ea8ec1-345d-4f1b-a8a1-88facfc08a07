package com.fansunited.automation.discussionapi.posts.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.discussionapi.GetReportedPostsByLeagueIdEndpoint.getReportedPostsForLeague;
import static com.fansunited.automation.helpers.PostsGenerator.addPostToPrivateDiscussion;
import static com.fansunited.automation.helpers.PostsGenerator.createPrivateLeague;
import static com.fansunited.automation.helpers.PostsGenerator.reportPost;

import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.helpers.PostsGenerator;
import com.fansunited.automation.validators.PostValidator;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Discussion Api - GET /v1/discussions/{league_id}/posts/reported endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetReportedPostsByLeagueIdTest extends DiscussionApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get reported posts by league ID")
  public void getReportedPostsByLeagueIDTest()
      throws HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {
    String email = createUser().getEmail();
    String leagueId = createPrivateLeague(1, email);
    addPostToPrivateDiscussion(leagueId, email, 6);
    List<String> postIds = PostsGenerator.getDiscussionPostsIds(leagueId);

    for (int i = 0; i < 3; i++) {
      reportPost(postIds.get(i), null, createUser());
    }
    var response = getReportedPostsForLeague(leagueId, email);
    currentTestResponse.set(response);
    PostValidator.validateAllPostsAreReported(response);
  }
}
