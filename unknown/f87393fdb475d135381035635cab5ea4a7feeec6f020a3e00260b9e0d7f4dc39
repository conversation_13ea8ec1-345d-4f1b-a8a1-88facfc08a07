{"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "members": {"type": "array", "items": [{"type": "string"}]}, "administrators": {"type": "array", "items": [{"type": "string"}]}, "banned": {"type": ["array", "null"], "items": [{"type": "string"}]}, "invites": {"type": ["array", "null"], "items": [{"type": "string"}]}, "description": {"type": "string"}, "users_can_invite_users": {"type": "boolean"}, "past_templates": {"type": ["array", "null"], "items": [{"type": "string"}]}, "invitation_code": {"type": "string"}, "template_id": {"type": "string"}, "pinned_posts": {"type": ["array", "null"], "items": [{"type": "string"}]}, "scoring_starts_at": {"type": "string"}}, "required": ["id", "name", "type", "members", "administrators", "banned", "invites", "description", "users_can_invite_users", "past_templates", "invitation_code", "template_id", "pinned_posts", "scoring_starts_at"]}}, "required": ["data"]}