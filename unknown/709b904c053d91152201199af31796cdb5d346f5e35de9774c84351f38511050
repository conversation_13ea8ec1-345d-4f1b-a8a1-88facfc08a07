package com.fansunited.automation.discussionapi.discussions.get;

import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsCountViaPostOrGetRequest.getDiscussionsPostsCountViaGetRequest;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

@DisplayName("Discussion Api - GET /v1/posts/count endpoint happy path tests")
public class GetDiscussionPostCountValidationTest extends DiscussionApiBaseTest {
  private final String fakeDiscussionId = UUID.randomUUID().toString();

  @ParameterizedTest(
      name =
          "Verify post count by discussion id cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getPostCountsByDiscussionIdsWithInvalidApyKeyTest(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {
    var response =
        getDiscussionsPostsCountViaGetRequest(
            fakeDiscussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify that the API returns a BAD_REQUEST when sending a request with a non-supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getPostCountByDiscussionIdsWithNonSupportedContentTypeTest(ContentType contentType)
      throws HttpException {
    var response =
        getDiscussionsPostsCountViaGetRequest(
            fakeDiscussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            null);

    currentTestResponse.set(response);
    response
        .then()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .assertThat()
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify discussion data cannot be fetched with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getPostCountByDiscussionIdsWithInvalidClientIdTest(
      InvalidClientIdHolder invalidClientIdHolder) throws HttpException {

    var response =
        getDiscussionsPostsCountViaGetRequest(
            fakeDiscussionId,
            invalidClientIdHolder.clintId(),
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(name = "Should return BAD REQUEST (400) when discussion ID is {0}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void getPostCountByInvalidDiscussionIds(String invalidDiscussionId)
      throws HttpException {

    var response =
        getDiscussionsPostsCountViaGetRequest(
            invalidDiscussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(
        response, List.of(HttpStatus.SC_BAD_REQUEST), STATUS_VALIDATION_ERROR);
  }
}
