#!/usr/bin/env bash
# Run test suite
# Input variables TEST_SUITE and ENV_PROFILE

export GOOGLE_APPLICATION_CREDENTIALS=src/test/resources/fans-united-stage-BQ.json

echo "TEST_SUITE: $TEST_SUITE"
echo "ENV_PROFILE: $ENV_PROFILE"
echo "GOOGLE_APPLICATION_CREDENTIALS: $GOOGLE_APPLICATION_CREDENTIALS"

EXCLUDED=disabled
if [ "$ENV_PROFILE" == "prod" ]; then
  EXCLUDED=disabled,local
fi

echo "EXCLUDED: $EXCLUDED"

if [ "$ENV_PROFILE" == "stage" ]; then
  # Start cloud proxy for mysql
  curl https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -o cloud_sql_proxy
  chmod +x cloud_sql_proxy
  ./cloud_sql_proxy -instances=fans-united-stage:europe-west1:shared=tcp:3306 &
fi
suite_executed=0
if [ "$TEST_SUITE" == "profile-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.profileapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE -Dsurefire.rerunFailingTestsCount=2
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "football-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.footballapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "loyalty-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.loyaltyapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE -Dsurefire.rerunFailingTestsCount=2
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "prediction-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.predictionapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE -Dsurefire.rerunFailingTestsCount=2
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "reporting-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.reportingapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "resolver" ]; then
  mvn clean test -Dtest="com.fansunited.automation.resolver.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE -Dsurefire.rerunFailingTestsCount=2
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "firebasefunctions" ]; then
  mvn clean test -Dtest="com.fansunited.automation.firebasefunctions.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "client-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.clientsapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "private-leagues-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.leaguesapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "discussions-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.discussionapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "minigames" ]; then
  mvn clean test -Dtest="com.fansunited.automation.minigames.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "voting-api" ]; then
  mvn clean test -Dtest="com.fansunited.automation.voting.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "all" ]; then
  mvn clean test -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ "$TEST_SUITE" == "disabled" ]; then
  mvn clean test -Dtest="com.fansunited.automation.footballapi.**" -DexcludedGroups=$EXCLUDED -Denv=$ENV_PROFILE
  export status=$?
  suite_executed=1
fi

if [ $suite_executed == 0 ]; then
  echo "Error: No test suite executed"
  echo 127 >status.txt
  exit 127
fi

echo $status >status.txt

pkill -f cloud_sql_proxy